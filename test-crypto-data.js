// Simple script to test fetching cryptocurrency data
import axios from 'axios';

async function testCryptoApis() {
  try {
    console.log("Testing Bitcoin data:");
    const btcResponse = await axios.get('http://localhost:5000/api/crypto/historical?cryptocurrency=bitcoin');
    console.log("Bitcoin data source:", btcResponse.data.source);
    console.log("Bitcoin data sample:", btcResponse.data.prices.slice(0, 2));
    
    console.log("\nTesting Solana data:");
    const solResponse = await axios.get('http://localhost:5000/api/crypto/historical?cryptocurrency=solana');
    console.log("Solana data source:", solResponse.data.source);
    console.log("Solana data sample:", solResponse.data.prices.slice(0, 2));
    
    // Try other coins
    console.log("\nTesting Ethereum data:");
    const ethResponse = await axios.get('http://localhost:5000/api/crypto/historical?cryptocurrency=ethereum');
    console.log("Ethereum data source:", ethResponse.data.source);
    console.log("Ethereum data sample:", ethResponse.data.prices.slice(0, 2));
    
  } catch (error) {
    console.error("Error testing crypto APIs:", error.message);
    if (error.response) {
      console.error("Response data:", error.response.data);
    }
  }
}

testCryptoApis();