import { 
  users, 
  activityLogs,
  chatMessages,
  paymentOrders,
  type User, 
  type InsertUser,
  type ActivityLog,
  type InsertActivityLog,
  type ChatMessage,
  type InsertChatMessage,
  type PaymentOrder,
  type InsertPaymentOrder
} from "@shared/schema";
import * as bcrypt from 'bcrypt';
import { log } from './vite';
import { db, pool } from './db';
import { eq, desc, and, or } from 'drizzle-orm';

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;
  validatePassword(user: User, password: string): Promise<boolean>;
  updateLastLogin(id: number): Promise<void>;
  
  // Activity log methods
  logActivity(activityLog: InsertActivityLog): Promise<ActivityLog>;
  getActivityLogs(userId?: number, limit?: number): Promise<ActivityLog[]>;
  
  // Chat message methods
  saveChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  getChatHistory(userId1: number, userId2: number, limit?: number): Promise<ChatMessage[]>;
  markMessagesAsRead(fromUserId: number, toUserId: number): Promise<void>;
  getUnreadMessageCount(userId: number): Promise<number>;
  
  // Payment order methods
  createPaymentOrder(order: InsertPaymentOrder): Promise<PaymentOrder>;
  getAllPaymentOrders(): Promise<PaymentOrder[]>;
  getPaymentOrderById(id: number): Promise<PaymentOrder | undefined>;
  updatePaymentOrderStatus(id: number, status: string, userId: number, notes?: string): Promise<PaymentOrder | undefined>;
  
  // System setup
  ensureAdminExists(): Promise<void>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }
  
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Hash the password before storing
    const hashedPassword = await bcrypt.hash(insertUser.password, 10);
    
    const [user] = await db.insert(users).values({
      username: insertUser.username,
      password: hashedPassword,
      fullName: insertUser.fullName || null,
      email: insertUser.email || null,
      role: insertUser.role || 'admin',
      isActive: insertUser.isActive !== undefined ? insertUser.isActive : true,
      twoFactorEnabled: insertUser.twoFactorEnabled !== undefined ? insertUser.twoFactorEnabled : false,
      twoFactorSecret: insertUser.twoFactorSecret || null,
      notificationSettings: insertUser.notificationSettings || null,
      appearanceSettings: insertUser.appearanceSettings || null
    }).returning();
    
    return user;
  }
  
  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    // If updating password, hash it
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }
    
    const [updatedUser] = await db.update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();
    
    return updatedUser;
  }
  
  async deleteUser(id: number): Promise<boolean> {
    const result = await db.delete(users).where(eq(users.id, id));
    return !!result.rowCount && result.rowCount > 0;
  }
  
  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }
  
  async updateLastLogin(id: number): Promise<void> {
    await db.update(users)
      .set({ lastLogin: new Date() })
      .where(eq(users.id, id));
  }
  
  async logActivity(insertActivityLog: InsertActivityLog): Promise<ActivityLog> {
    const [activityLog] = await db.insert(activityLogs).values({
      userId: insertActivityLog.userId,
      action: insertActivityLog.action,
      details: insertActivityLog.details || null,
      ipAddress: insertActivityLog.ipAddress || null
    }).returning();
    
    return activityLog;
  }
  
  async getActivityLogs(userId?: number, limit?: number): Promise<ActivityLog[]> {
    // Build the SQL query manually to avoid TypeScript issues with Drizzle
    let sql = 'SELECT * FROM activity_logs';
    const params: any[] = [];
    
    // Add conditions if userId is defined
    if (userId !== undefined) {
      sql += ' WHERE user_id = $1';
      params.push(userId);
    }
    
    // Add ordering
    sql += ' ORDER BY timestamp DESC';
    
    // Add limit if specified
    if (limit !== undefined) {
      sql += userId !== undefined ? ' LIMIT $2' : ' LIMIT $1';
      params.push(limit);
    }
    
    // Use the pool imported at the top of the file
    const result = await pool.query(sql, params);
    
    // Map the results to match the ActivityLog type
    return result.rows.map((row: any) => ({
      id: row.id,
      userId: row.user_id,
      action: row.action,
      details: row.details,
      timestamp: row.timestamp,
      ipAddress: row.ip_address
    }));
  }
  
  async saveChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    const [savedMessage] = await db.insert(chatMessages).values({
      fromUserId: message.fromUserId,
      toUserId: message.toUserId,
      message: message.message,
      isRead: message.isRead || false
    }).returning();
    
    return savedMessage;
  }
  
  async getChatHistory(userId1: number, userId2: number, limit = 50): Promise<ChatMessage[]> {
    // Get messages between the two users (in both directions)
    const messages = await db.select()
      .from(chatMessages)
      .where(
        or(
          and(
            eq(chatMessages.fromUserId, userId1),
            eq(chatMessages.toUserId, userId2)
          ),
          and(
            eq(chatMessages.fromUserId, userId2),
            eq(chatMessages.toUserId, userId1)
          )
        )
      )
      .orderBy(desc(chatMessages.timestamp))
      .limit(limit);
    
    // Return messages in chronological order (oldest first)
    return messages.reverse();
  }
  
  async markMessagesAsRead(fromUserId: number, toUserId: number): Promise<void> {
    // Mark all messages from fromUserId to toUserId as read
    await db.update(chatMessages)
      .set({ isRead: true })
      .where(
        and(
          eq(chatMessages.fromUserId, fromUserId),
          eq(chatMessages.toUserId, toUserId),
          eq(chatMessages.isRead, false)
        )
      );
  }
  
  async getUnreadMessageCount(userId: number): Promise<number> {
    // Count unread messages where this user is the recipient
    const sql = `
      SELECT COUNT(*) as count 
      FROM chat_messages 
      WHERE to_user_id = $1 AND is_read = false
    `;
    const result = await pool.query(sql, [userId]);
    return parseInt(result.rows[0]?.count || '0', 10);
  }
  
  async ensureAdminExists(): Promise<void> {
    const adminUser = await this.getUserByUsername('admin');
    
    if (!adminUser) {
      log('Creating default admin user', 'storage');
      await this.createUser({
        username: 'admin',
        password: 'orcacapital2025',  // Default password, should be changed after first login
        fullName: 'Admin User',
        email: '<EMAIL>',
        role: 'super_admin',
        isActive: true,
        twoFactorEnabled: false,
        notificationSettings: JSON.stringify({
          email: true,
          browser: true,
          mobile: false
        }),
        appearanceSettings: JSON.stringify({
          theme: 'dark',
          fontSize: 'medium',
          colorMode: 'default'
        })
      });
    }
  }
  
  // Payment order methods
  async createPaymentOrder(order: InsertPaymentOrder): Promise<PaymentOrder> {
    const [paymentOrder] = await db.insert(paymentOrders).values({
      fullName: order.fullName,
      email: order.email,
      phone: order.phone,
      telegramUsername: order.telegramUsername || null,
      botName: order.botName,
      paymentMethod: order.paymentMethod,
      paymentPlan: order.paymentPlan,
      amount: order.amount,
      currency: order.currency,
      transactionId: order.transactionId,
      status: order.status || 'pending',
      notes: order.notes || null
    }).returning();
    
    return paymentOrder;
  }
  
  async getAllPaymentOrders(): Promise<PaymentOrder[]> {
    // Query all payment orders with the most recent first
    const result = await pool.query(`
      SELECT 
        po.*,
        u.username as verified_by_username
      FROM 
        payment_orders po
      LEFT JOIN 
        users u ON po.verified_by = u.id
      ORDER BY 
        po.created_at DESC
    `);
    
    // Map the results to match the PaymentOrder type
    return result.rows.map((row: any) => ({
      id: row.id,
      fullName: row.full_name,
      email: row.email,
      phone: row.phone,
      telegramUsername: row.telegram_username,
      botName: row.bot_name,
      paymentMethod: row.payment_method,
      paymentPlan: row.payment_plan,
      amount: row.amount,
      currency: row.currency,
      transactionId: row.transaction_id,
      status: row.status,
      notes: row.notes,
      verifiedBy: row.verified_by,
      verifiedAt: row.verified_at,
      createdAt: row.created_at,
      verifiedByUsername: row.verified_by_username
    }));
  }
  
  async getPaymentOrderById(id: number): Promise<PaymentOrder | undefined> {
    const [order] = await db.select().from(paymentOrders).where(eq(paymentOrders.id, id));
    return order;
  }
  
  async updatePaymentOrderStatus(id: number, status: string, userId: number, notes?: string): Promise<PaymentOrder | undefined> {
    const updateData: any = { 
      status, 
      verifiedBy: userId, 
      verifiedAt: new Date() 
    };
    
    // Only update notes if provided
    if (notes !== undefined) {
      updateData.notes = notes;
    }
    
    const [updatedOrder] = await db.update(paymentOrders)
      .set(updateData)
      .where(eq(paymentOrders.id, id))
      .returning();
      
    return updatedOrder;
  }
}

// Replace memory storage with database storage
export const storage = new DatabaseStorage();
