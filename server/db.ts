import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

// During deployment build, DATABASE_URL might not be available yet
// We'll check for it when actual database operations are needed
const DATABASE_URL = process.env.DATABASE_URL;
// Don't throw error immediately to allow build to complete

// Create pool and db only if DATABASE_URL is available
// This allows the build to complete even without DATABASE_URL
let pool;
let db;

try {
  if (DATABASE_URL) {
    pool = new Pool({ connectionString: DATABASE_URL });
    db = drizzle({ client: pool, schema });
  } else {
    console.warn("DATABASE_URL not set. Database operations will fail.");
    // Create placeholder objects that will throw errors when used
    // This allows the application to start but database operations will fail
    pool = {} as Pool;
    db = {} as ReturnType<typeof drizzle>;
  }
} catch (error) {
  console.error("Error initializing database connection:", error);
  pool = {} as Pool;
  db = {} as ReturnType<typeof drizzle>;
}

export { pool, db };
