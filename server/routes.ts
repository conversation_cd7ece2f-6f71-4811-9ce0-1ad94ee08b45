import { Express, Request, Response, NextFunction } from 'express';
import { createServer, Server } from 'http';
import path from 'path';
import fs from 'fs';
import PDFDocument from 'pdfkit';
import axios from 'axios';
import nodemailer from 'nodemailer';
import session from 'express-session';
import MemoryStore from 'memorystore';
import { WebSocketServer, WebSocket } from 'ws';
import { storage } from './storage';
import { db, pool } from './db';
import { log } from './vite';
import { insertPaymentOrderSchema } from '@shared/schema';

// Session configuration
const SessionStore = MemoryStore(session);

// Extend express-session with custom properties
declare module 'express-session' {
  interface SessionData {
    authenticated: boolean;
    userId: number;
    username: string;
    userRole?: string;
  }
}

interface CryptoHistoricalData {
  [year: number]: number;
}

// Auth middleware
// Keep track of online users (those with active sessions)
const onlineUsers = new Set<number>();
// Track last activity time for each user to detect stale sessions
const userLastActivity = new Map<number, number>();

// Function to broadcast user status changes will be defined after WebSocket server is created

// Clean up stale users every 5 minutes (users inactive for more than 15 minutes)
const INACTIVE_THRESHOLD = 15 * 60 * 1000; // 15 minutes in milliseconds

// In-memory waitlist storage since we don't have a database table for it yet
const waitlistEntries: any[] = [];

const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {
  if (req.session && (req.session as any).authenticated) {
    // Add user to online users set when they're authenticated
    if ((req.session as any).userId) {
      onlineUsers.add((req.session as any).userId);
      // Update last activity time
      userLastActivity.set((req.session as any).userId, Date.now());
    }
    return next();
  }
  return res.status(401).json({ error: 'Unauthorized' });
};

// We use the onlineUsers Set at the top of the file to track active sessions

export async function registerRoutes(app: Express): Promise<Server> {
  // Handle whitepaper access requests
  app.post('/api/request-whitepaper', async (req: Request, res: Response) => {
    const { fullName, email, company, reason, whitepaperId, whitepaperTitle } = req.body;
    
    if (!fullName || !email || !reason || !whitepaperId || !whitepaperTitle) {
      return res.status(400).json({ 
        success: false, 
        message: 'Missing required fields' 
      });
    }
    
    const timestamp = new Date().toISOString();
    const newRequest = {
      fullName,
      email,
      company: company || 'Not provided',
      reason,
      whitepaperId,
      whitepaperTitle,
      status: 'pending',
      timestamp
    };
    
    try {
      // Store the request in a file as backup
      const requestsDir = path.join(process.cwd(), 'whitepaper-requests');
      
      if (!fs.existsSync(requestsDir)) {
        fs.mkdirSync(requestsDir, { recursive: true });
      }
      
      const requestsFile = path.join(requestsDir, 'requests.json');
      let existingRequests = [];
      
      if (fs.existsSync(requestsFile)) {
        const fileContent = fs.readFileSync(requestsFile, 'utf8');
        try {
          existingRequests = JSON.parse(fileContent);
        } catch (err) {
          console.error('Error parsing existing whitepaper requests:', err);
        }
      }
      
      existingRequests.push(newRequest);
      fs.writeFileSync(requestsFile, JSON.stringify(existingRequests, null, 2));
      console.log('Whitepaper request saved to file');
      
      // Try to send email notification
      try {
        const transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
          }
        });
        
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: '<EMAIL>',
          subject: `Whitepaper Access Request: ${whitepaperTitle}`,
          text: `
            New whitepaper access request:
            
            Name: ${fullName}
            Email: ${email}
            Company: ${company || 'Not provided'}
            Whitepaper: ${whitepaperTitle}
            
            Reason for request:
            ${reason}
            
            Timestamp: ${new Date().toLocaleString()}
          `
        };
        
        await transporter.sendMail(mailOptions);
        console.log('Whitepaper request email notification sent');
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError);
        // Continue even if email fails, as we've saved to file
      }
      
      return res.json({ 
        success: true, 
        message: 'Your whitepaper access request has been submitted' 
      });
    } catch (error) {
      console.error('Error saving whitepaper request:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to submit whitepaper request' 
      });
    }
  });
  
  // Get whitepaper requests for admin dashboard
  app.get('/api/whitepaper-requests', isAuthenticated, (req: Request, res: Response) => {
    try {
      const requestsFile = path.join(process.cwd(), 'whitepaper-requests', 'requests.json');
      
      if (fs.existsSync(requestsFile)) {
        const fileContent = fs.readFileSync(requestsFile, 'utf8');
        const requests = JSON.parse(fileContent);
        return res.json(requests);
      }
      
      return res.json([]);
    } catch (error) {
      console.error('Error fetching whitepaper requests:', error);
      return res.status(500).json({ error: 'Failed to fetch whitepaper requests' });
    }
  });
  
  // Assign a whitepaper request to an admin
  app.post('/api/assign-whitepaper-request', isAuthenticated, async (req: Request, res: Response) => {
    const { email, whitepaperId, adminId } = req.body;
    
    if (!email || !whitepaperId || !adminId) {
      return res.status(400).json({ success: false, message: 'Missing required fields' });
    }
    
    try {
      // Get current user info for tracking who made the assignment
      const userId = (req.session as any).userId;
      const username = (req.session as any).username || 'admin';
      
      // Fetch the assigned admin's details from the database
      const assignedAdmin = await storage.getUser(adminId);
      
      if (!assignedAdmin) {
        return res.status(404).json({ success: false, message: 'Assigned admin not found' });
      }
      
      const assignmentInfo = {
        userId: assignedAdmin.id,
        username: assignedAdmin.username,
        fullName: assignedAdmin.fullName || assignedAdmin.username,
        timestamp: new Date().toISOString()
      };
      
      // Update request with assignment info in the file
      const requestsFile = path.join(process.cwd(), 'whitepaper-requests', 'requests.json');
      
      if (fs.existsSync(requestsFile)) {
        const fileContent = fs.readFileSync(requestsFile, 'utf8');
        let requests = JSON.parse(fileContent);
        
        // Find the request and update it with assignment info
        let requestFound = false;
        requests = requests.map((req: any) => {
          if (req.email === email && req.whitepaperId === whitepaperId) {
            requestFound = true;
            return { 
              ...req, 
              assignedTo: assignmentInfo
            };
          }
          return req;
        });
        
        if (!requestFound) {
          return res.status(404).json({ success: false, message: 'Whitepaper request not found' });
        }
        
        fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));
        
        // Log this assignment action
        await storage.logActivity({
          userId: userId || 1, // Fallback to admin user if userId is undefined
          action: 'CLIENT_ASSIGNED',
          details: `Assigned client ${email} (${whitepaperId}) to ${assignedAdmin.fullName || assignedAdmin.username}`,
          ipAddress: req.ip || null
        });
        
        return res.json({ success: true });
      } else {
        return res.status(404).json({ success: false, message: 'Whitepaper requests file not found' });
      }
    } catch (error) {
      console.error('Failed to assign whitepaper request:', error);
      return res.status(500).json({ success: false, message: 'Failed to assign client' });
    }
  });

  // Approve or deny whitepaper request
  app.post('/api/respond-to-whitepaper-request', isAuthenticated, async (req: Request, res: Response) => {
    const { email, whitepaperId, whitepaperTitle, approved, message } = req.body;
    
    if (!email || !whitepaperId || !whitepaperTitle) {
      return res.status(400).json({ success: false, message: 'Missing required fields' });
    }
    
    try {
      // Get current user info for tracking who approved/denied
      const userId = (req.session as any).userId;
      const username = (req.session as any).username || 'admin';
      
      // Fetch the user's details from the database
      const currentUser = userId !== undefined ? await storage.getUser(userId) : undefined;
      
      const approverInfo = {
        userId: userId || 1, // Fallback to admin user if userId is undefined
        username: username,
        fullName: currentUser?.fullName || username,
        timestamp: new Date().toISOString()
      };
      
      // Update request status in the file
      const requestsFile = path.join(process.cwd(), 'whitepaper-requests', 'requests.json');
      
      if (fs.existsSync(requestsFile)) {
        const fileContent = fs.readFileSync(requestsFile, 'utf8');
        let requests = JSON.parse(fileContent);
        
        // Find the request and update its status with approver info
        requests = requests.map((req: any) => {
          if (req.email === email && req.whitepaperId === whitepaperId) {
            return { 
              ...req, 
              status: approved ? 'approved' : 'denied',
              approvedBy: approved ? approverInfo : undefined,
              deniedBy: !approved ? approverInfo : undefined
            };
          }
          return req;
        });
        
        fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));
      }
      
      // Send email response to the requester
      try {
        const transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
          }
        });
        
        const subject = approved 
          ? `Access Granted: ${whitepaperTitle}` 
          : `Regarding Your Whitepaper Request: ${whitepaperTitle}`;
          
        let emailContent = message;
        
        // If approved, include the link to access the whitepaper
        if (approved) {
          // Generate a unique temporary access token (this is a simple implementation)
          const accessToken = Buffer.from(`${email}:${whitepaperId}:${Date.now()}`).toString('base64');
          const whitepaperLink = `${req.headers.origin}/api/whitepapers/${whitepaperId}?token=${accessToken}`;
          
          emailContent += `\n\nYou can access the whitepaper here: ${whitepaperLink}`;
          emailContent += `\n\nThis link is valid for the next 7 days.`;
        }
        
        // Create HTML version of the email with proper formatting
        const htmlContent = `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1e40af;">Orca Capital</h2>
          <p>${emailContent.replace(/\n/g, '<br>')}</p>
          <p style="margin-top: 20px; color: #666;">
            <strong>Orca Capital Investments</strong><br>
            525 West 8th Avenue<br>
            Vancouver, BC V5Z 1C6<br>
            Email: <EMAIL>
          </p>
        </div>`;
        
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: email,
          subject: subject,
          text: emailContent,
          html: htmlContent
        };
        
        await transporter.sendMail(mailOptions);
        console.log('Whitepaper request response email sent');
        
        return res.json({ success: true });
      } catch (emailError: any) {
        console.error('Failed to send whitepaper response email:', emailError);
        
        // Check for authentication errors specifically
        if (emailError.code === 'EAUTH') {
          return res.status(500).json({ 
            success: false, 
            error: 'Email authentication failed. Please check the email credentials.',
            details: 'The system is unable to authenticate with the email provider. This may be due to incorrect username/password or security settings on the email account.'
          });
        }
        
        // Handle other types of errors with more details
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to send email response',
          details: emailError.message || 'Unknown email error'
        });
      }
    } catch (error) {
      console.error('Error processing whitepaper request response:', error);
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  });

  // Modified whitepaper route to check for authentication or valid token
  app.get('/api/whitepapers/:filename', (req: Request, res: Response) => {
    const { filename } = req.params;
    const { token } = req.query;
    
    // Check if user is authenticated as admin or has a valid token
    const isAdmin = req.session && (req.session as any).authenticated;
    let hasValidToken = false;
    
    // If there's a token, validate it
    if (token && typeof token === 'string') {
      try {
        // Decode the token
        const decodedData = Buffer.from(token, 'base64').toString('utf8');
        const [email, docId, timestamp] = decodedData.split(':');
        
        // Check if token is not expired (7 days validity)
        const tokenDate = new Date(parseInt(timestamp));
        const now = new Date();
        const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
        
        if (now.getTime() - tokenDate.getTime() < sevenDaysInMs) {
          hasValidToken = true;
        }
      } catch (error) {
        console.error('Error validating token:', error);
      }
    }
    
    // If not admin and no valid token, return unauthorized
    if (!isAdmin && !hasValidToken) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'You need to request access to view this whitepaper'
      });
    }
    
    try {
      // Set the content type
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);

      // Create a new PDF document
      const doc = new PDFDocument({
        margins: { top: 50, bottom: 50, left: 50, right: 50 },
        info: {
          Title: filename,
          Author: 'Orca Capital',
          Subject: 'Investment Whitepaper',
          Keywords: 'cryptocurrency, investment, whitepaper'
        }
      });

      // Pipe the PDF to the response
      doc.pipe(res);
      
      // Setup response to handle the output of the PDF
      const buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers);
        res.end(pdfBuffer);
      });
      
      // Add logo
      const logoPath = path.join(process.cwd(), 'orca-logo.svg');
      const logoPathAlt = path.join(process.cwd(), 'public', 'orca-logo.svg');
      
      try {
        if (fs.existsSync(logoPath)) {
          doc.image(logoPath, 50, 45, { width: 100 });
        } else if (fs.existsSync(logoPathAlt)) {
          doc.image(logoPathAlt, 50, 45, { width: 100 });
        } else {
          console.log('Logo not found, continuing without logo');
        }
      } catch (error) {
        console.log('Error adding logo, continuing without logo:', error);
      }
      
      // Add title based on filename
      let title = "Orca Capital";
      let content = "This document contains confidential information from Orca Capital.";
      let sections: {heading: string, content: string}[] = [];
      
      // Customize content based on the requested whitepaper
      if (filename.includes('ai-investing')) {
        title = "AI Investment Strategy Whitepaper";
        content = "Orca Capital's AI Investment Strategy leverages advanced machine learning models to identify promising blockchain projects and optimize investment decisions. Our proprietary algorithms analyze market trends, on-chain metrics, social sentiment, and project fundamentals to create a holistic investment approach.";
        
        sections = [
          {
            heading: "Executive Summary",
            content: "This whitepaper outlines Orca Capital's innovative approach to cryptocurrency and blockchain investment through artificial intelligence. Our strategy combines proprietary machine learning models, natural language processing, and quantitative analysis to identify high-potential investments in the rapidly evolving blockchain space."
          },
          {
            heading: "Introduction to AI-Powered Investing",
            content: "The cryptocurrency market presents unique challenges and opportunities for investors. Traditional investment methodologies often fall short in this highly volatile, 24/7 global market. Orca Capital has developed a comprehensive AI framework that continuously monitors market conditions, analyzes on-chain data, and evaluates project fundamentals to make data-driven investment decisions at scale."
          },
          {
            heading: "Core Technology Stack",
            content: "Our investment platform utilizes several key technologies:\n\n- Deep Learning Networks: Custom neural networks trained on historical price data and market patterns\n- Natural Language Processing: Advanced algorithms that analyze news, social media, and developer communications\n- On-Chain Analytics: Real-time monitoring of blockchain transactions, wallet movements, and smart contract interactions\n- Quantitative Models: Statistical models that identify market inefficiencies and arbitrage opportunities"
          },
          {
            heading: "Investment Process",
            content: "1. Data Collection: Gathering diverse datasets from multiple sources including exchanges, blockchain explorers, social platforms, and news aggregators\n2. Pre-processing: Cleaning, normalizing, and preparing data for analysis\n3. AI Analysis: Running predictive models to identify investment opportunities\n4. Risk Assessment: Evaluating potential investments against our comprehensive risk framework\n5. Portfolio Construction: Optimizing asset allocation based on risk-adjusted return projections\n6. Continuous Monitoring: Real-time tracking of positions and market conditions"
          },
          {
            heading: "Performance Metrics",
            content: "Our AI investment strategy is evaluated against multiple performance indicators including:\n\n- Absolute Returns: Measuring total investment gains\n- Risk-Adjusted Returns: Analyzing returns in relation to volatility (Sharpe and Sortino ratios)\n- Drawdown Analysis: Evaluating maximum portfolio value decline\n- Market Correlation: Assessing strategy independence from broader market movements"
          },
          {
            heading: "Conclusion",
            content: "Orca Capital's AI investment strategy represents a significant advancement in blockchain investment methodology. By leveraging artificial intelligence, we aim to deliver superior risk-adjusted returns while navigating the unique challenges of the cryptocurrency market."
          }
        ];
      } else if (filename.includes('eliza-os')) {
        title = "ELIZA OS Framework Whitepaper";
        content = "ELIZA OS is an open-source framework developed by Orca Capital that enables developers to build and deploy autonomous AI agents for blockchain environments. These agents can perform various functions including trading, market analysis, social media engagement, and more.";
        
        sections = [
          {
            heading: "Introduction to ELIZA OS",
            content: "ELIZA OS represents a breakthrough in autonomous agent technology for blockchain environments. This open-source framework provides developers with the tools to create specialized AI agents capable of interacting with cryptocurrency markets, blockchain networks, and social platforms without human intervention."
          },
          {
            heading: "Technical Architecture",
            content: "ELIZA OS is built on a modular, extensible architecture consisting of:\n\n- Core Engine: The central processing unit that coordinates agent activities and learning processes\n- Perception Layer: Components that gather and interpret data from various sources\n- Action Layer: Modules that execute decisions across different platforms and protocols\n- Memory System: Long and short-term storage mechanisms for experiential learning\n- Learning Framework: Systems for continuous improvement through reinforcement learning"
          },
          {
            heading: "Agent Types and Capabilities",
            content: "The framework supports development of multiple agent types including:\n\n- Trading Agents: Autonomous systems for market analysis and cryptocurrency trading\n- Research Agents: Specialized in gathering and synthesizing information about blockchain projects\n- Social Engagement Agents: Systems that monitor and participate in community discussions\n- Network Monitoring Agents: Tools that track on-chain activities and network health\n- Investment Analysis Agents: Advanced systems for evaluating potential investments"
          },
          {
            heading: "Development Guidelines",
            content: "Building on ELIZA OS follows a structured approach:\n\n1. Agent Specification: Defining the agent's purpose, inputs, outputs, and success metrics\n2. Module Selection: Choosing appropriate pre-built modules or developing custom components\n3. Training Process: Initializing the agent with baseline knowledge and learning parameters\n4. Testing Environment: Validating agent performance in controlled scenarios\n5. Deployment: Launching the agent in production environments with appropriate safeguards\n6. Monitoring: Continuous assessment of agent performance and behavior"
          },
          {
            heading: "Security and Ethics",
            content: "ELIZA OS incorporates robust security measures and ethical guidelines:\n\n- Multi-level Authentication: Ensuring only authorized users can access and modify agents\n- Behavioral Constraints: Built-in limitations that prevent harmful actions\n- Audit Trail: Comprehensive logging of all agent activities and decisions\n- Ethical Framework: Guidelines for responsible AI deployment in financial contexts\n- Human Oversight: Optional intervention mechanisms for critical decisions"
          },
          {
            heading: "Future Development",
            content: "The ELIZA OS roadmap includes several key initiatives:\n\n- Enhanced Interoperability: Deeper integration with diverse blockchain ecosystems\n- Collaborative Learning: Mechanisms for agents to share insights while maintaining privacy\n- Specialized Domain Expertise: Development of industry-specific knowledge bases\n- Improved Natural Language Capabilities: More sophisticated communication abilities\n- Community Governance: Transition toward community-led framework development"
          }
        ];
      } else if (filename.includes('market-analysis')) {
        title = "Crypto Market Analysis Methodology";
        content = "Orca Capital employs a comprehensive methodology for analyzing cryptocurrency markets, combining quantitative analysis, on-chain metrics, fundamental research, and sentiment analysis to form a complete picture of market opportunities.";
        
        sections = [
          {
            heading: "Introduction to Crypto Market Analysis",
            content: "This document outlines Orca Capital's structured approach to cryptocurrency market analysis. Our methodology combines traditional financial analysis techniques with specialized methods designed for blockchain assets, creating a robust framework for evaluating investment opportunities in this unique market."
          },
          {
            heading: "Multi-factor Analysis Framework",
            content: "Our analysis is structured around four core pillars:\n\n1. Quantitative Analysis: Statistical evaluation of price movements, volatility, and market patterns\n2. On-chain Metrics: Assessment of blockchain activity, network health, and user adoption\n3. Fundamental Analysis: Evaluation of project teams, technology, and business models\n4. Sentiment Analysis: Monitoring of market psychology and community engagement"
          },
          {
            heading: "Quantitative Indicators",
            content: "Key metrics we track include:\n\n- Technical Indicators: Moving averages, RSI, MACD, and proprietary oscillators\n- Volatility Measures: Historical volatility, implied volatility, and volatility term structures\n- Volume Analysis: Trading volume, volume profiles, and liquidity depth\n- Market Microstructure: Order book dynamics, bid-ask spreads, and market impact coefficients\n- Correlation Analysis: Asset correlations across different market conditions"
          },
          {
            heading: "On-chain Analytics",
            content: "Our blockchain analysis focuses on:\n\n- Network Activity: Transaction counts, active addresses, and new address creation\n- Value Transfer: Transaction values, whale movements, and exchange flows\n- Network Health: Hash rate, node distribution, and network security metrics\n- Token Economics: Supply distribution, concentration metrics, and staking ratios\n- Smart Contract Activity: Contract interactions, TVL (Total Value Locked), and protocol metrics"
          },
          {
            heading: "Fundamental Research Process",
            content: "Our fundamental analysis includes:\n\n1. Team Assessment: Evaluation of team credentials, track record, and commitment\n2. Technology Review: Analysis of codebase, innovation level, and technical sustainability\n3. Tokenomics Evaluation: Assessment of token utility, distribution, and economic models\n4. Competitive Positioning: Comparison with similar projects and market differentiation\n5. Development Activity: Monitoring of GitHub contributions, updates, and roadmap progress\n6. Regulatory Considerations: Analysis of compliance requirements and regulatory risks"
          },
          {
            heading: "Sentiment Analysis Techniques",
            content: "We gauge market sentiment through:\n\n- Social Media Monitoring: Analysis of discussions across Twitter, Reddit, Discord, and other platforms\n- Developer Community Engagement: Assessment of technical community participation\n- Institutional Interest: Tracking of institutional announcements and investments\n- Media Coverage: Evaluation of news sentiment and media attention\n- Search Trends: Analysis of search volume and interest indicators"
          }
        ];
      } else if (filename.includes('security-practices')) {
        title = "Security Best Practices";
        content = "Orca Capital maintains the highest standards of security for client funds and data. This document outlines our comprehensive security infrastructure, protocols, and best practices implemented across all levels of our organization.";
        
        sections = [
          {
            heading: "Security Philosophy and Framework",
            content: "Orca Capital's security approach is built on the principles of defense-in-depth, least privilege, and continuous improvement. We employ multiple layers of security controls, regular assessments, and industry best practices to safeguard client assets and information."
          },
          {
            heading: "Infrastructure Security",
            content: "Our infrastructure security includes:\n\n- Advanced Firewall Protection: Enterprise-grade firewalls with intrusion detection and prevention\n- DDoS Mitigation: Robust protection against distributed denial-of-service attacks\n- Encrypted Communications: All data transmissions secured with TLS 1.3 and strong ciphers\n- Secure Cloud Architecture: Isolated network segments, private subnets, and strict access controls\n- Regular Penetration Testing: Independent security assessments conducted quarterly"
          },
          {
            heading: "Asset Storage Security",
            content: "For cryptocurrency asset security, we implement:\n\n- Multi-signature Technology: All critical wallets require multiple independent authorizations\n- Cold Storage: 95% of assets stored in air-gapped, offline environments\n- Hardware Security Modules (HSMs): Military-grade encryption for key storage\n- Geographically Distributed Storage: Assets and backups distributed across multiple secure locations\n- Regular Audit Procedures: Comprehensive proof-of-reserves verification process"
          },
          {
            heading: "Access Control Systems",
            content: "Our access management practices include:\n\n- Multi-factor Authentication (MFA): Required for all employee and client accounts\n- Role-based Access Control: Granular permissions based on job requirements\n- Biometric Verification: Physical access to critical areas restricted by biometric controls\n- Privileged Access Management: Just-in-time access for administrative functions\n- Session Management: Automatic timeouts and monitoring of all authenticated sessions"
          },
          {
            heading: "Operational Security",
            content: "Day-to-day security operations include:\n\n- 24/7 Security Operations Center: Continuous monitoring for suspicious activities\n- Comprehensive Logging: Immutable audit trails of all system interactions\n- Regular Employee Training: Ongoing security awareness education for all team members\n- Incident Response Procedures: Documented processes for addressing security events\n- Vendor Security Assessment: Rigorous evaluation of all third-party service providers"
          },
          {
            heading: "Client Security Recommendations",
            content: "We advise clients to follow these security best practices:\n\n1. Use Hardware Wallets: Store personal cryptocurrency on dedicated hardware devices\n2. Enable Two-Factor Authentication: Activate 2FA on all financial and email accounts\n3. Use Password Managers: Generate and store strong, unique passwords\n4. Be Vigilant Against Phishing: Verify communications through official channels\n5. Keep Software Updated: Maintain current operating systems and security patches\n6. Practice Safe Browsing: Use secure networks and be cautious with downloads"
          }
        ];
      } else if (filename.includes('developer-guide')) {
        title = "Developer Guide";
        content = "This comprehensive guide provides developers with everything they need to understand and integrate with Orca Capital's platform. It covers API access, SDK usage, authentication, data models, and best practices for building on our infrastructure.";
        
        sections = [
          {
            heading: "Introduction to Orca Capital Platform",
            content: "This developer guide provides comprehensive information for integrating with the Orca Capital platform. Our platform offers access to AI-powered investment insights, market data, and portfolio management tools through a robust API system and client libraries."
          },
          {
            heading: "Getting Started",
            content: "To begin developing with our platform:\n\n1. Register a Developer Account: Create an account at developers.orcacapital.com\n2. Generate API Credentials: Obtain your API keys from the developer dashboard\n3. Choose Your SDK: Select from our officially supported SDKs (JavaScript, Python, Java, Go)\n4. Set Up Development Environment: Configure your environment following our quick-start guide\n5. Explore Documentation: Review available endpoints, models, and tutorials"
          },
          {
            heading: "Authentication and Authorization",
            content: "Secure access to our API is managed through:\n\n- API Key Authentication: Basic access using your developer API key\n- OAuth 2.0 Integration: For accessing user-specific data with proper permissions\n- JWT Tokens: Stateless authentication for client applications\n- Rate Limiting: Tiered access levels with appropriate request limits\n- IP Whitelisting: Additional security for production environments"
          },
          {
            heading: "Core API Concepts",
            content: "Our API follows RESTful principles with these characteristics:\n\n- Predictable Resource URIs: Logically organized endpoint structure\n- JSON Response Format: Consistent data formatting across all endpoints\n- HTTP Status Codes: Standard response codes to indicate request outcomes\n- Versioning: Clear version control through URI path versioning\n- Pagination: Efficient handling of large data sets with cursor-based pagination\n- Error Handling: Detailed error responses with actionable information"
          },
          {
            heading: "Available Resources and Endpoints",
            content: "Main API resources include:\n\n- Market Data: Real-time and historical pricing for cryptocurrencies\n- Analysis Endpoints: Access to AI-generated market insights and predictions\n- Portfolio Management: Tools for tracking and analyzing investments\n- User Management: Account creation and management functions\n- Notifications: Configuration of alerts and update preferences\n- Transaction Endpoints: Methods for managing asset transactions"
          },
          {
            heading: "SDK Usage Examples",
            content: "Example code for common operations:\n\n```javascript\n// JavaScript SDK example\nconst OrcaCapital = require('orcacapital-sdk');\n\nconst client = new OrcaCapital.Client({\n  apiKey: 'your-api-key',\n  environment: 'production'\n});\n\n// Fetch market data\nasync function getMarketData() {\n  try {\n    const response = await client.market.getPrices(['BTC', 'ETH', 'SOL']);\n    console.log(response.data);\n  } catch (error) {\n    console.error('Error fetching market data:', error);\n  }\n}\n\ngetMarketData();\n```"
          },
          {
            heading: "Webhook Integration",
            content: "For real-time updates, our webhook system allows you to:\n\n- Register Webhook URLs: Configure endpoints to receive event notifications\n- Select Event Types: Choose which events trigger notifications\n- Process Payloads: Handle incoming webhook data securely\n- Verify Signatures: Validate webhook authenticity using HMAC signatures\n- Manage Deliveries: Track and replay webhook events as needed"
          }
        ];
      } else if (filename.includes('api-reference')) {
        title = "API Reference";
        content = "This documentation provides a comprehensive reference for all Orca Capital API endpoints. It includes detailed information on request parameters, response formats, authentication requirements, error codes, and example implementations.";
        
        sections = [
          {
            heading: "API Overview",
            content: "The Orca Capital API provides programmatic access to cryptocurrency market data, AI-powered investment insights, portfolio management tools, and user account features. This reference documents all available endpoints, parameters, and response structures."
          },
          {
            heading: "Base URLs and Environments",
            content: "Our API is available in multiple environments:\n\n- Production: https://api.orcacapital.com/v1\n- Sandbox: https://sandbox-api.orcacapital.com/v1\n\nAll API requests must use HTTPS protocol. HTTP requests will be rejected."
          },
          {
            heading: "Authentication",
            content: "All API requests require authentication using one of these methods:\n\n- API Key (X-API-Key header): For server-to-server requests\n- Bearer Token: For authenticated user sessions\n- OAuth 2.0: For third-party application access\n\nExample request header:\n```\nX-API-Key: your-api-key-here\nContent-Type: application/json\nAccept: application/json\n```"
          },
          {
            heading: "Market Data Endpoints",
            content: "GET /market/prices\nRetrieve current prices for specified cryptocurrencies.\n\nParameters:\n- symbols (required): Comma-separated list of cryptocurrency symbols\n- currency (optional): Fiat currency for price conversion (default: USD)\n\nResponse example:\n```json\n{\n  \"data\": [\n    {\n      \"symbol\": \"BTC\",\n      \"price\": 57382.45,\n      \"change_24h\": 2.35,\n      \"volume_24h\": 28792456123,\n      \"market_cap\": 1076543287690,\n      \"last_updated\": \"2025-03-28T10:15:30Z\"\n    },\n    {\n      \"symbol\": \"ETH\",\n      \"price\": 3245.78,\n      \"change_24h\": 1.87,\n      \"volume_24h\": 15876543210,\n      \"market_cap\": 386754321098,\n      \"last_updated\": \"2025-03-28T10:15:30Z\"\n    }\n  ],\n  \"timestamp\": \"2025-03-28T10:15:32Z\"\n}\n```"
          },
          {
            heading: "Analysis Endpoints",
            content: "GET /analysis/predictions\nRetrieve AI-generated price predictions for specified assets.\n\nParameters:\n- symbol (required): Cryptocurrency symbol\n- timeframe (required): Prediction timeframe (24h, 7d, 30d, 90d)\n- confidence (optional): Include confidence intervals (true/false)\n\nResponse example:\n```json\n{\n  \"data\": {\n    \"symbol\": \"BTC\",\n    \"current_price\": 57382.45,\n    \"prediction\": {\n      \"timeframe\": \"7d\",\n      \"price\": 61250.75,\n      \"change_percent\": 6.74,\n      \"confidence_interval\": {\n        \"lower\": 58945.30,\n        \"upper\": 63520.15\n      },\n      \"factors\": [\n        \"Positive on-chain metrics\",\n        \"Increasing institutional interest\",\n        \"Favorable technical indicators\"\n      ]\n    },\n    \"generated_at\": \"2025-03-28T10:10:00Z\"\n  }\n}\n```"
          },
          {
            heading: "User Account Endpoints",
            content: "GET /user/profile\nRetrieve the authenticated user's profile information.\n\nParameters: None (uses authentication token to identify user)\n\nResponse example:\n```json\n{\n  \"data\": {\n    \"user_id\": \"usr_12345abcde\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"John Smith\",\n    \"account_type\": \"premium\",\n    \"created_at\": \"2024-11-15T09:24:58Z\",\n    \"preferences\": {\n      \"notification_email\": true,\n      \"notification_push\": true,\n      \"currency\": \"USD\",\n      \"theme\": \"dark\"\n    }\n  }\n}\n```"
          },
          {
            heading: "Error Codes and Handling",
            content: "Our API uses standard HTTP status codes and includes detailed error information:\n\n- 400 Bad Request: Invalid parameters or request format\n- 401 Unauthorized: Missing or invalid authentication\n- 403 Forbidden: Valid authentication but insufficient permissions\n- 404 Not Found: Requested resource doesn't exist\n- 429 Too Many Requests: Rate limit exceeded\n- 500 Internal Server Error: Unexpected server error\n\nError response format:\n```json\n{\n  \"error\": {\n    \"code\": \"rate_limit_exceeded\",\n    \"message\": \"API rate limit exceeded\",\n    \"details\": \"Request limit of 100 requests per minute reached\",\n    \"request_id\": \"req_7890xyz\"\n  }\n}\n```"
          }
        ];
      }
      
      // Add title to PDF
      doc.fontSize(25)
         .text(title, 50, 160);
      
      // Add company info
      doc.fontSize(12)
         .moveDown()
         .text('Orca Capital', { align: 'left' })
         .text('525 West 8th Avenue', { align: 'left' })
         .text('Vancouver, BC V5Z 1C6', { align: 'left' })
         .moveDown(2);
      
      // Add the main content introduction
      doc.fontSize(14)
         .text('Executive Overview', { underline: true })
         .moveDown(0.5);
      
      doc.fontSize(12)
         .text(content, {
           paragraphGap: 10,
           align: 'left'
         })
         .moveDown(2);
      
      // Add sections
      if (sections && sections.length > 0) {
        sections.forEach((section) => {
          // Add section heading
          doc.fontSize(14)
             .text(section.heading, { underline: true })
             .moveDown(0.5);
          
          // Add section content
          doc.fontSize(12)
             .text(section.content, {
               paragraphGap: 10,
               align: 'left'
             })
             .moveDown(1.5);
        });
      }
      
      // Add footer
      doc.fontSize(10)
         .text(`© ${new Date().getFullYear()} Orca Capital. All rights reserved.`, 50, doc.page.height - 50, {
           align: 'center',
           width: doc.page.width - 100
         });
      
      // Finalize PDF
      doc.end();
    } catch (error) {
      console.error("Error generating PDF:", error);
      if (!res.headersSent) {
        res.status(500).json({ error: "Failed to generate PDF" });
      }
    }
  });

  // Bitcoin historical data endpoint
  app.get('/api/bitcoin/historical', async (req: Request, res: Response) => {
    try {
      // Add small delay to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Try to fetch from CoinGecko first
      try {
        // Free API limited to 10-30 calls per minute
        // Check if we have the CoinGecko API key in the environment
        let apiKey = process.env.COINGECKO_API_KEY || '';
        
        // Add the CG- prefix if it's not already present
        if (apiKey && !apiKey.startsWith('CG-')) {
          apiKey = `CG-${apiKey}`;
          console.log('Added CG- prefix to CoinGecko API key');
        }
        
        let params: any = {
          vs_currency: 'usd',
          days: '365' // Limit to 1 year for the free API
          // Using 365 days for the free/demo API as per the error message:
          // "Public API users are limited to querying historical data within the past 365 days"
        };
        
        // Set up headers
        const headers: Record<string, string> = {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json'
        };
        
        // Set the base URL for the CoinGecko API
        let baseUrl;
        
        // If we have an API key
        if (apiKey) {
          // Based on the error message, we're assuming we have a demo key
          // Use the regular API URL with the demo key parameter
          baseUrl = 'https://api.coingecko.com/api/v3/coins/bitcoin/market_chart';
          console.log(`Using CoinGecko API with Demo API key (api.coingecko.com)`);
          // Add the API key as a parameter
          params.x_cg_demo_api_key = apiKey;
          
          // Log the URL format we're using
          console.log(`Using format: api.coingecko.com with x_cg_demo_api_key parameter`);
          
          /* Original code with check for "Demo" in key:
          if (apiKey.includes('Demo')) {
            baseUrl = 'https://api.coingecko.com/api/v3/coins/bitcoin/market_chart';
            console.log(`Using CoinGecko API with Demo API key`);
            // Add the API key as a parameter
            params.x_cg_demo_api_key = apiKey;
          } else {
            // Otherwise, use the Pro API URL with the key as a query parameter
            baseUrl = `https://pro-api.coingecko.com/api/v3/coins/bitcoin/market_chart?x_cg_pro_api_key=${apiKey}`;
            console.log(`Using CoinGecko Pro API with paid API key`);
          }
          */
        } else {
          // No API key, use the free API URL
          baseUrl = 'https://api.coingecko.com/api/v3/coins/bitcoin/market_chart';
          console.log(`Using CoinGecko free API (no API key)`);
        }
          
        console.log(`Using CoinGecko API URL: ${baseUrl} with API key: ${apiKey ? 'Yes' : 'No'}`);
        // DO NOT log the actual API key value for security reasons!
          
        const response = await axios.get(baseUrl, {
            params,
            timeout: 5000, // 5 second timeout
            headers
          }
        );
        
        if (response.data && response.data.prices) {
          console.log('Successfully fetched Bitcoin data from CoinGecko');
          return res.json(response.data);
        } else {
          throw new Error('Invalid response format from CoinGecko API');
        }
      } catch (apiError: any) {
        // Check for rate limiting (status 429) or other API errors
        if (apiError.response) {
          console.error(`Error fetching from CoinGecko API: Status: ${apiError.response.status}, Data:`, 
            apiError.response.data || '(no data)',
            'Headers:', JSON.stringify(apiError.response.headers || {}));
          
          if (apiError.response.status === 429) {
            console.log('CoinGecko API rate limit exceeded, using fallback data');
          }
        } else {
          console.error('Error fetching from CoinGecko API:', apiError.message);
        }
        throw apiError; // Propagate to outer catch block for fallback
      }
    } catch (error) {
      console.error('Using fallback Bitcoin historical data');
      
      // Fallback data in case of API failure
      const fallbackData = {
        prices: [
          [1583020800000, 29000], // 2020
          [1614556800000, 45000], // 2021
          [1646092800000, 38000], // 2022
          [1677628800000, 42000], // 2023
          [1709251200000, 52000], // 2024
          [Date.now(), 55000]     // Now
        ]
      };
      
      return res.json(fallbackData);
    }
  });
  
  // In-memory cache for conversion rates to reduce API calls
  const conversionRateCache: {
    [key: string]: {
      conversionRate: number;
      fromPriceUsd: number;
      toPriceUsd: number;
      timestamp: number;
    }
  } = {};
  
  // Default conversion rates for common pairs when API fails
  const defaultConversionRates: Record<string, Record<string, number>> = {
    'bitcoin': {
      'ethereum': 45.84, // 1 BTC ≈ 45.84 ETH (82000/1788.79)
      'solana': 385.0,  // 1 BTC ≈ 385 SOL
      'ripple': 32500,  // 1 BTC ≈ 32,500 XRP
      'dogecoin': 310000, // 1 BTC ≈ 310,000 DOGE
      'tether': 82000,  // 1 BTC ≈ 82,000 USDT
      'cardano': 145000 // 1 BTC ≈ 145,000 ADA
    },
    'ethereum': {
      'bitcoin': 0.073, // 1 ETH ≈ 0.073 BTC
      'solana': 28.0,   // 1 ETH ≈ 28 SOL
      'ripple': 2370,   // 1 ETH ≈ 2,370 XRP
      'dogecoin': 22500, // 1 ETH ≈ 22,500 DOGE
      'tether': 4900,   // 1 ETH ≈ 4,900 USDT
      'cardano': 10600  // 1 ETH ≈ 10,600 ADA
    },
    'solana': {
      'bitcoin': 0.0026, // 1 SOL ≈ 0.0026 BTC
      'ethereum': 0.036, // 1 SOL ≈ 0.036 ETH
      'ripple': 85,     // 1 SOL ≈ 85 XRP
      'dogecoin': 810,  // 1 SOL ≈ 810 DOGE
      'tether': 175,    // 1 SOL ≈ 175 USDT
      'cardano': 380    // 1 SOL ≈ 380 ADA
    }
  };
  
  // Crypto price conversion endpoint
  app.get('/api/crypto/convert', async (req: Request, res: Response) => {
    try {
      // Get the from and to cryptocurrencies, and the amount from query parameters
      const { fromCurrency, toCurrency, amount } = req.query;
      
      if (!fromCurrency || !toCurrency || !amount) {
        return res.status(400).json({ 
          error: 'Missing required parameters',
          message: 'fromCurrency, toCurrency, and amount are required'
        });
      }
      
      const fromCryptoId = String(fromCurrency).toLowerCase();
      const toCryptoId = String(toCurrency).toLowerCase();
      const amountValue = parseFloat(String(amount));
      
      if (isNaN(amountValue) || amountValue <= 0) {
        return res.status(400).json({ 
          error: 'Invalid amount',
          message: 'Amount must be a positive number'
        });
      }
      
      // Check if it's the same currency - convert 1:1
      if (fromCryptoId === toCryptoId) {
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount: amountValue,
          conversionRate: 1,
          source: 'direct',
          timestamp: Date.now(),
          fallbackUsed: false
        });
      }
      
      // Create a cache key for this currency pair
      const cacheKey = `${fromCryptoId}-${toCryptoId}`;
      const reverseCacheKey = `${toCryptoId}-${fromCryptoId}`;
      const cacheExpiration = 5 * 60 * 1000; // 5 minutes in milliseconds
      
      // Check if we have a valid cached result
      const now = Date.now();
      if (conversionRateCache[cacheKey] && (now - conversionRateCache[cacheKey].timestamp < cacheExpiration)) {
        // Use cached conversion rate
        const cachedData = conversionRateCache[cacheKey];
        const convertedAmount = amountValue * cachedData.conversionRate;
        
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount,
          conversionRate: cachedData.conversionRate,
          fromPriceUsd: cachedData.fromPriceUsd,
          toPriceUsd: cachedData.toPriceUsd,
          source: 'cache',
          timestamp: now
        });
      }
      
      // Check if we have the reverse pair cached (can calculate inverse rate)
      if (conversionRateCache[reverseCacheKey] && (now - conversionRateCache[reverseCacheKey].timestamp < cacheExpiration)) {
        const cachedData = conversionRateCache[reverseCacheKey];
        // Inverse the conversion rate for the reverse pair
        const conversionRate = 1 / cachedData.conversionRate;
        const convertedAmount = amountValue * conversionRate;
        
        // Cache this direction too
        conversionRateCache[cacheKey] = {
          conversionRate,
          fromPriceUsd: cachedData.toPriceUsd,
          toPriceUsd: cachedData.fromPriceUsd,
          timestamp: now
        };
        
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount,
          conversionRate,
          fromPriceUsd: cachedData.toPriceUsd,
          toPriceUsd: cachedData.fromPriceUsd,
          source: 'cache-inverse',
          timestamp: now
        });
      }
      
      try {
        // Get current prices for both currencies in USD to calculate conversion rate
        let apiKey = process.env.COINGECKO_API_KEY || '';
        
        // Add CG- prefix if not present (CoinGecko API key format)
        if (apiKey && !apiKey.startsWith('CG-')) {
          apiKey = `CG-${apiKey}`;
          console.log('Added CG- prefix to CoinGecko API key');
        }
        
        // API URLs for both currencies
        let fromCurrencyUrl;
        let toCurrencyUrl;
        
        // Check if the API key is a demo key (doesn't contain proper UUID format)
        const isProApiKey = apiKey && /CG-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i.test(apiKey);
        
        if (isProApiKey) {
          // Use pro API endpoint for paid API keys
          fromCurrencyUrl = `https://pro-api.coingecko.com/api/v3/simple/price?ids=${fromCryptoId}&vs_currencies=usd&x_cg_pro_api_key=${apiKey}`;
          toCurrencyUrl = `https://pro-api.coingecko.com/api/v3/simple/price?ids=${toCryptoId}&vs_currencies=usd&x_cg_pro_api_key=${apiKey}`;
          console.log('Using pro-api.coingecko.com endpoint with Pro API key');
        } else if (apiKey) {
          // Use regular API with demo key in parameter
          fromCurrencyUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${fromCryptoId}&vs_currencies=usd&x_cg_demo_api_key=${apiKey}`;
          toCurrencyUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${toCryptoId}&vs_currencies=usd&x_cg_demo_api_key=${apiKey}`;
          console.log('Using api.coingecko.com endpoint with Demo API key');
        } else {
          // Use regular API without any key
          fromCurrencyUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${fromCryptoId}&vs_currencies=usd`;
          toCurrencyUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${toCryptoId}&vs_currencies=usd`;
          console.log('Using api.coingecko.com endpoint without API key');
        }
        
        // Make parallel requests for better performance
        const [fromResponse, toResponse] = await Promise.all([
          axios.get(fromCurrencyUrl),
          axios.get(toCurrencyUrl)
        ]);
        
        // Extract prices from responses
        const fromPriceUsd = fromResponse.data[fromCryptoId]?.usd;
        const toPriceUsd = toResponse.data[toCryptoId]?.usd;
        
        if (!fromPriceUsd || !toPriceUsd) {
          throw new Error('Price data not available from API');
        }
        
        // Calculate conversion rate and converted amount
        const conversionRate = toPriceUsd / fromPriceUsd;
        const convertedAmount = amountValue * conversionRate;
        
        // Cache the conversion rate
        conversionRateCache[cacheKey] = {
          conversionRate,
          fromPriceUsd,
          toPriceUsd,
          timestamp: now
        };
        
        // Return conversion data
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount,
          conversionRate,
          fromPriceUsd,
          toPriceUsd,
          source: 'api',
          timestamp: now,
          fallbackUsed: false
        });
      } catch (apiError: any) {
        // Log detailed error information
        console.error('API error:', apiError.message);
        if (apiError.response) {
          console.error('API response data:', apiError.response.data);
          console.error('API response status:', apiError.response.status);
          console.error('API response headers:', apiError.response.headers);
        }
        
        // Try getting rates from default conversion table
        if (defaultConversionRates[fromCryptoId]?.[toCryptoId]) {
          const conversionRate = defaultConversionRates[fromCryptoId][toCryptoId];
          const convertedAmount = amountValue * conversionRate;
          
          // Estimate USD prices based on BTC ≈ $82,000 as base
          const estimatedFromUsd = fromCryptoId === 'bitcoin' ? 82000 : 
                                  (fromCryptoId === 'ethereum' ? 1788.79 : 
                                  (fromCryptoId === 'solana' ? 175 : 1));
          
          const estimatedToUsd = toCryptoId === 'bitcoin' ? 82000 : 
                               (toCryptoId === 'ethereum' ? 1788.79 : 
                               (toCryptoId === 'solana' ? 175 : 1));
          
          return res.status(200).json({
            fromCurrency: fromCryptoId,
            toCurrency: toCryptoId,
            amount: amountValue,
            convertedAmount,
            conversionRate,
            fromPriceUsd: estimatedFromUsd,
            toPriceUsd: estimatedToUsd,
            source: 'default',
            timestamp: now,
            fallbackUsed: true
          });
        }
        
        // For reverse pairs, check if we can use the default rates in reverse
        if (defaultConversionRates[toCryptoId]?.[fromCryptoId]) {
          const inverseRate = defaultConversionRates[toCryptoId][fromCryptoId];
          const conversionRate = 1 / inverseRate;
          const convertedAmount = amountValue * conversionRate;
          
          // Estimate USD prices based on BTC ≈ $82,000 as base
          const estimatedFromUsd = fromCryptoId === 'bitcoin' ? 68000 : 
                                  (fromCryptoId === 'ethereum' ? 4900 : 
                                  (fromCryptoId === 'solana' ? 175 : 1));
          
          const estimatedToUsd = toCryptoId === 'bitcoin' ? 68000 : 
                               (toCryptoId === 'ethereum' ? 4900 : 
                               (toCryptoId === 'solana' ? 175 : 1));
          
          return res.status(200).json({
            fromCurrency: fromCryptoId,
            toCurrency: toCryptoId,
            amount: amountValue,
            convertedAmount,
            conversionRate,
            fromPriceUsd: estimatedFromUsd,
            toPriceUsd: estimatedToUsd,
            source: 'default-inverse',
            timestamp: now,
            fallbackUsed: true
          });
        }
        
        // Last resort - use a simple approximation
        // This ensures users get some reasonable value rather than errors
        const approximateRate = getApproximateConversionRate(fromCryptoId, toCryptoId);
        const convertedAmount = amountValue * approximateRate;
        
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount,
          conversionRate: approximateRate,
          source: 'approximation',
          timestamp: now,
          fallbackUsed: true
        });
      }
    } catch (error: any) {
      console.error('Error in crypto conversion:', error.message);
      
      // If all else fails, return a reasonable approximation
      try {
        const fromCryptoId = String(req.query.fromCurrency).toLowerCase();
        const toCryptoId = String(req.query.toCurrency).toLowerCase();
        const amountValue = parseFloat(String(req.query.amount));
        
        const approximateRate = getApproximateConversionRate(fromCryptoId, toCryptoId);
        const convertedAmount = amountValue * approximateRate;
        
        return res.status(200).json({
          fromCurrency: fromCryptoId,
          toCurrency: toCryptoId,
          amount: amountValue,
          convertedAmount,
          conversionRate: approximateRate,
          source: 'fallback-approximation',
          timestamp: Date.now(),
          fallbackUsed: true
        });
      } catch (fallbackError) {
        // If even the fallback fails, return the error
        return res.status(500).json({
          error: 'Internal server error',
          message: error.message || 'An error occurred during currency conversion'
        });
      }
    }
  });
  
  // Helper function to get approximate conversion rates when all else fails
  function getApproximateConversionRate(fromCryptoId: string, toCryptoId: string): number {
    // Base conversion map with current rates (April 2025)
    const baseMap: Record<string, number> = {
      'bitcoin': 82000,
      'ethereum': 1788.79,
      'solana': 175,
      'ripple': 2.1,
      'dogecoin': 0.215,
      'tether': 1,
      'usdc': 1,
      'cardano': 0.47,
      'avalanche': 36,
      'polkadot': 8.5,
      'tron': 0.16,
      'shiba-inu': 0.00003,
      'dai': 1,
      'litecoin': 90,
      'polygon': 0.8,
      'binancecoin': 600,
      'wrapped-bitcoin': 68000,
      'chainlink': 14,
      'cosmos': 9,
      'uniswap': 11,
      'stellar': 0.12,
      'monero': 173,
      'toncoin': 6.7,
      'arbitrum': 1.25,
      'apecoin': 1.35,
      'aptos': 8.2,
      'pepe': 0.0000097,
      'algorand': 0.19,
      'kaspa': 0.09
    };
    
    // Get the USD values for both currencies
    const fromUsdValue = baseMap[fromCryptoId] || 1;
    const toUsdValue = baseMap[toCryptoId] || 1;
    
    // Calculate conversion rate
    return toUsdValue / fromUsdValue;
  }

  // Crypto historical data endpoint
  app.get('/api/crypto/historical', async (req: Request, res: Response) => {
    try {
      // Get the cryptocurrency and period from the query parameters
      const { cryptocurrency = 'bitcoin', period = '1year' } = req.query;
      
      // Validate the cryptocurrency ID
      const cryptoId = String(cryptocurrency).toLowerCase();
      
      // Mapping of cryptocurrencies to their respective IDs across various APIs
      const cryptoMappings: Record<string, {
        coinCapId?: string;
        coingeckoId?: string;
        geckoTerminalId?: string;
        coinStatsId?: string;
        network?: string;
        tokenId?: string;
      }> = {
        'bitcoin': {
          coinCapId: 'bitcoin',
          coingeckoId: 'bitcoin',
          geckoTerminalId: 'wbtc',
          coinStatsId: 'bitcoin',
          network: 'ethereum'
        },
        'ethereum': {
          coinCapId: 'ethereum',
          coingeckoId: 'ethereum',
          geckoTerminalId: 'eth',
          coinStatsId: 'ethereum',
          network: 'ethereum',
          tokenId: '******************************************'
        },
        'solana': {
          coinCapId: 'solana',
          coingeckoId: 'solana',
          geckoTerminalId: 'sol',
          coinStatsId: 'solana',
          network: 'ethereum'
        },
        'ripple': {
          coinCapId: 'xrp',
          coingeckoId: 'ripple',
          geckoTerminalId: 'xrp',
          coinStatsId: 'ripple',
          network: 'ethereum'
        },
        'dogecoin': {
          coinCapId: 'dogecoin',
          coingeckoId: 'dogecoin',
          geckoTerminalId: 'doge',
          coinStatsId: 'dogecoin',
          network: 'ethereum'
        },
        'cardano': {
          coinCapId: 'cardano',
          coingeckoId: 'cardano',
          coinStatsId: 'cardano',
          network: 'ethereum'
        },
        'polkadot': {
          coinCapId: 'polkadot',
          coingeckoId: 'polkadot',
          coinStatsId: 'polkadot',
          network: 'ethereum'
        },
        'binancecoin': {
          coinCapId: 'binance-coin',
          coingeckoId: 'binancecoin',
          coinStatsId: 'binancecoin',
          network: 'ethereum'
        },
        'polygon': {
          coinCapId: 'polygon',
          coingeckoId: 'matic-network',
          coinStatsId: 'polygon',
          network: 'ethereum'
        },
        'kaspa': {
          coinCapId: 'kaspa',
          coingeckoId: 'kaspa',
          geckoTerminalId: 'kas',
          coinStatsId: 'kaspa',
          network: 'ethereum'
        }
      };
      
      // Get mapping for the requested cryptocurrency
      const mapping = cryptoMappings[cryptoId];
      
      // Fallback historical price points by cryptocurrency
      const getHistoricalPricePoints = (cryptoId: string, currentPrice?: number): Record<string, number> => {
        // Fallback data for different cryptocurrencies
        switch (cryptoId) {
          case 'bitcoin':
            return {
              2018: 3840,
              2019: 7200,
              2020: 29000,
              2021: 47000,
              2022: 16500,
              2023: 42000,
              2024: 52000,
              2025: currentPrice || 82000
            };
          case 'ethereum':
            return {
              2018: 138,
              2019: 130,
              2020: 730,
              2021: 3700,
              2022: 1200,
              2023: 1800,
              2024: 3200,
              2025: currentPrice || 1788.79
            };
          case 'solana':
            return {
              2018: 0,
              2019: 0.5,
              2020: 1.8,
              2021: 178,
              2022: 12,
              2023: 105,
              2024: 175,
              2025: currentPrice || 225
            };
          case 'kaspa':
            return {
              2018: 0,
              2019: 0,
              2020: 0,
              2021: 0.00001,
              2022: 0.005,
              2023: 0.025,
              2024: 0.065,
              2025: currentPrice || 0.090
            };
          default:
            return {
              2018: 100,
              2019: 150,
              2020: 200,
              2021: 400,
              2022: 300,
              2023: 500,
              2024: 700,
              2025: currentPrice || 900
            };
        }
      };
      
      // Convert period to days for API requests
      const getPeriodInDays = (periodInput: string | any): string => {
        switch(periodInput) {
          case '1month': return '30';
          case '3month': return '90';
          case '6month': return '180';
          case '1year': return '365';
          case '3year': 
          case '5year': 
          case 'max':
            // Limit to 365 days for free/demo API as per the error message:
            // "Public API users are limited to querying historical data within the past 365 days"
            console.log(`Limiting period ${periodInput} to 365 days for CoinGecko free/demo API`);
            return '365';
          default: return '365'; // Default to 1 year
        }
      };
      
      // Generate fallback data for API failures
      const generateFallbackData = (cryptoId: string): { prices: [number, number][]; source: string } => {
        const historicalPrices = getHistoricalPricePoints(cryptoId);
        
        // For Kaspa, create a more detailed chart with quarterly data points
        if (cryptoId === 'kaspa') {
          const detailedPrices: [number, number][] = [];
          
          // Add quarterly data points for each year
          Object.entries(historicalPrices).forEach(([year, endYearPrice]) => {
            const yearNum = parseInt(year);
            
            // Skip years with zero price
            if (endYearPrice <= 0) {
              detailedPrices.push([new Date(yearNum, 0, 1).getTime(), 0]);
              return;
            }
            
            // For years 2022-2025, create quarterly data points
            if (yearNum >= 2022) {
              // Calculate values that increase each quarter
              let prevYearPrice = yearNum > 2022 ? historicalPrices[yearNum - 1] : 0;
              
              // Q1 (January)
              detailedPrices.push([
                new Date(yearNum, 0, 1).getTime(), 
                prevYearPrice + (endYearPrice - prevYearPrice) * 0.25
              ]);
              
              // Q2 (April)
              detailedPrices.push([
                new Date(yearNum, 3, 1).getTime(), 
                prevYearPrice + (endYearPrice - prevYearPrice) * 0.5
              ]);
              
              // Q3 (July)
              detailedPrices.push([
                new Date(yearNum, 6, 1).getTime(), 
                prevYearPrice + (endYearPrice - prevYearPrice) * 0.75
              ]);
              
              // Q4 (October)
              detailedPrices.push([
                new Date(yearNum, 9, 1).getTime(), 
                endYearPrice
              ]);
            } else {
              // For earlier years, just use the yearly value
              detailedPrices.push([new Date(yearNum, 0, 1).getTime(), endYearPrice]);
            }
          });
          
          return {
            prices: detailedPrices,
            source: 'fallback'
          };
        }
        
        // For other cryptocurrencies, use the standard yearly data points
        return {
          prices: Object.entries(historicalPrices).map(([year, price]) => {
            const timestamp = new Date(parseInt(year), 0, 1).getTime();
            return [timestamp, price];
          }),
          source: 'fallback'
        };
      };
      
      // If no mapping exists for this cryptocurrency, use fallback data
      if (!mapping) {
        console.log(`No mapping found for ${cryptoId}, using fallback data`);
        return res.json(generateFallbackData(cryptoId));
      }
      
      // Try multiple APIs in sequence, with fallbacks
      try {
        // 1. Try CoinCap API first (free, no API key needed)
        try {
          console.log(`Trying CoinCap API for ${cryptoId} data`);
          
          // Convert period to milliseconds for start time
          const now = Date.now();
          let startTime;
          
          switch(String(period)) {
            case '1month':
              startTime = now - 30 * 24 * 60 * 60 * 1000;
              break;
            case '3month':
              startTime = now - 90 * 24 * 60 * 60 * 1000;
              break;
            case '6month':
              startTime = now - 180 * 24 * 60 * 60 * 1000;
              break;
            case '1year':
              startTime = now - 365 * 24 * 60 * 60 * 1000;
              break;
            case '3year':
              startTime = now - 3 * 365 * 24 * 60 * 60 * 1000;
              break;
            case '5year':
              startTime = now - 5 * 365 * 24 * 60 * 60 * 1000;
              break;
            case 'max':
              startTime = now - 10 * 365 * 24 * 60 * 60 * 1000; // 10 years max
              break;
            default:
              startTime = now - 365 * 24 * 60 * 60 * 1000; // Default to 1 year
          }
          
          // Use the coinCapId if available, otherwise try the cryptoId
          const coinCapIdToUse = mapping.coinCapId || cryptoId;
          
          // Get asset information first to get proper name, symbol, etc.
          const assetResponse = await axios.get(
            `https://api.coincap.io/v2/assets/${coinCapIdToUse}`, {
              timeout: 5000,
              headers: {
                'User-Agent': 'Mozilla/5.0',
                'Accept': 'application/json'
              }
            }
          );
          
          if (assetResponse.data && assetResponse.data.data) {
            const asset = assetResponse.data.data;
            
            // Now get historical price data
            const historyResponse = await axios.get(
              `https://api.coincap.io/v2/assets/${coinCapIdToUse}/history`, {
                params: {
                  interval: 'd1', // daily data
                  start: startTime,
                  end: now
                },
                timeout: 5000,
                headers: {
                  'User-Agent': 'Mozilla/5.0',
                  'Accept': 'application/json'
                }
              }
            );
            
            if (historyResponse.data && historyResponse.data.data && Array.isArray(historyResponse.data.data)) {
              console.log(`Successfully fetched ${cryptoId} data from CoinCap API`);
              
              // Format the response to match our expected format
              const formattedPrices = historyResponse.data.data.map((item: any) => {
                return [parseInt(item.time), parseFloat(item.priceUsd)];
              });
              
              return res.json({
                prices: formattedPrices,
                source: 'coincap',
                crypto: {
                  id: cryptoId,
                  name: asset.name,
                  symbol: asset.symbol,
                  currentPrice: parseFloat(asset.priceUsd)
                }
              });
            }
          }
        } catch (coincapError: any) {
          console.error(`Error fetching ${cryptoId} data from CoinCap API:`, coincapError.message || coincapError);
        }
        
        // 2. Try CoinStats API as second option (free, no API key needed)
        if (mapping.coinStatsId) {
          try {
            console.log(`Trying CoinStats API for ${cryptoId} data`);
            
            const coinStatsResponse = await axios.get(
              `https://api.coinstats.app/public/v1/charts`, {
                params: {
                  period: 'all',
                  coinId: mapping.coinStatsId
                },
                timeout: 5000,
                headers: {
                  'User-Agent': 'Mozilla/5.0',
                  'Accept': 'application/json'
                }
              }
            );
            
            if (coinStatsResponse.data && Array.isArray(coinStatsResponse.data.chart)) {
              console.log(`Successfully fetched ${cryptoId} data from CoinStats API`);
              
              // Format data to match the expected format (arrays of [timestamp, price])
              const formattedPrices = coinStatsResponse.data.chart.map((point: number[]) => {
                return [point[0], point[1]]; // timestamp, price
              });
              
              return res.json({
                prices: formattedPrices,
                source: 'coinstats'
              });
            }
          } catch (coinStatsError: any) {
            console.error(`Error fetching ${cryptoId} data from CoinStats API:`, coinStatsError.message || coinStatsError);
          }
        }
        
        // 3. Try GeckoTerminal API as third option
        if (mapping.geckoTerminalId || mapping.tokenId) {
          try {
            console.log(`Fetching ${cryptoId} data from GeckoTerminal API`);
            const baseUrl = 'https://api.geckoterminal.com/api/v2';
            let response;
            
            // If we have a specific token ID, use that endpoint
            if (mapping.tokenId) {
              response = await axios.get(`${baseUrl}/networks/${mapping.network}/tokens/${mapping.tokenId}/ohlcv/hour`);
            } else if (mapping.geckoTerminalId) {
              // For other tokens, search by network
              response = await axios.get(`${baseUrl}/search/tokens?query=${mapping.geckoTerminalId}&network=${mapping.network}&include=network`);
            }
            
            // Process GeckoTerminal API response
            if (response?.data && response.data.data) {
              console.log(`Successfully fetched ${cryptoId} data from GeckoTerminal API`);
              
              // Process data based on response format
              let geckoPrices: [number, number][] = [];
              
              try {
                // Check if it's the tokens endpoint response
                if (response.data.data.length > 0 && response.data.data[0].attributes && response.data.data[0].attributes.token_prices) {
                  // Get first token result
                  const tokenPrices = response.data.data[0].attributes.token_prices;
                  const priceValue = Object.values(tokenPrices)[0] as string;
                  const price = parseFloat(priceValue);
                  
                  // Create historical data from fallback with current price
                  const historicalPrices = getHistoricalPricePoints(cryptoId, price);
                  geckoPrices = Object.entries(historicalPrices).map(([year, price]) => {
                    const timestamp = new Date(parseInt(year), 0, 1).getTime();
                    return [timestamp, price];
                  });
                }
                
                // Check if it's the OHLCV endpoint response
                else if (response.data.data && Array.isArray(response.data.data.attributes.ohlcv_list)) {
                  // Format: [[timestamp, open, high, low, close, volume], ...]
                  geckoPrices = response.data.data.attributes.ohlcv_list.map((ohlcv: any[]) => {
                    const timestamp = ohlcv[0];
                    const closePrice = ohlcv[4]; // Using close price
                    return [timestamp, closePrice];
                  });
                }
                
                // Return processed data if we have results
                if (geckoPrices.length > 0) {
                  return res.json({
                    prices: geckoPrices,
                    source: 'geckoterminal'
                  });
                }
              } catch (processingError) {
                console.error('Error processing GeckoTerminal data:', processingError);
              }
            }
          } catch (geckoTerminalError: any) {
            console.error(`Error fetching ${cryptoId} data from GeckoTerminal API:`, geckoTerminalError.message || geckoTerminalError);
          }
        }
        
        // 4. Try CoinGecko API as fourth option (may require API key)
        if (mapping.coingeckoId) {
          try {
            console.log(`Fetching ${cryptoId} data from CoinGecko API`);
            const days = getPeriodInDays(period);
            const interval = days === 'max' || parseInt(days || '0') > 90 ? 'weekly' : 'daily';
            
            // Check if we have the CoinGecko API key in the environment
            let apiKey = process.env.COINGECKO_API_KEY || '';
            
            // Add the CG- prefix if it's not already present
            if (apiKey && !apiKey.startsWith('CG-')) {
              apiKey = `CG-${apiKey}`;
              console.log('Added CG- prefix to CoinGecko API key');
            }
            
            let params: any = {
              vs_currency: 'usd',
              days: days
              // 'interval' parameter is causing errors with demo API, so remove it
              // Acceptable values for public API are 'daily' for < 90 days, otherwise don't use the parameter
            };
            
            // Set up headers
            const headers: Record<string, string> = {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': 'application/json'
            };
            
            // Set the base URL for the CoinGecko API
            let baseUrl;
            
            // If we have an API key
            if (apiKey) {
              // Based on the error message, we're assuming we have a demo key
              // Use the regular API URL with the demo key parameter
              baseUrl = `https://api.coingecko.com/api/v3/coins/${mapping.coingeckoId}/market_chart`;
              console.log(`Using CoinGecko API with Demo API key (api.coingecko.com)`);
              // Add the API key as a parameter
              params.x_cg_demo_api_key = apiKey;
              
              // Log the URL format we're using
              console.log(`Using format: api.coingecko.com with x_cg_demo_api_key parameter`);
              
              /* Original code with check for "Demo" in key:
              if (apiKey.includes('Demo')) {
                baseUrl = `https://api.coingecko.com/api/v3/coins/${mapping.coingeckoId}/market_chart`;
                console.log(`Using CoinGecko API with Demo API key`);
                // Add the API key as a parameter
                params.x_cg_demo_api_key = apiKey;
              } else {
                // Otherwise, use the Pro API URL with the key as a query parameter
                baseUrl = `https://pro-api.coingecko.com/api/v3/coins/${mapping.coingeckoId}/market_chart?x_cg_pro_api_key=${apiKey}`;
                console.log(`Using CoinGecko Pro API with paid API key`);
              }
              */
            } else {
              // No API key, use the free API URL
              baseUrl = `https://api.coingecko.com/api/v3/coins/${mapping.coingeckoId}/market_chart`;
              console.log(`Using CoinGecko free API (no API key)`);
            }
              
            console.log(`Using CoinGecko API URL: ${baseUrl} with API key: ${apiKey ? 'Yes' : 'No'}`);
            // DO NOT log the actual API key value for security reasons!
              
            const response = await axios.get(baseUrl, {
              params,
              timeout: 5000,
              headers
            }
            );
            
            if (response.data && response.data.prices) {
              console.log(`Successfully fetched ${cryptoId} data from CoinGecko API`);
              return res.json({
                ...response.data,
                source: 'coingecko'
              });
            }
          } catch (coingeckoError: any) {
            if (coingeckoError.response) {
              console.error(`Error fetching ${cryptoId} data from CoinGecko API: Status: ${coingeckoError.response.status}, Data:`, 
                coingeckoError.response.data || '(no data)',
                'Headers:', JSON.stringify(coingeckoError.response.headers || {}));
            } else {
              console.error(`Error fetching ${cryptoId} data from CoinGecko API:`, coingeckoError.message || coingeckoError);
            }
          }
        }
        
        // 5. If all APIs fail, use fallback data
        console.log(`All API attempts failed for ${cryptoId}, using fallback data`);
        return res.json(generateFallbackData(cryptoId));
        
      } catch (error: any) {
        // Handle any unexpected errors
        console.error(`Error processing API response for ${cryptoId}:`, error);
        return res.json(generateFallbackData(cryptoId));
      }
    } catch (error) {
      console.error('Error in /api/crypto/historical:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });
  
  // Translation files route
  app.get('/locales/:lng/:ns.json', (req: Request, res: Response) => {
    const { lng, ns } = req.params;
    // More permissive regex to handle variants like en-US
    const language = lng.replace(/[^a-zA-Z-]/g, '');
    const namespace = ns.replace(/[^a-zA-Z]/g, '');
    
    // First try the exact language code (e.g., 'en-US')
    let filePath = path.join('public', 'locales', language, `${namespace}.json`);
    
    try {
      // Check if the exact language file exists
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for an hour
        return res.json(JSON.parse(fileContent));
      } 
      
      // If not found, try the base language (e.g., 'en' from 'en-US')
      const baseLanguage = language.split('-')[0];
      if (baseLanguage !== language) {
        const fallbackPath = path.join('public', 'locales', baseLanguage, `${namespace}.json`);
        
        if (fs.existsSync(fallbackPath)) {
          const fileContent = fs.readFileSync(fallbackPath, 'utf8');
          console.log(`Using fallback translation: ${fallbackPath} instead of ${filePath}`);
          res.setHeader('Cache-Control', 'public, max-age=3600');
          return res.json(JSON.parse(fileContent));
        }
      }
      
      // If we still don't have a match, try English as the ultimate fallback
      if (baseLanguage !== 'en') {
        const englishFallback = path.join('public', 'locales', 'en', `${namespace}.json`);
        
        if (fs.existsSync(englishFallback)) {
          const fileContent = fs.readFileSync(englishFallback, 'utf8');
          console.log(`Using English fallback for: ${language}`);
          res.setHeader('Cache-Control', 'public, max-age=3600');
          return res.json(JSON.parse(fileContent));
        }
      }
      
      // No translation found at all
      console.log(`Translation file not found: ${filePath} and no fallbacks available`);
      return res.status(404).json({ error: 'Translation file not found' });
    } catch (error) {
      console.error(`Error serving translation file: ${error}`);
      return res.status(500).json({ error: 'Error serving translation file' });
    }
  });
  
  // Form submissions listing endpoint
  // Login endpoint
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      
      if (!username || !password) {
        return res.status(400).json({ error: 'Username and password are required' });
      }
      
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        // Log failed login attempt
        await storage.logActivity({
          userId: 0, // 0 for unknown user
          action: 'LOGIN_FAILED',
          details: `Failed login attempt for username: ${username} (user not found)`,
          ipAddress: ipAddress as string
        });
        
        return res.status(401).json({ error: 'Invalid credentials' });
      }
      
      // Check if account is active
      if (user.isActive === false) {
        await storage.logActivity({
          userId: user.id,
          action: 'LOGIN_FAILED',
          details: `Login attempt for disabled account: ${username}`,
          ipAddress: ipAddress as string
        });
        
        return res.status(401).json({ error: 'Account is disabled. Please contact administrator.' });
      }
      
      const isPasswordValid = await storage.validatePassword(user, password);
      
      if (!isPasswordValid) {
        // Log failed login
        await storage.logActivity({
          userId: user.id,
          action: 'LOGIN_FAILED',
          details: `Failed login attempt for username: ${username} (invalid password)`,
          ipAddress: ipAddress as string
        });
        
        return res.status(401).json({ error: 'Invalid credentials' });
      }
      
      // Update last login time
      await storage.updateLastLogin(user.id);
      
      // Log successful login with role info if available
      const roleInfo = user.role && user.role !== 'admin' ? ` (${user.role})` : '';
      const nameInfo = user.fullName ? ` - ${user.fullName}` : '';
      
      await storage.logActivity({
        userId: user.id,
        action: 'LOGIN',
        details: `User logged in: ${username}${nameInfo}${roleInfo}`,
        ipAddress: ipAddress as string
      });
      
      // Set session data
      req.session.authenticated = true;
      req.session.userId = user.id;
      req.session.username = user.username;
      req.session.userRole = user.role || 'admin';
      
      // Add user to online users set and update last activity time
      onlineUsers.add(user.id);
      userLastActivity.set(user.id, Date.now());
      
      // Broadcast to all connected clients
      broadcastUserStatus(user.id, 'online');
      
      // Also broadcast to admins specifically if it's an admin
      if (user.role === 'admin' || user.role === 'super_admin') {
        broadcastAdminStatus(user.id, 'online');
      }
      
      return res.json({ 
        success: true, 
        message: 'Login successful',
        user: {
          id: user.id,
          username: user.username
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({ error: 'An error occurred during login' });
    }
  });

  // Logout endpoint
  app.post('/api/auth/logout', (req: Request, res: Response) => {
    // Store userId before destroying the session
    const userId = req.session.userId;
    
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ error: 'Logout failed' });
      }
      
      // Remove user from online users set if they exist
      if (userId) {
        onlineUsers.delete(userId);
        userLastActivity.delete(userId);
        
        // Get the user details to check if they're an admin
        storage.getUser(userId)
          .then(user => {
            // Broadcast to all connected clients
            broadcastUserStatus(userId, 'offline');
            
            // Also broadcast to admins specifically if it's an admin
            if (user && (user.role === 'admin' || user.role === 'super_admin')) {
              broadcastAdminStatus(userId, 'offline');
            }
          })
          .catch(err => {
            console.error('Error getting user info for logout broadcast:', err);
            // Just broadcast to everyone if we can't get user details
            broadcastUserStatus(userId, 'offline');
          });
      }
      
      res.clearCookie('connect.sid');
      return res.json({ success: true, message: 'Logged out successfully' });
    });
  });

  // Check if user is authenticated
  // Get a list of online users (by user IDs)
  app.get('/api/auth/online-users', async (req: Request, res: Response) => {
    if (!req.session.authenticated) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    try {
      // Get all online users from our in-memory set
      // Convert Set to Array and filter out the current user
      const onlineUserIds = Array.from(onlineUsers)
        .filter(id => id !== req.session.userId);
      
      return res.json(onlineUserIds);
    } catch (error) {
      console.error('Error fetching online users:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  });
  
  app.get('/api/auth/status', async (req: Request, res: Response) => {
    if (req.session.authenticated) {
      try {
        // Get full user details from storage
        const user = await storage.getUser(req.session.userId as number);
        
        if (user) {
          return res.json({ 
            authenticated: true,
            user: {
              id: user.id,
              username: user.username,
              fullName: user.fullName,
              role: user.role,
              twoFactorEnabled: user.twoFactorEnabled,
              notificationSettings: user.notificationSettings ? JSON.parse(user.notificationSettings) : null,
              appearanceSettings: user.appearanceSettings ? JSON.parse(user.appearanceSettings) : null
            }
          });
        } else {
          // If user not found in storage but session exists
          return res.json({ 
            authenticated: true,
            user: {
              id: req.session.userId,
              username: req.session.username
            }
          });
        }
      } catch (error) {
        console.error('Error getting user details:', error);
        // Fallback to session data only
        return res.json({ 
          authenticated: true,
          user: {
            id: req.session.userId,
            username: req.session.username
          }
        });
      }
    }
    
    return res.json({ authenticated: false });
  });
  
  // Get all users - Admin only
  app.get('/api/users', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      
      // Don't send password hashes to the client
      const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin
      }));
      
      return res.json(safeUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }
  });
  
  // Create a new user - Super Admin only
  app.post('/api/users', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { username, password, fullName, email, role, isActive } = req.body;
      
      // Check if user is a super_admin
      if (req.session.userRole !== 'super_admin') {
        return res.status(403).json({ error: 'Only super admins can create users' });
      }
      
      // Validate required fields
      if (!username || !password) {
        return res.status(400).json({ error: 'Username and password are required' });
      }
      
      // Check if username already exists
      const existingUser = await storage.getUserByUsername(username);
      if (existingUser) {
        return res.status(400).json({ error: 'Username already exists' });
      }
      
      // Create the new user
      const user = await storage.createUser({
        username,
        password,
        fullName: fullName || null,
        email: email || null,
        role: role || 'admin',
        isActive: isActive !== undefined ? isActive : true
      });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: 'USER_CREATED',
        details: `User created: ${username}`,
        ipAddress: ipAddress as string
      });
      
      // Create a safe user object (without password) for response and broadcasting
      const safeUser = {
        id: user.id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt
      };
      
      // Broadcast to all user-management WebSocket clients that a new user has been added
      adminClients.forEach((client, key) => {
        // Only send to user-management websocket connections
        if (client.purpose === 'user-management' && client.ws.readyState === WebSocket.OPEN) {
          console.log(`Broadcasting new user to admin: ${client.username} (${key})`);
          client.ws.send(JSON.stringify({
            type: 'userListUpdate',
            action: 'added',
            data: safeUser
          }));
        }
      });
      
      // Return the new user without password
      return res.status(201).json(safeUser);
    } catch (error) {
      console.error('Error creating user:', error);
      return res.status(500).json({ error: 'Failed to create user' });
    }
  });
  
  // Update a user - Admin only
  app.put('/api/users/:id', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { username, password, fullName, email, role, isActive } = req.body;
      
      // Check if user exists
      const existingUser = await storage.getUser(userId);
      if (!existingUser) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      // If updating username, check if it already exists
      if (username && username !== existingUser.username) {
        const userWithSameUsername = await storage.getUserByUsername(username);
        if (userWithSameUsername) {
          return res.status(400).json({ error: 'Username already exists' });
        }
      }
      
      // Update the user
      const updatedUser = await storage.updateUser(userId, {
        username,
        password,
        fullName,
        email,
        role,
        isActive
      });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: 'USER_UPDATED',
        details: `User updated: ${existingUser.username}`,
        ipAddress: ipAddress as string
      });
      
      // Create a safe user object (without password) for response and broadcasting
      const safeUser = {
        id: updatedUser?.id,
        username: updatedUser?.username,
        fullName: updatedUser?.fullName,
        email: updatedUser?.email,
        role: updatedUser?.role,
        isActive: updatedUser?.isActive,
        createdAt: updatedUser?.createdAt,
        lastLogin: updatedUser?.lastLogin
      };
      
      // Broadcast to all user-management WebSocket clients that a user has been updated
      adminClients.forEach((client, key) => {
        // Only send to user-management websocket connections
        if (client.purpose === 'user-management' && client.ws.readyState === WebSocket.OPEN) {
          console.log(`Broadcasting updated user to admin: ${client.username} (${key})`);
          client.ws.send(JSON.stringify({
            type: 'userListUpdate',
            action: 'updated',
            data: safeUser
          }));
        }
      });
      
      // Return the updated user without password
      return res.json(safeUser);
    } catch (error) {
      console.error('Error updating user:', error);
      return res.status(500).json({ error: 'Failed to update user' });
    }
  });
  
  // Delete a user - Super Admin only
  app.delete('/api/users/:id', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      
      // Check if user is a super_admin
      if (req.session.userRole !== 'super_admin') {
        return res.status(403).json({ error: 'Only super admins can delete users' });
      }
      
      // Prevent deleting your own account
      if (userId === req.session.userId) {
        return res.status(400).json({ error: 'You cannot delete your own account' });
      }
      
      // Check if user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      // Delete the user
      const success = await storage.deleteUser(userId);
      
      if (success) {
        // Log the action
        const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
        await storage.logActivity({
          userId: req.session.userId as number,
          action: 'USER_DELETED',
          details: `User deleted: ${user.username}`,
          ipAddress: ipAddress as string
        });
        
        // Broadcast to all user-management WebSocket clients that a user has been deleted
        adminClients.forEach((client, key) => {
          // Only send to user-management websocket connections
          if (client.purpose === 'user-management' && client.ws.readyState === WebSocket.OPEN) {
            console.log(`Broadcasting deleted user to admin: ${client.username} (${key})`);
            client.ws.send(JSON.stringify({
              type: 'userListUpdate',
              action: 'deleted',
              data: { id: userId }
            }));
          }
        });
        
        return res.json({ success: true, message: 'User deleted successfully' });
      } else {
        return res.status(500).json({ error: 'Failed to delete user' });
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      return res.status(500).json({ error: 'Failed to delete user' });
    }
  });
  
  // Change password endpoint
  app.post('/api/users/:id/change-password', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { currentPassword, newPassword } = req.body;
      
      // Users can only change their own password unless they're an admin
      if (userId !== req.session.userId && req.session.userRole !== 'admin') {
        return res.status(403).json({ error: 'Unauthorized to change this user\'s password' });
      }
      
      // Get the user
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      // Verify current password
      const isPasswordValid = await storage.validatePassword(user, currentPassword);
      
      if (!isPasswordValid) {
        return res.status(401).json({ error: 'Current password is incorrect' });
      }
      
      // Update the password
      await storage.updateUser(userId, { 
        password: newPassword 
      });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: 'PASSWORD_CHANGED',
        details: `Password changed for user: ${user.username}`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, message: 'Password updated successfully' });
    } catch (error) {
      console.error('Error changing password:', error);
      return res.status(500).json({ error: 'Failed to change password' });
    }
  });
  
  // Two-factor authentication setup endpoint
  app.post('/api/users/2fa/setup', isAuthenticated, async (req: Request, res: Response) => {
    try {
      // For now we'll simulate 2FA setup since we don't have the actual implementation
      // In a real app, you would use a library like speakeasy to generate a secret and QR code
      
      // Simulate generating a QR code URL
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/OrcaCapital:${req.session.username}?secret=ABCDEFGHIJKLMNOP&issuer=OrcaCapital`;
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: '2FA_SETUP_INITIATED',
        details: `Started 2FA setup for account`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, qrCodeUrl });
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      return res.status(500).json({ error: 'Failed to set up two-factor authentication' });
    }
  });
  
  // Two-factor authentication verification endpoint
  app.post('/api/users/2fa/verify', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { code } = req.body;
      
      // Simulate code verification (in a real app you would verify with speakeasy)
      // Always success for demo
      const isCodeValid = true;
      
      if (!isCodeValid) {
        return res.status(401).json({ error: 'Invalid verification code' });
      }
      
      // Update user to mark 2FA as enabled
      await storage.updateUser(req.session.userId as number, { twoFactorEnabled: true });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: '2FA_ENABLED',
        details: `Enabled 2FA for account`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, message: 'Two-factor authentication enabled' });
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      return res.status(500).json({ error: 'Failed to verify code' });
    }
  });
  
  // Disable 2FA endpoint
  app.post('/api/users/2fa/disable', isAuthenticated, async (req: Request, res: Response) => {
    try {
      // Update user to mark 2FA as disabled
      await storage.updateUser(req.session.userId as number, { twoFactorEnabled: false });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: '2FA_DISABLED',
        details: `Disabled 2FA for account`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, message: 'Two-factor authentication disabled' });
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      return res.status(500).json({ error: 'Failed to disable two-factor authentication' });
    }
  });
  
  // Save notification settings
  app.post('/api/users/notifications/settings', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { emailNotifications, marketAlerts, securityAlerts } = req.body;
      
      // Update user preferences
      await storage.updateUser(req.session.userId as number, { 
        notificationSettings: JSON.stringify({
          emailNotifications,
          marketAlerts,
          securityAlerts
        })
      });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: 'NOTIFICATION_SETTINGS_UPDATED',
        details: `Updated notification settings`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, message: 'Notification settings updated' });
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return res.status(500).json({ error: 'Failed to update notification settings' });
    }
  });
  
  // Save appearance settings
  app.post('/api/users/appearance/settings', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { theme, darkMode } = req.body;
      
      // Update user preferences
      await storage.updateUser(req.session.userId as number, { 
        appearanceSettings: JSON.stringify({
          theme,
          darkMode
        })
      });
      
      // Log the action
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown';
      await storage.logActivity({
        userId: req.session.userId as number,
        action: 'APPEARANCE_SETTINGS_UPDATED',
        details: `Updated appearance settings`,
        ipAddress: ipAddress as string
      });
      
      return res.json({ success: true, message: 'Appearance settings updated' });
    } catch (error) {
      console.error('Error updating appearance settings:', error);
      return res.status(500).json({ error: 'Failed to update appearance settings' });
    }
  });
  
  // Get activity logs - Admin only
  app.get('/api/activity-logs', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
      
      const logs = await storage.getActivityLogs(userId, limit);
      
      return res.json(logs);
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      return res.status(500).json({ error: 'Failed to fetch activity logs' });
    }
  });
  
  // Endpoint to notify clients about application updates
  app.post('/api/notify-app-update', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userRole = req.session.userRole || '';
      
      // Only super admins can trigger app update notifications
      if (userRole !== 'super_admin') {
        return res.status(403).json({ 
          error: 'Forbidden: Only super admins can trigger app update notifications' 
        });
      }
      
      const { version, details } = req.body;
      
      if (!version) {
        return res.status(400).json({ error: 'Version is required' });
      }
      
      // Broadcast the update to all connected clients
      broadcastAppUpdate(version, details);
      
      // Log this activity
      await storage.logActivity({
        userId: (req.session as any).userId || 1, // Fallback to admin if userId is not available
        action: 'APP_UPDATE_NOTIFICATION',
        details: `App update notification sent for version ${version}`,
        ipAddress: req.headers['x-forwarded-for']?.toString() || req.socket.remoteAddress || null
      });
      
      return res.json({ success: true, message: 'App update notification sent' });
    } catch (error) {
      console.error('Error triggering app update notification:', error);
      return res.status(500).json({ error: 'Failed to trigger app update notification' });
    }
  });
  
  // Get chat history between two users
  app.get('/api/chat/history/:userId', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const currentUserId = req.session.userId as number;
      const otherUserId = parseInt(req.params.userId);
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
      
      if (!currentUserId || !otherUserId) {
        return res.status(400).json({ error: 'Invalid user IDs' });
      }
      
      // Get chat history between the two users
      const messages = await storage.getChatHistory(currentUserId, otherUserId, limit);
      
      // Mark messages from other user as read
      await storage.markMessagesAsRead(otherUserId, currentUserId);
      
      // Format messages for the client
      const formattedMessages = await Promise.all(messages.map(async (msg) => {
        const sender = await storage.getUser(msg.fromUserId);
        return {
          id: msg.id,
          from: {
            userId: sender?.id || msg.fromUserId,
            username: sender?.username || 'Unknown',
            fullName: sender?.fullName || null
          },
          text: msg.message,
          timestamp: msg.timestamp,
          isRead: msg.isRead
        };
      }));
      
      return res.json(formattedMessages);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return res.status(500).json({ error: 'Failed to fetch chat history' });
    }
  });
  
  // Get unread message count for current user
  app.get('/api/chat/unread', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const userId = req.session.userId as number;
      
      if (!userId) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }
      
      const count = await storage.getUnreadMessageCount(userId);
      return res.json({ count });
    } catch (error) {
      console.error('Error fetching unread message count:', error);
      return res.status(500).json({ error: 'Failed to fetch unread message count' });
    }
  });

  // Protected endpoint to get form submissions - requires authentication
  app.get('/api/form-submissions', isAuthenticated, (req: Request, res: Response) => {
    try {
      const submissionsDir = path.join('public', 'form-submissions');
      
      // If directory doesn't exist yet, return empty array
      if (!fs.existsSync(submissionsDir)) {
        return res.json([]);
      }
      
      // Read all submission files
      const files = fs.readdirSync(submissionsDir);
      const submissions = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(submissionsDir, file);
          const content = fs.readFileSync(filePath, 'utf8');
          try {
            const submissionData = JSON.parse(content);
            submissions.push(submissionData);
          } catch (parseError) {
            console.error(`Error parsing submission file ${file}:`, parseError);
          }
        }
      }
      
      // Sort submissions by timestamp (newest first)
      submissions.sort((a, b) => {
        const dateA = new Date(a.timestamp).getTime();
        const dateB = new Date(b.timestamp).getTime();
        return dateB - dateA;
      });
      
      return res.json(submissions);
    } catch (error) {
      console.error('Error fetching form submissions:', error);
      return res.status(500).json({ error: 'Failed to fetch form submissions' });
    }
  });
  
  // Send email response to a submission
  app.post('/api/respond-to-submission', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const { email, subject, message } = req.body;
      
      if (!email || !subject || !message) {
        return res.status(400).json({ error: 'Email, subject, and message are required' });
      }
      
      // Create transporter using the same configuration as form submissions
      const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
      
      try {
        // Send mail
        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: email,
          subject: subject,
          text: message,
          html: `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2 style="color: #1e40af;">Orca Capital</h2>
                  <p>${message.replace(/\n/g, '<br>')}</p>
                  <p style="margin-top: 20px; color: #666;">
                    <strong>Orca Capital Investments</strong><br>
                    525 West 8th Avenue<br>
                    Vancouver, BC V5Z 1C6<br>

                    Email: <EMAIL>
                  </p>
                </div>`
        });
        
        return res.json({ success: true, message: 'Response sent successfully' });
      } catch (emailError: any) {
        console.error('Error sending response:', emailError);
        
        // Check for authentication errors specifically
        if (emailError.code === 'EAUTH') {
          return res.status(500).json({ 
            error: 'Email authentication failed. Please check the email credentials.',
            details: 'The system is unable to authenticate with the email provider. This may be due to incorrect username/password or security settings on the email account.'
          });
        }
        
        // Handle other types of errors
        return res.status(500).json({ 
          error: 'Failed to send email response',
          details: emailError.message || 'Unknown email error'
        });
      }
    } catch (error) {
      console.error('Error in response endpoint:', error);
      return res.status(500).json({ error: 'Server error processing your request' });
    }
  });

  // Investment consultation form submission endpoint
  app.post('/api/submit-consultation', async (req: Request, res: Response) => {
    try {
      const { fullName, email, phone, investmentAmount, investmentType, message } = req.body;
      
      // Convert investment amount range to readable format
      let readableAmount = investmentAmount;
      if (investmentAmount === '10000-25000') readableAmount = '$10,000 - $25,000';
      if (investmentAmount === '25000-50000') readableAmount = '$25,000 - $50,000';
      if (investmentAmount === '50000-100000') readableAmount = '$50,000 - $100,000';
      if (investmentAmount === '100000-plus') readableAmount = 'Over $100,000';

      // Convert investment type to readable format
      let readableInvestmentType = investmentType;
      if (investmentType === 'blockchain-ventures') readableInvestmentType = 'Blockchain Ventures';
      if (investmentType === 'crypto-index') readableInvestmentType = 'Crypto Index Fund';
      if (investmentType === 'staking-yield') readableInvestmentType = 'Staking & Yield';
      if (investmentType === 'undecided') readableInvestmentType = 'Undecided';

      // Create email content
      const emailHtml = `
        <h2>New Investment Consultation Request</h2>
        <p><strong>Name:</strong> ${fullName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
        <p><strong>Investment Amount:</strong> ${readableAmount}</p>
        <p><strong>Investment Type:</strong> ${readableInvestmentType}</p>
        <p><strong>Message:</strong> ${message || 'No message provided'}</p>
        <p><em>Submitted from Orca Capital website on ${new Date().toLocaleString()}</em></p>
      `;

      // Create text version as fallback
      const emailText = `
        New Investment Consultation Request
        
        Name: ${fullName}
        Email: ${email}
        Phone: ${phone || 'Not provided'}
        Investment Amount: ${readableAmount}
        Investment Type: ${readableInvestmentType}
        Message: ${message || 'No message provided'}
        
        Submitted from Orca Capital website on ${new Date().toLocaleString()}
      `;

      try {
        // Log the form submission for record-keeping
        console.log('New consultation form submission:', {
          fullName,
          email,
          phone: phone || 'Not provided',
          investmentAmount: readableAmount,
          investmentType: readableInvestmentType,
          message: message || 'No message provided',
          timestamp: new Date().toISOString()
        });
        
        // Store the submission in a JSON file as a backup
        try {
          const submissionsDir = path.join('public', 'form-submissions');
          
          // Create the directory if it doesn't exist
          if (!fs.existsSync(submissionsDir)) {
            fs.mkdirSync(submissionsDir, { recursive: true });
          }
          
          // Create a unique filename based on timestamp
          const timestamp = new Date().getTime();
          const filename = `submission-${timestamp}.json`;
          const filePath = path.join(submissionsDir, filename);
          
          // Write the form data to the file
          const formData = {
            fullName,
            email,
            phone: phone || 'Not provided',
            investmentAmount: readableAmount,
            investmentType: readableInvestmentType,
            message: message || 'No message provided',
            timestamp: new Date().toISOString()
          };
          
          fs.writeFileSync(filePath, JSON.stringify(formData, null, 2));
          console.log(`Form submission saved to ${filePath}`);
        } catch (fileError) {
          console.error('Error saving form submission to file:', fileError);
        }
        
        // Now send the email using nodemailer
        try {
          // Create a transporter with explicit Gmail SMTP settings
          const transporter = nodemailer.createTransport({
            host: 'smtp.gmail.com',
            port: 587,
            secure: false, // Use TLS
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASS
            },
            debug: true, // Show debug output
            logger: true // Log information about the mail transport
          });
          
          // Setup email options - using the same email for both from and to
          const mailOptions = {
            from: process.env.EMAIL_USER, // Just use the email address without a display name
            to: process.env.EMAIL_USER, // Send to the same Gmail address
            subject: 'New Investment Consultation Request from ' + fullName,
            text: emailText,
            html: emailHtml
          };
          
          // Send the email
          const info = await transporter.sendMail(mailOptions);
          console.log('Email sent:', info.response);
          
          return res.status(200).json({ 
            success: true,
            message: 'Your consultation request has been received. Our team will contact you shortly.'
          });
        } catch (emailError) {
          console.error('Error sending email:', emailError);
          
          // If email fails, we still have the submission saved to file
          console.log('Form successfully saved to file as backup');
          
          // Return success to the user since we have the data saved
          return res.status(200).json({ 
            success: true,
            message: 'Your consultation request has been received. Our team will contact you shortly.'
          });
        }
      } catch (error) {
        console.error('Form processing error:', error);
        
        // If something unexpected happens
        return res.status(500).json({ 
          error: 'Failed to process your consultation form. Please try again later.',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Error submitting consultation form:', error);
      return res.status(500).json({ 
        error: 'Failed to send consultation form. Please try again or contact us directly.'
      });
    }
  });
  
  // Handle funding application submissions
  app.post('/api/submit-funding-application', async (req: Request, res: Response) => {
    try {
      const { 
        fullName, 
        email, 
        phone, 
        projectName, 
        projectType, 
        fundingAmount, 
        projectDescription 
      } = req.body;
      
      // Validate the form data
      if (!fullName || !email || !projectName || !projectDescription) {
        return res.status(400).json({ 
          success: false, 
          message: 'Please fill in all required fields' 
        });
      }
      
      // Convert project type to readable format
      let readableProjectType = projectType;
      if (projectType === 'defi') readableProjectType = 'DeFi Protocol';
      if (projectType === 'nft') readableProjectType = 'NFT Project';
      if (projectType === 'dao') readableProjectType = 'DAO';
      if (projectType === 'infrastructure') readableProjectType = 'Web3 Infrastructure';
      if (projectType === 'game') readableProjectType = 'Blockchain Game';
      if (projectType === 'tooling') readableProjectType = 'Developer Tooling';
      if (projectType === 'other') readableProjectType = 'Other';

      // Convert funding amount to readable format
      let readableFundingAmount = fundingAmount;
      if (fundingAmount === 'seed') readableFundingAmount = 'Seed ($25K - $100K)';
      if (fundingAmount === 'growth') readableFundingAmount = 'Growth ($100K - $500K)';
      if (fundingAmount === 'strategic') readableFundingAmount = 'Strategic ($500K+)';
      
      // Create email content
      const emailHtml = `
        <h2>New Funding Application</h2>
        <h3>Project: ${projectName}</h3>
        
        <h4>Contact Information:</h4>
        <p><strong>Name:</strong> ${fullName}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
        
        <h4>Project Details:</h4>
        <p><strong>Project Type:</strong> ${readableProjectType}</p>
        <p><strong>Funding Tier:</strong> ${readableFundingAmount}</p>
        <p><strong>Project Description:</strong> ${projectDescription}</p>
        
        <p><em>Submitted from Orca Capital website on ${new Date().toLocaleString()}</em></p>
      `;

      // Create text version as fallback
      const emailText = `
        New Funding Application
        
        Project: ${projectName}
        
        Contact Information:
        Name: ${fullName}
        Email: ${email}
        Phone: ${phone || 'Not provided'}
        
        Project Details:
        Project Type: ${readableProjectType}
        Funding Tier: ${readableFundingAmount}
        Project Description: ${projectDescription}
        
        Submitted from Orca Capital website on ${new Date().toLocaleString()}
      `;

      try {
        // Log the form submission for record-keeping
        console.log('New funding application submission:', {
          fullName,
          email,
          phone: phone || 'Not provided',
          projectName,
          projectType: readableProjectType,
          fundingAmount: readableFundingAmount,
          projectDescription,
          timestamp: new Date().toISOString()
        });
        
        // Store the submission in a JSON file as a backup
        try {
          const submissionsDir = path.join('public', 'funding-applications');
          
          // Create the directory if it doesn't exist
          if (!fs.existsSync(submissionsDir)) {
            fs.mkdirSync(submissionsDir, { recursive: true });
          }
          
          // Create a unique filename based on timestamp
          const timestamp = new Date().getTime();
          const filename = `application-${timestamp}.json`;
          const filePath = path.join(submissionsDir, filename);
          
          // Write the form data to the file
          const formData = {
            fullName,
            email,
            phone: phone || 'Not provided',
            projectName,
            projectType: readableProjectType,
            fundingAmount: readableFundingAmount,
            projectDescription,
            timestamp: new Date().toISOString()
          };
          
          fs.writeFileSync(filePath, JSON.stringify(formData, null, 2));
          console.log(`Funding application saved to ${filePath}`);
        } catch (fileError) {
          console.error('Error saving funding application to file:', fileError);
        }
        
        // Now send the email using nodemailer
        try {
          // Create a transporter with explicit Gmail SMTP settings
          const transporter = nodemailer.createTransport({
            host: 'smtp.gmail.com',
            port: 587,
            secure: false, // Use TLS
            auth: {
              user: process.env.EMAIL_USER,
              pass: process.env.EMAIL_PASS
            },
            debug: true, // Show debug output
            logger: true // Log information about the mail transport
          });
          
          // Setup email options
          const mailOptions = {
            from: process.env.EMAIL_USER,
            to: process.env.EMAIL_USER,
            subject: 'New Funding Application: ' + projectName,
            text: emailText,
            html: emailHtml
          };
          
          // Send the email
          const info = await transporter.sendMail(mailOptions);
          console.log('Email sent:', info.response);
          
          return res.status(200).json({ 
            success: true,
            message: 'Your funding application has been received. Our team will review it and contact you shortly.'
          });
        } catch (emailError) {
          console.error('Error sending email:', emailError);
          
          // If email fails, we still have the submission saved to file
          console.log('Application successfully saved to file as backup');
          
          // Return success to the user since we have the data saved
          return res.status(200).json({ 
            success: true,
            message: 'Your funding application has been received. Our team will review it and contact you shortly.'
          });
        }
      } catch (error) {
        console.error('Form processing error:', error);
        
        // If something unexpected happens
        return res.status(500).json({ 
          error: 'Failed to process your application. Please try again later.',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Error submitting funding application:', error);
      return res.status(500).json({ 
        error: 'Failed to submit application. Please try again or contact us directly.'
      });
    }
  });

  // Handle trading bot payment orders
  app.post('/api/submit-payment-order', async (req: Request, res: Response) => {
    try {
      // Validate request data using the schema
      const result = insertPaymentOrderSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: 'Invalid payment order data',
          errors: result.error.format()
        });
      }
      
      // Store payment order in database
      const paymentOrder = await storage.createPaymentOrder(result.data);
      
      // Try to send email notification about the new order
      try {
        const transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
          }
        });
        
        const mailOptions = {
          from: process.env.EMAIL_USER,
          to: '<EMAIL>',
          subject: `New ${paymentOrder.category === 'trading-bot' ? 'Trading Bot' : paymentOrder.category} Order from ${paymentOrder.fullName}`,
          text: `
            New ${paymentOrder.category === 'trading-bot' ? 'Trading Bot' : paymentOrder.category} order:
            
            Order ID: ${paymentOrder.id}
            Category: ${paymentOrder.category}
            Name: ${paymentOrder.fullName}
            Email: ${paymentOrder.email}
            Phone: ${paymentOrder.phone}
            Telegram: ${paymentOrder.telegramUsername || 'Not provided'}
            
            Bot: ${paymentOrder.botName}
            Payment Method: ${paymentOrder.paymentMethod}
            Payment Plan: ${paymentOrder.paymentPlan}
            Amount: ${paymentOrder.currency === 'USD' || paymentOrder.currency === 'USDC' ? 
                   '$' + (paymentOrder.amount / 100).toLocaleString('en-US', {
                     minimumFractionDigits: 2,
                     maximumFractionDigits: 2
                   }) : 
                   `${paymentOrder.amount} ${paymentOrder.currency}`}
            
            ${paymentOrder.transactionId ? `Transaction ID: ${paymentOrder.transactionId}` : ''}
            
            Status: ${paymentOrder.status}
            Timestamp: ${paymentOrder.createdAt ? new Date(paymentOrder.createdAt).toLocaleString() : new Date().toLocaleString()}
          `,
          html: `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">New ${paymentOrder.category === 'trading-bot' ? 'Trading Bot' : paymentOrder.category} Order</h2>
            <p><strong>Order ID:</strong> ${paymentOrder.id}</p>
            <p><strong>Category:</strong> ${paymentOrder.category}</p>
            <p><strong>Name:</strong> ${paymentOrder.fullName}</p>
            <p><strong>Email:</strong> ${paymentOrder.email}</p>
            <p><strong>Phone:</strong> ${paymentOrder.phone}</p>
            <p><strong>Telegram:</strong> ${paymentOrder.telegramUsername || 'Not provided'}</p>
            
            <hr style="border: 1px solid #eee; margin: 20px 0;">
            
            <p><strong>Bot:</strong> ${paymentOrder.botName}</p>
            <p><strong>Payment Method:</strong> ${paymentOrder.paymentMethod}</p>
            <p><strong>Payment Plan:</strong> ${paymentOrder.paymentPlan}</p>
            <p><strong>Amount:</strong> ${paymentOrder.currency === 'USD' || paymentOrder.currency === 'USDC' ? 
              '$' + (paymentOrder.amount / 100).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              }) : 
              `${paymentOrder.amount} ${paymentOrder.currency}`}</p>
            
            ${paymentOrder.transactionId ? `<p><strong>Transaction ID:</strong> ${paymentOrder.transactionId}</p>` : ''}
            
            <p><strong>Status:</strong> <span style="color: ${
              paymentOrder.status === 'pending' ? '#f59e0b' : 
              paymentOrder.status === 'completed' ? '#10b981' : 
              paymentOrder.status === 'cancelled' ? '#ef4444' : '#6b7280'
            };">${paymentOrder.status}</span></p>
            
            <p><strong>Timestamp:</strong> ${paymentOrder.createdAt ? new Date(paymentOrder.createdAt).toLocaleString() : new Date().toLocaleString()}</p>
            
            <div style="margin-top: 30px; color: #666; font-size: 14px;">
              <p>Orca Capital</p>
              <p>525 West 8th Avenue, Vancouver, BC V5Z 1C6</p>
              <p><EMAIL></p>
            </div>
          </div>`
        };
        
        await transporter.sendMail(mailOptions);
        console.log('Payment order email notification sent');
      } catch (emailError) {
        console.error('Failed to send payment order email notification:', emailError);
        // Continue even if email fails, as we've saved to the database
      }
      
      // Return success response with the created order
      return res.status(201).json({
        success: true,
        message: 'Your payment order has been submitted successfully. Our team will review it and contact you soon.',
        order: paymentOrder
      });
    } catch (error) {
      console.error('Error processing payment order:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while processing your payment order. Please try again later.'
      });
    }
  });
  
  // Get all payment orders (admin only)
  app.get('/api/payment-orders', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const orders = await storage.getAllPaymentOrders();
      return res.json(orders);
    } catch (error) {
      console.error('Error fetching payment orders:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch payment orders'
      });
    }
  });
  
  // Get a specific payment order by ID (admin only)
  app.get('/api/payment-orders/:id', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const orderId = parseInt(req.params.id);
      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order ID'
        });
      }
      
      const order = await storage.getPaymentOrderById(orderId);
      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Payment order not found'
        });
      }
      
      return res.json(order);
    } catch (error) {
      console.error('Error fetching payment order:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch payment order'
      });
    }
  });
  
  // Update payment order status (admin only)
  app.put('/api/payment-orders/:id/status', isAuthenticated, async (req: Request, res: Response) => {
    try {
      const orderId = parseInt(req.params.id);
      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order ID'
        });
      }
      
      const { status, notes } = req.body;
      if (!status || !['pending', 'completed', 'cancelled', 'refunded'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid status value'
        });
      }
      
      const userId = (req.session as any).userId;
      const updatedOrder = await storage.updatePaymentOrderStatus(orderId, status, userId, notes);
      
      if (!updatedOrder) {
        return res.status(404).json({
          success: false,
          message: 'Payment order not found'
        });
      }
      
      // Log this activity
      await storage.logActivity({
        userId: userId || 1,
        action: 'PAYMENT_STATUS_UPDATED',
        details: `Updated payment order #${orderId} status to ${status}${notes ? ' with notes' : ''}`,
        ipAddress: req.ip || null
      });
      
      return res.json({
        success: true,
        message: 'Payment order status updated',
        order: updatedOrder
      });
    } catch (error) {
      console.error('Error updating payment order status:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update payment order status'
      });
    }
  });
  
  const httpServer = createServer(app);
  
  // Set up WebSocket server on a different path from Vite's HMR
  // Houdini Swap API Configuration
  const HOUDINI_API_BASE_URL = 'https://api.houdiniswap.com';
  const HOUDINI_API_KEY = process.env.HOUDINI_SWAP_API_KEY;
  
  // Configure Axios for Houdini Swap API requests
  const houdiniSwapClient = axios.create({
    baseURL: HOUDINI_API_BASE_URL,
    headers: {
      'Authorization': `Bearer ${HOUDINI_API_KEY}`,
      'Content-Type': 'application/json',
      'User-Agent': 'GhostSwap/1.0.0',
      'Accept': 'application/json'
    },
    timeout: 10000 // 10 seconds timeout
  });
  
  // Proxy for Houdini Swap API
  app.post('/api/houdini-swap/create', async (req: Request, res: Response) => {
    try {
      // Extract required parameters from request body
      const { fromCurrency, toCurrency, amount, walletAddress, isAdvanced } = req.body;
      
      if (!fromCurrency || !toCurrency || !amount || !walletAddress) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters: fromCurrency, toCurrency, amount, and walletAddress are required'
        });
      }
      
      // Make request to Houdini Swap API
      try {
        console.log(`Attempting to create swap: ${fromCurrency} to ${toCurrency}, amount: ${amount}`);
        console.log(`Using Houdini API key: ${HOUDINI_API_KEY ? 'Available' : 'Missing'}`);
        
        if (!HOUDINI_API_KEY) {
          throw new Error('Houdini Swap API key is not configured');
        }
        
        // Prepare the request payload
        const payload = {
          fromCurrency,
          toCurrency,
          amount,
          walletAddress,
          callbackUrl: `${req.protocol}://${req.get('host')}/api/houdini-swap/callback`,
          // Optional parameters for advanced mode (mixer functionality)
          mode: isAdvanced ? 'mixer' : 'standard',
          mixingDepth: isAdvanced ? 4 : 1, // Deeper mixing in advanced mode
          privacyLevel: isAdvanced ? 'max' : 'standard'
        };
        
        // Make the API call using the configured client
        const response = await houdiniSwapClient.post('/api/v1/swap', payload);
        
        // Process the response
        if (response.data && response.data.success) {
          // Store transaction ID for later reference
          const txData = {
            txId: response.data.txId,
            fromCurrency,
            toCurrency,
            amount,
            walletAddress,
            status: 'pending',
            createdAt: new Date().toISOString()
          };
          
          // We would store this in a real database in production
          console.log('New Houdini Swap transaction created successfully:', txData);
          
          return res.status(200).json({
            success: true,
            ...txData
          });
        } else {
          // API returned an error
          console.warn('Houdini API returned an error response:', response.data);
          return res.status(400).json({
            success: false,
            message: response.data.message || 'Error from Houdini Swap API',
            errorCode: response.data.errorCode
          });
        }
      } catch (apiError: any) {
        console.error('Error calling Houdini Swap API:', apiError);
        
        // Check if we got a response with error details
        if (apiError.response && apiError.response.data) {
          console.warn('API error response:', apiError.response.data);
          return res.status(apiError.response.status || 500).json({
            success: false,
            message: apiError.response.data.message || 'Error from Houdini Swap API',
            errorCode: apiError.response.data.errorCode
          });
        }
        
        // If we can't reach the actual API, safely simulate a transaction for demo purposes
        console.log('Using simulated response for Houdini Swap API');
        
        // Generate a consistent transaction ID for tracking
        const mockTxId = `ghost-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
        
        return res.status(200).json({
          success: true,
          txId: mockTxId,
          fromCurrency,
          toCurrency,
          amount,
          walletAddress,
          status: 'pending',
          createdAt: new Date().toISOString(),
          estimatedCompletionTime: new Date(Date.now() + 1000 * 60 * 15).toISOString(), // 15 min
          networkFee: (parseFloat(amount) * 0.003).toFixed(8), // 0.3% network fee
          privacyFee: isAdvanced ? (parseFloat(amount) * 0.02).toFixed(8) : (parseFloat(amount) * 0.018).toFixed(8) // 1.8-2% privacy fee
        });
      }
      
    } catch (error) {
      console.error('Error creating Houdini Swap transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create Houdini Swap transaction',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
  
  // Callback handler for Houdini Swap
  app.post('/api/houdini-swap/callback', async (req: Request, res: Response) => {
    try {
      // Extract transaction details from callback
      const { txId, status, txHash, fromAmount, toAmount, fee, signature } = req.body;
      
      if (!txId || !status) {
        return res.status(400).json({
          success: false,
          message: 'Missing required parameters in callback'
        });
      }
      
      // Verify callback authenticity using signature
      // In a production environment, we would verify the signature here using the shared secret
      // For now, we'll log it and assume it's valid
      console.log('Received callback with signature:', signature);
      
      // Update transaction status in database (mock for now)
      console.log('Received Houdini Swap callback:', {
        txId,
        status,
        txHash,
        fromAmount,
        toAmount,
        fee,
        updatedAt: new Date().toISOString()
      });
      
      // In a real implementation, we would:
      // 1. Update the transaction status in the database
      // 2. Notify the user via WebSocket if they are connected
      
      // Send notification to connected WebSocket clients
      wss.clients.forEach((client: WebSocket) => {
        if (client.readyState === WebSocket.OPEN) {
          try {
            client.send(JSON.stringify({
              type: 'ghost_swap_update',
              data: {
                txId,
                status,
                txHash,
                fromAmount,
                toAmount,
                fee,
                updatedAt: new Date().toISOString()
              }
            }));
          } catch (error) {
            console.error('Error sending Ghost Swap update notification:', error);
          }
        }
      });
      
      return res.status(200).json({
        success: true,
        message: 'Callback received successfully'
      });
    } catch (error) {
      console.error('Error processing Houdini Swap callback:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to process callback',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
  
  // Proxy for Houdini Swap Status API
  app.get('/api/houdini-swap/status', async (req: Request, res: Response) => {
    try {
      // Extract query parameters
      const { txId } = req.query;
      
      if (!txId) {
        return res.status(400).json({
          success: false,
          message: 'Missing required txId parameter'
        });
      }
      
      // Set up headers with API key
      const headers = {
        'Authorization': `Bearer ${HOUDINI_API_KEY}`,
        'Content-Type': 'application/json'
      };
      
      // Make request to Houdini Swap API
      const apiUrl = `${HOUDINI_API_BASE_URL}/api/v1/status/${txId}`;
      const response = await axios.get(apiUrl, { headers });
      
      if (response.data && response.data.success) {
        return res.status(200).json(response.data);
      } else {
        // If the API returned an error
        return res.status(400).json({
          success: false,
          message: response.data.message || 'Error fetching transaction status',
          errorCode: response.data.errorCode
        });
      }
    } catch (error: any) {
      console.error('Error fetching from Houdini Swap API:', error);
      
      // Check if we got a response with error details
      if (error.response && error.response.data) {
        return res.status(error.response.status || 500).json({
          success: false,
          message: error.response.data.message || 'Error fetching transaction status',
          errorCode: error.response.data.errorCode
        });
      }
      
      // Generic error response
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch data from Houdini Swap API',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Handle waitlist form submissions
  app.post('/api/waitlist', (req: Request, res: Response) => {
    try {
      const { name, email, userType, interestLevel } = req.body;
      
      if (!name || !email || !userType || !interestLevel) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields'
        });
      }
      
      // Store in memory array for now
      const newEntry = {
        id: waitlistEntries.length + 1,
        name,
        email,
        userType,
        interestLevel,
        timestamp: new Date().toISOString(),
        ip: req.ip || 'unknown'
      };
      
      waitlistEntries.push(newEntry);
      console.log('New waitlist entry:', newEntry);
      
      return res.status(200).json({
        success: true,
        message: 'Successfully added to waitlist'
      });
    } catch (error) {
      console.error('Error processing waitlist submission:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to process waitlist submission'
      });
    }
  });

  const wss = new WebSocketServer({ 
    server: httpServer,
    path: '/ws'
  });
  
  // Function to broadcast user status changes to all connected clients
  function broadcastUserStatus(userId: number, status: 'online' | 'offline') {
    console.log(`Broadcasting user status: ${userId} is ${status}`);
    
    // Prepare user status update message
    const statusMessage = JSON.stringify({
      type: 'user_status_update',
      userId,
      status,
      timestamp: new Date().toISOString()
    });
    
    // Send to all connected WebSocket clients
    wss.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(statusMessage);
      }
    });
  }
  
  // Now that we have the broadcastUserStatus function, set up the cleanup interval
  // Clean up stale users every 5 minutes (users inactive for more than 15 minutes)
  setInterval(() => {
    const now = Date.now();
    const staleUsers: number[] = [];
    
    userLastActivity.forEach((lastActivity, userId) => {
      if (now - lastActivity > INACTIVE_THRESHOLD) {
        staleUsers.push(userId);
      }
    });
    
    // Remove stale users
    staleUsers.forEach(userId => {
      console.log(`Removing stale user ${userId} from online users`);
      onlineUsers.delete(userId);
      userLastActivity.delete(userId);
      // Broadcast status change
      broadcastUserStatus(userId, 'offline');
    });
  }, 5 * 60 * 1000);
  
  // Track connected regular clients for analytics
  const analyticsClients = new Set<WebSocket>();
  
  // Track admin clients for internal messaging
  type ConnectedAdmin = {
    ws: WebSocket;
    userId: number;
    username: string;
    fullName: string | null;
    role: string;
    purpose: 'chat' | 'user-management'; // Identify what this connection is for
  };
  
  // removed duplicate definition of broadcastUserStatus
  
  // Using number | string as key type to support both numeric userId for chat
  // and string keys like 'userId-usermgmt' for user management connections
  const adminClients = new Map<number | string, ConnectedAdmin>();
  
  // Generate random analytics data for the dashboard
  const generateRandomData = () => {
    return {
      timestamp: new Date().toISOString(),
      visitors: Math.floor(Math.random() * 50) + 100,
      submissions: Math.floor(Math.random() * 5) + 1,
      whitepaperRequests: Math.floor(Math.random() * 3),
      topReferrers: [
        { name: 'Google', count: Math.floor(Math.random() * 30) + 20 },
        { name: 'Twitter', count: Math.floor(Math.random() * 20) + 10 },
        { name: 'LinkedIn', count: Math.floor(Math.random() * 15) + 5 },
      ],
      investmentTypes: [
        { type: 'Crypto', percent: 40 + Math.floor(Math.random() * 10) },
        { type: 'DeFi', percent: 25 + Math.floor(Math.random() * 10) },
        { type: 'NFT', percent: 10 + Math.floor(Math.random() * 10) },
        { type: 'Other', percent: 5 + Math.floor(Math.random() * 5) },
      ],
      recentActivity: [
        { action: 'New login', time: '2 minutes ago' },
        { action: 'Form submission', time: '15 minutes ago' },
        { action: 'Whitepaper request', time: '1 hour ago' },
      ]
    };
  };
  
  // Function to notify all connected app-update-listeners about a new app version
  function broadcastAppUpdate(version: string, details?: string) {
    console.log(`Broadcasting app update notification: v${version}`);
    
    // Count how many clients we're notifying
    let updatedClientCount = 0;
    
    wss.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(JSON.stringify({
            type: 'app_update',
            data: {
              version,
              details: details || 'A new version is available. Please refresh to update.'
            }
          }));
          updatedClientCount++;
        } catch (error) {
          console.error('Error sending app update notification:', error);
        }
      }
    });
    
    console.log(`App update notification sent to ${updatedClientCount} clients`);
  }
  
  // Function to broadcast admin online status changes
  function broadcastAdminStatus(userId: number, status: 'online' | 'offline') {
    console.log(`Broadcasting admin status: ${userId} is ${status}`);
    
    // Find admin info by iterating through all clients
    let adminInfo: ConnectedAdmin | null = null;
    
    adminClients.forEach((client, key) => {
      if (client.userId === userId && client.purpose === 'chat') {
        adminInfo = client;
        console.log(`Found admin info for ${userId}: ${client.username}`);
      }
    });
    
    if (!adminInfo && status === 'online') {
      console.log(`Cannot broadcast online status: Admin ${userId} not found`);
      return;
    }
    
    // Create updated admin list, only including chat admins (not user-management connections)
    const onlineAdmins = Array.from(adminClients.values())
      .filter(client => client.purpose === 'chat')
      .map(client => ({
        userId: client.userId,
        username: client.username,
        fullName: client.fullName,
        role: client.role
      }));
    
    console.log(`Broadcasting to ${adminClients.size} clients, ${onlineAdmins.length} online admins`);
    
    // Broadcast to all connected admin clients with purpose = 'chat'
    adminClients.forEach((client, key) => {
      if (client.purpose === 'chat' && client.ws.readyState === WebSocket.OPEN) {
        console.log(`Sending status update to ${client.username} (${key})`);
        
        // Send status update
        client.ws.send(JSON.stringify({
          type: 'adminStatus',
          data: {
            userId: userId || 1, // Fallback to admin user if userId is undefined
            username: adminInfo?.username || 'Unknown',
            fullName: adminInfo?.fullName || null,
            status
          }
        }));
        
        // Send updated list
        client.ws.send(JSON.stringify({
          type: 'adminList',
          data: onlineAdmins
        }));
      }
    });
  }
  
  // Handle WebSocket connections
  wss.on('connection', (ws: WebSocket, request) => {
    // Check for admin connection parameters
    const url = new URL(request.url || '', `http://${request.headers.host}`);
    const connectionType = url.searchParams.get('type') || 'analytics';
    const clientType = url.searchParams.get('clientType');
    
    // Check for app update notifier client type
    if (clientType === 'app-update-notifier') {
      // This is a connection listening for app updates
      console.log('App update notifier connected');
      
      // Store this connection for later broadcasts about app updates
      // No specific client data needed, this is a general listener
      
      // Setup ping for keeping connection alive
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ type: 'ping' }));
        } else {
          clearInterval(pingInterval);
        }
      }, 30000); // Send ping every 30 seconds
      
      // Handle connection close
      ws.on('close', () => {
        console.log('App update listener disconnected');
        clearInterval(pingInterval);
      });
      
      // Handle messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          if (data.type === 'ping') {
            ws.send(JSON.stringify({ type: 'pong' }));
          }
        } catch (error) {
          console.error('Error parsing message from app update listener:', error);
        }
      });
      
    } else if (connectionType === 'admin') {
      // Parse common admin connection parameters
      const userId = Number(url.searchParams.get('userId') || '0');
      const username = url.searchParams.get('username') || 'unknown';
      const fullName = url.searchParams.get('fullName') || null;
      const role = url.searchParams.get('role') || 'user';
      
      // Allow all authenticated users to chat, but admin-only features require admin role
      if (userId > 0) {
        // Check if this is a user-management connection or chat
        const connectionType = url.searchParams.get('adminType') || 'chat';
        console.log(`User connected with type: ${connectionType}`);
        
        // For user management, only admins are allowed
        if (connectionType === 'user-management' && !(role === 'admin' || role === 'super_admin')) {
          console.log(`Unauthorized user tried to access user-management: ${username} (${userId}, role: ${role})`);
          ws.close(1008, 'Unauthorized: Only admins can access user management features');
          return;
        }
        
        if (connectionType === 'user-management') {
          // This is a user management connection
          console.log(`Admin connected to user management: ${username} (${userId})`);
          
          // Store admin connection for user management
          const clientData: ConnectedAdmin = { 
            ws, 
            userId, 
            username, 
            fullName, 
            role, 
            purpose: 'user-management' as 'user-management' 
          };
          
          // Use a special key to avoid conflicts with chat connections
          const userMgmtKey = `${userId}-usermgmt`;
          
          // Store the connection with the special key
          // This allows the same admin to have both chat and user-management connections
          adminClients.set(userMgmtKey, clientData);
          
          // Log this activity
          storage.logActivity({
            userId,
            action: 'ADMIN_USER_MANAGEMENT_CONNECTED',
            details: `Admin ${username} connected to the user management system`,
            ipAddress: request.headers['x-forwarded-for']?.toString() || request.socket.remoteAddress || null
          }).catch(err => console.error('Failed to log activity:', err));
          
          // Handle WebSocket messages from this user management connection
          ws.on('message', (message) => {
            try {
              // No need to process messages from user management - this is just for receiving updates
              // We keep this handler to prevent errors but don't do anything with the messages
              console.log('Received user management message (ignoring)');
            } catch (error) {
              console.error('Error processing user management WebSocket message:', error);
            }
          });

          // Handle user management disconnection
          ws.on('close', () => {
            console.log(`Admin disconnected from user management: ${username} (${userId})`);
            
            // Remove from admin clients
            adminClients.delete(userMgmtKey);
            
            // Log this activity
            storage.logActivity({
              userId,
              action: 'ADMIN_USER_MANAGEMENT_DISCONNECTED',
              details: `Admin ${username} disconnected from the user management system`,
              ipAddress: request.headers['x-forwarded-for']?.toString() || request.socket.remoteAddress || null
            }).catch(err => console.error('Failed to log activity:', err));
          });
        } else {
          // This is a regular admin chat connection
          console.log(`Admin connected to chat: ${username} (${userId})`);
          
          // Store admin connection
          // Add a flag to mark this as a chat or user-management connection
          const clientData: ConnectedAdmin = { 
            ws, 
            userId, 
            username, 
            fullName, 
            role, 
            purpose: 'chat' as 'chat' // Explicitly cast as literal type 'chat'
          };
          adminClients.set(userId, clientData);
          
          // Send the current online admins list to the newly connected admin
          const onlineAdmins = Array.from(adminClients.values())
            .filter(client => client.purpose === 'chat')
            .map(client => ({
              userId: client.userId,
              username: client.username,
              fullName: client.fullName,
              role: client.role
            }));
          
          if (ws.readyState === WebSocket.OPEN) {
            // First send the list of online admins
            ws.send(JSON.stringify({
              type: 'adminList',
              data: onlineAdmins
            }));
            
            // For chat connections, also send the list of all users from the database
            // This ensures that new employees can message anyone regardless of online status
            if (clientData.purpose === 'chat') {
              console.log(`Sending all users list to admin ${username} (${userId})`);
              
              // Get all users from the database (wrapped in an async function)
              (async () => {
                try {
                  const allUsers = await storage.getAllUsers();
                  
                  // Send user list to the admin (if still connected)
                  if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                      type: 'userListInitial',
                      data: allUsers.map(user => ({
                        id: user.id,
                        username: user.username,
                        full_name: user.fullName,
                        email: user.email || '',
                        role: user.role || 'user'
                      }))
                    }));
                  }
                } catch (error) {
                  console.error('Error sending initial user list to admin:', error);
                }
              })();
            }
          }
          
          // Notify all other admins about the new connection
          broadcastAdminStatus(userId, 'online');
          
          // Log this activity
          storage.logActivity({
            userId,
            action: 'ADMIN_CHAT_CONNECTED',
            details: `Admin ${username} connected to the chat system`,
            ipAddress: request.headers['x-forwarded-for']?.toString() || request.socket.remoteAddress || null
          }).catch(err => console.error('Failed to log activity:', err));
          
          // Handle admin chat messages
          ws.on('message', (message) => {
            try {
              const parsedMessage = JSON.parse(message.toString());
              
              if (parsedMessage.type === 'chat') {
                // Handle chat messages
                const { toUserId, text, isRegularUser, hasAttachments } = parsedMessage.data;
                const fromAdmin = adminClients.get(userId);
                
                if (fromAdmin) {
                  const timestamp = new Date().toISOString();
                  
                  // Format the message with sender info
                  const formattedMessage = {
                    type: 'chat',
                    data: {
                      from: {
                        userId: fromAdmin.userId,
                        username: fromAdmin.username,
                        fullName: fromAdmin.fullName
                      },
                      text,
                      timestamp,
                      hasAttachments: hasAttachments || false
                    }
                  };
                  
                  // Save message to database for persistence
                  (async () => {
                    try {
                      await storage.saveChatMessage({
                        fromUserId: fromAdmin.userId,
                        toUserId: toUserId || 0, // 0 for broadcast messages
                        message: text,
                        isRead: false
                      });
                    } catch (error) {
                      console.error('Error saving chat message to database:', error);
                    }
                  })();
                  
                  if (isRegularUser) {
                    // This is a message to a regular user (not an admin)
                    // For now, just log it since we don't have regular user websockets yet
                    console.log(`Admin ${fromAdmin.username} sent message to user ${toUserId}: ${text}`);
                    
                    // Log activity for admin to user chat
                    storage.logActivity({
                      userId: fromAdmin.userId,
                      action: 'ADMIN_TO_USER_MESSAGE',
                      details: `Message sent to user ID: ${toUserId}`,
                      ipAddress: request.headers['x-forwarded-for']?.toString() || request.socket.remoteAddress || null
                    }).catch(err => console.error('Failed to log activity:', err));
                    
                    // Send back to sender for chat history
                    if (ws.readyState === WebSocket.OPEN) {
                      ws.send(JSON.stringify(formattedMessage));
                    }
                    
                    // TODO: In the future, relay this to the user's client if they're connected
                  }
                  // Send to specific admin if toUserId is provided and not a regular user
                  else if (toUserId) {
                    console.log(`Attempting to send message to admin ID: ${toUserId}`);
                    // Debug log of all connected admins
                    console.log('Connected admins:', Array.from(adminClients.entries()).map(([key, client]) => 
                      `${key}: ${client.username} (purpose: ${client.purpose})`
                    ));
                    
                    const numericToUserId = Number(toUserId);
                    console.log(`Converted recipient ID: ${toUserId} -> ${numericToUserId}`);
                    
                    // Look for the recipient admin among all connected clients
                    let recipientFound = false;
                    
                    // Enhanced matching logic with extensive logging
                    console.log(`Message from ${fromAdmin.username} (${fromAdmin.userId}) to admin ID: ${toUserId} (numeric: ${numericToUserId})`);
                    
                    // Get admin info for debugging
                    const adminInfo = Array.from(adminClients.entries()).find(([_, client]) => 
                      client.userId === numericToUserId && client.purpose === 'chat'
                    );
                    
                    if (adminInfo) {
                      console.log(`Found admin info for ${numericToUserId}: ${adminInfo[1].username}`);
                    } else {
                      console.log(`No admin info found for ID ${numericToUserId}`);
                    }
                    
                    adminClients.forEach((client, key) => {
                      // Check purpose first to filter non-chat connections
                      if (client.purpose !== 'chat') {
                        console.log(`Skipping non-chat connection: ${client.username} (${key}, purpose: ${client.purpose})`);
                        return;
                      }
                      
                      // Enhanced matching to ensure proper ID comparison
                      const clientId = Number(client.userId); // Ensure numeric comparison
                      const targetId = Number(toUserId);      // Ensure numeric comparison
                      
                      console.log(`Checking: Client ${client.username} (ID:${clientId}) against target ID:${targetId}`);
                      
                      if (clientId === targetId && client.ws.readyState === WebSocket.OPEN) {
                        console.log(`★ Found recipient admin: ${client.username} (ID:${clientId})`);
                        client.ws.send(JSON.stringify(formattedMessage));
                        recipientFound = true;
                      }
                    });
                    
                    if (!recipientFound) {
                      console.log(`Admin recipient not found or not connected: ${toUserId}`);
                      // Message will still be saved to DB for offline delivery
                    }
                    
                    // Always send back to sender for chat history
                    if (ws.readyState === WebSocket.OPEN) {
                      console.log(`Sending confirmation back to sender: ${fromAdmin.username}`);
                      ws.send(JSON.stringify(formattedMessage));
                    }
                  } else {
                    // Broadcast to all admins if no specific recipient
                    adminClients.forEach(client => {
                      if (client.ws.readyState === WebSocket.OPEN) {
                        client.ws.send(JSON.stringify(formattedMessage));
                      }
                    });
                  }
                }
              } else if (parsedMessage.type === 'typing') {
                // Handle typing indicator
                const { toUserId, isTyping } = parsedMessage.data;
                console.log(`Typing indicator for admin ID: ${toUserId}, isTyping: ${isTyping}`);
                
                const numericToUserId = Number(toUserId);
                console.log(`Converted typing indicator recipient ID: ${toUserId} -> ${numericToUserId}`);
                
                // Look for the recipient admin among all connected clients
                let recipientFound = false;
                
                // Enhanced matching logic with additional logging
                console.log(`Typing indicator from ${username} (${userId}) to admin ID: ${toUserId} (numeric: ${numericToUserId})`);
                
                adminClients.forEach((client, key) => {
                  // Check purpose first to filter non-chat connections
                  if (client.purpose !== 'chat') {
                    console.log(`Skipping non-chat connection for typing: ${client.username} (${key}, purpose: ${client.purpose})`);
                    return;
                  }
                  
                  // Enhanced matching to ensure proper ID comparison
                  const clientId = Number(client.userId); // Ensure numeric comparison
                  const targetId = Number(toUserId);      // Ensure numeric comparison
                  
                  console.log(`Checking typing: Client ${client.username} (ID:${clientId}) against target ID:${targetId}`);
                  
                  if (clientId === targetId && client.ws.readyState === WebSocket.OPEN) {
                    console.log(`★ Sending typing indicator to admin: ${client.username} (ID:${clientId})`);
                    client.ws.send(JSON.stringify({
                      type: 'typing',
                      data: {
                        userId,
                        isTyping
                      }
                    }));
                    recipientFound = true;
                  }
                });
                
                if (!recipientFound) {
                  console.log(`Cannot send typing indicator: Admin recipient not found or not connected: ${toUserId}`);
                }
              }
            } catch (error) {
              console.error('Error processing admin WebSocket message:', error);
            }
          });
          
          // Handle admin disconnection
          ws.on('close', () => {
            console.log(`Admin disconnected from chat: ${username} (${userId})`);
            
            // Remove from admin clients
            adminClients.delete(userId);
            
            // Notify other admins
            broadcastAdminStatus(userId, 'offline');
            
            // Log this activity
            storage.logActivity({
              userId,
              action: 'ADMIN_CHAT_DISCONNECTED',
              details: `Admin ${username} disconnected from the chat system`,
              ipAddress: request.headers['x-forwarded-for']?.toString() || request.socket.remoteAddress || null
            }).catch(err => console.error('Failed to log activity:', err));
          });
        }
      } else {
        // Not authenticated
        ws.close(1008, 'Unauthorized: You must be logged in to use this feature');
      }
    } else {
      // Regular analytics connection
      console.log('Analytics client connected to WebSocket');
      analyticsClients.add(ws);
      
      // Send initial data
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'init',
          data: generateRandomData()
        }));
      }
      
      // Handle messages from analytics clients
      ws.on('message', (message) => {
        try {
          const parsedMessage = JSON.parse(message.toString());
          
          if (parsedMessage.type === 'get_data') {
            if (ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'data_update',
                data: generateRandomData()
              }));
            }
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });
      
      // Handle client disconnection
      ws.on('close', () => {
        console.log('Analytics client disconnected from WebSocket');
        analyticsClients.delete(ws);
      });
      
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        analyticsClients.delete(ws);
      });
    }
  });
  
  // Set up periodic data updates for analytics clients (every 30 seconds)
  setInterval(() => {
    const liveData = generateRandomData();
    
    analyticsClients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'data_update',
          data: liveData
        }));
      }
    });
  }, 30000);
  
  return httpServer;
}
