import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowDown, Wallet, Refresh<PERSON>w, Copy, ExternalLink } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Crypto options with logos
const cryptoOptions = [
  { value: "btc", label: "Bitcoin (BTC)", logo: "https://cryptologos.cc/logos/bitcoin-btc-logo.svg" },
  { value: "eth", label: "Ethereum (ETH)", logo: "https://cryptologos.cc/logos/ethereum-eth-logo.svg" },
  { value: "usdt", label: "Tether (USDT)", logo: "https://cryptologos.cc/logos/tether-usdt-logo.svg" },
  { value: "usdc", label: "USD Coin (USDC)", logo: "https://cryptologos.cc/logos/usd-coin-usdc-logo.svg" },
  { value: "xmr", label: "Monero (XMR)", logo: "https://cryptologos.cc/logos/monero-xmr-logo.svg" },
  { value: "sol", label: "Solana (SOL)", logo: "https://cryptologos.cc/logos/solana-sol-logo.svg" },
  { value: "bnb", label: "BNB (BNB)", logo: "https://cryptologos.cc/logos/bnb-bnb-logo.svg" },
  { value: "doge", label: "Dogecoin (DOGE)", logo: "https://cryptologos.cc/logos/dogecoin-doge-logo.svg" },
  { value: "ada", label: "Cardano (ADA)", logo: "https://cryptologos.cc/logos/cardano-ada-logo.svg" },
  { value: "dot", label: "Polkadot (DOT)", logo: "https://cryptologos.cc/logos/polkadot-new-dot-logo.svg" }
];

export default function SimpleSwapInterface() {
  const { toast } = useToast();
  const [fromCrypto, setFromCrypto] = useState("btc");
  const [toCrypto, setToCrypto] = useState("eth");
  const [amount, setAmount] = useState("");
  const [estimatedReceive, setEstimatedReceive] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [isCalculating, setIsCalculating] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [orderId, setOrderId] = useState("");
  
  // Get logo for a crypto symbol
  const getCryptoLogo = (symbol: string) => {
    const crypto = cryptoOptions.find(c => c.value === symbol);
    return crypto?.logo || "";
  };
  
  // Get name for a crypto symbol
  const getCryptoName = (symbol: string) => {
    const crypto = cryptoOptions.find(c => c.value === symbol);
    return crypto?.label || symbol.toUpperCase();
  };
  
  // Swap the from/to cryptocurrencies
  const handleSwap = () => {
    const temp = fromCrypto;
    setFromCrypto(toCrypto);
    setToCrypto(temp);
    setAmount("");
    setEstimatedReceive("");
  };
  
  // Calculate estimated received amount
  useEffect(() => {
    if (amount && parseFloat(amount) > 0) {
      setIsCalculating(true);
      
      // Simulate API call for estimation
      setTimeout(() => {
        // Simple estimation logic (in a real app, this would be from an API)
        let rate = 0;
        
        if (fromCrypto === "btc" && toCrypto === "eth") {
          rate = 45.84; // 1 BTC = 45.84 ETH (for demonstration only)
        } else if (fromCrypto === "eth" && toCrypto === "btc") {
          rate = 0.0218; // 1 ETH = 0.0218 BTC (for demonstration only)
        } else if (fromCrypto === "btc" && toCrypto === "usdt") {
          rate = 82000; // 1 BTC = $82,000 USDT (for demonstration only)
        } else if (fromCrypto === "eth" && toCrypto === "usdt") {
          rate = 1788.79; // 1 ETH = $1,788.79 USDT (for demonstration only)
        } else {
          // Default fallback rate (random)
          rate = 1.5 + Math.random() * 10;
        }
        
        const estimated = (parseFloat(amount) * rate).toFixed(6);
        setEstimatedReceive(estimated);
        setIsCalculating(false);
      }, 500);
    } else {
      setEstimatedReceive("");
    }
  }, [fromCrypto, toCrypto, amount]);
  
  // Handle review order
  const handleOrderReview = () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid amount to exchange.",
        variant: "destructive"
      });
      return;
    }
    
    if (!walletAddress) {
      toast({
        title: "Missing wallet address",
        description: `Please enter a valid ${getCryptoName(toCrypto)} wallet address.`,
        variant: "destructive"
      });
      return;
    }
    
    // Generate fake order ID
    const newOrderId = `order-${Math.random().toString(36).substring(2, 10)}`;
    setOrderId(newOrderId);
    setShowOrderModal(true);
  };
  
  // Handle order confirmation
  const handleOrderConfirm = () => {
    toast({
      title: "Order initiated",
      description: `Your swap order ${orderId} has been initiated. You will receive ${estimatedReceive} ${toCrypto.toUpperCase()} soon.`,
      variant: "default"
    });
    setShowOrderModal(false);
    
    // Reset form after successful order
    setAmount("");
    setEstimatedReceive("");
    setWalletAddress("");
  };
  
  return (
    <section className="relative">
      <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-[700px] mx-auto">
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] p-5 rounded-t-md">
            <div className="flex justify-between items-center">
              <div className="text-white">
                <h3 className="text-xl font-medium">Crypto-to-Crypto Swaps</h3>
                <p className="text-white/80 text-base">Fast, secure, and anonymous</p>
              </div>
              <div className="flex items-center space-x-4 text-white">
                {/* Swap icon with animation */}
                <motion.span 
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20"
                  whileHover={{ rotate: 180, transition: { duration: 0.5 } }}
                >
                  <motion.svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    xmlns="http://www.w3.org/2000/svg"
                    animate={{ rotate: [0, 360] }}
                    transition={{ repeat: Infinity, duration: 6, ease: "linear" }}
                  >
                    <path d="M7 10C7 10 9.5 7.5 12 7.5C14.5 7.5 17 10 17 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M17 14C17 14 14.5 16.5 12 16.5C9.5 16.5 7 14 7 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M19 7L16.5 9.5L14 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M5 17L7.5 14.5L10 17" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </motion.svg>
                </motion.span>
              </div>
            </div>
          </div>
          
          {/* Content */}
          <div className="bg-[#0F1B2D] p-6 rounded-b-md border-t border-[#1F2B47]">
            {/* Swap Form */}
            <div className="space-y-6">
              {/* From */}
              <div className="space-y-2">
                <Label className="text-white text-sm mb-2">You Send</Label>
                <div className="grid grid-cols-3 gap-3">
                  <div className="col-span-1">
                    <Select value={fromCrypto} onValueChange={setFromCrypto}>
                      <SelectTrigger className="w-full bg-[#1A2A47] border-[#2A3A57] text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#1A2A47] border-[#2A3A57] text-white">
                        <SelectGroup>
                          {cryptoOptions.map((crypto) => (
                            <SelectItem key={crypto.value} value={crypto.value} className="flex items-center">
                              <div className="flex items-center">
                                <img 
                                  src={crypto.logo} 
                                  alt={crypto.label}
                                  className="w-5 h-5 mr-2"
                                />
                                {crypto.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <img 
                          src={getCryptoLogo(fromCrypto)} 
                          alt={fromCrypto} 
                          className="w-5 h-5"
                        />
                      </div>
                      <Input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0.00"
                        className="bg-[#1A2A47] border-[#2A3A57] text-white pl-10 pr-24"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                        {fromCrypto.toUpperCase()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Swap direction button */}
              <div className="flex justify-center">
                <motion.button
                  onClick={handleSwap}
                  className="bg-[#1A2A47] h-8 w-8 rounded-full flex items-center justify-center border border-[#2A3A57]"
                  whileHover={{ rotate: 180, scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ArrowDown className="text-[#20c4cb] h-4 w-4" />
                </motion.button>
              </div>
              
              {/* To */}
              <div className="space-y-2">
                <Label className="text-white text-sm mb-2">You Receive</Label>
                <div className="grid grid-cols-3 gap-3">
                  <div className="col-span-1">
                    <Select value={toCrypto} onValueChange={setToCrypto}>
                      <SelectTrigger className="w-full bg-[#1A2A47] border-[#2A3A57] text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#1A2A47] border-[#2A3A57] text-white">
                        <SelectGroup>
                          {cryptoOptions.map((crypto) => (
                            <SelectItem key={crypto.value} value={crypto.value} className="flex items-center">
                              <div className="flex items-center">
                                <img 
                                  src={crypto.logo} 
                                  alt={crypto.label}
                                  className="w-5 h-5 mr-2"
                                />
                                {crypto.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <img 
                          src={getCryptoLogo(toCrypto)} 
                          alt={toCrypto} 
                          className="w-5 h-5"
                        />
                      </div>
                      <Input
                        readOnly
                        value={isCalculating ? "Calculating..." : estimatedReceive}
                        placeholder="0.00"
                        className="bg-[#1A2A47] border-[#2A3A57] text-white pl-10 pr-24"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                        {toCrypto.toUpperCase()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Wallet address */}
              <div className="space-y-2">
                <Label className="text-white text-sm mb-2">
                  {toCrypto.toUpperCase()} Recipient Address
                </Label>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <Wallet className="text-gray-400 h-4 w-4" />
                  </div>
                  <Input
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value)}
                    placeholder={`Enter ${toCrypto.toUpperCase()} wallet address`}
                    className="bg-[#1A2A47] border-[#2A3A57] text-white pl-10"
                  />
                </div>
                <p className="text-xs text-gray-400">
                  Make sure this is a valid {getCryptoName(toCrypto)} address
                </p>
              </div>
              
              {/* Exchange button */}
              <motion.button
                onClick={handleOrderReview}
                className="w-full py-3 rounded-md bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] text-white font-medium"
                style={{
                  boxShadow: '0 0 15px rgba(32,196,203,0.4)'
                }}
                whileHover={{ 
                  scale: 1.02,
                  boxShadow: '0 0 20px rgba(32,196,203,0.6)' 
                }}
                whileTap={{ scale: 0.98 }}
                disabled={isCalculating || !amount || !walletAddress}
              >
                Review Swap
              </motion.button>
              
              {/* Additional information */}
              <div className="mt-6 space-y-3">
                {/* Rate and fee info */}
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400">Rate</span>
                  <span className="text-white">
                    {isCalculating ? (
                      <RefreshCw className="h-3 w-3 inline animate-spin mr-1" />
                    ) : (
                      amount && estimatedReceive ? 
                      `1 ${fromCrypto.toUpperCase()} ≈ ${(parseFloat(estimatedReceive) / parseFloat(amount)).toFixed(6)} ${toCrypto.toUpperCase()}` :
                      `--`
                    )}
                  </span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400">Network Fee</span>
                  <span className="text-white">Included</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400">ETA</span>
                  <span className="text-white">10-30 minutes</span>
                </div>
              </div>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 text-center mt-8">
              {[
                { title: "40+", subtitle: "Supported Coins" },
                { title: "No KYC", subtitle: "Anonymous" },
                { title: "Fast", subtitle: "Settlements" },
                { title: "Secure", subtitle: "Transactions" }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-[#4287fb] text-base font-medium">{item.title}</div>
                  <div className="text-[#647288] text-xs sm:text-sm">{item.subtitle}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Order review modal */}
      <Dialog open={showOrderModal} onOpenChange={setShowOrderModal}>
        <DialogContent className="bg-[#0F1B2D] text-white border-[#2A3A57]">
          <DialogHeader>
            <DialogTitle className="text-xl font-medium">Confirm Swap Order</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="bg-[#1A2A47] p-4 rounded-md border border-[#2A3A57]">
              <div className="flex justify-between items-center">
                <div className="space-y-1">
                  <span className="text-gray-400 text-xs">You Send</span>
                  <div className="flex items-center">
                    <img 
                      src={getCryptoLogo(fromCrypto)} 
                      alt={fromCrypto} 
                      className="w-5 h-5 mr-2"
                    />
                    <span className="font-medium">{amount} {fromCrypto.toUpperCase()}</span>
                  </div>
                </div>
                
                <ArrowDown className="text-gray-500 h-5 w-5 mx-4" />
                
                <div className="space-y-1 text-right">
                  <span className="text-gray-400 text-xs">You Receive</span>
                  <div className="flex items-center justify-end">
                    <img 
                      src={getCryptoLogo(toCrypto)} 
                      alt={toCrypto} 
                      className="w-5 h-5 mr-2"
                    />
                    <span className="font-medium">{estimatedReceive} {toCrypto.toUpperCase()}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-400 text-xs">Recipient Address</Label>
              <div className="flex items-center justify-between bg-[#1A2A47] p-2 rounded-md border border-[#2A3A57]">
                <div className="truncate text-sm flex-1">
                  {walletAddress}
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button 
                        onClick={() => {
                          navigator.clipboard.writeText(walletAddress);
                          toast({ title: "Address copied", variant: "default" });
                        }}
                        className="ml-2 p-1 hover:bg-[#2A3A57] rounded"
                      >
                        <Copy className="h-4 w-4 text-gray-400" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy address</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-400 text-xs">Order ID</Label>
              <div className="bg-[#1A2A47] p-2 rounded-md border border-[#2A3A57] text-sm">
                {orderId}
              </div>
            </div>
            
            <div className="pt-2">
              <p className="text-xs text-gray-400">
                By confirming this order, you agree to our terms of service and privacy policy.
                This transaction is irreversible once processed.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowOrderModal(false)}
              className="border-[#2A3A57] text-white hover:bg-[#1A2A47] hover:text-white"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleOrderConfirm}
              className="bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] text-white hover:opacity-90"
            >
              Confirm Order
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </section>
  );
}