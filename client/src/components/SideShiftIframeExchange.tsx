import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, ExternalLink, Loader2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

export default function SideShiftIframeExchange() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [iframeError, setIframeError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeHeight, setIframeHeight] = useState(580);
  
  useEffect(() => {
    // Set appropriate height based on viewport
    if (window.innerWidth < 768) {
      setIframeHeight(650); // Taller on mobile
    }
    
    // Handle window resize
    const handleResize = () => {
      setIframeHeight(window.innerWidth < 768 ? 650 : 580);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Handle iframe load events
  const handleIframeLoad = () => {
    console.log('SideShift iframe loaded successfully');
    setIsLoading(false);
    setIframeLoaded(true);
  };
  
  const handleIframeError = () => {
    console.error('Error loading SideShift iframe');
    setIsLoading(false);
    setIframeError(true);
    toast({
      title: "Connection error",
      description: "Failed to load SideShift AI exchange. Please try again later.",
      variant: "destructive"
    });
  };
  
  // Reload iframe
  const reloadIframe = () => {
    setIsLoading(true);
    setIframeError(false);
    setIframeLoaded(false);
    
    // Force iframe refresh by updating ref source
    if (iframeRef.current) {
      const currentSrc = iframeRef.current.src;
      iframeRef.current.src = '';
      
      // Small delay before setting source back
      setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.src = currentSrc;
        }
      }, 100);
    }
  };
  
  // Direct link to SideShift as fallback
  const openSideShiftExternal = () => {
    window.open('https://sideshift.ai/a/Tu1KaCKsqE', '_blank');
  };

  return (
    <section className="relative">
      <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-[700px] mx-auto">
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] p-5 rounded-t-md">
            <div className="flex justify-between items-center">
              <div className="text-white">
                <h3 className="text-xl font-medium">Crypto-to-Crypto Swaps</h3>
                <p className="text-white/80 text-base">Fast, secure, and anonymous</p>
              </div>
              <div className="flex items-center space-x-4 text-white">
                {/* Swap icon with animation */}
                <motion.span 
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20"
                  whileHover={{ rotate: 180, transition: { duration: 0.5 } }}
                >
                  <motion.svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    xmlns="http://www.w3.org/2000/svg"
                    animate={{ rotate: [0, 360] }}
                    transition={{ repeat: Infinity, duration: 6, ease: "linear" }}
                  >
                    <path d="M7 10C7 10 9.5 7.5 12 7.5C14.5 7.5 17 10 17 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M17 14C17 14 14.5 16.5 12 16.5C9.5 16.5 7 14 7 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M19 7L16.5 9.5L14 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M5 17L7.5 14.5L10 17" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </motion.svg>
                </motion.span>
              </div>
            </div>
          </div>
          
          {/* Content */}
          <div className="bg-[#0F1B2D] p-4 sm:p-8 rounded-b-md border-t border-[#1F2B47]">
            {/* Introduction text */}
            <p className="text-gray-400 text-xs sm:text-sm text-center mb-6 sm:mb-8 px-2">
              Use SideShift AI to instantly swap between cryptocurrencies.
              No KYC required, secure and private transactions.
            </p>
            
            {/* Loading state */}
            {isLoading && (
              <div className="flex justify-center items-center" style={{ height: `${iframeHeight}px` }}>
                <div className="text-center">
                  <Loader2 className="w-10 h-10 animate-spin mx-auto mb-4 text-[#20c4cb]" />
                  <p className="text-gray-400">Loading SideShift exchange...</p>
                </div>
              </div>
            )}
            
            {/* Error state */}
            {iframeError && (
              <div className="flex flex-col justify-center items-center" style={{ height: `${iframeHeight}px` }}>
                <div className="text-center">
                  <AlertCircle className="w-12 h-12 text-orange-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">Connection Error</h3>
                  <p className="text-gray-400 mb-6">Unable to load the SideShift exchange interface.</p>
                  
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <motion.button 
                      onClick={reloadIframe}
                      className="bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] text-white px-5 py-2.5 rounded flex items-center justify-center"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Loader2 className="w-4 h-4 mr-2" />
                      Try Again
                    </motion.button>
                    
                    <motion.button 
                      onClick={openSideShiftExternal}
                      className="bg-gray-700 text-white px-5 py-2.5 rounded flex items-center justify-center"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open in New Tab
                    </motion.button>
                  </div>
                </div>
              </div>
            )}
            
            {/* Iframe container */}
            <div 
              className={`overflow-hidden rounded-md transition-opacity duration-300 ${isLoading ? 'opacity-0 h-0' : 'opacity-100'} ${iframeError ? 'hidden' : ''}`}
            >
              <iframe
                ref={iframeRef}
                src="https://sideshift.ai/a/Tu1KaCKsqE"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                style={{ 
                  width: '100%', 
                  height: `${iframeHeight}px`,
                  border: 'none',
                  borderRadius: '8px',
                  backgroundColor: '#0F1B2D'
                }}
                allow="clipboard-write"
                title="SideShift AI Exchange"
              />
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 text-center mt-6">
              {[
                { title: "40+", subtitle: "Supported Coins" },
                { title: "No KYC", subtitle: "Anonymous" },
                { title: "Fast", subtitle: "Settlements" },
                { title: "Secure", subtitle: "Transactions" }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-[#4287fb] text-base font-medium">{item.title}</div>
                  <div className="text-[#647288] text-xs sm:text-sm">{item.subtitle}</div>
                </div>
              ))}
            </div>
            
            {/* Optional: Direct link */}
            <div className="mt-4 text-center">
              <button 
                onClick={openSideShiftExternal}
                className="text-xs text-[#20c4cb] hover:text-[#9d55ff] transition-colors flex items-center mx-auto"
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Open in new tab if having issues
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}