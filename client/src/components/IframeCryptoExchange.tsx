import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, Maximize2, Minimize2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export default function IframeCryptoExchange() {
  const { t } = useTranslation();
  const [fullscreen, setFullscreen] = useState(false);

  // Direct iframe integration with SideShift - much more reliable than script loading
  return (
    <section className="relative">
      <div className="container mx-auto px-4 py-8 sm:px-6 lg:px-8 relative z-10">
        <div className={`mx-auto ${fullscreen ? 'max-w-full' : 'max-w-[900px]'}`}>
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] p-4 md:p-5 rounded-t-md">
            <div className="flex justify-between items-center">
              <div className="text-white">
                <h3 className="text-lg md:text-xl font-medium">Crypto-to-Crypto Exchange</h3>
                <p className="text-white/80 text-sm md:text-base">Fast, secure, and anonymous</p>
              </div>
              <div className="flex items-center space-x-2 md:space-x-4 text-white">
                {/* Toggle fullscreen */}
                <button 
                  onClick={() => setFullscreen(!fullscreen)}
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  {fullscreen ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
                </button>
                
                {/* External link to SideShift */}
                <a 
                  href="https://sideshift.ai" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  <ExternalLink size={18} />
                </a>
              </div>
            </div>
          </div>
          
          {/* Iframe container with responsive height */}
          <div className="bg-[#0F1B2D] rounded-b-md border-t border-[#1F2B47] overflow-hidden">
            <div className={`w-full ${fullscreen ? 'h-[80vh]' : 'h-[600px]'} relative`}>
              {/* SideShift iframe - direct integration */}
              <iframe
                src="https://sideshift.ai/btc/eth"
                title="SideShift Exchange"
                className="w-full h-full border-0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 p-4 md:p-6 text-center border-t border-[#1F2B47]">
              {[
                { title: "40+", subtitle: "Supported Coins" },
                { title: "No KYC", subtitle: "Anonymous" },
                { title: "Fast", subtitle: "Settlements" },
                { title: "Secure", subtitle: "Transactions" }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-[#4287fb] text-base font-medium">{item.title}</div>
                  <div className="text-[#647288] text-xs sm:text-sm">{item.subtitle}</div>
                </div>
              ))}
            </div>
            
            {/* Alternative exchanges */}
            <div className="p-4 text-center border-t border-[#1F2B47]">
              <p className="text-xs text-gray-400 mb-3">Alternative exchanges:</p>
              <div className="flex flex-wrap justify-center gap-2">
                {[
                  { name: "ChangeNOW", url: "https://changenow.io/?from=btc&to=eth&amount=0.1&fromNetwork=btc&toNetwork=eth" },
                  { name: "Changelly", url: "https://changelly.com/exchange/btc/eth" },
                  { name: "SimpleSwap", url: "https://simpleswap.io/coins/bitcoin/ethereum" }
                ].map((exchange, idx) => (
                  <a 
                    key={idx}
                    href={exchange.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="py-1.5 px-3 text-xs bg-[rgba(32,196,203,0.1)] text-[#20c4cb] border border-[rgba(32,196,203,0.3)] rounded-full 
                              hover:bg-[rgba(32,196,203,0.2)] transition-colors duration-200"
                  >
                    {exchange.name}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}