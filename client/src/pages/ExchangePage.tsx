import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/Header';
import HoudiniSwapWidget from '../components/HoudiniSwapWidget';
import Footer from '../components/Footer';
import SnowEffect from '../components/SnowEffect';
import HeroLightSection from '../components/HeroLightSection';
import { motion } from 'framer-motion';
import { Ghost, ShieldAlert } from 'lucide-react';
// Import the Marco Polo image using the @assets alias
import marcoPoloImage from '@assets/Enrico_Podio_-_<PERSON>_<PERSON>_1867_-_(MeisterDrucke-1185437).jpg';

export default function ExchangePage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white text-gray-800 relative">
      {/* <PERSON> portrait as a fixed position image */}
      <img 
        src={marcoPoloImage} 
        alt="Marco Polo Portrait"
        className="absolute top-20 left-1/2 transform -translate-x-1/2 opacity-25 pointer-events-none"
        style={{
          maxWidth: '380px',
          maxHeight: '50vh',
          zIndex: 0
        }}
      />
      <Header />
      <main className="pt-20 relative z-10">
        <div className="w-full mx-auto px-1 sm:px-4 py-10 sm:py-16 sm:max-w-[900px] max-w-[98%]">
          <div className="text-center mb-8">
            <motion.h1
              className="text-5xl md:text-6xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-purple-600"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="flex items-center justify-center gap-3">
                <Ghost className="w-12 h-12 text-[#20c4cb] animate-pulse" />
                Exchange
                <ShieldAlert className="w-8 h-8 text-purple-400" />
              </span>
            </motion.h1>
            <motion.p
              className="text-lg text-gray-600 max-w-3xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {t('exchange.subtitle', 'Privacy-focused cryptocurrency exchange with maximum anonymity')}
            </motion.p>
          </div>

          <div className="w-full mx-auto px-0">
            <HeroLightSection />
            <div className="pt-3">
              <div className="w-full mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-8">
                  <div className="lg:col-span-3 bg-black bg-opacity-90 rounded-lg p-2 sm:p-6 shadow-lg">
                    <HoudiniSwapWidget />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
