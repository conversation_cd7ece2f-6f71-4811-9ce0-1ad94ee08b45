import React from 'react';
import { useTranslation } from 'react-i18next';
import HoudiniSwapWidget from '@/components/HoudiniSwapWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { LockIcon, Eye, Shield, Zap, Key } from 'lucide-react';

const SwapPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-4 py-8 relative">
      {/* Background elements for futuristic effect */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0 opacity-20 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-[rgba(32,196,203,0.2)] blur-3xl"></div>
        <div className="absolute bottom-40 right-10 w-48 h-48 rounded-full bg-[rgba(157,85,255,0.15)] blur-3xl"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-[rgba(157,85,255,0.1)] blur-xl"></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex flex-col items-center mb-12">
          <div className="flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-[rgba(32,196,203,0.2)] to-[rgba(157,85,255,0.2)] backdrop-blur-sm mb-4 border border-[rgba(32,196,203,0.3)]">
            <span className="text-3xl">👻</span>
          </div>
          <h1 className="text-4xl font-bold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-[#9d55ff]">
            {t('swap.pageTitle', 'Ghost Swap')}
          </h1>
          <div className="h-0.5 w-24 bg-gradient-to-r from-[rgba(32,196,203,0.5)] to-[rgba(157,85,255,0.5)] rounded-full mb-4"></div>
          <p className="text-lg text-center mb-6 max-w-2xl text-gray-300">
            {t('swap.pageDescription', 'The future of untraceable cryptocurrency exchanges. Swap your digital assets with complete privacy and zero footprint.')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <HoudiniSwapWidget />
          </div>

          <div className="space-y-6">
            <Card className="border-0 shadow-[0_0_15px_rgba(32,196,203,0.15)] bg-gradient-to-br from-gray-900 to-[#0e1b38] text-white overflow-hidden relative">
              <div className="absolute top-0 right-0 w-32 h-32 bg-[rgba(32,196,203,0.03)] rounded-full translate-x-16 -translate-y-16"></div>
              <CardHeader className="border-b border-[rgba(32,196,203,0.2)]">
                <CardTitle className="text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] flex items-center gap-2">
                  <Zap className="h-4 w-4 text-[#20c4cb]" />
                  {t('swap.howItWorks', 'The Ghost Protocol')}
                </CardTitle>
                <CardDescription className="text-gray-400">
                  {t('swap.howItWorksDescription', 'Advanced privacy-focused swapping in 5 simple steps')}
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <ol className="space-y-3 pt-2 counter-reset-step">
                  {[
                    t('swap.step1', 'Select the cryptocurrencies you want to ghost swap'),
                    t('swap.step2', 'Enter the amount and your receiving wallet address'),
                    t('swap.step3', 'Activate stealth mode for maximum privacy (optional)'),
                    t('swap.step4', 'Confirm and initiate your untraceable transaction'),
                    t('swap.step5', 'Receive your assets through encrypted secure channels')
                  ].map((step, i) => (
                    <li key={i} className="flex items-start gap-3 text-sm">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-[rgba(32,196,203,0.1)] border border-[rgba(32,196,203,0.3)] flex items-center justify-center text-xs font-mono text-[#20c4cb]">
                        {i+1}
                      </div>
                      <div className="text-gray-300 pt-0.5">{step}</div>
                    </li>
                  ))}
                </ol>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-[0_0_15px_rgba(32,196,203,0.15)] bg-gradient-to-br from-gray-900 to-[#0e1b38] text-white overflow-hidden relative">
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-[rgba(157,85,255,0.03)] rounded-full -translate-x-12 translate-y-12"></div>
              <CardHeader className="border-b border-[rgba(32,196,203,0.2)]">
                <CardTitle className="text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] flex items-center gap-2">
                  <Shield className="h-4 w-4 text-[#9d55ff]" />
                  {t('swap.benefits', 'Ghost Advantages')}
                </CardTitle>
                <CardDescription className="text-gray-400">
                  {t('swap.benefitsDescription', 'Cutting-edge features of our privacy technology')}
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="grid grid-cols-1 gap-3 pt-2">
                  {[
                    {
                      title: t('swap.benefit1', 'Zero Knowledge Privacy'),
                      description: 'Complete anonymity with cryptographic zero-knowledge proofs',
                      icon: <Eye className="h-4 w-4 text-[#20c4cb]" />
                    },
                    {
                      title: t('swap.benefit2', 'Quantum-Resistant Encryption'),
                      description: 'Future-proof technology secure against quantum computing attacks',
                      icon: <Shield className="h-4 w-4 text-[#20c4cb]" />
                    },
                    {
                      title: t('swap.benefit3', 'Chain-Agnostic Protocol'),
                      description: 'Seamlessly swap between any blockchains with our cross-chain technology',
                      icon: <Zap className="h-4 w-4 text-[#20c4cb]" />
                    },
                    {
                      title: t('swap.benefit4', 'Decentralized Routing'),
                      description: 'No central point of failure with our distributed network architecture',
                      icon: <Key className="h-4 w-4 text-[#20c4cb]" />
                    },
                    {
                      title: t('swap.benefit5', 'Self-Destructing Transactions'),
                      description: 'Transaction records automatically vanish after completion',
                      icon: <LockIcon className="h-4 w-4 text-[#20c4cb]" />
                    }
                  ].map((benefit, i) => (
                    <div key={i} className="flex items-start gap-3">
                      <div className="flex-shrink-0 h-8 w-8 rounded-md bg-[rgba(32,196,203,0.1)] border border-[rgba(32,196,203,0.3)] flex items-center justify-center">
                        {benefit.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{benefit.title}</h4>
                        <p className="text-xs text-gray-400 mt-0.5">{benefit.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-[0_0_15px_rgba(32,196,203,0.15)] bg-gradient-to-br from-gray-900 to-[#0e1b38] text-white">
              <CardHeader className="border-b border-[rgba(32,196,203,0.2)]">
                <CardTitle className="text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-[#9d55ff]">
                  {t('swap.faq', 'Ghost Protocol FAQ')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="general" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 bg-[rgba(32,196,203,0.05)] backdrop-blur-sm border border-[rgba(32,196,203,0.2)] rounded-lg p-1">
                    <TabsTrigger 
                      value="general" 
                      className="data-[state=active]:bg-[rgba(32,196,203,0.15)] data-[state=active]:text-[#20c4cb] text-gray-400 rounded-md transition-all duration-300"
                    >
                      {t('swap.general', 'General')}
                    </TabsTrigger>
                    <TabsTrigger 
                      value="technical"
                      className="data-[state=active]:bg-[rgba(32,196,203,0.15)] data-[state=active]:text-[#20c4cb] text-gray-400 rounded-md transition-all duration-300"
                    >
                      {t('swap.technical', 'Technical')}
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="general" className="space-y-4 pt-4">
                    <div className="border border-[rgba(32,196,203,0.2)] rounded-md p-3 bg-[rgba(32,196,203,0.03)]">
                      <h4 className="font-medium text-[#20c4cb] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#20c4cb] rounded-full animate-pulse"></div>
                        {t('swap.faq1', 'What is Ghost Swap?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq1Answer', 'Ghost Swap is a next-generation privacy-focused exchange protocol that allows you to swap cryptocurrencies with complete anonymity. Your transactions leave no trace and cannot be linked to your identity.')}
                      </p>
                    </div>
                    <div className="border border-[rgba(32,196,203,0.2)] rounded-md p-3 bg-[rgba(32,196,203,0.03)]">
                      <h4 className="font-medium text-[#20c4cb] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#20c4cb] rounded-full animate-pulse"></div>
                        {t('swap.faq2', 'How long do ghost swaps take?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq2Answer', 'Most ghost swaps complete within 5-15 minutes. Our advanced routing technology finds the fastest and most secure paths for your transaction, ensuring minimal wait times.')}
                      </p>
                    </div>
                    <div className="border border-[rgba(32,196,203,0.2)] rounded-md p-3 bg-[rgba(32,196,203,0.03)]">
                      <h4 className="font-medium text-[#20c4cb] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#20c4cb] rounded-full animate-pulse"></div>
                        {t('swap.faq3', 'Is Ghost Swap truly untraceable?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq3Answer', 'Absolutely. Ghost Swap employs advanced cryptographic techniques including zero-knowledge proofs, multi-layered routing, and self-destructing transaction records to ensure complete privacy and untraceability.')}
                      </p>
                    </div>
                  </TabsContent>
                  <TabsContent value="technical" className="space-y-4 pt-4">
                    <div className="border border-[rgba(157,85,255,0.2)] rounded-md p-3 bg-[rgba(157,85,255,0.03)]">
                      <h4 className="font-medium text-[#9d55ff] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#9d55ff] rounded-full animate-pulse"></div>
                        {t('swap.faq4', 'How does the privacy technology work?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq4Answer', 'Ghost Swap uses a multi-layered approach combining encrypted relays, blockchain mixers, and zero-knowledge cryptography. Your transaction is routed through multiple decentralized nodes, breaking the chain of custody and ensuring no single entity can track the full path.')}
                      </p>
                    </div>
                    <div className="border border-[rgba(157,85,255,0.2)] rounded-md p-3 bg-[rgba(157,85,255,0.03)]">
                      <h4 className="font-medium text-[#9d55ff] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#9d55ff] rounded-full animate-pulse"></div>
                        {t('swap.faq5', 'What happens if a swap fails?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq5Answer', 'Ghost Swap incorporates an advanced fail-safe system. If a swap fails at any point, your assets are automatically returned to a temporary secure address, from which you can reclaim them with your private key.')}
                      </p>
                    </div>
                    <div className="border border-[rgba(157,85,255,0.2)] rounded-md p-3 bg-[rgba(157,85,255,0.03)]">
                      <h4 className="font-medium text-[#9d55ff] flex items-center gap-2">
                        <div className="h-1 w-1 bg-[#9d55ff] rounded-full animate-pulse"></div>
                        {t('swap.faq6', 'Which wallets are supported?')}
                      </h4>
                      <p className="text-sm text-gray-300 mt-2">
                        {t('swap.faq6Answer', 'Ghost Swap supports all standard cryptocurrency wallets with additional enhanced compatibility for privacy-focused wallets. Any wallet address can be used as a destination for receiving your swapped assets.')}
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SwapPage;