import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  ArrowRight,
  Bot,
  PlusSquare,
  TrendingUp,
  BarChart3,
  Clock,
  Zap,
  Star,
  Users,
  Shield,
  Clock3,
  AlertCircle,
  ChevronRight,
  Bookmark,
  Cpu,
  ArrowUpRight,
  DollarSign,
  Copy,
  X,
  Check,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';

// Types
interface TradingBot {
  id: string;
  name: string;
  description: string;
  strategy: string;
  monthlyFee: number;
  yearlyFee?: number;
  logo: JSX.Element;
  createdBy: string;
  rating: number;
  reviewCount: number;
  subscribers: number;
  monthlyReturn: number;
  tradingVolume: string;
  winRate: number;
  tags: string[];
  riskLevel: 'Low' | 'Medium' | 'High';
  features: string[];
  category: 'trend' | 'arbitrage' | 'market-making' | 'portfolio' | 'defi' | 'meme';
  tradingPairs: string[];
  timeFrame: string;
}

export default function TradingBotMarketplace() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [selectedBot, setSelectedBot] = useState<TradingBot | null>(null);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [showSolanaPayment, setShowSolanaPayment] = useState(false);
  const [transactionId, setTransactionId] = useState('');
  const [telegramUsername, setTelegramUsername] = useState('');
  const [solanaWalletCopied, setSolanaWalletCopied] = useState(false);
  
  const SOLANA_WALLET_ADDRESS = '4av41YfRcsKPY4zKxoE9vC5ZofRLvHE7dS9CUKzTHtwj';

  // Sample data for trading bots
  const tradingBots: TradingBot[] = [
    {
      id: 'solana-meme-scout',
      name: 'Solana Meme Scout',
      description: 'Specialized AI trading bot for identifying and capitalizing on emerging Solana meme coins with high viral potential before mainstream discovery.',
      strategy: 'Uses sentiment analysis, social media monitoring, and on-chain metrics to identify promising Solana meme coins early in their lifecycle with minimal risk exposure.',
      monthlyFee: 40,
      yearlyFee: 400,
      logo: <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 p-1.5 bg-gradient-to-br from-purple-500 to-purple-700 text-white rounded-md" viewBox="0 0 397.7 311.7" fill="currentColor">
              <path d="M64.6,237.9c2.4-2.4,5.7-3.8,9.2-3.8h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,237.9z"/>
              <path d="M64.6,3.8C67.1,1.4,70.4,0,73.8,0h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,3.8z"/>
              <path d="M333.1,120.1c-2.4-2.4-5.7-3.8-9.2-3.8H6.5c-5.8,0-8.7,7-4.6,11.1l62.7,62.7c2.4,2.4,5.7,3.8,9.2,3.8h317.4c5.8,0,8.7-7,4.6-11.1L333.1,120.1z"/>
            </svg>,
      createdBy: 'Orca Capital Meme Division',
      rating: 4.9,
      reviewCount: 136,
      subscribers: 967,
      monthlyReturn: 14.2,
      tradingVolume: '$21.5M',
      winRate: 73,
      tags: ['Meme Coins', 'Solana', 'Viral Detection'],
      riskLevel: 'High',
      features: ['Social media trend detection', 'Liquidity analysis', 'Automated entry/exit', 'Stop-loss management'],
      category: 'meme',
      tradingPairs: ['BONK/SOL', 'BOME/SOL', 'WIF/SOL', 'Emerging SPL tokens'],
      timeFrame: '5m - 1d'
    },
    {
      id: 'eliza-momentum',
      name: 'ELIZA Momentum',
      description: 'Advanced trend-following AI that identifies momentum shifts across multiple timeframes with sentiment analysis integration.',
      strategy: 'Uses proprietary algorithms to detect market momentum shifts before they occur, combining technical indicators with AI-driven social sentiment analysis.',
      monthlyFee: 4000,
      yearlyFee: 40000,
      logo: <TrendingUp className="h-8 w-8 p-1.5 bg-gradient-to-br from-blue-500 to-blue-700 text-white rounded-md" />,
      createdBy: 'Orca Capital Core Team',
      rating: 4.7,
      reviewCount: 128,
      subscribers: 842,
      monthlyReturn: 8.4,
      tradingVolume: '$14.2M',
      winRate: 68,
      tags: ['Trend Following', 'AI-Enhanced', 'Multi-Timeframe'],
      riskLevel: 'Medium',
      features: ['AI-driven entry/exit points', 'Real-time sentiment analysis', 'Custom risk parameters', 'Automatic position sizing'],
      category: 'trend',
      tradingPairs: ['BTC/USD', 'ETH/USD', 'SOL/USD', 'BNB/USD'],
      timeFrame: '1h - 1d'
    },
    {
      id: 'quantum-arbitrage',
      name: 'Quantum Arbitrage',
      description: 'Multi-exchange arbitrage bot that capitalizes on price differentials across major exchanges with millisecond execution.',
      strategy: 'Scans 15+ exchanges simultaneously to identify and execute on price discrepancies, accounting for fees and slippage with custom ML models.',
      monthlyFee: 6000,
      yearlyFee: 60000,
      logo: <Zap className="h-8 w-8 p-1.5 bg-gradient-to-br from-purple-500 to-indigo-700 text-white rounded-md" />,
      createdBy: 'QuantEdge Labs',
      rating: 4.5,
      reviewCount: 84,
      subscribers: 612,
      monthlyReturn: 7.2,
      tradingVolume: '$28.7M',
      winRate: 91,
      tags: ['Arbitrage', 'High-Frequency', 'Multi-Exchange'],
      riskLevel: 'Low',
      features: ['Sub-second execution', 'Automatic fee optimization', 'Slippage prediction', 'Risk distribution'],
      category: 'arbitrage',
      tradingPairs: ['BTC/USDT', 'ETH/USDT', 'USDC/USDT', 'XRP/USDT'],
      timeFrame: '1s - 1m'
    },
    {
      id: 'defi-yield-hunter',
      name: 'DeFi Yield Hunter',
      description: 'Autonomous yield farming bot that rotates capital between DeFi protocols to maximize returns while minimizing impermanent loss.',
      strategy: 'Continuously analyzes yield opportunities across lending platforms, liquidity pools, and staking protocols to maximize APY while managing risk.',
      monthlyFee: 4000,
      yearlyFee: 40000,
      logo: <Cpu className="h-8 w-8 p-1.5 bg-gradient-to-br from-green-500 to-emerald-700 text-white rounded-md" />,
      createdBy: 'DeFi Maximalist Collective',
      rating: 4.3,
      reviewCount: 73,
      subscribers: 518,
      monthlyReturn: 12.6,
      tradingVolume: '$8.1M',
      winRate: 76,
      tags: ['Yield Farming', 'Auto-Compounding', 'Cross-Chain'],
      riskLevel: 'Medium',
      features: ['Impermanent loss protection', 'Gas optimization', 'Protocol risk assessment', 'Smart rebalancing'],
      category: 'defi',
      tradingPairs: ['ETH/USDC LP', 'wBTC/ETH LP', 'stETH', 'AAVE'],
      timeFrame: '6h - 7d'
    },
    {
      id: 'alpha-portfolios',
      name: 'Alpha Portfolios',
      description: 'AI-driven portfolio management bot that rebalances assets based on market conditions and risk parameters.',
      strategy: 'Uses machine learning to dynamically allocate assets across cryptocurrencies, stablecoins, and DeFi protocols based on market phases and risk preferences.',
      monthlyFee: 4000,
      yearlyFee: 40000,
      logo: <BarChart3 className="h-8 w-8 p-1.5 bg-gradient-to-br from-orange-500 to-red-700 text-white rounded-md" />,
      createdBy: 'Quantum Finance AI',
      rating: 4.6,
      reviewCount: 109,
      subscribers: 742,
      monthlyReturn: 6.8,
      tradingVolume: '$31.5M',
      winRate: 82,
      tags: ['Portfolio Management', 'Adaptive Allocation', 'Macro Strategy'],
      riskLevel: 'Medium',
      features: ['Volatility targeting', 'Drawdown management', 'Sector rotation', 'Correlation analysis'],
      category: 'portfolio',
      tradingPairs: ['BTC', 'ETH', 'SOL', 'DOT', 'MATIC', 'USDC'],
      timeFrame: '1d - 30d'
    },
    {
      id: 'market-maker-pro',
      name: 'Market Maker Pro',
      description: 'Professional market making bot that provides liquidity while capturing bid-ask spreads across major pairs.',
      strategy: 'Deploys sophisticated order book strategies to provide liquidity and capture spreads, with dynamic order sizing and placement based on volatility and volume.',
      monthlyFee: 10000,
      yearlyFee: 100000,
      logo: <Clock className="h-8 w-8 p-1.5 bg-gradient-to-br from-blue-400 to-cyan-700 text-white rounded-md" />,
      createdBy: 'Liquidity Labs',
      rating: 4.8,
      reviewCount: 56,
      subscribers: 289,
      monthlyReturn: 5.9,
      tradingVolume: '$57.2M',
      winRate: 89,
      tags: ['Market Making', 'Spread Capture', 'High Volume'],
      riskLevel: 'Low',
      features: ['Dynamic bid-ask management', 'Volatility-based positioning', 'Inventory management', 'Anti-frontrunning protection'],
      category: 'market-making',
      tradingPairs: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'AVAX/USDT'],
      timeFrame: '1s - 5m'
    }
  ];

  const handleSubscribe = (bot: TradingBot) => {
    // Redirect to the Solana payment flow
    handleSolanaPayment(bot);
  };
  
  const handleSolanaPayment = (bot: TradingBot) => {
    setSelectedBot(bot);
    setShowSolanaPayment(true);
  };
  
  const handleCopyWalletAddress = () => {
    navigator.clipboard.writeText(SOLANA_WALLET_ADDRESS);
    setSolanaWalletCopied(true);
    setTimeout(() => setSolanaWalletCopied(false), 3000);
  };
  
  const handleSolanaPaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!transactionId || !telegramUsername) {
      toast({
        title: "Missing Information",
        description: "Please provide both the transaction ID and your Telegram username.",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Send payment data to server
      const response = await fetch('/api/payment-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fullName: "Anonymous",
          email: "<EMAIL>",
          phone: '',
          telegramUsername: telegramUsername,
          botName: selectedBot?.name || 'Trading Bot',
          paymentMethod: 'solana',
          paymentPlan: 'one-time',
          amount: selectedBot?.id === 'market-maker-pro' ? 1000000 : 
                 selectedBot?.id === 'quantum-arbitrage' ? 600000 : 400000, // $10,000, $6,000 or $4,000 in cents - one-time purchase
          currency: 'USD',
          transactionId: transactionId,
          status: 'pending',
          category: 'trading-bot',
          notes: "Solana payment submission - Anonymous user with Telegram contact only"
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to submit payment information');
      }
      
      toast({
        title: "Payment Verification Submitted",
        description: "We will verify your transaction and provide access to your trading bot shortly. We'll contact you via Telegram with further instructions.",
      });
      
      // Reset form and close dialog
      setTransactionId('');
      setTelegramUsername('');
      setShowSolanaPayment(false);
    } catch (error) {
      console.error('Error submitting payment:', error);
      toast({
        title: "Submission Error",
        description: "There was a problem submitting your payment information. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white flex flex-col">
      <Header />
      <div className="container mx-auto px-4 py-24 md:py-28">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#151D44] mb-4">
            <span className="whitespace-nowrap">ELIZA OS</span> Trading Bot Marketplace
          </h1>
          <p className="text-gray-600 max-w-3xl mx-auto mb-8 text-lg">
            Access a curated selection of AI-powered trading bots built on the <span className="whitespace-nowrap">Orca Capital</span> ELIZA OS framework. All bots come with proven track records and professional support.
          </p>

          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full">
              <Shield className="h-5 w-5 text-blue-600" />
              <span className="text-blue-800 text-sm font-medium">Vetted Strategies</span>
            </div>
            <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
              <Users className="h-5 w-5 text-green-600" />
              <span className="text-green-800 text-sm font-medium">2,500+ Active Users</span>
            </div>
            <div className="flex items-center space-x-2 bg-purple-50 px-4 py-2 rounded-full">
              <Cpu className="h-5 w-5 text-purple-600" />
              <span className="text-purple-800 text-sm font-medium">ELIZA OS Framework</span>
            </div>
          </div>
        </motion.div>

        <Tabs defaultValue="all" className="w-full mb-6">
          <div className="flex justify-between items-center mb-6">
            <TabsList className="bg-slate-100">
              <TabsTrigger value="all">All Bots</TabsTrigger>
              <TabsTrigger value="trend">Trend Following</TabsTrigger>
              <TabsTrigger value="arbitrage">Arbitrage</TabsTrigger>
              <TabsTrigger value="market-making">Market Making</TabsTrigger>
              <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
              <TabsTrigger value="defi">DeFi</TabsTrigger>
              <TabsTrigger value="meme">Meme Coins</TabsTrigger>
            </TabsList>

            <div className="hidden md:flex items-center space-x-2">
              <span className="text-sm text-gray-500">Sort by:</span>
              <select className="bg-white border border-gray-200 rounded-md text-sm px-3 py-1.5">
                <option>Popularity</option>
                <option>Return Rate</option>
                <option>Newest</option>
                <option>Price: Low to High</option>
              </select>
            </div>
          </div>

          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tradingBots.map((bot, index) => (
                <BotCard 
                  key={bot.id} 
                  bot={bot} 
                  index={index} 
                  onSubscribe={() => handleSubscribe(bot)}
                  onSolanaPayment={() => handleSolanaPayment(bot)}
                  isSubscribing={isSubscribing && selectedBot?.id === bot.id}
                />
              ))}
            </div>
          </TabsContent>

          {['trend', 'arbitrage', 'market-making', 'portfolio', 'defi', 'meme'].map((category) => (
            <TabsContent key={category} value={category} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {tradingBots
                  .filter(bot => bot.category === category)
                  .map((bot, index) => (
                    <BotCard 
                      key={bot.id} 
                      bot={bot} 
                      index={index} 
                      onSubscribe={() => handleSubscribe(bot)}
                      onSolanaPayment={() => handleSolanaPayment(bot)}
                      isSubscribing={isSubscribing && selectedBot?.id === bot.id}
                    />
                  ))
                }
              </div>
            </TabsContent>
          ))}
        </Tabs>


      </div>

      {/* FAQ Section */}
      <div className="bg-blue-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center text-[#151D44] mb-10">
            Frequently Asked Questions
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {[
              {
                question: "How do I connect a bot to my exchange account?",
                answer: "Our bots use secure API connections to your exchange accounts. You'll need to create API keys with trading permissions (but not withdrawal permissions) and enter them in your bot settings."
              },
              {
                question: "What exchanges are supported?",
                answer: "Currently, our bots support major exchanges including Binance, Coinbase Pro, Kraken, Bybit, and various DeFi exchanges. We're continuously adding support for more exchanges as they become available."
              },
              {
                question: "How much capital do I need to start?",
                answer: "We recommend at least $5,000 in trading capital for optimal performance. Some strategies perform better with higher capital due to minimum order sizes and fee efficiency."
              },
              {
                question: "Are the returns guaranteed?",
                answer: "No. The historical performance figures shown are based on backtesting and actual trading results, but past performance is not indicative of future results. All trading involves risk."
              },
              {
                question: "Can I customize the bot's parameters?",
                answer: "Yes, all bots allow customization of key parameters like risk levels, trading pairs, and timeframes. Advanced users can also modify the core strategy through our API."
              },
              {
                question: "Do I need to pay any recurring fees?",
                answer: "No. Our trading bots are available with a one-time purchase option. There are no recurring subscription fees. You'll continue to receive updates and support as part of your purchase."
              }
            ].map((faq, i) => (
              <Card key={i} className="border border-blue-100">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-start">
                    <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                    {faq.question}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="mt-10 text-center">
            <Button variant="link" className="text-blue-600 hover:text-blue-800">
              View all FAQs <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>
      <Footer />
      
      {/* Solana Payment Dialog */}
      <Dialog open={showSolanaPayment} onOpenChange={setShowSolanaPayment}>
        <DialogContent className="sm:max-w-[500px] max-h-[85vh] overflow-y-auto p-3 sm:p-6 w-[95%]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-purple-600" viewBox="0 0 397.7 311.7" fill="currentColor">
                <path d="M64.6,237.9c2.4-2.4,5.7-3.8,9.2-3.8h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,237.9z"/>
                <path d="M64.6,3.8C67.1,1.4,70.4,0,73.8,0h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,3.8z"/>
                <path d="M333.1,120.1c-2.4-2.4-5.7-3.8-9.2-3.8H6.5c-5.8,0-8.7,7-4.6,11.1l62.7,62.7c2.4,2.4,5.7,3.8,9.2,3.8h317.4c5.8,0,8.7-7,4.6-11.1L333.1,120.1z"/>
              </svg>
              Payment
            </DialogTitle>
            <DialogDescription>
              Get exclusive access to {selectedBot?.name}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div className="bg-slate-50 p-3 rounded-lg border border-slate-200 mb-3">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-slate-700">One-time Purchase</span>
                <span className="font-bold text-purple-700">
                  {selectedBot?.id === 'market-maker-pro' ? '$10,000 USD' : 
                   selectedBot?.id === 'quantum-arbitrage' ? '$6,000 USD' : '$4,000 USD'}
                </span>
              </div>
              <div className="text-xs text-slate-500 mb-1">
                <strong>Full access to trading bot</strong> - Includes lifetime updates and support
              </div>
              <div className="text-sm text-slate-500 mb-3">
                One-time payment for lifetime access
              </div>
              <div className="bg-white p-3 rounded-md border border-slate-200 relative">
                <div className="flex items-center justify-between mb-1">
                  <Label htmlFor="wallet-address" className="text-xs font-medium text-slate-700">
                    Send payment to this wallet address:
                  </Label>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 px-2 text-xs"
                    onClick={handleCopyWalletAddress}
                  >
                    {solanaWalletCopied ? (
                      <><Check className="h-3.5 w-3.5 mr-1" /> Copied</>
                    ) : (
                      <><Copy className="h-3.5 w-3.5 mr-1" /> Copy</>
                    )}
                  </Button>
                </div>
                <Input 
                  id="wallet-address" 
                  value={SOLANA_WALLET_ADDRESS}
                  className="font-mono text-xs sm:text-sm bg-slate-50 break-all"
                  readOnly
                />
              </div>
            </div>
            
            <form onSubmit={handleSolanaPaymentSubmit} className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="tx-id" className="text-sm">
                  Transaction ID <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="tx-id" 
                  placeholder="Enter your transaction ID"
                  value={transactionId}
                  onChange={(e) => setTransactionId(e.target.value)}
                  required
                />
                <p className="text-xs text-slate-500">
                  After sending payment, copy the transaction ID from your wallet or explorer.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="telegram" className="text-sm">
                  Telegram Username <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="telegram" 
                  placeholder="@username"
                  value={telegramUsername}
                  onChange={(e) => setTelegramUsername(e.target.value)}
                  required
                />
                <p className="text-xs text-slate-500">
                  We'll use this to contact you and provide access to your trading bot after purchase.
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes" className="text-sm">
                  Additional Notes (Optional)
                </Label>
                <Textarea 
                  id="notes" 
                  placeholder="Any specific requirements or questions"
                  className="resize-none"
                  rows={2}
                />
              </div>
              
              <DialogFooter className="mt-6 flex flex-col sm:flex-row gap-2">
                <Button type="button" variant="outline" onClick={() => setShowSolanaPayment(false)} className="w-full sm:w-auto order-2 sm:order-1">
                  Cancel
                </Button>
                <Button type="submit" className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto order-1 sm:order-2">
                  Verify Payment
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface BotCardProps {
  bot: TradingBot;
  index: number;
  onSubscribe: () => void;
  onSolanaPayment?: () => void;
  isSubscribing: boolean;
}

function BotCard({ bot, index, onSubscribe, onSolanaPayment, isSubscribing }: BotCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <Card className="overflow-hidden border-gray-200 h-full flex flex-col hover:shadow-lg transition-shadow duration-300">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-3">
              {bot.logo}
              <div>
                <CardTitle className="text-lg">{bot.name}</CardTitle>
                <CardDescription className="text-xs text-gray-500">by {bot.createdBy}</CardDescription>
              </div>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Bookmark className="h-4 w-4 text-gray-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Save to watchlist</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="flex items-center mt-3 space-x-2">
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
              <span className="ml-1 text-sm font-medium">{bot.rating}</span>
            </div>
            <span className="text-xs text-gray-500">({bot.reviewCount} reviews)</span>
            <span className="text-xs text-gray-400">|</span>
            <div className="flex items-center">
              <Users className="h-3.5 w-3.5 text-gray-500" />
              <span className="ml-1 text-xs text-gray-500">{bot.subscribers}</span>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pb-4 flex-grow">
          <p className="text-sm text-gray-600 mb-4">{bot.description}</p>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-slate-50 rounded-lg p-2.5">
              <div className="text-xs text-gray-500 mb-1">Monthly Return</div>
              <div className="flex items-center">
                <span className="text-lg font-bold text-green-600">{bot.monthlyReturn}%</span>
              </div>
            </div>
            <div className="bg-slate-50 rounded-lg p-2.5">
              <div className="text-xs text-gray-500 mb-1">Win Rate</div>
              <div className="flex items-center">
                <span className="text-lg font-bold text-blue-600">{bot.winRate}%</span>
              </div>
            </div>
          </div>
          
          <div className="mb-4">
            <div className="flex items-center gap-1.5 flex-wrap">
              {bot.tags.map((tag, i) => (
                <Badge key={i} variant="secondary" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
          
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <div className="flex items-center">
              <Clock3 className="h-3.5 w-3.5 mr-1" />
              {bot.timeFrame}
            </div>
            <div>
              Risk: <span className={`font-medium ${
                bot.riskLevel === 'Low' ? 'text-green-600' :
                bot.riskLevel === 'Medium' ? 'text-amber-600' : 'text-red-600'
              }`}>{bot.riskLevel}</span>
            </div>
          </div>
          
          <div className="text-xs text-gray-500 flex flex-wrap gap-1">
            Trading pairs: {bot.tradingPairs.map((pair, i) => (
              <span key={i} className="font-medium">
                {pair}{i < bot.tradingPairs.length - 1 ? ',' : ''}
              </span>
            ))}
          </div>
        </CardContent>
        
        <CardFooter className="pt-0 border-t">
          <div className="w-full">
            <div className="flex flex-col gap-1 mb-3">
              <div className="flex justify-between items-center">
                <div>
                  <span className="text-2xl font-bold text-gray-900">
                    {bot.id === 'market-maker-pro' ? '$10,000' : 
                     bot.id === 'quantum-arbitrage' ? '$6,000' : '$4,000'}
                  </span>
                </div>
                <Badge variant="outline" className="font-normal bg-orange-50 text-orange-700 border-orange-200">One-time Purchase</Badge>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  Full access to AI trading bot
                </div>
                <div className="text-xs text-gray-500">
                  Lifetime updates
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-center mb-3">
              <div className="relative w-full">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-gray-200"></span>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">Payment Options</span>
                </div>
              </div>
            </div>
            
            <Button 
                className="w-full bg-gradient-to-r from-purple-600 to-emerald-400 hover:from-purple-700 hover:to-emerald-500 flex items-center justify-center"
                onClick={onSolanaPayment}
                disabled={isSubscribing}
              >
                {isSubscribing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    {/* Solana Logo SVG */}
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="h-4 w-4 mr-2 text-white" 
                      viewBox="0 0 397.7 311.7" 
                      fill="currentColor"
                    >
                      <path d="M64.6,237.9c2.4-2.4,5.7-3.8,9.2-3.8h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,237.9z"/>
                      <path d="M64.6,3.8C67.1,1.4,70.4,0,73.8,0h317.4c5.8,0,8.7,7,4.6,11.1l-62.7,62.7c-2.4,2.4-5.7,3.8-9.2,3.8H6.5c-5.8,0-8.7-7-4.6-11.1L64.6,3.8z"/>
                      <path d="M333.1,120.1c-2.4-2.4-5.7-3.8-9.2-3.8H6.5c-5.8,0-8.7,7-4.6,11.1l62.7,62.7c2.4,2.4,5.7,3.8,9.2,3.8h317.4c5.8,0,8.7-7,4.6-11.1L333.1,120.1z"/>
                    </svg>
                    <span className="mr-1">Pay with SOL</span>
                  </>
                )}
              </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}