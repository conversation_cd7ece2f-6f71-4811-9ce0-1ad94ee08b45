import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { AlertCircle, Home } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "wouter";

export default function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-b from-blue-900 to-black p-4">
      <Card className="w-full max-w-md border-blue-200/20 bg-blue-950/90 backdrop-blur-md">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center mb-4">
            <AlertCircle className="h-16 w-16 text-blue-400 mb-4" />
            <h1 className="text-2xl font-bold text-white">404 Page Not Found</h1>
            <p className="mt-4 text-blue-200">
              The page you're looking for doesn't exist or has been moved.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pb-6">
          <Link href="/">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Home className="mr-2 h-4 w-4" /> Return to Home
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
