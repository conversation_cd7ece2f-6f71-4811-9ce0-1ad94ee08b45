import { useEffect } from 'react';
import { useLocation } from 'wouter';

// This component simply redirects /swap to /exchange with a query parameter to activate the ghost tab
const SwapRedirect = () => {
  const [_, setLocation] = useLocation();

  useEffect(() => {
    setLocation('/exchange?tab=ghost');
  }, [setLocation]);

  return (
    <div className="flex items-center justify-center h-screen bg-[#151c33]">
      <div className="text-center text-white">
        <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-[#20c4cb] rounded-full inline-block mb-4"></div>
        <p>Redirecting to Ghost Mixer...</p>
      </div>
    </div>
  );
};

export default SwapRedirect;