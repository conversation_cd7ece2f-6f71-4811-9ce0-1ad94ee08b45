import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SnowEffect from '../components/SnowEffect';
import { Button } from '@/components/ui/button';
import { FundingApplicationForm } from '../components/FundingApplicationForm';
import { useState } from 'react';
import { ArrowRightIcon, Clock, Globe, Coins, TrendingUp } from 'lucide-react';

export default function SolanaPage() {
  const [openFundingForm, setOpenFundingForm] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      <SnowEffect count={50} speed={0.5} color="#f0f8ff" />
      <Header />
      <main className="pt-20">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-6xl mx-auto">
            {/* Solana Banner */}
            <div className="flex justify-center mb-4">
              <div className="bg-purple-100 text-purple-800 py-2 px-6 rounded-full flex items-center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                  <path d="M19.6617 15.5C19.3937 15.5 19.1257 15.412 18.9157 15.233L12.9987 10.203L7.08175 15.233C6.66175 15.591 6.03975 15.546 5.67075 15.127C5.30175 14.707 5.34675 14.085 5.76675 13.716L12.3407 8.116C12.7187 7.794 13.2777 7.794 13.6567 8.116L20.2307 13.716C20.6507 14.084 20.6957 14.706 20.3267 15.127C20.1077 15.379 19.8857 15.5 19.6617 15.5Z" fill="#9333EA"/>
                </svg>
                Powered by Solana Blockchain
              </div>
            </div>
            
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Solana Community Fundraising</h1>
              <p className="text-lg text-gray-700 mb-8 max-w-3xl mx-auto">
                Leverage the power of community funding on the Solana blockchain.
              </p>
            </div>
            
            {/* Benefits Section */}
            <div className="grid md:grid-cols-3 gap-6 mb-16">
              <div className="bg-gradient-to-r from-purple-500 to-green-500 p-0.5 rounded-lg">
                <div className="bg-white p-6 rounded-lg h-full">
                  <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                    <Coins className="h-6 w-6 text-purple-700" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Low Transaction Fees</h3>
                  <p className="text-gray-600">
                    Solana's high-performance blockchain enables fundraising with minimal gas fees, making it accessible for startups of all sizes.
                  </p>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-500 to-green-500 p-0.5 rounded-lg">
                <div className="bg-white p-6 rounded-lg h-full">
                  <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                    <Clock className="h-6 w-6 text-purple-700" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">High-Speed Transactions</h3>
                  <p className="text-gray-600">
                    Experience lightning-fast funding with Solana's 65,000+ TPS, ensuring real-time contribution tracking and instant confirmation.
                  </p>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-500 to-green-500 p-0.5 rounded-lg">
                <div className="bg-white p-6 rounded-lg h-full">
                  <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                    <Globe className="h-6 w-6 text-purple-700" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Global Investor Pool</h3>
                  <p className="text-gray-600">
                    Connect with a worldwide community of Web 3 investors and enthusiasts who can fund your project regardless of geographic location.
                  </p>
                </div>
              </div>
            </div>
            
            {/* How It Works Section */}
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-12">How Solana Community Fundraising Works</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white p-6 rounded-lg border border-gray-200 relative">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700">
                      <rect width="18" height="14" x="3" y="3" rx="2" />
                      <path d="M4 7h2" />
                      <path d="M4 11h2" />
                      <path d="M17 11h.01" />
                      <path d="M17 7h.01" />
                      <path d="M7 11h6" />
                      <path d="M7 7h8" />
                      <path d="M10 17v4" />
                      <path d="M14 17v4" />
                      <path d="M8 17v1" />
                      <path d="M16 17v1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-center">Create Project</h3>
                  <p className="text-gray-600 text-center">
                    Set up your fundraising campaign with details about your Web 3 project and funding goals.
                  </p>
                  <div className="hidden md:block absolute -right-10 top-1/2 transform -translate-y-1/2 z-10">
                    <ArrowRightIcon className="h-8 w-8 text-purple-300" />
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-lg border border-gray-200 relative">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700">
                      <path d="M18 21a8 8 0 0 0-16 0" />
                      <circle cx="10" cy="8" r="5" />
                      <path d="M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-center">Attract Investors</h3>
                  <p className="text-gray-600 text-center">
                    Your project is presented to our global community of blockchain investors and enthusiasts.
                  </p>
                  <div className="hidden md:block absolute -right-10 top-1/2 transform -translate-y-1/2 z-10">
                    <ArrowRightIcon className="h-8 w-8 text-purple-300" />
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-lg border border-gray-200 relative">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700">
                      <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                      <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                      <circle cx="12" cy="16" r="1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-center">Secure Funding</h3>
                  <p className="text-gray-600 text-center">
                    Funds are securely held in escrow on the Solana blockchain until fundraising milestones are met.
                  </p>
                  <div className="hidden md:block absolute -right-10 top-1/2 transform -translate-y-1/2 z-10">
                    <ArrowRightIcon className="h-8 w-8 text-purple-300" />
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-700">
                      <path d="M12 20h9" />
                      <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z" />
                      <path d="m16 8 2 2" />
                      <path d="m2 22 4-4" />
                      <circle cx="10" cy="6" r="4" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-center">Build & Launch</h3>
                  <p className="text-gray-600 text-center">
                    Access your funds and leverage Orca Capital's network to build and launch your Web 3 startup.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Featured Projects Section */}
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-12">Featured Fundraising Projects</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-purple-500 to-blue-500 p-0.5 rounded-lg overflow-hidden">
                  <div className="bg-white p-6 rounded-lg h-full">
                    <div className="flex items-center mb-4">
                      <div className="bg-purple-700 h-10 w-10 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                        D
                      </div>
                      <h3 className="text-xl font-bold">DefiHub</h3>
                    </div>
                    <p className="text-gray-500 text-sm mb-2">DeFi</p>
                    <p className="text-gray-600 mb-6">
                      Decentralized finance protocol for cross-chain swaps and liquidity pools
                    </p>
                    <div className="mb-2">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="font-medium">Raised: $152,000</span>
                        <span className="text-gray-500">$250,000 goal</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-purple-500 to-green-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-0.5 rounded-lg overflow-hidden">
                  <div className="bg-white p-6 rounded-lg h-full">
                    <div className="flex items-center mb-4">
                      <div className="bg-blue-600 h-10 w-10 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                        B
                      </div>
                      <h3 className="text-xl font-bold">BlockLearn</h3>
                    </div>
                    <p className="text-gray-500 text-sm mb-2">Education</p>
                    <p className="text-gray-600 mb-6">
                      Web 3 education platform with learn-to-earn mechanics and on-chain credentials
                    </p>
                    <div className="mb-2">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="font-medium">Raised: $78,000</span>
                        <span className="text-gray-500">$120,000 goal</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-red-500 to-pink-500 p-0.5 rounded-lg overflow-hidden">
                  <div className="bg-white p-6 rounded-lg h-full">
                    <div className="flex items-center mb-4">
                      <div className="bg-red-500 h-10 w-10 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                        N
                      </div>
                      <h3 className="text-xl font-bold">NFT Verse</h3>
                    </div>
                    <p className="text-gray-500 text-sm mb-2">NFTs & Gaming</p>
                    <p className="text-gray-600 mb-6">
                      Metaverse platform for NFT creators and collectors with virtual galleries
                    </p>
                    <div className="mb-2">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="font-medium">Raised: $315,000</span>
                        <span className="text-gray-500">$400,000 goal</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gradient-to-r from-red-500 to-pink-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* CTA Section */}
            <div className="bg-gradient-to-r from-purple-700 to-indigo-800 rounded-xl p-10 text-white text-center">
              <h2 className="text-3xl font-bold mb-4">Launch Your Web 3 Startup with Solana</h2>
              <p className="text-lg mb-8 max-w-3xl mx-auto">
                Ready to turn your blockchain vision into reality? Join our Solana-powered community fundraising platform and connect with global investors who believe in Web 3 innovation.
              </p>
              <Button 
                className="bg-white text-purple-800 hover:bg-gray-100 px-8 py-6 rounded-lg text-lg font-medium transition-all"
                onClick={() => setOpenFundingForm(true)}
              >
                Apply for Fundraising
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
      <FundingApplicationForm 
        open={openFundingForm} 
        onOpenChange={setOpenFundingForm} 
      />
    </div>
  );
}