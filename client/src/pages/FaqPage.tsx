import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/Header';
import Faq from '../components/Faq';
import Footer from '../components/Footer';
import SnowEffect from '../components/SnowEffect';

export default function FaqPage() {
  const { t } = useTranslation();
  
  return (
    <div className="min-h-screen bg-white">
      <SnowEffect count={50} speed={0.5} color="#f0f8ff" />
      <Header />
      <main className="pt-20">
        <div className="container mx-auto px-4 py-16">
          <h1 className="text-4xl md:text-5xl font-bold text-center mb-8">
            {t('faq.title', 'Frequently Asked Questions')}
          </h1>
          <p className="text-gray-700 max-w-3xl mx-auto text-center mb-12">
            {t('faq.subtitle', 'Get answers to the most common questions about Orca Capital\'s AI-powered blockchain investment strategies')}
          </p>
          <Faq />
        </div>
      </main>
      <Footer />
    </div>
  );
}