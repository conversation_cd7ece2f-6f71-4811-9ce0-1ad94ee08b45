import React, { useState } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SnowEffect from '../components/SnowEffect';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { showNotification } from '../components/Notification';
import { FaFilePdf, FaFileAlt, FaCode, FaChartLine, FaShieldAlt, FaRobot } from 'react-icons/fa';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import axios from 'axios';
import { useTranslation } from 'react-i18next';

export default function WhitepaperPage() {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [organization, setOrganization] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  
  // Dialog open states
  const [devGuideOpen, setDevGuideOpen] = useState(false);
  const [apiRefOpen, setApiRefOpen] = useState(false);
  const [githubRepoOpen, setGithubRepoOpen] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !email) {
      showNotification({
        type: 'error',
        title: 'Required Fields',
        message: 'Please fill in your name and email address.'
      });
      return;
    }

    setLoading(true);
    try {
      await axios.post('/api/request-whitepaper', {
        name,
        email,
        organization,
        message
      });
      
      showNotification({
        type: 'success',
        title: 'Request Submitted',
        message: 'Thank you for your interest. We will send you the whitepaper shortly.'
      });
      
      // Reset the form
      setName('');
      setEmail('');
      setOrganization('');
      setMessage('');
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Submission Error',
        message: 'There was an error submitting your request. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const whitepapers = [
    {
      title: 'AI-Powered Investment Strategy',
      description: 'An in-depth exploration of how Marko Polo uses artificial intelligence to transform investment decision-making in cryptocurrency markets.',
      icon: <FaRobot className="h-10 w-10 text-white" />,
      date: 'March 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    },
    {
      title: 'ELIZA OS Framework',
      description: 'Technical whitepaper on the ELIZA OS framework for building autonomous AI agents. This document covers the architecture, capabilities, and implementation details.',
      icon: <FaCode className="h-10 w-10 text-white" />,
      date: 'March 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    },
    {
      title: 'Crypto Market Analysis Methodology',
      description: 'Our comprehensive approach to analyzing cryptocurrency markets, including on-chain data analysis, sentiment analysis, technical indicators, and AI pattern detection.',
      icon: <FaChartLine className="h-10 w-10 text-white" />,
      date: 'March 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    },
    {
      title: 'Security Best Practices',
      description: 'A detailed overview of our security infrastructure, protocols, and best practices for protecting digital assets. This paper provides recommendations for enterprise-level security.',
      icon: <FaShieldAlt className="h-10 w-10 text-white" />,
      date: 'January 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    },
    {
      title: t('whitepaper.papers.developerGuide.title'),
      description: t('whitepaper.papers.developerGuide.description', { interpolation: { escapeValue: false } }),
      icon: <FaCode className="h-10 w-10 text-white" />,
      date: 'March 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    },
    {
      title: t('whitepaper.papers.apiReference.title'),
      description: t('whitepaper.papers.apiReference.description', { interpolation: { escapeValue: false } }),
      icon: <FaFileAlt className="h-10 w-10 text-white" />,
      date: 'March 2025',
      color: 'bg-blue-600',
      downloadText: 'Request Access',
      secured: true
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <SnowEffect count={50} speed={0.5} color="#f0f8ff" />
      <Header />
      <main className="pt-20">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-center mb-8">Whitepaper</h1>
            <p className="text-gray-700 max-w-3xl mx-auto text-center mb-12">
              Download our comprehensive whitepapers to learn more about our technology, research, and
              methodology. These documents provide in-depth information about our approach to AI-powered
              investment and blockchain analysis.
            </p>

            <Tabs defaultValue="all" className="mb-12">
              <TabsList className="w-full max-w-md mx-auto flex justify-center mb-8">
                <TabsTrigger value="all">All Papers</TabsTrigger>
                <TabsTrigger value="technology">Technology</TabsTrigger>
                <TabsTrigger value="research">Research</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
              </TabsList>
              
              <TabsContent value="all">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {whitepapers.map((paper, index) => (
                    <WhitepaperCard key={index} paper={paper} />
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="technology">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {whitepapers
                    .filter(paper => ['AI-Powered Investment Strategy', 'ELIZA OS Framework', 'Developer Guide', 'API Reference'].includes(paper.title))
                    .map((paper, index) => (
                      <WhitepaperCard key={index} paper={paper} />
                    ))}
                </div>
              </TabsContent>
              
              <TabsContent value="research">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {whitepapers
                    .filter(paper => ['Crypto Market Analysis Methodology'].includes(paper.title))
                    .map((paper, index) => (
                      <WhitepaperCard key={index} paper={paper} />
                    ))}
                </div>
              </TabsContent>
              
              <TabsContent value="security">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {whitepapers
                    .filter(paper => ['Security Best Practices'].includes(paper.title))
                    .map((paper, index) => (
                      <WhitepaperCard key={index} paper={paper} />
                    ))}
                </div>
              </TabsContent>
            </Tabs>

            <section className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-8">Technical Documentation</h2>
              <p className="text-gray-700 max-w-3xl mx-auto text-center mb-10">
                Access our comprehensive technical documentation for developers, system architects, and technical teams.
              </p>
              
              {/* Developer Guide */}
              <div className="grid md:grid-cols-3 gap-8">
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                  <div className="bg-blue-600 p-4">
                    <h3 className="text-xl font-bold text-white flex items-center">
                      <FaCode className="mr-2" /> {t('whitepaper.papers.developerGuide.title')}
                    </h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4" dangerouslySetInnerHTML={{ __html: t('whitepaper.papers.developerGuide.description') }}>
                    </p>
                    <ul className="list-disc text-gray-600 ml-5 mb-6 text-sm">
                      <li>SDK setup and configuration</li>
                      <li>Authentication & authorization</li>
                      <li>Error handling</li>
                      <li>Webhooks integration</li>
                      <li>Sample applications</li>
                    </ul>
                    <div className="flex gap-2">
                      <a href="#request-form" className="flex-1">
                        <Button variant="outline" className="w-full">
                          Request Access
                        </Button>
                      </a>
                      <Button variant="secondary" className="px-3" onClick={() => setDevGuideOpen(true)}>
                        Read More
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Dialog open={devGuideOpen} onOpenChange={setDevGuideOpen}>
                  <DialogContent className="max-w-4xl max-h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>{t('whitepaper.papers.developerGuide.title')}</DialogTitle>
                      <DialogDescription>
                        Complete implementation guide for integrating with the Marko Polo platform
                      </DialogDescription>
                    </DialogHeader>
                    <ScrollArea className="h-[60vh] pr-4">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Introduction</h3>
                          <p className="mt-2 text-gray-700">
                            The Marko Polo Developer Guide provides comprehensive documentation for developers looking to integrate with our platform. This guide covers everything from API authentication to implementation details for various features.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">SDK Setup</h3>
                          <p className="mt-2 text-gray-700">
                            Our SDK is available for multiple programming languages including JavaScript, Python, Java, and Go. Installation is straightforward using package managers:
                          </p>
                          <div className="mt-2 font-mono text-sm bg-gray-100 p-3 rounded">
                            <code>npm install @marko-polo/sdk</code><br />
                            <code>pip install marko-polo-sdk</code><br />
                            <code>go get github.com/markopolo/sdk</code>
                          </div>
                          <p className="mt-2 text-gray-700">
                            After installation, initialize the SDK with your API credentials:
                          </p>
                          <div className="mt-2 font-mono text-sm bg-gray-100 p-3 rounded">
                            <code>
                              const MarkoPoloSDK = require('@marko-polo/sdk');<br />
                              const client = new MarkoPoloSDK.Client(&#123;<br />
                              &nbsp;&nbsp;apiKey: 'YOUR_API_KEY',<br />
                              &nbsp;&nbsp;apiSecret: 'YOUR_API_SECRET'<br />
                              &#125;);
                            </code>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Authentication</h3>
                          <p className="mt-2 text-gray-700">
                            Our API uses OAuth 2.0 for authentication. The SDK handles authentication automatically, but you can also implement it manually:
                          </p>
                          <ol className="list-decimal ml-5 mt-2 text-gray-700">
                            <li>Register your application in the Developer Portal</li>
                            <li>Receive your client ID and client secret</li>
                            <li>Implement the OAuth 2.0 authorization code flow</li>
                            <li>Store and refresh the access tokens as needed</li>
                          </ol>
                          <p className="mt-2 text-gray-700">
                            Access tokens are valid for 24 hours, while refresh tokens are valid for 30 days.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Error Handling</h3>
                          <p className="mt-2 text-gray-700">
                            Our API returns standard HTTP status codes along with detailed error messages in the response body. We recommend implementing comprehensive error handling to manage:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li>Authentication errors (401, 403)</li>
                            <li>Rate limiting (429)</li>
                            <li>Invalid requests (400)</li>
                            <li>Server errors (500)</li>
                          </ul>
                          <p className="mt-2 text-gray-700">
                            The SDK provides built-in error handling with custom error classes for different error types.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Webhooks Integration</h3>
                          <p className="mt-2 text-gray-700">
                            Webhooks allow your application to receive real-time updates from Marko Polo. To integrate webhooks:
                          </p>
                          <ol className="list-decimal ml-5 mt-2 text-gray-700">
                            <li>Create an endpoint in your application to receive webhook events</li>
                            <li>Register the webhook URL in the Developer Portal</li>
                            <li>Implement signature verification to validate that the webhook is from Marko Polo</li>
                            <li>Process the webhook payload based on the event type</li>
                          </ol>
                          <p className="mt-2 text-gray-700">
                            We support webhooks for various events including transaction alerts, price threshold notifications, and system updates.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Sample Applications</h3>
                          <p className="mt-2 text-gray-700">
                            We provide sample applications to help you get started with our SDK:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>Portfolio Tracker:</strong> A simple web application to track cryptocurrency portfolios</li>
                            <li><strong>Market Data Dashboard:</strong> Real-time market data visualization</li>
                            <li><strong>Trading Bot:</strong> Automated trading bot based on predefined strategies</li>
                            <li><strong>Alert System:</strong> Price and event alert system using webhooks</li>
                          </ul>
                          <p className="mt-2 text-gray-700">
                            All sample applications are available in our GitHub repository with step-by-step documentation.
                          </p>
                        </div>
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
                
                {/* API Reference */}
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                  <div className="bg-blue-600 p-4">
                    <h3 className="text-xl font-bold text-white flex items-center">
                      <FaFileAlt className="mr-2" /> {t('whitepaper.papers.apiReference.title')}
                    </h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4" dangerouslySetInnerHTML={{ __html: t('whitepaper.papers.apiReference.description') }}>
                    </p>
                    <ul className="list-disc text-gray-600 ml-5 mb-6 text-sm">
                      <li>REST API endpoints</li>
                      <li>GraphQL schema reference</li>
                      <li>Rate limiting specifications</li>
                      <li>Versioning policy</li>
                      <li>Response codes & error handling</li>
                    </ul>
                    <div className="flex gap-2">
                      <a href="#request-form" className="flex-1">
                        <Button variant="outline" className="w-full">
                          Request Access
                        </Button>
                      </a>
                      <Button variant="secondary" className="px-3" onClick={() => setApiRefOpen(true)}>
                        Read More
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Dialog open={apiRefOpen} onOpenChange={setApiRefOpen}>
                  <DialogContent className="max-w-4xl max-h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>{t('whitepaper.papers.apiReference.title')}</DialogTitle>
                      <DialogDescription>
                        Complete documentation of the Marko Polo API endpoints and schemas
                      </DialogDescription>
                    </DialogHeader>
                    <ScrollArea className="h-[60vh] pr-4">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">API Overview</h3>
                          <p className="mt-2 text-gray-700">
                            The Marko Polo API is a comprehensive suite of endpoints that provide access to cryptocurrency data, trading functionalities, and portfolio management features. We offer both REST and GraphQL APIs to accommodate different integration needs.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Base URLs</h3>
                          <div className="mt-2 font-mono text-sm bg-gray-100 p-3 rounded">
                            <p><strong>Production:</strong> https://api.markopolo.com/v1</p>
                            <p><strong>Sandbox:</strong> https://sandbox-api.markopolo.com/v1</p>
                            <p><strong>GraphQL:</strong> https://api.markopolo.com/graphql</p>
                          </div>
                          <p className="mt-2 text-gray-700">
                            All API requests must be made over HTTPS. Calls made over HTTP will fail.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Authentication</h3>
                          <p className="mt-2 text-gray-700">
                            All API requests require authentication. We support two authentication methods:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>API Key:</strong> Used for simple read-only operations</li>
                            <li><strong>OAuth 2.0:</strong> Required for operations that modify data or access sensitive information</li>
                          </ul>
                          <p className="mt-2 text-gray-700">
                            Include your API key in the <code>X-API-Key</code> HTTP header:
                          </p>
                          <div className="mt-2 font-mono text-sm bg-gray-100 p-3 rounded">
                            <code>X-API-Key: your_api_key_here</code>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">REST API Endpoints</h3>
                          <div className="mt-4 overflow-x-auto">
                            <table className="min-w-full border border-gray-300 divide-y divide-gray-300">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Endpoint</th>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Method</th>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                <tr>
                                  <td className="px-4 py-2 font-mono text-xs">/assets</td>
                                  <td className="px-4 py-2">GET</td>
                                  <td className="px-4 py-2">List all available cryptocurrency assets</td>
                                </tr>
                                <tr>
                                  <td className="px-4 py-2 font-mono text-xs">/assets/{"{asset_id}"}</td>
                                  <td className="px-4 py-2">GET</td>
                                  <td className="px-4 py-2">Get detailed information about a specific asset</td>
                                </tr>
                                <tr>
                                  <td className="px-4 py-2 font-mono text-xs">/market-data</td>
                                  <td className="px-4 py-2">GET</td>
                                  <td className="px-4 py-2">Get current market data for all assets</td>
                                </tr>
                                <tr>
                                  <td className="px-4 py-2 font-mono text-xs">/portfolios</td>
                                  <td className="px-4 py-2">GET</td>
                                  <td className="px-4 py-2">List all portfolios for the authenticated user</td>
                                </tr>
                                <tr>
                                  <td className="px-4 py-2 font-mono text-xs">/portfolios</td>
                                  <td className="px-4 py-2">POST</td>
                                  <td className="px-4 py-2">Create a new portfolio</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">GraphQL Schema</h3>
                          <p className="mt-2 text-gray-700">
                            Our GraphQL API allows you to request exactly the data you need in a single query. The schema includes:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>Types:</strong> Asset, MarketData, Portfolio, Transaction, User, etc.</li>
                            <li><strong>Queries:</strong> getAssets, getPortfolios, getMarketData, etc.</li>
                            <li><strong>Mutations:</strong> createPortfolio, addTransaction, updateSettings, etc.</li>
                            <li><strong>Subscriptions:</strong> Real-time updates for price changes, portfolio values, etc.</li>
                          </ul>
                          <p className="mt-2 text-gray-700">
                            Example GraphQL query:
                          </p>
                          <div className="mt-2 font-mono text-sm bg-gray-100 p-3 rounded">
                            <code>
                              query &#123;<br />
                              &nbsp;&nbsp;getAsset(id: "bitcoin") &#123;<br />
                              &nbsp;&nbsp;&nbsp;&nbsp;name<br />
                              &nbsp;&nbsp;&nbsp;&nbsp;symbol<br />
                              &nbsp;&nbsp;&nbsp;&nbsp;currentPrice<br />
                              &nbsp;&nbsp;&nbsp;&nbsp;marketCap<br />
                              &nbsp;&nbsp;&nbsp;&nbsp;priceChangePercentage24h<br />
                              &nbsp;&nbsp;&#125;<br />
                              &#125;
                            </code>
                          </div>
                        </div>
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
                
                {/* GitHub Repository */}
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                  <div className="bg-blue-600 p-4">
                    <h3 className="text-xl font-bold text-white flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                      </svg> 
                      {t('whitepaper.papers.githubRepository.title', 'GitHub Repository')}
                    </h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4" dangerouslySetInnerHTML={{ __html: t('whitepaper.papers.githubRepository.description', 'Explore and contribute to our open-source ELIZA OS framework. Clone the repository, submit pull requests, and join our developer community.') }}>
                    </p>
                    <ul className="list-disc text-gray-600 ml-5 mb-6 text-sm">
                      <li>Framework source code</li>
                      <li>Example projects</li>
                      <li>Technical documentation</li>
                      <li>Contribution guidelines</li>
                      <li>Issue tracking & roadmap</li>
                    </ul>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        className="flex-1"
                        onClick={() => window.open("https://github.com/elizaOS/eliza", "_blank")}
                      >
                        View Repository
                      </Button>
                      <Button variant="secondary" className="px-3" onClick={() => setGithubRepoOpen(true)}>
                        Read More
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Dialog open={githubRepoOpen} onOpenChange={setGithubRepoOpen}>
                  <DialogContent className="max-w-4xl max-h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>{t('whitepaper.papers.githubRepository.title', 'ELIZA OS GitHub Repository')}</DialogTitle>
                      <DialogDescription>
                        Explore and contribute to our open-source AI framework for crypto trading
                      </DialogDescription>
                    </DialogHeader>
                    <ScrollArea className="h-[60vh] pr-4">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">About ELIZA OS</h3>
                          <p className="mt-2 text-gray-700">
                            ELIZA OS is our open-source framework for building autonomous AI agents focused on cryptocurrency trading, analysis, and social media engagement. The framework is designed to be modular, extensible, and accessible to developers of all skill levels.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Repository Structure</h3>
                          <p className="mt-2 text-gray-700">
                            The repository is organized into the following main directories:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>/core:</strong> The core ELIZA OS framework, including the base agent architecture, state management, and module system</li>
                            <li><strong>/modules:</strong> Pre-built modules for common tasks such as data analysis, trading strategies, and social media interaction</li>
                            <li><strong>/examples:</strong> Example projects and agents built with ELIZA OS</li>
                            <li><strong>/docs:</strong> Comprehensive documentation and tutorials</li>
                            <li><strong>/tests:</strong> Unit and integration tests</li>
                          </ul>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Getting Started</h3>
                          <p className="mt-2 text-gray-700">
                            To get started with ELIZA OS:
                          </p>
                          <ol className="list-decimal ml-5 mt-2 text-gray-700">
                            <li>Clone the repository: <code>git clone https://github.com/elizaOS/eliza.git</code></li>
                            <li>Install dependencies: <code>npm install</code> or <code>yarn</code></li>
                            <li>Run the examples: <code>npm run examples</code></li>
                            <li>Follow the tutorials in the docs directory</li>
                          </ol>
                          <p className="mt-2 text-gray-700">
                            We recommend starting with the "Basic Trading Bot" example to understand the core concepts.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Example Projects</h3>
                          <p className="mt-2 text-gray-700">
                            The repository includes several example projects:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>Basic Trading Bot:</strong> A simple bot that makes trading decisions based on technical indicators</li>
                            <li><strong>Sentiment Analyzer:</strong> An agent that analyzes social media sentiment to inform trading decisions</li>
                            <li><strong>Market Researcher:</strong> An agent that collects and analyzes market data from multiple sources</li>
                            <li><strong>Social Media Agent:</strong> An agent that can post updates, respond to mentions, and engage with the community</li>
                            <li><strong>Multi-Agent System:</strong> A demonstration of multiple agents working together to form a comprehensive trading system</li>
                          </ul>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Contributing</h3>
                          <p className="mt-2 text-gray-700">
                            We welcome contributions from the community! To contribute:
                          </p>
                          <ol className="list-decimal ml-5 mt-2 text-gray-700">
                            <li>Fork the repository</li>
                            <li>Create a new branch for your feature or bugfix</li>
                            <li>Make your changes, following our coding standards</li>
                            <li>Add tests for your changes</li>
                            <li>Submit a pull request with a clear description of your changes</li>
                          </ol>
                          <p className="mt-2 text-gray-700">
                            All contributions are reviewed by our team. We value quality over quantity, so please ensure your code is well-tested and documented.
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-xl font-bold text-blue-800">Community</h3>
                          <p className="mt-2 text-gray-700">
                            Join our developer community:
                          </p>
                          <ul className="list-disc ml-5 mt-2 text-gray-700">
                            <li><strong>Discord:</strong> Join our Discord server for real-time discussions and support</li>
                            <li><strong>Forums:</strong> Participate in detailed discussions on our developer forums</li>
                            <li><strong>Meetups:</strong> Attend our monthly virtual meetups with presentations from core team members and community contributors</li>
                            <li><strong>Hackathons:</strong> Participate in our quarterly hackathons with prizes for the best ELIZA OS implementations</li>
                          </ul>
                        </div>
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
              </div>
            </section>

            <div id="request-form" className="bg-gray-50 rounded-xl p-8 mb-8">
              <h2 className="text-2xl font-bold mb-6 text-center">Request Whitepaper</h2>
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Name *
                    </label>
                    <Input
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      placeholder="Your email address"
                    />
                  </div>

                  <div>
                    <label htmlFor="organization" className="block text-sm font-medium text-gray-700 mb-1">
                      Organization
                    </label>
                    <Input
                      id="organization"
                      value={organization}
                      onChange={(e) => setOrganization(e.target.value)}
                      placeholder="Your organization (optional)"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Let us know which whitepaper you're interested in and any specific questions"
                      rows={4}
                    />
                  </div>

                  <div className="mt-6">
                    <Button 
                      type="submit" 
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      disabled={loading}
                    >
                      {loading ? 'Submitting...' : 'Submit Request'}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

interface WhitepaperCardProps {
  paper: {
    title: string;
    description: string;
    icon: React.ReactNode;
    date: string;
    color: string;
    downloadText: string;
    secured: boolean;
  };
}

function WhitepaperCard({ paper }: WhitepaperCardProps) {
  return (
    <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-200 flex flex-col h-full">
      <div className={`${paper.color} p-6 text-white flex justify-between items-start`}>
        <div className="w-12 h-12 flex items-center justify-center">
          {paper.icon}
        </div>
        <div className="text-xs opacity-80">{paper.date}</div>
      </div>
      <div className="p-6 flex-grow">
        <h3 className="text-xl font-bold mb-3">{paper.title}</h3>
        <p className="text-gray-600 text-sm mb-6 line-clamp-3">{paper.description}</p>
      </div>
      <div className="px-6 pb-6 mt-auto">
        <a href="#request-form" className="block w-full">
          <Button variant="outline" className="w-full">
            {paper.downloadText}
          </Button>
        </a>
        {paper.secured && (
          <div className="flex justify-center items-center mt-2 text-xs text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            Secured document
          </div>
        )}
      </div>
    </div>
  );
}