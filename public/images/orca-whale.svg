<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Ocean background -->
  <defs>
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#0a4b78" />
      <stop offset="60%" stop-color="#0c5c8f" />
      <stop offset="100%" stop-color="#146a9e" />
    </linearGradient>
    <radialGradient id="lightRay" cx="50%" cy="10%" r="70%" fx="50%" fy="10%">
      <stop offset="0%" stop-color="rgba(255, 255, 255, 0.15)" />
      <stop offset="100%" stop-color="rgba(255, 255, 255, 0)" />
    </radialGradient>
  </defs>
  
  <!-- Ocean background -->
  <rect width="100%" height="100%" fill="url(#oceanGradient)" />
  
  <!-- Light rays coming from the surface -->
  <ellipse cx="600" cy="-100" rx="800" ry="600" fill="url(#lightRay)" />
  
  <!-- Ocean surface ripples -->
  <path d="M0,150 Q300,100 600,150 T1200,100" fill="none" stroke="rgba(255, 255, 255, 0.1)" stroke-width="2" />
  <path d="M0,120 Q300,170 600,120 T1200,170" fill="none" stroke="rgba(255, 255, 255, 0.1)" stroke-width="2" />
  
  <!-- Bubbles -->
  <circle cx="300" cy="400" r="5" fill="rgba(255, 255, 255, 0.3)" />
  <circle cx="320" cy="370" r="3" fill="rgba(255, 255, 255, 0.3)" />
  <circle cx="280" cy="430" r="4" fill="rgba(255, 255, 255, 0.3)" />
  <circle cx="900" cy="300" r="6" fill="rgba(255, 255, 255, 0.2)" />
  <circle cx="930" cy="320" r="4" fill="rgba(255, 255, 255, 0.2)" />
  <circle cx="870" cy="280" r="3" fill="rgba(255, 255, 255, 0.2)" />
  
  <!-- Orca body -->
  <g transform="translate(400, 350) scale(0.8)">
    <!-- Orca main body -->
    <path d="M300,200 C500,150 650,250 750,200 C780,180 800,150 800,100 C800,50 780,30 750,20 C700,5 650,0 600,0 C400,0 250,50 150,100 C50,150 0,250 0,300 C0,400 100,450 200,450 C250,450 280,430 300,400 C350,350 300,300 300,200 Z" fill="black" />
    
    <!-- White patches -->
    <path d="M600,0 C400,0 250,50 150,100 C50,150 0,250 0,300 C0,400 100,450 200,450 C240,450 270,440 290,420 L300,420 C350,400 375,350 375,300 C375,250 350,200 300,200 C250,200 200,260 200,300 C200,340 250,400 300,400 C350,400 400,350 400,300 C400,200 350,100 300,50 L600,0 Z" fill="white" />
    <ellipse cx="650" cy="50" rx="30" ry="20" fill="white" />
    
    <!-- Dorsal fin -->
    <path d="M500,0 C520,-50 540,-80 560,-80 C580,-80 590,-60 600,-40 C610,-20 620,0 600,0 L500,0 Z" fill="black" />
    
    <!-- Tail -->
    <path d="M750,200 C780,180 800,150 800,100 C800,300 900,300 950,250 C970,230 980,200 980,180 C980,150 950,150 930,170 C910,190 900,200 880,200 L750,200 Z" fill="black" />
    <path d="M930,170 C910,190 900,200 880,200 L800,200 C800,250 850,230 900,200 C920,185 925,175 930,170 Z" fill="white" />
    
    <!-- Eye -->
    <ellipse cx="150" cy="150" rx="15" ry="10" fill="white" />
    <circle cx="158" cy="150" r="5" fill="black" />
  </g>
  
  <!-- Small fish in the background -->
  <g transform="translate(200, 600) scale(0.3)">
    <path d="M0,0 C50,-30 100,-30 150,0 C200,30 250,30 300,0 L350,20 L300,40 C250,70 200,70 150,40 C100,10 50,10 0,40 L-50,20 L0,0 Z" fill="rgba(180, 220, 255, 0.6)" />
  </g>
  <g transform="translate(900, 550) scale(0.25) rotate(20)">
    <path d="M0,0 C50,-30 100,-30 150,0 C200,30 250,30 300,0 L350,20 L300,40 C250,70 200,70 150,40 C100,10 50,10 0,40 L-50,20 L0,0 Z" fill="rgba(180, 220, 255, 0.6)" />
  </g>
  <g transform="translate(750, 700) scale(0.2) rotate(-10)">
    <path d="M0,0 C50,-30 100,-30 150,0 C200,30 250,30 300,0 L350,20 L300,40 C250,70 200,70 150,40 C100,10 50,10 0,40 L-50,20 L0,0 Z" fill="rgba(180, 220, 255, 0.6)" />
  </g>
</svg>