// Ghost Swap Referral Service - Real Backend Implementation
const express = require('express');
const { Pool } = require('pg');
const { ethers } = require('ethers');
const router = express.Router();

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/ghostswap',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// USDC Contract Configuration
const USDC_CONTRACT_ADDRESS = '******************************************'; // Mainnet USDC
const USDC_ABI = [
  'function transfer(address to, uint256 amount) returns (bool)',
  'function balanceOf(address account) view returns (uint256)',
  'function decimals() view returns (uint8)'
];

// Ethereum provider and wallet for payouts
const provider = new ethers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL);
const payoutWallet = new ethers.Wallet(process.env.PAYOUT_PRIVATE_KEY, provider);
const usdcContract = new ethers.Contract(USDC_CONTRACT_ADDRESS, USDC_ABI, payoutWallet);

// Generate unique referral code
function generateReferralCode() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'GHOST-';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// Create new user or get existing user
router.post('/create', async (req, res) => {
  try {
    const { walletAddress, referralCode: referredByCode } = req.body;
    
    if (!walletAddress) {
      return res.status(400).json({ error: 'Wallet address required' });
    }

    // Check if user already exists
    const existingUser = await pool.query(
      'SELECT * FROM referral_users WHERE wallet_address = $1',
      [walletAddress.toLowerCase()]
    );

    if (existingUser.rows.length > 0) {
      return res.json({ 
        success: true, 
        user: existingUser.rows[0],
        message: 'User already exists'
      });
    }

    // Generate unique referral code
    let newReferralCode;
    let codeExists = true;
    while (codeExists) {
      newReferralCode = generateReferralCode();
      const codeCheck = await pool.query(
        'SELECT id FROM referral_users WHERE referral_code = $1',
        [newReferralCode]
      );
      codeExists = codeCheck.rows.length > 0;
    }

    // Validate referrer code if provided
    let validReferrer = null;
    if (referredByCode) {
      const referrerCheck = await pool.query(
        'SELECT referral_code FROM referral_users WHERE referral_code = $1',
        [referredByCode]
      );
      if (referrerCheck.rows.length > 0) {
        validReferrer = referredByCode;
      }
    }

    // Create new user
    const newUser = await pool.query(
      `INSERT INTO referral_users 
       (wallet_address, referral_code, referred_by_code) 
       VALUES ($1, $2, $3) 
       RETURNING *`,
      [walletAddress.toLowerCase(), newReferralCode, validReferrer]
    );

    res.json({ 
      success: true, 
      user: newUser.rows[0],
      message: 'User created successfully'
    });

  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update payout address
router.post('/update-payout', async (req, res) => {
  try {
    const { walletAddress, payoutAddress } = req.body;
    
    if (!walletAddress || !payoutAddress) {
      return res.status(400).json({ error: 'Wallet address and payout address required' });
    }

    // Validate Ethereum address format
    if (!ethers.isAddress(payoutAddress)) {
      return res.status(400).json({ error: 'Invalid payout address format' });
    }

    const result = await pool.query(
      `UPDATE referral_users 
       SET payout_address = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE wallet_address = $2 
       RETURNING *`,
      [payoutAddress.toLowerCase(), walletAddress.toLowerCase()]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ 
      success: true, 
      user: result.rows[0],
      message: 'Payout address updated successfully'
    });

  } catch (error) {
    console.error('Error updating payout address:', error);
    res.status(500).json({ error: 'Failed to update payout address' });
  }
});

// Track swap and calculate commission
router.post('/commission', async (req, res) => {
  try {
    const { walletAddress, tradingVolume, transactionHash, fromCurrency, toCurrency, amountFrom, amountTo } = req.body;
    
    if (!walletAddress || !tradingVolume) {
      return res.status(400).json({ error: 'Wallet address and trading volume required' });
    }

    // Get user and their referrer
    const user = await pool.query(
      'SELECT * FROM referral_users WHERE wallet_address = $1',
      [walletAddress.toLowerCase()]
    );

    if (user.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user.rows[0];
    const commissionRate = 0.01; // 1%
    const commissionAmount = userData.referred_by_code ? tradingVolume * commissionRate : 0;

    // Record the swap
    const swapResult = await pool.query(
      `INSERT INTO referral_swaps 
       (user_wallet_address, transaction_hash, from_currency, to_currency, 
        amount_from, amount_to, usd_value, commission_rate, commission_amount, referrer_code) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) 
       RETURNING *`,
      [
        walletAddress.toLowerCase(),
        transactionHash,
        fromCurrency,
        toCurrency,
        amountFrom || 0,
        amountTo || 0,
        tradingVolume,
        commissionRate,
        commissionAmount,
        userData.referred_by_code
      ]
    );

    res.json({ 
      success: true, 
      swap: swapResult.rows[0],
      commission: commissionAmount,
      message: 'Swap tracked successfully'
    });

  } catch (error) {
    console.error('Error tracking commission:', error);
    res.status(500).json({ error: 'Failed to track commission' });
  }
});

// Get user stats
router.get('/stats/:walletAddress', async (req, res) => {
  try {
    const { walletAddress } = req.params;
    
    const stats = await pool.query(
      'SELECT * FROM referral_stats WHERE wallet_address = $1',
      [walletAddress.toLowerCase()]
    );

    if (stats.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ 
      success: true, 
      stats: stats.rows[0]
    });

  } catch (error) {
    console.error('Error getting stats:', error);
    res.status(500).json({ error: 'Failed to get stats' });
  }
});

// Process commission payouts (automated job)
router.post('/process-payouts', async (req, res) => {
  try {
    // Get all users with pending commissions and payout addresses
    const pendingPayouts = await pool.query(`
      SELECT 
        u.referral_code,
        u.payout_address,
        (u.total_commissions_earned - u.total_commissions_paid) as pending_amount
      FROM referral_users u
      WHERE u.payout_address IS NOT NULL 
        AND (u.total_commissions_earned - u.total_commissions_paid) >= 10.00
        AND u.status = 'active'
    `);

    const results = [];

    for (const payout of pendingPayouts.rows) {
      try {
        // Convert to USDC units (6 decimals)
        const amountUsdc = ethers.parseUnits(payout.pending_amount.toString(), 6);
        
        // Send USDC transaction
        const tx = await usdcContract.transfer(payout.payout_address, amountUsdc);
        
        // Record payout
        await pool.query(
          `INSERT INTO commission_payouts 
           (referrer_code, payout_address, amount_usdc, transaction_hash, payout_status) 
           VALUES ($1, $2, $3, $4, 'processing')`,
          [payout.referral_code, payout.payout_address, payout.pending_amount, tx.hash]
        );

        // Update user's paid commissions
        await pool.query(
          `UPDATE referral_users 
           SET total_commissions_paid = total_commissions_paid + $1, updated_at = CURRENT_TIMESTAMP 
           WHERE referral_code = $2`,
          [payout.pending_amount, payout.referral_code]
        );

        results.push({
          referralCode: payout.referral_code,
          amount: payout.pending_amount,
          txHash: tx.hash,
          status: 'sent'
        });

      } catch (error) {
        console.error(`Payout failed for ${payout.referral_code}:`, error);
        results.push({
          referralCode: payout.referral_code,
          amount: payout.pending_amount,
          error: error.message,
          status: 'failed'
        });
      }
    }

    res.json({ 
      success: true, 
      payouts: results,
      message: `Processed ${results.length} payouts`
    });

  } catch (error) {
    console.error('Error processing payouts:', error);
    res.status(500).json({ error: 'Failed to process payouts' });
  }
});

module.exports = router;
