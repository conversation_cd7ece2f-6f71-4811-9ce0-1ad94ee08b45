#!/usr/bin/env node

/**
 * Admin Portal Test Script
 * Tests all the fixed admin portal endpoints
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAdminPortal() {
  console.log('🔧 Testing Fixed Admin Portal\n');

  let cookies = '';

  try {
    // Step 1: Login
    console.log('1️⃣ Testing admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'orcacapital2025'
    }, {
      withCredentials: true
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      cookies = loginResponse.headers['set-cookie']?.join('; ') || '';
    } else {
      console.log('❌ Login failed');
      return;
    }

    // Step 2: Test Ghost Swap Analytics
    console.log('\n2️⃣ Testing Ghost Swap Analytics...');
    try {
      const analyticsResponse = await axios.get(`${BASE_URL}/api/admin/ghost-swap-analytics`, {
        headers: { Cookie: cookies }
      });
      console.log('✅ Ghost Swap Analytics working!');
      console.log('   Total Swaps:', analyticsResponse.data.stats?.totalSwaps || 'N/A');
      console.log('   Total Volume:', analyticsResponse.data.stats?.totalVolume || 'N/A');
    } catch (error) {
      console.log('❌ Ghost Swap Analytics failed:', error.response?.status);
    }

    // Step 3: Test Activity Logs
    console.log('\n3️⃣ Testing Activity Logs...');
    try {
      const logsResponse = await axios.get(`${BASE_URL}/api/admin/activity-logs`, {
        headers: { Cookie: cookies }
      });
      console.log('✅ Activity Logs working!');
      console.log('   Log entries:', logsResponse.data.length);
    } catch (error) {
      console.log('❌ Activity Logs failed:', error.response?.status);
    }

    // Step 4: Test Users Management
    console.log('\n4️⃣ Testing User Management...');
    try {
      const usersResponse = await axios.get(`${BASE_URL}/api/admin/users`, {
        headers: { Cookie: cookies }
      });
      console.log('✅ User Management working!');
      console.log('   Users count:', usersResponse.data.length);
    } catch (error) {
      console.log('❌ User Management failed:', error.response?.status);
    }

    // Step 5: Test Payment Orders
    console.log('\n5️⃣ Testing Payment Orders...');
    try {
      const ordersResponse = await axios.get(`${BASE_URL}/api/admin/payment-orders`, {
        headers: { Cookie: cookies }
      });
      console.log('✅ Payment Orders working!');
      console.log('   Orders count:', ordersResponse.data.length);
    } catch (error) {
      console.log('❌ Payment Orders failed:', error.response?.status);
    }

    // Step 6: Test Creating Activity Log
    console.log('\n6️⃣ Testing Activity Log Creation...');
    try {
      const createLogResponse = await axios.post(`${BASE_URL}/api/admin/activity-logs`, {
        action: 'TEST_ACTION',
        details: 'Testing admin portal fixes'
      }, {
        headers: { Cookie: cookies }
      });
      console.log('✅ Activity Log Creation working!');
      console.log('   Created log ID:', createLogResponse.data.data?.id);
    } catch (error) {
      console.log('❌ Activity Log Creation failed:', error.response?.status);
    }

    // Step 7: Test Auth Status
    console.log('\n7️⃣ Testing Auth Status...');
    try {
      const statusResponse = await axios.get(`${BASE_URL}/api/auth/status`, {
        headers: { Cookie: cookies }
      });
      console.log('✅ Auth Status working!');
      console.log('   Authenticated:', statusResponse.data.authenticated);
      console.log('   User:', statusResponse.data.user?.username);
    } catch (error) {
      console.log('❌ Auth Status failed:', error.response?.status);
    }

    console.log('\n🎉 Admin Portal Test Complete!');
    console.log('\n📊 SUMMARY:');
    console.log('   All major admin portal endpoints have been tested.');
    console.log('   The fixes should resolve the API endpoint errors.');
    console.log('   Admin portal should now work without 404/401 errors.');

  } catch (error) {
    console.log('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminPortal().catch(console.error);
