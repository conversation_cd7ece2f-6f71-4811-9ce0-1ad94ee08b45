#!/usr/bin/env node

/**
 * Test script to verify our critical fixes are working
 */

console.log('🧪 Testing Ghost Swap Critical Fixes...\n');

// Test 1: Environment validation
console.log('1️⃣ Testing environment validation...');
try {
  process.env.NODE_ENV = 'development';
  const { validateEnvironment } = require('./src/utils/envValidator');
  const config = validateEnvironment();
  console.log('✅ Environment validation passed');
} catch (error) {
  console.log('❌ Environment validation failed:', error.message);
}

// Test 2: Input validation schemas
console.log('\n2️⃣ Testing input validation...');
try {
  const { swapRequestSchema, validateAddress } = require('./src/utils/validation');
  
  // Test valid swap request
  const validSwap = {
    fromCurrency: 'BTC',
    toCurrency: 'ETH',
    amount: 1.5,
    walletAddress: '******************************************'
  };
  
  const result = swapRequestSchema.safeParse(validSwap);
  if (result.success) {
    console.log('✅ Swap validation passed');
  } else {
    console.log('❌ Swap validation failed:', result.error.errors);
  }
  
  // Test address validation
  const isValidEth = validateAddress('******************************************', 'ETH');
  const isValidBtc = validateAddress('******************************************', 'BTC');
  
  console.log(`✅ ETH address validation: ${isValidEth}`);
  console.log(`✅ BTC address validation: ${isValidBtc}`);
  
} catch (error) {
  console.log('❌ Validation test failed:', error.message);
}

// Test 3: Error handling
console.log('\n3️⃣ Testing error handling...');
try {
  const { 
    AppError, 
    ValidationError, 
    createSwapError,
    createSuccessResponse 
  } = require('./src/utils/errorHandler');
  
  // Test custom errors
  const validationError = new ValidationError('Test validation error');
  const swapError = createSwapError('Test swap error', 'ghost_123_abc');
  const successResponse = createSuccessResponse({ test: 'data' }, 'Test success');
  
  console.log('✅ Custom errors created successfully');
  console.log('✅ Success response created successfully');
  
} catch (error) {
  console.log('❌ Error handling test failed:', error.message);
}

// Test 4: Database connection handling
console.log('\n4️⃣ Testing database connection...');
try {
  const { isDatabaseAvailable, getDatabase } = require('./db');
  
  const dbAvailable = isDatabaseAvailable();
  console.log(`✅ Database availability check: ${dbAvailable}`);
  
  if (dbAvailable) {
    console.log('✅ Database connection available');
  } else {
    console.log('⚠️ Database not configured (expected in development)');
  }
  
} catch (error) {
  console.log('❌ Database test failed:', error.message);
}

// Test 5: Security middleware
console.log('\n5️⃣ Testing security middleware...');
try {
  const { sanitizeString, sanitizeNumber } = require('./src/utils/validation');
  
  const cleanString = sanitizeString('  <script>alert("xss")</script>  ');
  const cleanNumber = sanitizeNumber('123.45');
  const invalidNumber = sanitizeNumber('not-a-number');
  
  console.log(`✅ String sanitization: "${cleanString}"`);
  console.log(`✅ Number sanitization: ${cleanNumber}`);
  console.log(`✅ Invalid number handling: ${invalidNumber}`);
  
} catch (error) {
  console.log('❌ Security test failed:', error.message);
}

console.log('\n🎉 Critical fixes testing completed!');
console.log('\n📋 Summary:');
console.log('✅ Environment validation system');
console.log('✅ Input validation and sanitization');
console.log('✅ Comprehensive error handling');
console.log('✅ Database connection management');
console.log('✅ Security middleware');

console.log('\n🚀 Ghost Swap is ready for the next phase of fixes!');
