import {
  AppError,
  ValidationError,
  AuthenticationError,
  NotFoundError,
  SwapError,
  BlockchainError,
  error<PERSON><PERSON><PERSON>,
  async<PERSON><PERSON><PERSON>,
  createValidationError,
  createSwapError,
  createSuccessResponse,
} from '../src/utils/errorHandler';

describe('Error Handling System', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = (global as any).testHelpers.createMockRequest();
    mockRes = (global as any).testHelpers.createMockResponse();
    mockNext = (global as any).testHelpers.createMockNext();
  });

  describe('Custom Error Classes', () => {
    test('should create AppError with correct properties', () => {
      const error = new AppError('Test error', 400, 'TEST_ERROR');
      
      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(400);
      expect(error.errorCode).toBe('TEST_ERROR');
      expect(error.isOperational).toBe(true);
      expect(error.name).toBe('AppError');
    });

    test('should create ValidationError with default status', () => {
      const error = new ValidationError('Validation failed');
      
      expect(error.message).toBe('Validation failed');
      expect(error.statusCode).toBe(400);
      expect(error.errorCode).toBe('VALIDATION_ERROR');
      expect(error.name).toBe('ValidationError');
    });

    test('should create AuthenticationError with default message', () => {
      const error = new AuthenticationError();
      
      expect(error.message).toBe('Authentication failed');
      expect(error.statusCode).toBe(401);
      expect(error.errorCode).toBe('AUTHENTICATION_ERROR');
    });

    test('should create NotFoundError with default message', () => {
      const error = new NotFoundError();
      
      expect(error.message).toBe('Resource not found');
      expect(error.statusCode).toBe(404);
      expect(error.errorCode).toBe('NOT_FOUND_ERROR');
    });

    test('should create SwapError with transaction ID', () => {
      const error = new SwapError('Swap failed', 'ghost_123_abc');
      
      expect(error.message).toBe('Swap failed (ghost_123_abc): Swap failed');
      expect(error.statusCode).toBe(500);
      expect(error.errorCode).toBe('SWAP_ERROR');
    });

    test('should create BlockchainError with chain info', () => {
      const error = new BlockchainError('RPC error', 'ETH');
      
      expect(error.message).toBe('Blockchain error on ETH: RPC error');
      expect(error.statusCode).toBe(503);
      expect(error.errorCode).toBe('BLOCKCHAIN_ERROR');
    });
  });

  describe('Error Handler Middleware', () => {
    test('should handle AppError correctly', () => {
      const error = new AppError('Test error', 400, 'TEST_ERROR');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Test error',
          errorCode: 'TEST_ERROR',
          timestamp: expect.any(String),
        })
      );
    });

    test('should handle generic Error in development', () => {
      process.env.NODE_ENV = 'development';
      const error = new Error('Generic error');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Generic error',
          errorCode: 'INTERNAL_ERROR',
          stack: expect.any(String),
        })
      );
    });

    test('should sanitize error message in production', () => {
      process.env.NODE_ENV = 'production';
      const error = new Error('Sensitive database error');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'An internal error occurred',
          errorCode: 'INTERNAL_ERROR',
        })
      );
      expect(mockRes.json).not.toHaveBeenCalledWith(
        expect.objectContaining({
          stack: expect.any(String),
        })
      );
    });

    test('should handle validation errors with details', () => {
      const error = {
        name: 'ZodError',
        errors: [
          { path: ['amount'], message: 'Amount is required' },
          { path: ['currency'], message: 'Invalid currency' },
        ],
      };
      
      errorHandler(error as any, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          errorCode: 'VALIDATION_ERROR',
          details: [
            { field: 'amount', message: 'Amount is required' },
            { field: 'currency', message: 'Invalid currency' },
          ],
        })
      );
    });

    test('should include request ID if present', () => {
      mockReq.headers['x-request-id'] = 'test-request-id';
      const error = new AppError('Test error');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          requestId: 'test-request-id',
        })
      );
    });
  });

  describe('Async Handler', () => {
    test('should handle successful async function', async () => {
      const asyncFn = async (req: any, res: any) => {
        res.json({ success: true });
      };
      
      const wrappedFn = asyncHandler(asyncFn);
      await wrappedFn(mockReq, mockRes, mockNext);
      
      expect(mockRes.json).toHaveBeenCalledWith({ success: true });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should catch and forward async errors', async () => {
      const asyncFn = async () => {
        throw new Error('Async error');
      };
      
      const wrappedFn = asyncHandler(asyncFn);
      await wrappedFn(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    test('should handle promise rejections', async () => {
      const asyncFn = async () => {
        return Promise.reject(new Error('Promise rejection'));
      };
      
      const wrappedFn = asyncHandler(asyncFn);
      await wrappedFn(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('Error Factory Functions', () => {
    test('should create validation error with details', () => {
      const error = createValidationError('Invalid input', { field: 'amount' });
      
      expect(error).toBeInstanceOf(ValidationError);
      expect(error.message).toBe('Invalid input');
      expect(error.statusCode).toBe(400);
    });

    test('should create swap error with transaction ID', () => {
      const error = createSwapError('Swap failed', 'ghost_123_abc');
      
      expect(error).toBeInstanceOf(SwapError);
      expect(error.message).toContain('ghost_123_abc');
      expect(error.statusCode).toBe(500);
    });
  });

  describe('Success Response Helper', () => {
    test('should create success response with data', () => {
      const response = createSuccessResponse({ id: 1, name: 'test' }, 'Operation successful');
      
      expect(response).toEqual({
        success: true,
        message: 'Operation successful',
        data: { id: 1, name: 'test' },
        timestamp: expect.any(String),
      });
    });

    test('should create success response with default message', () => {
      const response = createSuccessResponse({ id: 1 });
      
      expect(response).toEqual({
        success: true,
        message: 'Operation completed successfully',
        data: { id: 1 },
        timestamp: expect.any(String),
      });
    });
  });

  describe('Error Logging', () => {
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    test('should log error details', () => {
      const error = new AppError('Test error', 400);
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      // Error logging is handled internally, we just verify it doesn't throw
      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    test('should log client errors as warnings', () => {
      const error = new ValidationError('Validation failed');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    test('should log server errors', () => {
      const error = new Error('Server error');
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe('Edge Cases', () => {
    test('should handle null error', () => {
      errorHandler(null as any, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
    });

    test('should handle error without message', () => {
      const error = { name: 'CustomError' };
      
      errorHandler(error as any, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
    });

    test('should handle circular reference in error', () => {
      const error: any = new Error('Test');
      error.circular = error;
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });
});
