import {
  swapRequestSchema,
  referralCreateSchema,
  validateAddress,
  validateAmount,
  validateSlippage,
  sanitizeString,
  sanitizeNumber,
  SUPPORTED_CURRENCIES,
} from '../src/utils/validation';

describe('Validation System', () => {
  describe('Address Validation', () => {
    test('should validate Ethereum addresses correctly', () => {
      expect(validateAddress('******************************************', 'ETH')).toBe(true);
      expect(validateAddress('0x742d35Cc6634C0532925a3b8D', 'ETH')).toBe(false);
      expect(validateAddress('invalid', 'ETH')).toBe(false);
    });

    test('should validate Bitcoin addresses correctly', () => {
      expect(validateAddress('******************************************', 'BTC')).toBe(true);
      expect(validateAddress('**********************************', 'BTC')).toBe(true);
      expect(validateAddress('invalid', 'BTC')).toBe(false);
    });

    test('should validate Solana addresses correctly', () => {
      expect(validateAddress('4av41YfRcsKPY4zKxoE9vC5ZofRLvHE7dS9CUKzTHtwj', 'SOL')).toBe(true);
      expect(validateAddress('invalid', 'SOL')).toBe(false);
    });

    test('should return false for unsupported currency', () => {
      expect(validateAddress('******************************************', 'INVALID' as any)).toBe(false);
    });
  });

  describe('Amount Validation', () => {
    test('should validate positive amounts', () => {
      expect(validateAmount(1)).toBe(true);
      expect(validateAmount(100.5)).toBe(true);
      expect(validateAmount(1000000)).toBe(true);
    });

    test('should reject invalid amounts', () => {
      expect(validateAmount(0)).toBe(false);
      expect(validateAmount(-1)).toBe(false);
      expect(validateAmount(1000001)).toBe(false);
      expect(validateAmount(Infinity)).toBe(false);
      expect(validateAmount(NaN)).toBe(false);
    });
  });

  describe('Slippage Validation', () => {
    test('should validate slippage tolerance', () => {
      expect(validateSlippage(0.001)).toBe(true);
      expect(validateSlippage(0.05)).toBe(true);
      expect(validateSlippage(0.5)).toBe(true);
    });

    test('should reject invalid slippage', () => {
      expect(validateSlippage(0)).toBe(false);
      expect(validateSlippage(0.0001)).toBe(false);
      expect(validateSlippage(0.6)).toBe(false);
    });
  });

  describe('Swap Request Schema', () => {
    const validSwapRequest = {
      fromCurrency: 'BTC' as const,
      toCurrency: 'ETH' as const,
      amount: 1.5,
      walletAddress: '******************************************',
    };

    test('should validate correct swap request', () => {
      const result = swapRequestSchema.safeParse(validSwapRequest);
      expect(result.success).toBe(true);
    });

    test('should reject missing fields', () => {
      const invalidRequest = { ...validSwapRequest };
      delete (invalidRequest as any).amount;
      
      const result = swapRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    test('should reject same from/to currency', () => {
      const invalidRequest = {
        ...validSwapRequest,
        toCurrency: 'BTC' as const,
      };
      
      const result = swapRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    test('should reject invalid currency', () => {
      const invalidRequest = {
        ...validSwapRequest,
        fromCurrency: 'INVALID' as any,
      };
      
      const result = swapRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    test('should reject invalid amount', () => {
      const invalidRequest = {
        ...validSwapRequest,
        amount: -1,
      };
      
      const result = swapRequestSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    test('should apply default slippage', () => {
      const result = swapRequestSchema.safeParse(validSwapRequest);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.slippage).toBe(0.005);
      }
    });

    test('should validate custom slippage', () => {
      const requestWithSlippage = {
        ...validSwapRequest,
        slippage: 0.01,
      };
      
      const result = swapRequestSchema.safeParse(requestWithSlippage);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.slippage).toBe(0.01);
      }
    });
  });

  describe('Referral Create Schema', () => {
    const validReferralRequest = {
      walletAddress: '******************************************',
    };

    test('should validate correct referral request', () => {
      const result = referralCreateSchema.safeParse(validReferralRequest);
      expect(result.success).toBe(true);
    });

    test('should validate with referral code', () => {
      const requestWithCode = {
        ...validReferralRequest,
        referredByCode: 'GHOST123ABC',
      };
      
      const result = referralCreateSchema.safeParse(requestWithCode);
      expect(result.success).toBe(true);
    });

    test('should reject invalid referral code format', () => {
      const invalidRequest = {
        ...validReferralRequest,
        referredByCode: 'invalid',
      };
      
      const result = referralCreateSchema.safeParse(invalidRequest);
      expect(result.success).toBe(false);
    });

    test('should reject missing wallet address', () => {
      const result = referralCreateSchema.safeParse({});
      expect(result.success).toBe(false);
    });
  });

  describe('Input Sanitization', () => {
    test('should sanitize strings correctly', () => {
      expect(sanitizeString('  hello world  ')).toBe('hello world');
      expect(sanitizeString('<script>alert("xss")</script>')).toBe('scriptalert(xss)/script');
      expect(sanitizeString('normal text')).toBe('normal text');
      expect(sanitizeString('a'.repeat(2000))).toHaveLength(1000);
    });

    test('should sanitize numbers correctly', () => {
      expect(sanitizeNumber('123')).toBe(123);
      expect(sanitizeNumber('123.45')).toBe(123.45);
      expect(sanitizeNumber('invalid')).toBe(null);
      expect(sanitizeNumber(null)).toBe(null);
      expect(sanitizeNumber(undefined)).toBe(null);
    });
  });

  describe('Supported Currencies', () => {
    test('should include major cryptocurrencies', () => {
      expect(SUPPORTED_CURRENCIES).toContain('BTC');
      expect(SUPPORTED_CURRENCIES).toContain('ETH');
      expect(SUPPORTED_CURRENCIES).toContain('SOL');
      expect(SUPPORTED_CURRENCIES).toContain('BNB');
      expect(SUPPORTED_CURRENCIES).toContain('HYPE');
      expect(SUPPORTED_CURRENCIES).toContain('KAS');
    });

    test('should have reasonable number of currencies', () => {
      expect(SUPPORTED_CURRENCIES.length).toBeGreaterThan(20);
      expect(SUPPORTED_CURRENCIES.length).toBeLessThan(50);
    });

    test('should not have duplicates', () => {
      const unique = [...new Set(SUPPORTED_CURRENCIES)];
      expect(unique).toHaveLength(SUPPORTED_CURRENCIES.length);
    });
  });

  describe('Edge Cases', () => {
    test('should handle null and undefined inputs', () => {
      expect(validateAmount(null as any)).toBe(false);
      expect(validateAmount(undefined as any)).toBe(false);
      expect(validateAddress(null as any, 'ETH')).toBe(false);
      expect(validateAddress(undefined as any, 'ETH')).toBe(false);
    });

    test('should handle empty strings', () => {
      expect(validateAddress('', 'ETH')).toBe(false);
      expect(sanitizeString('')).toBe('');
    });

    test('should handle very large numbers', () => {
      expect(validateAmount(Number.MAX_SAFE_INTEGER)).toBe(false);
      expect(validateAmount(1e20)).toBe(false);
    });

    test('should handle special characters in addresses', () => {
      expect(validateAddress('0x<script>', 'ETH')).toBe(false);
      expect(validateAddress('bc1"injection', 'BTC')).toBe(false);
    });
  });
});
