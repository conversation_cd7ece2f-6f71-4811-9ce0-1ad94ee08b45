/**
 * Type definitions for test environment
 */

declare global {
  namespace NodeJS {
    interface Global {
      testHelpers: {
        createMockRequest: (overrides?: any) => any;
        createMockResponse: () => any;
        createMockNext: () => jest.Mock;
        sleep: (ms: number) => Promise<void>;
        generateRandomAddress: (type?: 'ETH' | 'BTC' | 'SOL') => string;
        generateRandomTxId: () => string;
      };
    }
  }

  var testHelpers: {
    createMockRequest: (overrides?: any) => any;
    createMockResponse: () => any;
    createMockNext: () => jest.Mock;
    sleep: (ms: number) => Promise<void>;
    generateRandomAddress: (type?: 'ETH' | 'BTC' | 'SOL') => string;
    generateRandomTxId: () => string;
  };
}

export {};
