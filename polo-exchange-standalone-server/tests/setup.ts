/**
 * Jest test setup file
 * Configures test environment and global mocks
 */

import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Mock console methods to reduce noise
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test timeout
jest.setTimeout(30000);

// Mock external services
jest.mock('@solana/web3.js', () => ({
  Connection: jest.fn().mockImplementation(() => ({
    getLatestBlockhash: jest.fn().mockResolvedValue({ blockhash: 'test' }),
    sendTransaction: jest.fn().mockResolvedValue('test-signature'),
  })),
  Keypair: {
    fromSecretKey: jest.fn().mockReturnValue({
      publicKey: { toString: () => 'test-public-key' },
      secretKey: new Uint8Array(64),
    }),
  },
  PublicKey: jest.fn().mockImplementation((key) => ({
    toString: () => key,
  })),
  SystemProgram: {
    transfer: jest.fn().mockReturnValue({}),
  },
  Transaction: jest.fn().mockImplementation(() => ({
    add: jest.fn(),
    sign: jest.fn(),
  })),
  sendAndConfirmTransaction: jest.fn().mockResolvedValue('test-signature'),
}));

jest.mock('ethers', () => ({
  ethers: {
    providers: {
      JsonRpcProvider: jest.fn().mockImplementation(() => ({
        getBlockNumber: jest.fn().mockResolvedValue(12345),
        getBalance: jest.fn().mockResolvedValue('1000000000000000000'),
      })),
    },
    Wallet: jest.fn().mockImplementation(() => ({
      address: '0xtest',
      connect: jest.fn(),
    })),
    Contract: jest.fn().mockImplementation(() => ({
      balanceOf: jest.fn().mockResolvedValue('1000000000000000000'),
      transfer: jest.fn().mockResolvedValue({ hash: 'test-hash' }),
    })),
    utils: {
      parseEther: jest.fn().mockReturnValue('1000000000000000000'),
      formatEther: jest.fn().mockReturnValue('1.0'),
    },
  },
}));

// Mock database
jest.mock('../db', () => ({
  isDatabaseAvailable: jest.fn().mockReturnValue(false),
  getDatabase: jest.fn().mockImplementation(() => {
    throw new Error('Database not available in test environment');
  }),
  getPool: jest.fn().mockImplementation(() => {
    throw new Error('Database pool not available in test environment');
  }),
  db: null,
  pool: null,
}));

// Global test helpers
(global as any).testHelpers = {
  createMockRequest: (overrides = {}) => ({
    body: {},
    query: {},
    params: {},
    headers: {},
    ip: '127.0.0.1',
    method: 'GET',
    url: '/test',
    ...overrides,
  }),

  createMockResponse: () => {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.end = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },

  createMockNext: () => jest.fn(),

  sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  generateRandomAddress: (type: 'ETH' | 'BTC' | 'SOL' = 'ETH') => {
    switch (type) {
      case 'ETH':
        return '0x' + Math.random().toString(16).substr(2, 40);
      case 'BTC':
        return 'bc1' + Math.random().toString(36).substr(2, 39);
      case 'SOL':
        return Math.random().toString(36).substr(2, 44);
      default:
        return '0x' + Math.random().toString(16).substr(2, 40);
    }
  },

  generateRandomTxId: () => {
    return `ghost_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  },
};

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Cleanup after all tests
afterAll(async () => {
  // Close any open handles
  await new Promise(resolve => setTimeout(resolve, 100));
});
