#!/usr/bin/env node

/**
 * Admin Login Test Script
 * Tests the admin login functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAdminLogin() {
  console.log('🔐 Testing Admin Login System\n');

  try {
    // Test 1: Login with correct credentials
    console.log('1️⃣ Testing login with correct credentials...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'orcacapital2025'
    }, {
      withCredentials: true
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log('   User:', loginResponse.data.user.username);
      console.log('   Role:', loginResponse.data.user.role);
      
      // Store cookies for subsequent requests
      const cookies = loginResponse.headers['set-cookie'];
      
      // Test 2: Check auth status
      console.log('\n2️⃣ Testing auth status...');
      const statusResponse = await axios.get(`${BASE_URL}/api/auth/status`, {
        headers: {
          Cookie: cookies ? cookies.join('; ') : ''
        }
      });
      
      if (statusResponse.data.authenticated) {
        console.log('✅ Auth status check successful!');
        console.log('   Authenticated:', statusResponse.data.authenticated);
        console.log('   User:', statusResponse.data.user.username);
      } else {
        console.log('❌ Auth status check failed');
      }
      
      // Test 3: Access admin endpoint
      console.log('\n3️⃣ Testing admin endpoint access...');
      try {
        const adminResponse = await axios.get(`${BASE_URL}/api/admin/payment-orders`, {
          headers: {
            Cookie: cookies ? cookies.join('; ') : ''
          }
        });
        
        console.log('✅ Admin endpoint access successful!');
        console.log('   Payment orders count:', adminResponse.data.length);
      } catch (adminError) {
        console.log('❌ Admin endpoint access failed:', adminError.response?.status);
      }
      
      // Test 4: Logout
      console.log('\n4️⃣ Testing logout...');
      const logoutResponse = await axios.post(`${BASE_URL}/api/auth/logout`, {}, {
        headers: {
          Cookie: cookies ? cookies.join('; ') : ''
        }
      });
      
      if (logoutResponse.data.success) {
        console.log('✅ Logout successful!');
      } else {
        console.log('❌ Logout failed');
      }
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.error);
    }

  } catch (error) {
    console.log('❌ Login test failed:', error.response?.data?.error || error.message);
  }

  // Test 5: Login with wrong credentials
  console.log('\n5️⃣ Testing login with wrong credentials...');
  try {
    await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'wrongpassword'
    });
    console.log('❌ Should have failed with wrong password');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Correctly rejected wrong credentials');
    } else {
      console.log('❌ Unexpected error:', error.response?.status);
    }
  }

  console.log('\n🎯 Admin Login Test Complete!');
}

// Run the test
testAdminLogin().catch(console.error);
