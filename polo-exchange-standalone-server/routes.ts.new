import { Express, Request, Response, NextFunction } from 'express';
import { createServer, Server } from 'http';
import path from 'path';
import fs from 'fs';
import PDFDocument from 'pdfkit';
import axios from 'axios';

interface CryptoHistoricalData {
  [year: number]: number;
}

export async function registerRoutes(app: Express): Promise<Server> {
  app.get('/api/bitcoin/historical', async (req: Request, res: Response) => {
    try {
      // Demo data - this would typically come from a cryptocurrency API
      const data = {
        prices: [
          [1583020800000, 29000], // 2020
          [1614556800000, 45000], // 2021
          [1646092800000, 38000], // 2022
          [1677628800000, 42000], // 2023
          [1709251200000, 52000], // 2024
          [Date.now(), 55000]     // Now
        ]
      };
      
      res.json(data);
    } catch (error) {
      console.error('Error fetching Bitcoin data:', error);
      res.status(500).json({ error: 'Failed to fetch Bitcoin data' });
    }
  });
  
  app.get('/api/crypto/historical', async (req: Request, res: Response) => {
    try {
      const { cryptocurrency = 'bitcoin', period = '1year' } = req.query;
      
      // Get API key from environment variables
      const apiKey = process.env.CMC_API_KEY;
      
      if (!apiKey) {
        console.warn('No CMC_API_KEY environment variable found, using mock data');
        
        let historicalPrices: CryptoHistoricalData;
        
        // Mock data for different cryptocurrencies
        switch (cryptocurrency) {
          case 'bitcoin':
            historicalPrices = {
              2018: 3840,
              2019: 7200,
              2020: 29000,
              2021: 47000,
              2022: 16500,
              2023: 42000,
              2024: 52000,
              2025: 58000
            };
            break;
          case 'ethereum':
            historicalPrices = {
              2018: 138,
              2019: 130,
              2020: 730,
              2021: 3700,
              2022: 1200,
              2023: 1800,
              2024: 3200,
              2025: 3900
            };
            break;
          case 'solana':
            historicalPrices = {
              2018: 0,
              2019: 0.5,
              2020: 1.8,
              2021: 178,
              2022: 12,
              2023: 105,
              2024: 175,
              2025: 225
            };
            break;
          case 'xrp':
            historicalPrices = {
              2018: 0.35,
              2019: 0.19,
              2020: 0.22,
              2021: 0.85,
              2022: 0.32,
              2023: 0.62,
              2024: 0.72,
              2025: 0.96
            };
            break;
          case 'dogecoin':
            historicalPrices = {
              2018: 0.002,
              2019: 0.003,
              2020: 0.004,
              2021: 0.16,
              2022: 0.07,
              2023: 0.08,
              2024: 0.13,
              2025: 0.22
            };
            break;
          default:
            historicalPrices = {
              2018: 100,
              2019: 150,
              2020: 200,
              2021: 400,
              2022: 300,
              2023: 500,
              2024: 700,
              2025: 900
            };
        }
        
        // Convert to the format expected by the client
        const mockData = {
          prices: Object.entries(historicalPrices).map(([year, price]) => {
            // Convert year to timestamp
            const timestamp = new Date(parseInt(year), 0, 1).getTime();
            return [timestamp, price];
          })
        };
        
        return res.json(mockData);
      }
      
      // For real data, we would make a request to CoinMarketCap or similar API
      try {
        // This would be replaced with actual API request
        console.log(`Would fetch data for ${cryptocurrency} over ${period} with api key ${apiKey.substring(0, 3)}...`);
        
        // For now, returning dummy data
        const dummyData = {
          prices: [
            [1615852800000, 55000],
            [1618531200000, 62000],
            [1621123200000, 35000],
            [1623715200000, 40000],
            [1626307200000, 32000],
            [1628985600000, 48000],
            [1631664000000, 42000],
            [1634256000000, 63000],
            [Date.now(), 57000]
          ]
        };
        
        res.json(dummyData);
      } catch (error) {
        console.error(`Error fetching cryptocurrency data:`, error);
        res.status(500).json({ error: 'Failed to fetch cryptocurrency data' });
      }
    } catch (error) {
      console.error('Error in /api/crypto/historical:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });
  
  app.get('/api/whitepapers/:filename', (req: Request, res: Response) => {
    const { filename } = req.params;
    
    try {
      // Set the content type
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);

      // Create a new PDF document
      const doc = new PDFDocument({
        margins: { top: 50, bottom: 50, left: 50, right: 50 },
        info: {
          Title: filename,
          Author: 'Orca Capital',
          Subject: 'Investment Whitepaper',
          Keywords: 'cryptocurrency, investment, whitepaper'
        }
      });

      // Pipe the PDF to the response
      doc.pipe(res);
      
      // Setup response to handle the output of the PDF
      const buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers);
        res.end(pdfBuffer);
      });
      
      // Add logo
      const logoPath = path.join(process.cwd(), 'orca-logo.svg');
      const logoPathAlt = path.join(process.cwd(), 'public', 'orca-logo.svg');
      
      try {
        if (fs.existsSync(logoPath)) {
          doc.image(logoPath, 50, 45, { width: 100 });
        } else if (fs.existsSync(logoPathAlt)) {
          doc.image(logoPathAlt, 50, 45, { width: 100 });
        } else {
          console.log('Logo not found, continuing without logo');
        }
      } catch (error) {
        console.log('Error adding logo, continuing without logo:', error);
      }
      
      // Add title based on filename
      let title = "Orca Capital";
      let content = "This document contains confidential information from Orca Capital.";
      
      // Customize content based on the requested whitepaper
      if (filename.includes('ai-investing')) {
        title = "AI Investment Strategy Whitepaper";
        content = `
        # AI Investment Strategy Whitepaper
        
        ## Executive Summary
        
        This whitepaper details how Orca Capital leverages artificial intelligence to transform investment decision-making in cryptocurrency markets. Our proprietary algorithms, neural networks, and machine learning models drive our market analysis and investment strategy, resulting in consistent alpha generation across market cycles.
        
        ## AI-Powered Investment Methodology
        
        Our AI systems continuously analyze market data, on-chain metrics, social sentiment, and macro trends to identify promising investment opportunities. The core components include:
        
        1. Predictive Analytics Models - Time-series forecasting using ensemble methods that combine LSTM neural networks, gradient boosting machines, and proprietary statistical models to predict price movements across multiple timeframes.
        
        2. Natural Language Processing for Sentiment Analysis - Advanced transformer-based language models process over 500,000 data points daily from social media, news articles, developer forums, and project documentation to gauge market sentiment and identify emerging narratives before they impact prices.
        
        3. Pattern Recognition Algorithms - Convolutional neural networks identify complex chart patterns and market structures across multiple timeframes, detecting regime changes and market inefficiencies that human analysts might miss.
        
        4. Quantitative Trading Systems - Self-optimizing algorithmic trading systems that execute with microsecond precision based on signals generated by our AI models, with dynamic parameter adjustment based on market volatility and liquidity conditions.
        
        ## Technical Infrastructure
        
        Our AI infrastructure is built on a distributed computing framework that includes:
        
        - High-performance GPU clusters for real-time model inference
        - Redundant data collection systems with multiple API connections to exchanges, blockchain nodes, and data providers
        - Low-latency execution framework with direct market access to major exchanges
        - Proprietary database architecture optimized for time-series data storage and retrieval
        - Quantum-resistant encryption for all sensitive data and model parameters
        
        ## Model Training Methodology
        
        Our models undergo rigorous training and validation processes:
        
        1. Historical Data Preprocessing - Cleaning, normalization, and feature engineering on petabytes of market data dating back to 2009
        
        2. Supervised Learning Phase - Initial model training on labeled datasets with known market outcomes
        
        3. Reinforcement Learning - Models are refined through simulated trading environments where they learn to maximize risk-adjusted returns
        
        4. Adversarial Testing - Models are stress-tested against adversarial scenarios designed to exploit potential weaknesses
        
        5. Continuous Evolution - Online learning algorithms adapt to changing market conditions in real-time
        
        ## Performance Metrics
        
        Our AI-driven approach has demonstrated significant outperformance compared to traditional investment strategies, with risk-adjusted returns exceeding benchmark indices by an average of 27% over the past three years. Key performance indicators include:
        
        - Sharpe Ratio: 3.2 (vs. industry average of 1.7)
        - Maximum Drawdown: 18% (vs. industry average of 34%)
        - Win Rate: 68% on all trading signals
        - Alpha Generation: +12.4% annualized after fees
        
        ## Risk Management Framework
        
        We implement a multi-layered risk management system that includes position sizing, volatility controls, and automated circuit breakers to protect capital during extreme market conditions:
        
        1. Adaptive Position Sizing - Dynamically adjusts exposure based on model confidence, market volatility, and correlation metrics
        
        2. Tail Risk Hedging - Automated options strategies and cross-asset hedges activated during periods of market stress
        
        3. Liquidity Management - Continuous monitoring of order book depth and market impact modeling to prevent adverse price movements during position entry/exit
        
        4. Counterparty Risk Controls - Diversification across exchanges and custodians with real-time monitoring of counterparty risk metrics
        
        5. Black Swan Detection - Neural network models specifically trained to identify market conditions that precede extreme events
        
        ## Future Developments
        
        Our AI research roadmap includes:
        
        - Integration of quantum computing for complex optimization problems
        - Explainable AI enhancements to provide transparent reasoning for investment decisions
        - Multi-agent systems that simulate market participant behavior for more accurate market modeling
        - Cross-chain analysis frameworks to identify opportunities across the fragmented blockchain ecosystem
        - Automated smart contract auditing and vulnerability detection
        
        © 2025 Orca Capital. All rights reserved.
        `;
      } else if (filename.includes('eliza-os')) {
        title = "ELIZA OS Framework Whitepaper";
        content = `
        # ELIZA OS Framework Whitepaper
        
        ## Introduction
        
        ELIZA OS is our revolutionary open-source framework for building autonomous AI agents specifically designed for blockchain and cryptocurrency applications. This document provides a comprehensive technical overview of the architecture, capabilities, implementation details, and development roadmap of our framework, which enables the deployment of sophisticated AI agents that can operate autonomously across various crypto ecosystems.
        
        ## Philosophical Foundation
        
        ELIZA OS is built on the premise that autonomous AI agents will become increasingly important participants in crypto markets and blockchain ecosystems. Our framework is designed to democratize access to sophisticated AI tools, allowing developers to create agents that can:
        
        - Process and interpret vast amounts of market data in real-time
        - Make decisions based on complex, multi-variable analysis
        - Execute actions across multiple platforms and protocols
        - Learn and adapt to changing market conditions
        - Collaborate with other agents to achieve common goals
        
        ## Core Architecture Overview
        
        ELIZA OS employs a modular, microservices-based architecture that enables developers to create specialized AI agents for specific tasks while maintaining interoperability across the ecosystem. The framework consists of four primary subsystems:
        
        ### 1. Perception Engine
        
        The Perception Engine is responsible for data ingestion, preprocessing, and interpretation. Key components include:
        
        - Data Connectors: Standardized interfaces for connecting to exchanges, blockchain nodes, social platforms, and other data sources
        - Stream Processing: Real-time handling of high-volume data streams using distributed computing techniques
        - Feature Extraction: Automated identification of relevant patterns and metrics from raw data
        - Semantic Understanding: Natural language processing capabilities for interpreting news, social media, and other textual information
        - Multi-modal Integration: Ability to combine and contextualize data from different sources and formats
        
        Technical implementation details:
        
        - Built on Apache Kafka and Flink for stream processing
        - Uses a custom-developed tensor processing library optimized for time-series data
        - Employs transformer-based NLP models with domain-specific fine-tuning
        - Implements proprietary anomaly detection algorithms
        
        ## Development Guidelines
        
        Best practices for creating and deploying autonomous agents using the ELIZA OS framework, including security considerations and recommended testing procedures.
        
        ## Use Cases
        
        ELIZA OS can be used to create a wide range of AI agents, including trading bots, market analyzers, social media monitors, and community engagement tools.
        
        © 2025 Orca Capital. All rights reserved.
        `;
      } else if (filename.includes('market-analysis')) {
        title = "Crypto Market Analysis Methodology";
        content = `
        # Crypto Market Analysis Methodology
        
        ## Executive Summary
        
        This document outlines Orca Capital's proprietary framework for comprehensive cryptocurrency market analysis. Our multi-dimensional approach integrates on-chain data analysis, sentiment analysis, technical indicators, and fundamental valuation metrics to identify investment opportunities across different market regimes. This methodology has been developed and refined through multiple market cycles and represents the culmination of our research team's expertise in quantitative finance, blockchain technology, and artificial intelligence.
        
        ## Integrated Data Architecture
        
        Our analysis is built on a sophisticated data architecture that processes over 8.7 billion data points daily from diverse sources, ensuring a comprehensive view of market dynamics.
        
        ### Primary Data Sources
        
        #### 1. On-Chain Metrics
        
        We collect and analyze blockchain data directly from node operators across 37 major blockchain networks, including:
        
        - Transaction Volumes: Analyzed by size, frequency, and participant types
        - Network Activity: Daily active addresses, new address creation, dormancy metrics
        - Token Velocity: Circulation speed and holder time distribution
        - Smart Contract Interactions: Usage patterns across DeFi, NFT, and other sectors
        - Staking and Validation: Participation rates and consensus dynamics
        
        #### 2. Exchange and Trading Data
        
        We maintain direct API connections to 73 centralized and decentralized exchanges, capturing:
        
        - Order Book Depth: Liquidity profiles at different price levels
        - Trading Volumes: Authenticated data verified against on-chain transfers
        - Futures and Options Markets: Open interest, funding rates, term structure
        - Market Microstructure: Tick-by-tick data for high-frequency patterns
        - Whale Transaction Monitoring: Large transaction tracking and analysis
        
        ## Multi-Dimensional Analytical Framework
        
        Our analytical framework applies multiple methodologies to the integrated data, organized into four primary dimensions:
        
        ### 1. Fundamental Analysis
        
        We evaluate the intrinsic value of crypto assets using:
        
        #### Network Value Models
        
        - Network Value to Transactions (NVT) Ratio: Comparing market capitalization to transaction value
        - Network Value to Metcalfe (NVM) Ratio: Analysis of value relative to network effects
        - Active Address Multiple: Valuation relative to network participation
        - Fee Revenue Analysis: Sustainability of network security through fee generation
        - Token Economic Design Assessment: Evaluation of monetary policy and incentive structures
        
        #### Cash Flow Projections
        
        - Protocol Revenue Models: Analysis of fee distribution and value accrual mechanisms
        - Token Holder Return Mechanisms: Staking yields, transaction fee sharing, buybacks
        - Discounted Cash Flow Adaptations: Modified DCF models for token economies
        - Terminal Value Estimation: Long-term sustainability and growth potential
        
        ## Market Cycle Models
        
        Our market cycle identification framework combines traditional cycle theory with crypto-specific adaptations to identify different phases and regime changes.
        
        ## Signal Generation and Portfolio Construction
        
        Our analytical framework produces investment signals through a multi-stage process.
        
        © 2025 Orca Capital. All rights reserved.
        `;
      } else if (filename.includes('security-practices')) {
        title = "Security Best Practices";
        content = `
        # Security Best Practices Whitepaper
        
        ## Executive Summary
        
        This comprehensive whitepaper outlines Orca Capital's industry-leading security infrastructure, protocols, and best practices for protecting digital assets in the high-risk cryptocurrency environment. Our approach combines military-grade technological solutions with rigorous operational procedures, providing institutional-grade security for our clients' investments. This document serves both as a description of our internal security posture and as guidance for institutional and individual investors seeking to enhance their own cryptocurrency security practices.
        
        ## Threat Landscape Analysis
        
        The cryptocurrency sector faces an evolving array of sophisticated threats requiring specialized defenses:
        
        ### Attack Vectors in Digital Asset Security
        
        #### Technical Threats
        
        - Endpoint Compromise: Malware targeting private key extraction from devices
        - Network Surveillance: Man-in-the-middle attacks during transaction broadcasts
        - Smart Contract Vulnerabilities: Exploitation of code flaws in on-chain protocols
        - API Security Flaws: Unauthorized access via exchange or service provider integrations
        - Cryptographic Weaknesses: Implementation errors in signing algorithms or entropy generation
        
        #### Social Engineering Threats
        
        - Phishing Operations: Sophisticated impersonation of legitimate services
        - SIM Swapping: Telecom account takeovers for bypassing two-factor authentication
        - Business Email Compromise: Targeting financial decision-makers with fraudulent instructions
        - Insider Threats: Exploitation of privileged access by employees or contractors
        - Physical Security Breaches: Direct access to secure areas, devices, or recovery materials
        
        ## Core Security Architecture
        
        Orca Capital's security framework is built on a principle of defense-in-depth, with multiple independent layers of protection:
        
        ### Asset Storage Infrastructure
        
        #### Cold Storage System
        
        - Air-Gapped Hardware Management: 95% of assets stored in completely isolated environments
        - Geographic Distribution: Storage devices and backup materials distributed across six jurisdictions
        - Custom Firmware Validation: Specialized verification of hardware wallet firmware
        - Supply Chain Verification: Component-level authentication of all security devices
        - Tamper-Evident Packaging: Physical security measures for all hardware components
        
        ## Client Security Guidelines
        
        We provide comprehensive guidance for our clients to enhance their own security posture:
        
        ### Hardware Wallet Implementation
        
        #### Device Selection
        
        - Vendor Evaluation Criteria: Guidelines for selecting trustworthy manufacturers
        - Supply Chain Security: Verification of authentic hardware sources
        - Feature Comparison: Analysis of security capabilities across options
        - Firmware Update Policies: Evaluation of the vendor's security maintenance
        - Physical Security Features: Assessment of tamper-evident properties
        
        ## Conclusion
        
        Security in the cryptocurrency space requires a holistic approach combining technological solutions, operational procedures, and human factors. Orca Capital's comprehensive security framework represents the state of the art in digital asset protection, adapting continuously to address emerging threats while maintaining usability for legitimate operations.
        
        © 2025 Orca Capital. All rights reserved.
        `;
      } else if (filename.includes('developer-guide')) {
        title = "Developer Guide";
        content = `
        # Orca Capital Developer Guide
        
        ## Introduction
        
        Welcome to the Orca Capital Developer Guide. This comprehensive document is designed for developers looking to integrate with our platform or build applications leveraging our APIs, SDKs, and services. Throughout this guide, you'll find detailed documentation, practical code examples, and best practices for working with our financial technology stack.
        
        Our developer ecosystem aims to provide secure, scalable, and flexible tools for building innovative applications in the cryptocurrency and blockchain space. Whether you're creating a trading application, portfolio management tool, data analytics platform, or entirely new financial service, this guide will help you navigate our technical infrastructure and capabilities.
        
        ### Who This Guide Is For
        
        This documentation is primarily intended for:
        
        - Software developers integrating with Orca Capital APIs
        - FinTech teams building on our infrastructure
        - Technical partners in our ecosystem
        - Internal engineering teams
        - Academic researchers utilizing our data
        
        ### Technology Stack Overview
        
        The Orca Capital platform is built on a modern technology stack that includes:
        
        - Highly available RESTful APIs
        - Real-time WebSocket data streams
        - Secure authentication protocols
        - Data normalization services
        - Blockchain integration layers
        - AI-powered market analysis tools
        
        ## Getting Started
        
        ### Developer Account Setup
        
        To begin working with our platform, you'll need to create a developer account:
        
        1. Register at developers.orcacapital.com
        2. Complete the verification process
        3. Generate your API credentials
        4. Set up your development environment
        5. Explore our interactive documentation
        
        ## API Integration
        
        Step-by-step instructions for authenticating with our API, making requests, and handling responses. This section includes code examples in multiple programming languages.
        
        ## SDKs and Libraries
        
        Overview of our official software development kits (SDKs) for various programming languages, including installation instructions and basic usage patterns.
        
        ## Webhooks and Event Processing
        
        Guidelines for setting up webhooks to receive real-time notifications about relevant events on our platform, with examples of payload structures and verification procedures.
        
        ## Testing and Sandbox Environment
        
        Information about our testing environment, including test credentials and simulated data for development and integration testing.
        
        © 2025 Orca Capital. All rights reserved.
        `;
      } else if (filename.includes('api-reference')) {
        title = "API Reference";
        content = `
        # Orca Capital API Reference
        
        Version: 1.0
        Last Updated: March 2025
        Base URL: https://api.orcacapital.com/v1
        
        ## Overview
        
        The Orca Capital API provides programmatic access to cryptocurrency market data, trading capabilities, blockchain information, and AI-driven analytics. This reference documents all available endpoints, request/response formats, and authentication mechanisms.
        
        ## Authentication
        
        All API requests require authentication. We support two primary authentication methods:
        
        ### API Key Authentication
        
        Most API endpoints require API key authentication:
        
        1. Obtain API credentials from the developer portal
        2. Include headers with each request (API key, timestamp, signature)
        
        ### OAuth 2.0 Authentication
        
        For user-specific operations, we support OAuth 2.0 with the following grant types:
        
        - Authorization Code Flow
        - Client Credentials Flow
        - Refresh Token Flow
        
        ## API Endpoints
        
        ### Market Data API
        
        #### Get Latest Quotes
        
        Retrieves current price information for specified assets.
        
        ### Assets API
        
        #### List Assets
        
        Retrieves a list of supported assets.
        
        ### Trading API
        
        #### Get Account Balance
        
        Retrieves the account balances for all assets.
        
        ## Data Models
        
        ### Asset
        
        Represents a cryptocurrency or digital asset.
        
        ### Quote
        
        Represents current market data for an asset.
        
        ## Error Handling
        
        The API uses standard HTTP status codes and provides detailed error responses.
        
        ## Rate Limiting
        
        The API implements rate limiting to ensure fair usage.
        
        ## Versioning and Deprecation Policy
        
        Information about our API versioning strategy and how we handle deprecation of features to ensure backward compatibility.
        
        © 2025 Orca Capital. All rights reserved.
        `;
      }
      
      // Add content to PDF
      doc.fontSize(25)
         .text(title, 50, 160);
      
      doc.fontSize(12)
         .moveDown()
         .text('Orca Capital', { align: 'left' })
         .text('525 West 8th Avenue', { align: 'left' })
         .text('Vancouver, BC V5Z 1C6', { align: 'left' })
         .moveDown(2);
      
      // Add the main content
      doc.fontSize(12)
         .text(content, {
           paragraphGap: 10,
           align: 'left'
         });
      
      // Add footer
      doc.fontSize(10)
         .text(`© ${new Date().getFullYear()} Orca Capital. All rights reserved.`, 50, doc.page.height - 50, {
           align: 'center',
           width: doc.page.width - 100
         });
      
      // Finalize PDF
      doc.end();
    } catch (error) {
      console.error("Error generating PDF:", error);
      if (!res.headersSent) {
        res.status(500).json({ error: "Failed to generate PDF" });
      }
    }
  });

  // Route to serve translation files
  app.get('/locales/:lng/:ns.json', (req: Request, res: Response) => {
    const { lng, ns } = req.params;
    // Sanitize parameters to prevent directory traversal
    const language = lng.replace(/[^a-zA-Z]/g, '');
    const namespace = ns.replace(/[^a-zA-Z]/g, '');
    
    const filePath = path.join('public', 'locales', language, `${namespace}.json`);
    
    try {
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        return res.json(JSON.parse(fileContent));
      } else {
        console.log(`Translation file not found: ${filePath}`);
        return res.status(404).json({ error: 'Translation file not found' });
      }
    } catch (error) {
      console.error(`Error serving translation file: ${error}`);
      return res.status(500).json({ error: 'Error serving translation file' });
    }
  });
  
  const httpServer = createServer(app);
  return httpServer;
}