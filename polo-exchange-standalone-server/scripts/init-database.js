const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

/**
 * Database initialization script
 * Run this to set up the real referral system database
 */
async function initializeDatabase() {
  console.log('🗄️ Initializing Ghost Swap database...\n');

  // Load environment variables
  require('dotenv').config({ path: path.join(__dirname, '../.env.production') });

  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    console.log('Please set up your database connection in .env.production');
    process.exit(1);
  }

  const db = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    // Test connection
    console.log('🔌 Testing database connection...');
    await db.query('SELECT NOW()');
    console.log('✅ Database connection successful\n');

    // Read and execute schema
    console.log('📋 Creating database schema...');
    const schemaPath = path.join(__dirname, '../database/referral-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`📝 Executing ${statements.length} SQL statements...\n`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        await db.query(statement);
        
        // Log progress for major operations
        if (statement.includes('CREATE TABLE')) {
          const tableName = statement.match(/CREATE TABLE (\w+)/)?.[1];
          console.log(`✅ Created table: ${tableName}`);
        } else if (statement.includes('CREATE INDEX')) {
          const indexName = statement.match(/CREATE INDEX (\w+)/)?.[1];
          console.log(`✅ Created index: ${indexName}`);
        } else if (statement.includes('CREATE VIEW')) {
          const viewName = statement.match(/CREATE VIEW (\w+)/)?.[1];
          console.log(`✅ Created view: ${viewName}`);
        } else if (statement.includes('CREATE TRIGGER')) {
          const triggerName = statement.match(/CREATE TRIGGER (\w+)/)?.[1];
          console.log(`✅ Created trigger: ${triggerName}`);
        }
        
      } catch (error) {
        // Skip errors for objects that already exist
        if (error.message.includes('already exists')) {
          console.log(`⚠️ Skipped: ${error.message.split(':')[0]}`);
        } else {
          console.error(`❌ Error executing statement ${i + 1}:`, error.message);
          console.log('Statement:', statement.substring(0, 100) + '...');
        }
      }
    }

    console.log('\n🎉 Database initialization completed!\n');

    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    const tables = await db.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    console.log('📊 Created tables:');
    tables.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}`);
    });

    console.log('\n🚀 Your referral system is now ready for production!');
    console.log('\nNext steps:');
    console.log('1. Set REFERRAL_MODE=production in your .env file');
    console.log('2. Add your Solana private key for payouts');
    console.log('3. Start your server with the production environment');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    await db.end();
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase().catch(console.error);
}

module.exports = { initializeDatabase };
