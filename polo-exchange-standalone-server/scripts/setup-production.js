#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

/**
 * Interactive setup script for Ghost Swap production referral system
 */
class ProductionSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.envPath = path.join(__dirname, '../.env.production');
  }

  async run() {
    console.log('🚀 Ghost Swap Production Referral System Setup\n');
    console.log('This script will help you configure real SOL payouts and database connection.\n');

    try {
      await this.checkPrerequisites();
      await this.setupDatabase();
      await this.setupSolanaWallet();
      await this.updateEnvironment();
      await this.initializeDatabase();
      await this.showFinalInstructions();
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async checkPrerequisites() {
    console.log('📋 Checking prerequisites...\n');

    // Check if .env.production exists
    if (!fs.existsSync(this.envPath)) {
      throw new Error('.env.production file not found. Please create it first.');
    }

    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`✅ Node.js version: ${nodeVersion}`);

    // Check if required packages are installed
    const packageJson = require('../../package.json');
    const requiredPackages = ['pg', '@solana/web3.js', 'bs58'];
    
    for (const pkg of requiredPackages) {
      if (!packageJson.dependencies[pkg] && !packageJson.devDependencies[pkg]) {
        console.log(`⚠️ Missing package: ${pkg}`);
        console.log(`Run: npm install ${pkg}`);
      } else {
        console.log(`✅ Package installed: ${pkg}`);
      }
    }

    console.log('\n');
  }

  async setupDatabase() {
    console.log('🗄️ Database Setup\n');
    console.log('You need a PostgreSQL database for production.');
    console.log('Recommended providers:');
    console.log('- Neon (https://neon.tech) - Free tier available');
    console.log('- Supabase (https://supabase.com) - Free tier available');
    console.log('- Railway (https://railway.app) - Simple deployment');
    console.log('- Local PostgreSQL installation\n');

    const databaseUrl = await this.ask('Enter your PostgreSQL connection string: ');
    
    if (!databaseUrl.startsWith('postgresql://')) {
      throw new Error('Invalid PostgreSQL connection string format');
    }

    this.databaseUrl = databaseUrl;
    console.log('✅ Database URL configured\n');
  }

  async setupSolanaWallet() {
    console.log('💰 Solana Wallet Setup\n');
    console.log('You need a Solana wallet with SOL for automatic payouts.');
    console.log('This wallet will send SOL to users when they reach the minimum payout threshold.\n');

    console.log('⚠️ SECURITY WARNING:');
    console.log('- Keep your private key secure');
    console.log('- Use a dedicated wallet for payouts');
    console.log('- Fund it with enough SOL for payouts\n');

    const privateKey = await this.ask('Enter your Solana wallet private key (base58 format): ');
    
    if (!privateKey || privateKey.length < 80) {
      throw new Error('Invalid Solana private key format');
    }

    this.solanaPrivateKey = privateKey;
    console.log('✅ Solana wallet configured\n');
  }

  async updateEnvironment() {
    console.log('⚙️ Updating environment configuration...\n');

    // Read current .env file
    let envContent = fs.readFileSync(this.envPath, 'utf8');

    // Update database URL
    if (envContent.includes('DATABASE_URL=')) {
      envContent = envContent.replace(
        /DATABASE_URL=.*/,
        `DATABASE_URL=${this.databaseUrl}`
      );
    } else {
      envContent += `\nDATABASE_URL=${this.databaseUrl}`;
    }

    // Update Solana private key
    if (envContent.includes('PAYOUT_WALLET_PRIVATE_KEY=')) {
      envContent = envContent.replace(
        /PAYOUT_WALLET_PRIVATE_KEY=.*/,
        `PAYOUT_WALLET_PRIVATE_KEY=${this.solanaPrivateKey}`
      );
    } else {
      envContent += `\nPAYOUT_WALLET_PRIVATE_KEY=${this.solanaPrivateKey}`;
    }

    // Set production mode
    if (envContent.includes('REFERRAL_MODE=')) {
      envContent = envContent.replace(
        /REFERRAL_MODE=.*/,
        'REFERRAL_MODE=production'
      );
    } else {
      envContent += '\nREFERRAL_MODE=production';
    }

    // Write updated file
    fs.writeFileSync(this.envPath, envContent);
    console.log('✅ Environment variables updated\n');
  }

  async initializeDatabase() {
    console.log('🔧 Initializing database...\n');
    
    const shouldInit = await this.ask('Initialize database tables now? (y/n): ');
    
    if (shouldInit.toLowerCase() === 'y' || shouldInit.toLowerCase() === 'yes') {
      try {
        const { initializeDatabase } = require('./init-database.js');
        await initializeDatabase();
        console.log('✅ Database initialized successfully\n');
      } catch (error) {
        console.error('❌ Database initialization failed:', error.message);
        console.log('You can run it manually later with: node scripts/init-database.js\n');
      }
    } else {
      console.log('⚠️ Remember to run: node scripts/init-database.js\n');
    }
  }

  async showFinalInstructions() {
    console.log('🎉 Setup Complete!\n');
    console.log('Your Ghost Swap referral system is now configured for production.\n');
    
    console.log('📋 Next Steps:');
    console.log('1. Fund your Solana wallet with SOL for payouts');
    console.log('2. Start your server: npm run start:production');
    console.log('3. Test with a small referral to verify payouts work');
    console.log('4. Monitor the system with: GET /api/swap/monitoring\n');
    
    console.log('🔍 Verification:');
    console.log('- Check your database has the referral tables');
    console.log('- Verify your Solana wallet has sufficient SOL');
    console.log('- Test referral link creation and tracking\n');
    
    console.log('⚠️ Important:');
    console.log('- Keep your private keys secure');
    console.log('- Monitor SOL balance for payouts');
    console.log('- Set up alerts for failed transactions\n');
    
    console.log('🆘 Support:');
    console.log('- Check logs for any errors');
    console.log('- Use the monitoring dashboard');
    console.log('- Test with small amounts first\n');
  }

  async ask(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer.trim());
      });
    });
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new ProductionSetup();
  setup.run().catch(console.error);
}

module.exports = ProductionSetup;
