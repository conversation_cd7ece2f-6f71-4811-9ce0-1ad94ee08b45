# Server Configuration
PORT=3001
NODE_ENV=production
SERVER_URL=https://yourdomain.com

# Database (if using one)
DATABASE_URL=postgresql://username:password@localhost:5432/ghostswap

# API Keys
INFURA_API_KEY=your_infura_api_key_here
ALCHEMY_API_KEY=your_alchemy_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# Liquidity Provider API Keys (for real swaps without your own liquidity)
CHANGENOW_API_KEY=your_changenow_api_key_here
ONEINCH_API_KEY=your_1inch_api_key_here
PARASWAP_API_KEY=your_paraswap_api_key_here
KYBERSWAP_API_KEY=your_kyberswap_api_key_here

# RPC Endpoints
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
POLYGON_RPC_URL=https://polygon-rpc.com/
AVALANCHE_RPC_URL=https://api.avax.network/ext/bc/C/rpc
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
BITCOIN_RPC_URL=https://api.blockcypher.com/v1/btc/main

# Deposit Addresses (Your actual wallet addresses)
ETH_DEPOSIT_ADDRESS=0xYourEthereumAddressHere
BNB_DEPOSIT_ADDRESS=0xYourBSCAddressHere
MATIC_DEPOSIT_ADDRESS=0xYourPolygonAddressHere
AVAX_DEPOSIT_ADDRESS=0xYourAvalancheAddressHere
SOL_DEPOSIT_ADDRESS=YourSolanaAddressHere
BTC_DEPOSIT_ADDRESS=YourBitcoinAddressHere

# Private Keys (KEEP THESE SECURE!)
# WARNING: Never commit these to version control
ETH_PRIVATE_KEY=0xYourEthereumPrivateKeyHere
TREASURY_PRIVATE_KEY=0xYourTreasuryPrivateKeyHere
TREASURY_ADDRESS=0xYourTreasuryAddressHere

# Ultra-Fast Swap Contract Addresses (populated after deployment)
GHOST_SWAP_INSTANT_1=""      # Ethereum
GHOST_SWAP_INSTANT_137=""    # Polygon
GHOST_SWAP_INSTANT_56=""     # BSC
GHOST_SWAP_INSTANT_42161=""  # Arbitrum
GHOST_SWAP_INSTANT_10=""     # Optimism
GHOST_SWAP_INSTANT_43114=""  # Avalanche

# Bridge Aggregator Contract Addresses
BRIDGE_AGGREGATOR_1=""       # Ethereum
BRIDGE_AGGREGATOR_137=""     # Polygon
BRIDGE_AGGREGATOR_56=""      # BSC
BRIDGE_AGGREGATOR_42161=""   # Arbitrum
BRIDGE_AGGREGATOR_10=""      # Optimism
BRIDGE_AGGREGATOR_43114=""   # Avalanche

# Speed Tier Configuration
LIGHTNING_TIER_ENABLED=true
LIGHTNING_TIER_FEE_RATE=0.06
FAST_TIER_ENABLED=true
FAST_TIER_FEE_RATE=0.04
ECONOMY_TIER_ENABLED=true
ECONOMY_TIER_FEE_RATE=0.02

# Liquidity Management
INITIAL_LIQUIDITY_ETHEREUM=50000000
INITIAL_LIQUIDITY_POLYGON=30000000
INITIAL_LIQUIDITY_BSC=40000000
INITIAL_LIQUIDITY_ARBITRUM=20000000
INITIAL_LIQUIDITY_OPTIMISM=15000000
INITIAL_LIQUIDITY_AVALANCHE=15000000

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_UPDATE_INTERVAL=30000
REBALANCING_ENABLED=true
REBALANCING_INTERVAL=300000

# Security
MAX_TRANSACTION_SIZE=1000000
MIN_TRANSACTION_SIZE=10
RATE_LIMIT_SWAPS_PER_MINUTE=10
RATE_LIMIT_QUOTES_PER_MINUTE=100

# Bridge Protocol Addresses (PRODUCTION READY)
LAYERZERO_V2_ENDPOINT_1=******************************************
LAYERZERO_V2_ENDPOINT_137=******************************************
LAYERZERO_V2_ENDPOINT_56=******************************************
LAYERZERO_V2_ENDPOINT_42161=******************************************
LAYERZERO_V2_ENDPOINT_10=******************************************
LAYERZERO_V2_ENDPOINT_43114=******************************************

HYPERLANE_MAILBOX_1=******************************************
HYPERLANE_MAILBOX_137=******************************************
HYPERLANE_MAILBOX_56=******************************************
HYPERLANE_MAILBOX_42161=******************************************
HYPERLANE_MAILBOX_10=******************************************
HYPERLANE_MAILBOX_43114=******************************************

CHAINLINK_CCIP_ROUTER_1=0x80226fc0Ee2b096224EeAc085Bb9a8cba1146f7D
CHAINLINK_CCIP_ROUTER_137=0x849c5ED5a80F5B408Dd4969b78c2C8fdf0565Bfe
CHAINLINK_CCIP_ROUTER_56=0x34B03Cb9086d7D758AC55af71584F81A598759FE
CHAINLINK_CCIP_ROUTER_42161=0x141fa059441E0ca23ce184B6A78bafD2A517DdE8
CHAINLINK_CCIP_ROUTER_10=0x261c05167db67B2b619f9d312e0753f3721ad6E8
CHAINLINK_CCIP_ROUTER_43114=0xF4c7E640EdA248ef95972845a62bdC74237805dB

# Flashbots Configuration (PRODUCTION READY)
FLASHBOTS_AUTH_KEY=0x0000000000000000000000000000000000000000000000000000000000000000
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
BNB_PRIVATE_KEY=0xYourBSCPrivateKeyHere
MATIC_PRIVATE_KEY=0xYourPolygonPrivateKeyHere
AVAX_PRIVATE_KEY=0xYourAvalanchePrivateKeyHere
SOL_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]
BTC_PRIVATE_KEY=YourBitcoinPrivateKeyHere

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Webhook URLs (for external notifications)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Monitoring & Alerts
SENTRY_DSN=your_sentry_dsn_here
DATADOG_API_KEY=your_datadog_api_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Swap Configuration
DEFAULT_FEE_RATE=0.03
MIN_SWAP_AMOUNT_USD=20
MAX_SWAP_AMOUNT_USD=49000
SLIPPAGE_TOLERANCE=0.01

# Liquidity Management
AUTO_REBALANCE_ENABLED=true
REBALANCE_THRESHOLD=0.1
EMERGENCY_STOP_ENABLED=false

# Backup & Recovery
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# ===== REFERRAL SYSTEM CONFIGURATION =====

# Referral Database
REFERRAL_DATABASE_URL=postgresql://username:password@localhost:5432/ghostswap_referrals

# SOL Payout Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOL_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]
PAYOUT_WALLET_ADDRESS=4av41YfRcsKPY4zKxoE9vC5ZofRLvHE7dS9CUKzTHtwj

# Referral Program Settings
REFERRAL_COMMISSION_RATE=0.01
MIN_PAYOUT_AMOUNT=10.00
PAYOUT_SCHEDULE_CRON=0 * * * *
MAX_GAS_PRICE_GWEI=50

# Notification Webhooks
DISCORD_PAYOUT_WEBHOOK=https://discord.com/api/webhooks/YOUR_PAYOUT_WEBHOOK
SLACK_PAYOUT_WEBHOOK=https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK

# Referral Analytics
REFERRAL_ANALYTICS_ENABLED=true
REFERRAL_TRACKING_ENABLED=true
REFERRAL_LINK_DOMAIN=https://ghostswap.io

# Security & Rate Limiting
REFERRAL_API_RATE_LIMIT=100
REFERRAL_JWT_SECRET=your-referral-jwt-secret
REFERRAL_ENCRYPTION_KEY=your-referral-encryption-key
