# 🚀 Ghost Swap Production Referral System Setup

## Overview

This guide will help you transition from the **development mode** (browser storage + fake payouts) to **production mode** (real database + actual SOL payouts).

## 🎯 What Changes in Production

### Development Mode (Current)
- ✅ Referral links work locally
- ✅ All features visible for testing
- ❌ Data stored in browser only
- ❌ No real SOL payouts
- ❌ Data lost when browser cleared

### Production Mode (After Setup)
- ✅ Real PostgreSQL database
- ✅ Actual SOL payouts to users
- ✅ Persistent data across devices
- ✅ Automatic commission tracking
- ✅ Real money transactions

## 🛠 Quick Setup (Automated)

### Option 1: Interactive Setup Script
```bash
cd server
npm run setup:production
```

This will guide you through:
1. Database configuration
2. Solana wallet setup
3. Environment variables
4. Database initialization

### Option 2: Manual Setup

#### Step 1: Set Up Database
Get a PostgreSQL database from:
- **Neon** (https://neon.tech) - Free tier
- **Supabase** (https://supabase.com) - Free tier  
- **Railway** (https://railway.app) - Simple deployment

#### Step 2: Configure Environment
Edit `server/.env.production`:

```bash
# Database
DATABASE_URL=********************************/ghostswap

# Solana Wallet for Payouts
PAYOUT_WALLET_PRIVATE_KEY=your_base58_private_key_here

# Enable Production Mode
REFERRAL_MODE=production
```

#### Step 3: Initialize Database
```bash
npm run init:database
```

#### Step 4: Start Production Server
```bash
npm run build
npm run start:production
```

## 💰 Solana Wallet Setup

### 1. Create Dedicated Payout Wallet
```bash
# Using Solana CLI
solana-keygen new --outfile payout-wallet.json

# Or use Phantom/Solflare and export private key
```

### 2. Fund the Wallet
- Transfer SOL for payouts (recommend starting with 10-50 SOL)
- Monitor balance regularly
- Set up alerts when balance gets low

### 3. Security Best Practices
- ✅ Use a dedicated wallet only for payouts
- ✅ Keep private key secure and encrypted
- ✅ Regular balance monitoring
- ✅ Backup private key securely
- ❌ Never share private key
- ❌ Don't use your personal wallet

## 🗄️ Database Requirements

### Minimum Specifications
- **PostgreSQL 12+**
- **1GB RAM** (minimum)
- **10GB Storage** (for growth)
- **SSL Connection** (recommended)

### Recommended Providers

#### Neon (Recommended for Beginners)
```bash
# Free tier includes:
- 0.5 GB storage
- 1 database
- SSL connection
- Easy setup
```

#### Supabase (Full-featured)
```bash
# Free tier includes:
- 500MB database
- Real-time features
- Built-in auth
- Dashboard
```

#### Railway (Simple Deployment)
```bash
# Pay-as-you-go
- Easy deployment
- Automatic scaling
- Built-in monitoring
```

## ⚙️ Configuration Options

### Referral Settings
```bash
# Minimum payout (USD)
MIN_PAYOUT_AMOUNT=25.00

# Commission rate (1% = 0.01)
REFERRAL_COMMISSION_RATE=0.01

# Signup bonus (USD)
SIGNUP_BONUS_AMOUNT=10.00
```

### Security Settings
```bash
# JWT secret for authentication
JWT_SECRET=your_super_secure_secret_here

# Encryption key (32 characters)
ENCRYPTION_KEY=your_32_character_encryption_key
```

## 🔍 Testing Production Setup

### 1. Verify Database Connection
```bash
# Check if tables were created
curl http://localhost:5001/api/swap/monitoring
```

### 2. Test Referral Creation
```bash
# Create test referral account
# Use your frontend or API directly
```

### 3. Test Small Payout
```bash
# Make a small swap to trigger commission
# Verify SOL payout occurs
```

## 📊 Monitoring & Maintenance

### Real-Time Monitoring
```bash
# System health dashboard
GET /api/swap/monitoring

# Referral statistics
GET /api/referral/stats

# Database status
GET /api/health
```

### Daily Tasks
- ✅ Check SOL wallet balance
- ✅ Monitor failed transactions
- ✅ Review payout logs
- ✅ Check system health

### Weekly Tasks
- ✅ Database backup
- ✅ Performance review
- ✅ Security audit
- ✅ Balance reconciliation

## 🚨 Troubleshooting

### Common Issues

#### "Database connection failed"
```bash
# Check connection string format
DATABASE_URL=********************************/dbname

# Test connection manually
psql $DATABASE_URL
```

#### "SOL payout failed"
```bash
# Check wallet balance
solana balance your_wallet_address

# Verify private key format
# Should be base58 string, ~88 characters
```

#### "Referral not tracking"
```bash
# Check production mode
echo $REFERRAL_MODE  # Should be "production"

# Check database tables
SELECT COUNT(*) FROM referral_users;
```

### Log Locations
```bash
# Application logs
tail -f logs/app.log

# Database logs
tail -f logs/database.log

# Payout logs
tail -f logs/payouts.log
```

## 🔐 Security Checklist

### Before Going Live
- [ ] Database uses SSL connection
- [ ] Private keys are encrypted
- [ ] Environment variables are secure
- [ ] Backup procedures in place
- [ ] Monitoring alerts configured
- [ ] Rate limiting enabled
- [ ] CORS properly configured

### Ongoing Security
- [ ] Regular security updates
- [ ] Monitor for suspicious activity
- [ ] Audit payout transactions
- [ ] Backup database regularly
- [ ] Rotate secrets periodically

## 📞 Support

### If You Need Help
1. **Check logs** for error messages
2. **Test with small amounts** first
3. **Use monitoring dashboard** for system status
4. **Verify environment variables** are correct
5. **Check database connectivity**

### Emergency Procedures
- **Stop payouts**: Set `REFERRAL_MODE=development`
- **Database issues**: Check connection and restore from backup
- **Wallet compromised**: Generate new wallet and update config
- **System overload**: Scale database and server resources

## 🎉 Success Indicators

Your production system is working when:
- ✅ Users can create referral accounts
- ✅ Referral links track properly
- ✅ Commissions calculate correctly
- ✅ SOL payouts happen automatically
- ✅ Database stores all data persistently
- ✅ Monitoring shows healthy status

**Congratulations! Your Ghost Swap referral system is now live with real SOL payouts! 🚀**
