# Cross-Chain Swap Monitoring System

## Overview

The Ghost Swap monitoring system provides comprehensive real-time tracking for cross-chain transactions across 37+ blockchains. It addresses the critical challenge of monitoring tokens being sent to different chains by implementing a unified state machine with multi-chain monitoring capabilities.

## Architecture

### Core Components

1. **TransactionStateManager** - Central state machine for transaction lifecycle
2. **UnifiedSwapMonitor** - Orchestrates all chain monitors and system health
3. **BaseChainMonitor** - Abstract base class for blockchain-specific monitors
4. **EVMChainMonitor** - EVM-compatible chain monitoring (Ethereum, BSC, Polygon, etc.)
5. **Database Schema** - Comprehensive tracking and analytics storage

### Supported Networks

#### EVM Chains (Implemented)
- **Ethereum (ETH)** - 12s blocks, 12 confirmations
- **BNB Smart Chain (BNB)** - 3s blocks, 15 confirmations  
- **Polygon (MATIC)** - 2s blocks, 20 confirmations
- **Avalanche (AVAX)** - 2s blocks, 10 confirmations
- **Arbitrum (ARB)** - 1s blocks, 5 confirmations
- **Optimism (OP)** - 2s blocks, 10 confirmations
- **Hyperliquid (HYPE)** - 1s blocks, 5 confirmations

#### Non-EVM Chains (Planned)
- **Solana (SOL)** - 1s blocks, 32 confirmations
- **Bitcoin (BTC)** - 600s blocks, 6 confirmations
- **Kaspa (KAS)** - 1s blocks, 10 confirmations

## Transaction Lifecycle

### State Machine
```
initiated → deposit_confirmed → bridging → completed
    ↓              ↓              ↓
  failed         failed        stuck → recovery
```

### State Descriptions
- **initiated** - Transaction created, waiting for deposit
- **deposit_confirmed** - Deposit detected and verified
- **bridging** - Cross-chain bridge in progress
- **completed** - Transaction successfully completed
- **failed** - Transaction failed (with retry logic)
- **stuck** - Transaction timeout (requires intervention)

## Database Schema

### Core Tables

#### cross_chain_transactions
Primary transaction tracking with complete lifecycle data:
- Transaction IDs, user addresses, chain/token details
- Amount tracking (in/out/expected)
- Status and timestamp tracking
- Bridge protocol and address information
- Retry logic and error handling

#### chain_monitoring_status
Real-time chain health monitoring:
- Block processing status
- RPC endpoint management with failover
- Performance metrics and uptime tracking

#### monitoring_events
Event-driven transaction processing:
- Deposit detection, bridge events, delivery confirmation
- Gas usage and pricing data
- Flexible JSONB data storage

#### failed_transactions
Comprehensive failure recovery:
- Failure stage identification
- Recovery strategy assignment
- Priority-based queue management

## API Endpoints

### Monitoring Dashboard
```
GET /api/swap/monitoring
```

Returns comprehensive system status:
```json
{
  "success": true,
  "timestamp": "2024-01-15T10:30:00Z",
  "unified_monitoring": {
    "isRunning": true,
    "totalChains": 7,
    "activeChains": 7,
    "totalAlerts": 0,
    "chains": {
      "ETH": { "isMonitoring": true, "lastBlock": 18950000 },
      "BNB": { "isMonitoring": true, "lastBlock": 35200000 }
    }
  },
  "transactions": {
    "total": 1250,
    "completed": 1180,
    "failed": 15,
    "in_progress": 55
  },
  "system_health": {
    "health_percentage": "100.00"
  }
}
```

## Monitoring Features

### Real-Time Tracking
- **WebSocket Subscriptions** - Instant block and transaction notifications
- **Polling Fallback** - Automatic fallback when WebSocket unavailable
- **Multi-RPC Failover** - Automatic switching between RPC providers

### Alert System
- **Transaction Failures** - Immediate alerts for failed swaps
- **Chain Offline** - Notifications when chains become unresponsive
- **Bridge Delays** - Warnings for stuck transactions
- **Liquidity Issues** - Alerts for low liquidity pools

### Performance Monitoring
- **Block Processing** - Real-time block height tracking
- **Response Times** - RPC endpoint performance monitoring
- **Success Rates** - Transaction completion statistics
- **Gas Tracking** - Gas price and usage analytics

## Implementation Guide

### Starting the Monitoring System

```typescript
import { unifiedSwapMonitor } from './services/UnifiedSwapMonitor';

// Start monitoring all supported chains
await unifiedSwapMonitor.startMonitoring();

// Or start specific chains
await unifiedSwapMonitor.startMonitoring(['ETH', 'BNB', 'SOL']);
```

### Creating Monitored Transactions

```typescript
import { transactionStateManager } from './services/TransactionStateManager';

const transaction = await transactionStateManager.createTransaction({
  userAddress: '******************************************',
  fromChain: 'ETH',
  toChain: 'BNB', 
  fromToken: 'ETH',
  toToken: 'BNB',
  amountIn: 1.0,
  expectedAmountOut: 0.97,
  recipientAddress: '******************************************'
});

// Start monitoring
await unifiedSwapMonitor.monitorTransaction(transaction);
```

### State Change Callbacks

```typescript
transactionStateManager.onStateChange(txId, (transaction) => {
  console.log(`Transaction ${txId} status: ${transaction.status}`);
  
  if (transaction.status === 'completed') {
    // Handle successful completion
  } else if (transaction.status === 'failed') {
    // Handle failure and retry logic
  }
});
```

## Configuration

### Chain Configuration
Each chain requires specific configuration:

```typescript
const chainConfig = {
  chainId: 'ETH',
  name: 'Ethereum',
  networkType: 'evm',
  rpcEndpoints: [
    'https://eth-mainnet.g.alchemy.com/v2/your-api-key',
    'https://mainnet.infura.io/v3/your-api-key'
  ],
  websocketEndpoint: 'wss://eth-mainnet.g.alchemy.com/v2/your-api-key',
  blockTime: 12,
  confirmations: 12
};
```

### Environment Variables
```bash
# Database
DATABASE_URL=********************************/ghostswap

# RPC Endpoints
ETH_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/your-api-key
BNB_RPC_URL=https://bsc-dataseed1.binance.org
SOL_RPC_URL=https://go.getblock.io/657dc00c73c84600906bfb1c02953e32

# Deposit Addresses
ETH_DEPOSIT_ADDRESS=******************************************
BNB_DEPOSIT_ADDRESS=******************************************
SOL_DEPOSIT_ADDRESS=4av41YfRcsKPY4zKxoE9vC5ZofRLvHE7dS9CUKzTHtwj
```

## Testing

### Run Monitoring Test
```bash
cd server
npm run test:monitoring
```

The test script verifies:
- ✅ Unified monitoring startup
- ✅ Transaction creation and tracking
- ✅ State transitions
- ✅ Failure handling
- ✅ Statistics collection
- ✅ System cleanup

## Roadmap

### Phase 1: Core Infrastructure ✅
- [x] Transaction state machine
- [x] EVM chain monitoring
- [x] Database schema
- [x] Basic alerting

### Phase 2: Scale to All Chains 🚧
- [ ] Solana monitor implementation
- [ ] Bitcoin/UTXO monitor implementation
- [ ] Cosmos/IBC monitor implementation
- [ ] Bridge protocol monitoring

### Phase 3: Advanced Features 📋
- [ ] Predictive failure detection
- [ ] Automatic liquidity rebalancing
- [ ] Advanced analytics dashboard
- [ ] Mobile app notifications

## Troubleshooting

### Common Issues

1. **RPC Endpoint Failures**
   - Check endpoint URLs and API keys
   - Verify network connectivity
   - Monitor rate limits

2. **WebSocket Disconnections**
   - System automatically falls back to polling
   - Check firewall settings
   - Verify WebSocket endpoint URLs

3. **Transaction Stuck**
   - Check bridge protocol status
   - Verify gas prices and network congestion
   - Manual intervention may be required

### Monitoring Logs
```bash
# View real-time monitoring logs
tail -f logs/monitoring.log

# Check specific chain status
curl http://localhost:5001/api/swap/monitoring
```

## Security Considerations

- **Private Key Management** - Never log or expose private keys
- **RPC Security** - Use authenticated endpoints when possible
- **Rate Limiting** - Implement proper rate limiting for API calls
- **Database Security** - Encrypt sensitive transaction data
- **Alert Security** - Secure alert channels (Slack, Discord, etc.)

## Support

For issues or questions about the monitoring system:
1. Check the troubleshooting section
2. Review monitoring logs
3. Test with the provided test script
4. Contact the development team
