{"name": "ghost-swap-server", "version": "1.0.0", "description": "Real cryptocurrency swap backend for Ghost Swap", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run test", "setup:production": "node scripts/setup-production.js", "init:database": "node scripts/init-database.js", "start:production": "NODE_ENV=production node dist/index.js", "simple-server": "node simple-server.js"}, "dependencies": {"@solana/web3.js": "^1.87.6", "@types/cookie-parser": "^1.4.9", "@types/express-session": "^1.18.2", "@types/ioredis": "^4.28.10", "@types/pg": "^8.15.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bitcoin-core": "^4.2.0", "bs58": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^7.2.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "pg": "^8.16.0", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.14.2", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.23", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.19.1", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "keywords": ["cryptocurrency", "swap", "defi", "blockchain", "ethereum", "solana", "bitcoin"], "author": "Your Name", "license": "MIT"}