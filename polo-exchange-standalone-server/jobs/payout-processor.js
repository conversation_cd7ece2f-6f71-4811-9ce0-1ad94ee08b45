// Automated Commission Payout Processor
// Runs every hour to process pending SOL payouts

const cron = require('node-cron');
const { Pool } = require('pg');
const { Connection, Keypair, PublicKey, SystemProgram, Transaction, sendAndConfirmTransaction, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const axios = require('axios');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Solana configuration
const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com', 'confirmed');
const payoutKeypair = Keypair.fromSecretKey(
  new Uint8Array(JSON.parse(process.env.SOL_PRIVATE_KEY))
);

console.log(`💰 Payout wallet: ${payoutKeypair.publicKey.toString()}`);

// Minimum payout threshold
const MIN_PAYOUT_AMOUNT = 10.00; // $10 USD minimum (paid in SOL equivalent)

// Get current SOL price from CoinGecko
async function getSolPrice() {
  try {
    const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd');
    return response.data.solana.usd;
  } catch (error) {
    console.error('❌ Failed to get SOL price, using fallback:', error);
    return 150; // Fallback SOL price
  }
}

// Process all pending payouts
async function processPayouts() {
  console.log('🔄 Starting automated payout processing...');
  
  try {
    // Get current SOL price
    const solPrice = await getSolPrice();
    console.log(`📊 Current SOL price: $${solPrice}`);

    // Check SOL balance
    const balance = await connection.getBalance(payoutKeypair.publicKey);
    const balanceSol = balance / LAMPORTS_PER_SOL;
    const balanceUsd = balanceSol * solPrice;
    console.log(`💰 Payout wallet SOL balance: ${balanceSol.toFixed(4)} SOL ($${balanceUsd.toLocaleString()})`);

    // Get pending payouts
    const pendingPayouts = await pool.query(`
      SELECT 
        u.referral_code,
        u.wallet_address,
        u.payout_address,
        (u.total_commissions_earned - u.total_commissions_paid) as pending_amount,
        COUNT(s.id) as pending_swaps
      FROM referral_users u
      LEFT JOIN referral_swaps s ON s.referrer_code = u.referral_code AND s.commission_status = 'pending'
      WHERE u.payout_address IS NOT NULL 
        AND (u.total_commissions_earned - u.total_commissions_paid) >= $1
        AND u.status = 'active'
      GROUP BY u.referral_code, u.wallet_address, u.payout_address, u.total_commissions_earned, u.total_commissions_paid
      ORDER BY pending_amount DESC
    `, [MIN_PAYOUT_AMOUNT]);

    console.log(`📊 Found ${pendingPayouts.rows.length} users with pending payouts`);

    if (pendingPayouts.rows.length === 0) {
      console.log('✅ No payouts to process');
      return;
    }

    let totalPayoutAmount = 0;
    const results = [];

    for (const payout of pendingPayouts.rows) {
      totalPayoutAmount += parseFloat(payout.pending_amount);
    }

    // Check if we have enough SOL balance
    const totalPayoutSol = totalPayoutAmount / solPrice;
    if (balanceSol < totalPayoutSol) {
      console.error(`❌ Insufficient SOL balance. Need: ${totalPayoutSol.toFixed(4)} SOL ($${totalPayoutAmount}), Have: ${balanceSol.toFixed(4)} SOL ($${balanceUsd})`);

      // Send alert (implement your notification system)
      await sendLowBalanceAlert(totalPayoutAmount, balanceUsd, solPrice);
      return;
    }

    // Process each payout
    for (const payout of pendingPayouts.rows) {
      try {
        console.log(`💸 Processing payout: ${payout.referral_code} - $${payout.pending_amount}`);
        
        const result = await processSinglePayout(payout, solPrice);
        results.push(result);

        // Wait 5 seconds between transactions (Solana is faster than Ethereum)
        await new Promise(resolve => setTimeout(resolve, 5000));
        
      } catch (error) {
        console.error(`❌ Payout failed for ${payout.referral_code}:`, error.message);
        results.push({
          referralCode: payout.referral_code,
          amount: payout.pending_amount,
          status: 'failed',
          error: error.message
        });
      }
    }

    // Log results
    const successful = results.filter(r => r.status === 'completed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    
    console.log(`✅ Payout processing complete: ${successful} successful, ${failed} failed`);
    
    // Send summary report
    await sendPayoutSummary(results);

  } catch (error) {
    console.error('❌ Error in payout processing:', error);
    await sendErrorAlert(error);
  }
}

// Process a single payout
async function processSinglePayout(payout, solPrice) {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Calculate SOL amount from USD value
    const amountSol = parseFloat(payout.pending_amount) / solPrice;
    const lamports = Math.floor(amountSol * LAMPORTS_PER_SOL);

    console.log(`💸 Sending ${amountSol.toFixed(6)} SOL ($${payout.pending_amount}) to ${payout.payout_address}`);

    // Create Solana transaction
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: payoutKeypair.publicKey,
        toPubkey: new PublicKey(payout.payout_address),
        lamports: lamports,
      })
    );

    // Send SOL transaction
    const signature = await sendAndConfirmTransaction(connection, transaction, [payoutKeypair]);

    console.log(`📤 SOL transaction sent: ${signature}`);
    
    // Record payout in database
    await client.query(
      `INSERT INTO commission_payouts
       (referrer_code, payout_address, amount_usd, amount_sol, transaction_signature, payout_status, blockchain_network)
       VALUES ($1, $2, $3, $4, $5, 'completed', 'solana')`,
      [payout.referral_code, payout.payout_address, payout.pending_amount, amountSol, signature]
    );

    // Update user's paid commissions
    await client.query(
      `UPDATE referral_users 
       SET total_commissions_paid = total_commissions_paid + $1, updated_at = CURRENT_TIMESTAMP 
       WHERE referral_code = $2`,
      [payout.pending_amount, payout.referral_code]
    );

    // Mark related swaps as paid
    await client.query(
      `UPDATE referral_swaps 
       SET commission_status = 'paid' 
       WHERE referrer_code = $1 AND commission_status = 'pending'`,
      [payout.referral_code]
    );

    await client.query('COMMIT');

    console.log(`✅ SOL payout completed: ${payout.referral_code} - ${amountSol.toFixed(6)} SOL ($${payout.pending_amount})`);

    return {
      referralCode: payout.referral_code,
      amount: payout.pending_amount,
      amountSol: amountSol,
      signature: signature,
      status: 'completed'
    };

  } catch (error) {
    await client.query('ROLLBACK');
    
    // Record failed payout
    await pool.query(
      `INSERT INTO commission_payouts
       (referrer_code, payout_address, amount_usd, amount_sol, payout_status)
       VALUES ($1, $2, $3, $4, 'failed')`,
      [payout.referral_code, payout.payout_address, payout.pending_amount, 0]
    );
    
    throw error;
  } finally {
    client.release();
  }
}

// Send low balance alert
async function sendLowBalanceAlert(neededUsd, availableUsd, solPrice) {
  const neededSol = neededUsd / solPrice;
  const availableSol = availableUsd / solPrice;

  console.log(`🚨 LOW SOL BALANCE ALERT: Need ${neededSol.toFixed(4)} SOL ($${neededUsd}), Have ${availableSol.toFixed(4)} SOL ($${availableUsd})`);

  try {
    // Example: Send to Discord webhook
    if (process.env.DISCORD_WEBHOOK_URL) {
      await axios.post(process.env.DISCORD_WEBHOOK_URL, {
        content: `🚨 **Ghost Swap Payout Alert**\n\n**Low SOL Balance!**\nNeeded: ${neededSol.toFixed(4)} SOL ($${neededUsd.toLocaleString()})\nAvailable: ${availableSol.toFixed(4)} SOL ($${availableUsd.toLocaleString()})\nSOL Price: $${solPrice}\n\nPlease fund the payout wallet: ${payoutKeypair.publicKey.toString()}`
      });
    }
  } catch (error) {
    console.error('Failed to send low balance alert:', error);
  }
}

// Send payout summary
async function sendPayoutSummary(results) {
  const successful = results.filter(r => r.status === 'completed');
  const failed = results.filter(r => r.status === 'failed');
  const totalPaid = successful.reduce((sum, r) => sum + parseFloat(r.amount), 0);
  
  console.log(`📊 Payout Summary: ${successful.length} successful ($${totalPaid.toFixed(2)}), ${failed.length} failed`);
  
  try {
    if (process.env.DISCORD_WEBHOOK_URL && results.length > 0) {
      const totalSol = successful.reduce((sum, r) => sum + parseFloat(r.amountSol || 0), 0);
      await axios.post(process.env.DISCORD_WEBHOOK_URL, {
        content: `💰 **Ghost Swap Payout Summary**\n\n✅ Successful: ${successful.length} (${totalSol.toFixed(4)} SOL / $${totalPaid.toFixed(2)})\n❌ Failed: ${failed.length}\n\nTotal processed: ${results.length} payouts`
      });
    }
  } catch (error) {
    console.error('Failed to send payout summary:', error);
  }
}

// Send error alert
async function sendErrorAlert(error) {
  console.error('🚨 PAYOUT ERROR:', error.message);
  
  try {
    if (process.env.DISCORD_WEBHOOK_URL) {
      await axios.post(process.env.DISCORD_WEBHOOK_URL, {
        content: `🚨 **Ghost Swap Payout Error**\n\n${error.message}\n\nPlease check the payout system immediately.`
      });
    }
  } catch (alertError) {
    console.error('Failed to send error alert:', alertError);
  }
}

// Schedule payout processing every hour
cron.schedule('0 * * * *', () => {
  console.log('⏰ Scheduled payout processing started');
  processPayouts();
});

// Manual trigger for testing
if (process.argv.includes('--run-now')) {
  console.log('🔧 Manual payout processing triggered');
  processPayouts();
}

console.log('🚀 Payout processor started - running every hour');

module.exports = { processPayouts };
