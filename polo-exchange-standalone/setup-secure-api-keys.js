#!/usr/bin/env node

/**
 * Secure API Key Setup Script
 * Encrypts and stores all external API keys securely
 */

import { getApiKeyManager } from './src/security/ApiKeyManager.js';
import readline from 'readline';
import crypto from 'crypto';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Hide input for sensitive data
const hiddenQuestion = (query) => {
  return new Promise((resolve) => {
    const stdin = process.stdin;
    const stdout = process.stdout;
    
    stdout.write(query);
    stdin.setRawMode(true);
    stdin.resume();
    stdin.setEncoding('utf8');
    
    let input = '';
    stdin.on('data', (char) => {
      char = char + '';
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004': // Ctrl+D
          stdin.setRawMode(false);
          stdin.pause();
          stdout.write('\n');
          resolve(input);
          break;
        case '\u0003': // Ctrl+C
          process.exit();
          break;
        case '\u007f': // Backspace
          if (input.length > 0) {
            input = input.slice(0, -1);
            stdout.write('\b \b');
          }
          break;
        default:
          input += char;
          stdout.write('*');
          break;
      }
    });
  });
};

const question = (query) => {
  return new Promise((resolve) => {
    rl.question(query, resolve);
  });
};

async function setupSecureApiKeys() {
  console.log('🔐 MARKO POLO CAPITAL - SECURE API KEY SETUP');
  console.log('=' .repeat(60));
  console.log('This script will encrypt and securely store all your API keys.\n');

  try {
    // Generate or get master password
    console.log('🔑 Master Password Setup');
    console.log('This password encrypts all your API keys. Keep it VERY secure!\n');
    
    const masterPassword = await hiddenQuestion('Enter master password (or press Enter to generate): ');
    
    let finalMasterPassword;
    if (!masterPassword.trim()) {
      finalMasterPassword = crypto.randomBytes(32).toString('hex');
      console.log(`\n🎲 Generated master password: ${finalMasterPassword}`);
      console.log('⚠️  SAVE THIS PASSWORD SECURELY - YOU CANNOT RECOVER IT!');
      
      const confirm = await question('\nPress Enter to continue after saving the password...');
    } else {
      finalMasterPassword = masterPassword.trim();
      console.log('✅ Master password set');
    }

    // Initialize API Key Manager
    const apiKeyManager = getApiKeyManager(finalMasterPassword);
    console.log('\n🔐 API Key Manager initialized\n');

    // API Key configurations
    const apiConfigs = [
      {
        provider: 'lifi',
        description: 'LiFi Cross-Chain Liquidity Protocol',
        example: 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e',
        required: true,
        currentValue: process.env.LIFI_API_KEY
      },
      {
        provider: 'oneinch',
        description: '1inch DEX Aggregator',
        example: 'abc123def456...',
        required: false,
        currentValue: process.env.ONEINCH_API_KEY
      },
      {
        provider: 'changenow',
        description: 'ChangeNOW Cross-Chain Exchange',
        example: 'def789ghi012...',
        required: false,
        currentValue: process.env.CHANGENOW_API_KEY
      },
      {
        provider: 'paraswap',
        description: 'ParaSwap Multi-Chain DEX Aggregator',
        example: 'ghi345jkl678...',
        required: false,
        currentValue: process.env.PARASWAP_API_KEY
      },
      {
        provider: 'zerox',
        description: '0x Protocol Professional Liquidity',
        example: 'jkl901mno234...',
        required: false,
        currentValue: process.env.ZEROX_API_KEY
      }
    ];

    console.log('📝 API Key Configuration');
    console.log('Enter your API keys for each provider (or press Enter to skip):\n');

    for (const config of apiConfigs) {
      console.log(`\n🌐 ${config.description} (${config.provider})`);
      
      if (config.currentValue) {
        console.log(`   Current: ${config.currentValue.substring(0, 10)}...`);
        const useExisting = await question('   Use existing key? (y/n): ');
        
        if (useExisting.toLowerCase() === 'y') {
          await apiKeyManager.storeApiKey({
            provider: config.provider,
            key: config.currentValue,
            description: config.description
          });
          console.log(`   ✅ Existing ${config.provider} key encrypted and stored`);
          continue;
        }
      }
      
      console.log(`   Example: ${config.example}`);
      const apiKey = await hiddenQuestion(`   Enter ${config.provider} API key: `);
      
      if (apiKey.trim()) {
        // Validate API key format
        if (apiKeyManager.validateApiKeyFormat(config.provider, apiKey.trim())) {
          await apiKeyManager.storeApiKey({
            provider: config.provider,
            key: apiKey.trim(),
            description: config.description
          });
          console.log(`   ✅ ${config.provider} key encrypted and stored`);
        } else {
          console.log(`   ❌ Invalid API key format for ${config.provider}`);
          const retry = await question('   Retry? (y/n): ');
          if (retry.toLowerCase() === 'y') {
            // Retry logic could be added here
            console.log(`   ⏭️  Skipping ${config.provider} for now`);
          }
        }
      } else if (config.required) {
        console.log(`   ⚠️  ${config.provider} is required but no key provided`);
      } else {
        console.log(`   ⏭️  Skipping ${config.provider}`);
      }
    }

    // Summary
    console.log('\n📊 Setup Summary');
    console.log('=' .repeat(40));
    
    const storedProviders = apiKeyManager.getStoredProviders();
    console.log(`✅ Encrypted API keys: ${storedProviders.length}`);
    
    storedProviders.forEach(provider => {
      console.log(`   🔐 ${provider}`);
    });

    // Create backup
    console.log('\n💾 Creating backup...');
    const backupPath = await apiKeyManager.backupKeys();
    console.log(`✅ Backup created: ${backupPath}`);

    // Update environment configuration
    console.log('\n🔧 Environment Configuration');
    console.log('Add this to your .env file:');
    console.log(`MASTER_PASSWORD=${finalMasterPassword}`);
    console.log('USE_ENCRYPTED_KEYS=true');

    // Test API key retrieval
    console.log('\n🧪 Testing API key retrieval...');
    for (const provider of storedProviders) {
      const retrievedKey = await apiKeyManager.getApiKey(provider);
      if (retrievedKey) {
        console.log(`✅ ${provider}: Successfully retrieved and decrypted`);
      } else {
        console.log(`❌ ${provider}: Failed to retrieve`);
      }
    }

    console.log('\n🎉 Secure API Key Setup Complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Save your master password securely');
    console.log('2. Update your .env file with MASTER_PASSWORD');
    console.log('3. Remove plain text API keys from .env');
    console.log('4. Test your exchange with encrypted keys');
    console.log('5. Keep the backup file in a secure location');

    console.log('\n⚠️  Security Reminders:');
    console.log('• Never commit the master password to version control');
    console.log('• Store backups in a different secure location');
    console.log('• Rotate keys regularly for maximum security');
    console.log('• Monitor API key usage through the analytics dashboard');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Additional utility functions
async function listStoredKeys() {
  console.log('🔐 Stored Encrypted API Keys');
  console.log('=' .repeat(40));
  
  const apiKeyManager = getApiKeyManager();
  const providers = apiKeyManager.getStoredProviders();
  
  if (providers.length === 0) {
    console.log('No encrypted API keys found.');
    return;
  }

  const stats = apiKeyManager.getUsageStats();
  
  stats.forEach(stat => {
    console.log(`\n🌐 ${stat.provider}`);
    console.log(`   Created: ${stat.createdAt.toLocaleDateString()}`);
    console.log(`   Last used: ${stat.lastUsed ? stat.lastUsed.toLocaleDateString() : 'Never'}`);
    console.log(`   Age: ${stat.daysSinceCreated} days`);
  });
}

async function testApiKeys() {
  console.log('🧪 Testing API Key Decryption');
  console.log('=' .repeat(40));
  
  const apiKeyManager = getApiKeyManager();
  const providers = apiKeyManager.getStoredProviders();
  
  for (const provider of providers) {
    try {
      const key = await apiKeyManager.getApiKey(provider);
      if (key) {
        console.log(`✅ ${provider}: ${key.substring(0, 10)}...`);
      } else {
        console.log(`❌ ${provider}: Failed to decrypt`);
      }
    } catch (error) {
      console.log(`❌ ${provider}: Error - ${error.message}`);
    }
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'setup':
    setupSecureApiKeys();
    break;
  case 'list':
    listStoredKeys();
    break;
  case 'test':
    testApiKeys();
    break;
  default:
    console.log('🔐 Marko Polo Capital - API Key Security Manager');
    console.log('\nUsage:');
    console.log('  node setup-secure-api-keys.js setup  - Setup encrypted API keys');
    console.log('  node setup-secure-api-keys.js list   - List stored keys');
    console.log('  node setup-secure-api-keys.js test   - Test key decryption');
    break;
}

export { setupSecureApiKeys, listStoredKeys, testApiKeys };
