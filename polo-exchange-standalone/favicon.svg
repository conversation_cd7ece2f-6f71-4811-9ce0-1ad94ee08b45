<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Cyber gradient -->
    <linearGradient id="ghostGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#20c4cb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#9d55ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#20c4cb;stop-opacity:1" />
    </linearGradient>

    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Circuit pattern -->
    <pattern id="circuits" x="0" y="0" width="8" height="8" patternUnits="userSpaceOnUse">
      <rect width="8" height="8" fill="none"/>
      <path d="M0 4h8M4 0v8" stroke="#20c4cb" stroke-width="0.2" opacity="0.3"/>
      <circle cx="4" cy="4" r="0.5" fill="#9d55ff" opacity="0.6"/>
    </pattern>
  </defs>

  <!-- Dark background -->
  <rect width="32" height="32" fill="#080c15"/>

  <!-- Background glow - BIGGER -->
  <circle cx="16" cy="16" r="15" fill="url(#ghostGradient)" opacity="0.2" filter="url(#glow)"/>
  <circle cx="16" cy="16" r="12" fill="url(#ghostGradient)" opacity="0.1" filter="url(#glow)"/>

  <!-- Ghost body - BIGGER -->
  <path d="M16 3
           C10 3 5 8 5 14
           L5 24
           C5 25.5 6.5 27 8 27
           L9.5 27 C10 26.5 10.5 27 11 27
           L12.5 27 C13 26.5 13.5 27 14 27
           L14.5 27 C15 26.5 15.5 27 16 27
           C16.5 27 17 26.5 17.5 27
           L18 27 C18.5 27 19 26.5 19.5 27
           L21 27 C21.5 27 22 26.5 22.5 27
           L24 27 C25.5 27 27 25.5 27 24
           L27 14 C27 8 22 3 16 3 Z"
        fill="url(#ghostGradient)"
        filter="url(#glow)"
        opacity="0.95"/>

  <!-- Circuit overlay on ghost - BIGGER -->
  <path d="M16 3
           C10 3 5 8 5 14
           L5 24
           C5 25.5 6.5 27 8 27
           L9.5 27 C10 26.5 10.5 27 11 27
           L12.5 27 C13 26.5 13.5 27 14 27
           L14.5 27 C15 26.5 15.5 27 16 27
           C16.5 27 17 26.5 17.5 27
           L18 27 C18.5 27 19 26.5 19.5 27
           L21 27 C21.5 27 22 26.5 22.5 27
           L24 27 C25.5 27 27 25.5 27 24
           L27 14 C27 8 22 3 16 3 Z"
        fill="url(#circuits)"
        opacity="0.3"/>

  <!-- Ghost eyes - BIGGER -->
  <circle cx="12" cy="13" r="2.5" fill="#000" opacity="0.9"/>
  <circle cx="20" cy="13" r="2.5" fill="#000" opacity="0.9"/>

  <!-- Eye glow - BIGGER -->
  <circle cx="12" cy="13" r="1.5" fill="#20c4cb" opacity="0.9"/>
  <circle cx="20" cy="13" r="1.5" fill="#20c4cb" opacity="0.9"/>

  <!-- Cyber elements - BIGGER -->
  <rect x="2" y="12" width="6" height="0.5" fill="#20c4cb" opacity="0.8">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite"/>
  </rect>
  <rect x="24" y="14" width="6" height="0.5" fill="#9d55ff" opacity="0.8">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="1.5s" repeatCount="indefinite"/>
  </rect>

  <!-- Data nodes - BIGGER -->
  <circle cx="4" cy="6" r="1.2" fill="#20c4cb" opacity="0.9">
    <animate attributeName="r" values="0.8;1.8;0.8" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="28" cy="7" r="1.2" fill="#9d55ff" opacity="0.9">
    <animate attributeName="r" values="0.8;1.8;0.8" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="3" cy="25" r="1.2" fill="#20c4cb" opacity="0.9">
    <animate attributeName="r" values="0.8;1.8;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="29" cy="26" r="1.2" fill="#9d55ff" opacity="0.9">
    <animate attributeName="r" values="0.8;1.8;0.8" dur="1.8s" repeatCount="indefinite"/>
  </circle>

  <!-- Central power core - BIGGER -->
  <circle cx="16" cy="18" r="1.5" fill="none" stroke="#20c4cb" stroke-width="0.5" opacity="0.8">
    <animate attributeName="r" values="1;2.5;1" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.4;1;0.4" dur="4s" repeatCount="indefinite"/>
  </circle>
</svg>