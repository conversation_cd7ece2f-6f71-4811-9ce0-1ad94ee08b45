// Test script for instant liquidity providers
import { InstantLiquidityAggregator } from './src/services/InstantLiquidityAggregator.js';

async function testInstantLiquidity() {
  console.log('🧪 Testing Instant Liquidity Providers for Marko Polo Capital...\n');

  const aggregator = new InstantLiquidityAggregator();

  // Test 1: Get provider statistics
  console.log('📊 Provider Statistics:');
  const stats = aggregator.getProviderStats();
  console.log(`Total Providers: ${stats.totalProviders}`);
  console.log(`Enabled Providers: ${stats.enabledProviders}`);
  console.log(`Average Reliability: ${(stats.avgReliability * 100).toFixed(1)}%\n`);

  stats.providers.forEach(provider => {
    console.log(`${provider.enabled ? '✅' : '❌'} ${provider.name}:`);
    console.log(`   Reliability: ${(provider.reliability * 100).toFixed(1)}%`);
    console.log(`   Execution Time: ${Math.round(provider.avgExecutionTime / 1000)}s`);
    console.log(`   Fee Rate: ${provider.feeRate}%`);
    console.log(`   Supported Chains: ${provider.supportedChains}`);
    console.log(`   Supported Tokens: ${provider.supportedTokens}\n`);
  });

  // Test 2: Get quotes for common pairs
  const testPairs = [
    { from: 'SOL', to: 'USDC', amount: 1 },
    { from: 'ETH', to: 'USDT', amount: 0.1 },
    { from: 'BTC', to: 'ETH', amount: 0.01 }
  ];

  for (const pair of testPairs) {
    try {
      console.log(`🔍 Testing ${pair.amount} ${pair.from} → ${pair.to}...`);
      
      const quotes = await aggregator.getInstantQuotes(
        pair.from,
        pair.to,
        pair.amount
      );

      if (quotes.bestQuote) {
        console.log(`✅ Best Quote: ${quotes.bestQuote.provider}`);
        console.log(`   Rate: ${quotes.bestQuote.rate.toFixed(4)}`);
        console.log(`   Output: ${quotes.bestQuote.toAmount.toFixed(6)} ${pair.to}`);
        console.log(`   Fee: ${quotes.bestQuote.fee.toFixed(6)} ${pair.from}`);
        console.log(`   Execution Time: ${Math.round(quotes.bestQuote.executionTime / 1000)}s`);
        console.log(`   Confidence: ${(quotes.confidence * 100).toFixed(1)}%`);
        
        if (quotes.allQuotes.length > 1) {
          console.log(`   Alternatives: ${quotes.allQuotes.length - 1} other providers`);
          console.log(`   Savings vs worst: ${quotes.savings.toFixed(6)} ${pair.to}`);
        }
      } else {
        console.log(`❌ No quotes available for ${pair.from}/${pair.to}`);
      }
      
      console.log('');
    } catch (error) {
      console.error(`❌ Error testing ${pair.from}/${pair.to}:`, error.message);
      console.log('');
    }
  }

  // Test 3: LiFi specific test (if API key is configured)
  if (process.env.LIFI_API_KEY) {
    console.log('🌉 Testing LiFi Cross-Chain Integration...');
    try {
      const lifiQuotes = await aggregator.getInstantQuotes('ETH', 'BNB', 0.1);
      if (lifiQuotes.bestQuote && lifiQuotes.bestQuote.provider === 'lifi') {
        console.log('✅ LiFi integration working!');
        console.log(`   Cross-chain rate: ${lifiQuotes.bestQuote.rate.toFixed(4)}`);
        console.log(`   Execution time: ${Math.round(lifiQuotes.bestQuote.executionTime / 1000)}s`);
      } else {
        console.log('⚠️ LiFi not selected as best provider');
      }
    } catch (error) {
      console.error('❌ LiFi test failed:', error.message);
    }
    console.log('');
  } else {
    console.log('⚠️ LiFi API key not configured, skipping LiFi test\n');
  }

  console.log('🎉 Instant Liquidity Test Complete!');
  console.log('Your Marko Polo Capital exchange now has access to:');
  console.log('• Multiple liquidity providers');
  console.log('• Real-time quote comparison');
  console.log('• Cross-chain swap capabilities');
  console.log('• Instant execution options');
}

// Run the test
testInstantLiquidity().catch(console.error);
