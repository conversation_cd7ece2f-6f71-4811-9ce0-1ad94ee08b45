// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title MockLiFiDiamond
 * @dev Mock LiFi Diamond contract for testing
 */
contract MockLiFiDiamond {
    event SwapExecuted(address indexed token, uint256 amount);

    function swapTokens(address token, uint256 amount) external {
        emit SwapExecuted(token, amount);
    }

    // Fallback function to accept any call (simulates LiFi execution)
    fallback() external payable {
        // Simulate successful LiFi execution
    }

    receive() external payable {}
}
