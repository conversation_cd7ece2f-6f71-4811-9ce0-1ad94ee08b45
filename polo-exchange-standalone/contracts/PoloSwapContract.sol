// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title PoloSwapContract
 * @dev Automated swap contract for Polo Exchange with LiFi integration
 * Users deposit tokens, contract automatically executes swaps with 3% fee
 */
contract PoloSwapContract is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    // Events
    event SwapRequested(
        address indexed user,
        address indexed fromToken,
        address indexed toToken,
        uint256 amount,
        address recipientAddress,
        uint256 swapId
    );
    
    event SwapCompleted(
        uint256 indexed swapId,
        address indexed user,
        uint256 amountOut,
        uint256 feeCollected
    );
    
    event FeeCollected(address indexed token, uint256 amount);

    // Structs
    struct SwapRequest {
        address user;
        address fromToken;
        address toToken;
        uint256 amount;
        address recipientAddress;
        uint256 feeAmount;
        bool completed;
        uint256 timestamp;
    }

    // State variables
    mapping(uint256 => SwapRequest) public swapRequests;
    mapping(address => uint256) public collectedFees;
    uint256 public nextSwapId = 1;
    uint256 public constant FEE_RATE = 300; // 3% = 300 basis points
    uint256 public constant BASIS_POINTS = 10000;
    
    // LiFi integration
    address public lifiDiamond;
    address public feeCollector;
    
    // Supported tokens
    mapping(address => bool) public supportedTokens;
    
    constructor(address _lifiDiamond, address _feeCollector) Ownable(msg.sender) {
        lifiDiamond = _lifiDiamond;
        feeCollector = _feeCollector;
    }

    /**
     * @dev Add supported token
     */
    function addSupportedToken(address token) external onlyOwner {
        supportedTokens[token] = true;
    }

    /**
     * @dev Remove supported token
     */
    function removeSupportedToken(address token) external onlyOwner {
        supportedTokens[token] = false;
    }

    /**
     * @dev Request a swap - users call this to initiate swaps
     * @param fromToken Token to swap from
     * @param toToken Token to swap to
     * @param amount Amount to swap
     * @param recipientAddress Where to send the swapped tokens
     */
    function requestSwap(
        address fromToken,
        address toToken,
        uint256 amount,
        address recipientAddress
    ) external nonReentrant {
        require(supportedTokens[fromToken], "From token not supported");
        require(amount > 0, "Amount must be greater than 0");
        require(recipientAddress != address(0), "Invalid recipient address");

        // Calculate fee
        uint256 feeAmount = (amount * FEE_RATE) / BASIS_POINTS;
        uint256 swapAmount = amount - feeAmount;

        // Transfer tokens from user
        IERC20(fromToken).safeTransferFrom(msg.sender, address(this), amount);

        // Store swap request
        swapRequests[nextSwapId] = SwapRequest({
            user: msg.sender,
            fromToken: fromToken,
            toToken: toToken,
            amount: swapAmount,
            recipientAddress: recipientAddress,
            feeAmount: feeAmount,
            completed: false,
            timestamp: block.timestamp
        });

        // Collect fee
        collectedFees[fromToken] += feeAmount;

        emit SwapRequested(
            msg.sender,
            fromToken,
            toToken,
            swapAmount,
            recipientAddress,
            nextSwapId
        );

        emit FeeCollected(fromToken, feeAmount);

        nextSwapId++;
    }

    /**
     * @dev Execute swap via LiFi (called by backend service)
     * @param swapId The swap request ID
     * @param lifiCalldata Calldata for LiFi execution
     */
    function executeSwap(
        uint256 swapId,
        bytes calldata lifiCalldata
    ) external onlyOwner nonReentrant {
        SwapRequest storage swap = swapRequests[swapId];
        require(!swap.completed, "Swap already completed");
        require(swap.amount > 0, "Invalid swap request");

        // Approve LiFi to spend tokens
        IERC20(swap.fromToken).forceApprove(lifiDiamond, swap.amount);

        // Execute LiFi swap
        (bool success, ) = lifiDiamond.call(lifiCalldata);
        require(success, "LiFi swap failed");

        // Mark as completed
        swap.completed = true;

        emit SwapCompleted(swapId, swap.user, 0, swap.feeAmount); // amountOut will be updated by backend
    }

    /**
     * @dev Emergency function to send tokens directly (if LiFi fails)
     * @param swapId The swap request ID
     * @param amountOut Amount of output tokens to send
     */
    function emergencyComplete(
        uint256 swapId,
        uint256 amountOut
    ) external onlyOwner nonReentrant {
        SwapRequest storage swap = swapRequests[swapId];
        require(!swap.completed, "Swap already completed");

        // Send output tokens to recipient
        IERC20(swap.toToken).safeTransfer(swap.recipientAddress, amountOut);

        // Mark as completed
        swap.completed = true;

        emit SwapCompleted(swapId, swap.user, amountOut, swap.feeAmount);
    }

    /**
     * @dev Withdraw collected fees
     * @param token Token to withdraw fees for
     * @param amount Amount to withdraw
     */
    function withdrawFees(address token, uint256 amount) external onlyOwner {
        require(collectedFees[token] >= amount, "Insufficient fees collected");
        
        collectedFees[token] -= amount;
        IERC20(token).safeTransfer(feeCollector, amount);
    }

    /**
     * @dev Emergency withdraw function
     * @param token Token to withdraw
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(owner(), amount);
    }

    /**
     * @dev Update LiFi Diamond address
     * @param _lifiDiamond New LiFi Diamond address
     */
    function updateLiFiDiamond(address _lifiDiamond) external onlyOwner {
        lifiDiamond = _lifiDiamond;
    }

    /**
     * @dev Update fee collector address
     * @param _feeCollector New fee collector address
     */
    function updateFeeCollector(address _feeCollector) external onlyOwner {
        feeCollector = _feeCollector;
    }

    /**
     * @dev Get swap request details
     * @param swapId The swap request ID
     */
    function getSwapRequest(uint256 swapId) external view returns (SwapRequest memory) {
        return swapRequests[swapId];
    }

    /**
     * @dev Get collected fees for a token
     * @param token Token address
     */
    function getCollectedFees(address token) external view returns (uint256) {
        return collectedFees[token];
    }

    /**
     * @dev Check if token is supported
     * @param token Token address
     */
    function isTokenSupported(address token) external view returns (bool) {
        return supportedTokens[token];
    }

    // Receive ETH
    receive() external payable {}
}
