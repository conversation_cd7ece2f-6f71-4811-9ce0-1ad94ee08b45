#!/usr/bin/env node

/**
 * Multi-Chain Deployment Script for Polo Swap Contracts
 * Deploys automated swap contracts across all major blockchains
 */

const { ethers } = require('hardhat');
const fs = require('fs');
const path = require('path');

// Network configurations
const NETWORKS = {
  ethereum: {
    name: 'Ethereum Mainnet',
    chainId: 1,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.ETHEREUM_RPC_URL,
    gasPrice: '20000000000', // 20 gwei
  },
  bsc: {
    name: 'Binance Smart Chain',
    chainId: 56,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.BSC_RPC_URL,
    gasPrice: '5000000000', // 5 gwei
  },
  polygon: {
    name: 'Polygon',
    chainId: 137,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.POLYGON_RPC_URL,
    gasPrice: '30000000000', // 30 gwei
  },
  avalanche: {
    name: 'Avalanche',
    chainId: 43114,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.AVALANCHE_RPC_URL,
    gasPrice: '25000000000', // 25 gwei
  },
  arbitrum: {
    name: 'Arbitrum',
    chainId: 42161,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.ARBITRUM_RPC_URL,
    gasPrice: '100000000', // 0.1 gwei
  },
  optimism: {
    name: 'Optimism',
    chainId: 10,
    lifiDiamond: '******************************************',
    rpcUrl: process.env.OPTIMISM_RPC_URL,
    gasPrice: '1000000', // 0.001 gwei
  }
};

// Token addresses for each network
const TOKEN_ADDRESSES = {
  ethereum: {
    USDC: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    USDT: '******************************************',
    DAI: '******************************************',
    WETH: '******************************************',
    WBTC: '******************************************',
    UNI: '******************************************',
    LINK: '******************************************',
    AAVE: '******************************************',
  },
  bsc: {
    USDC: '******************************************',
    USDT: '******************************************',
    BUSD: '******************************************',
    WBNB: '******************************************',
    BTCB: '******************************************',
  },
  polygon: {
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WMATIC: '******************************************',
    WETH: '******************************************',
  },
  avalanche: {
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WAVAX: '******************************************',
    WETH: '******************************************',
  }
};

class PoloContractDeployer {
  constructor() {
    this.deployedContracts = {};
    this.deploymentSummary = [];
  }

  async deployToNetwork(networkName) {
    console.log(`\n🚀 Deploying to ${NETWORKS[networkName].name}...`);
    
    try {
      // Setup provider and signer
      const provider = new ethers.JsonRpcProvider(NETWORKS[networkName].rpcUrl);
      const wallet = new ethers.Wallet(process.env.DEPLOYER_PRIVATE_KEY, provider);
      
      console.log(`📍 Deployer address: ${wallet.address}`);
      
      // Check balance
      const balance = await provider.getBalance(wallet.address);
      console.log(`💰 Balance: ${ethers.formatEther(balance)} ETH`);
      
      if (balance === 0n) {
        throw new Error('Insufficient balance for deployment');
      }

      // Deploy PoloSwapContract
      const PoloSwapContract = await ethers.getContractFactory('PoloSwapContract', wallet);
      
      const feeCollector = wallet.address; // Use deployer as initial fee collector
      const lifiDiamond = NETWORKS[networkName].lifiDiamond;
      
      console.log(`📄 Deploying PoloSwapContract...`);
      console.log(`   LiFi Diamond: ${lifiDiamond}`);
      console.log(`   Fee Collector: ${feeCollector}`);
      
      const contract = await PoloSwapContract.deploy(lifiDiamond, feeCollector, {
        gasPrice: NETWORKS[networkName].gasPrice
      });
      
      await contract.waitForDeployment();
      const contractAddress = await contract.getAddress();
      
      console.log(`✅ PoloSwapContract deployed to: ${contractAddress}`);
      
      // Add supported tokens
      console.log(`🪙 Adding supported tokens...`);
      const tokens = TOKEN_ADDRESSES[networkName] || {};
      
      for (const [symbol, address] of Object.entries(tokens)) {
        try {
          const tx = await contract.addSupportedToken(address);
          await tx.wait();
          console.log(`   ✅ Added ${symbol}: ${address}`);
        } catch (error) {
          console.log(`   ❌ Failed to add ${symbol}: ${error.message}`);
        }
      }
      
      // Store deployment info
      this.deployedContracts[networkName] = {
        network: NETWORKS[networkName].name,
        chainId: NETWORKS[networkName].chainId,
        contractAddress,
        lifiDiamond,
        feeCollector,
        supportedTokens: tokens,
        deploymentTx: contract.deploymentTransaction()?.hash,
        timestamp: new Date().toISOString()
      };
      
      this.deploymentSummary.push({
        network: networkName,
        status: 'success',
        address: contractAddress,
        tokensAdded: Object.keys(tokens).length
      });
      
      console.log(`🎉 ${NETWORKS[networkName].name} deployment completed!`);
      
    } catch (error) {
      console.error(`❌ Deployment to ${networkName} failed:`, error.message);
      
      this.deploymentSummary.push({
        network: networkName,
        status: 'failed',
        error: error.message
      });
    }
  }

  async deployToAllNetworks() {
    console.log('🌐 Starting multi-chain deployment of Polo Swap Contracts...\n');
    
    const networks = Object.keys(NETWORKS);
    
    for (const network of networks) {
      await this.deployToNetwork(network);
      
      // Wait between deployments
      if (network !== networks[networks.length - 1]) {
        console.log('\n⏳ Waiting 10 seconds before next deployment...');
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }
    
    this.generateDeploymentReport();
  }

  generateDeploymentReport() {
    console.log('\n📊 DEPLOYMENT SUMMARY');
    console.log('='.repeat(50));
    
    this.deploymentSummary.forEach(deployment => {
      const status = deployment.status === 'success' ? '✅' : '❌';
      console.log(`${status} ${deployment.network.toUpperCase()}`);
      
      if (deployment.status === 'success') {
        console.log(`   Contract: ${deployment.address}`);
        console.log(`   Tokens: ${deployment.tokensAdded} added`);
      } else {
        console.log(`   Error: ${deployment.error}`);
      }
    });
    
    // Save deployment data
    const deploymentData = {
      timestamp: new Date().toISOString(),
      deployer: process.env.DEPLOYER_ADDRESS,
      contracts: this.deployedContracts,
      summary: this.deploymentSummary
    };
    
    const outputPath = path.join(__dirname, 'deployments.json');
    fs.writeFileSync(outputPath, JSON.stringify(deploymentData, null, 2));
    
    console.log(`\n💾 Deployment data saved to: ${outputPath}`);
    
    // Generate frontend config
    this.generateFrontendConfig();
  }

  generateFrontendConfig() {
    const frontendConfig = {
      contracts: {},
      networks: {}
    };
    
    Object.entries(this.deployedContracts).forEach(([network, data]) => {
      frontendConfig.contracts[network] = {
        address: data.contractAddress,
        chainId: data.chainId,
        supportedTokens: data.supportedTokens
      };
      
      frontendConfig.networks[data.chainId] = {
        name: data.network,
        contractAddress: data.contractAddress,
        lifiDiamond: data.lifiDiamond
      };
    });
    
    const configPath = path.join(__dirname, '../src/config/contracts.json');
    fs.writeFileSync(configPath, JSON.stringify(frontendConfig, null, 2));
    
    console.log(`🔧 Frontend config generated: ${configPath}`);
  }
}

// Main execution
async function main() {
  const deployer = new PoloContractDeployer();
  
  // Check if deploying to specific network
  const targetNetwork = process.argv[2];
  
  if (targetNetwork && NETWORKS[targetNetwork]) {
    await deployer.deployToNetwork(targetNetwork);
  } else {
    await deployer.deployToAllNetworks();
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { PoloContractDeployer, NETWORKS, TOKEN_ADDRESSES };
