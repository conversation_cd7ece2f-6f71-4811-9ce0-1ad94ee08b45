# 🚀 Cloudflare Pages Deployment Guide

## Quick Deploy to Cloudflare Pages

### Option 1: Automatic Deployment (Recommended)

1. **Install Wrangler CLI:**
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare:**
   ```bash
   npm run cf:login
   ```

3. **Create the project:**
   ```bash
   npm run cf:init
   ```

4. **Deploy to production:**
   ```bash
   npm run deploy:production
   ```

### Option 2: Manual Cloudflare Dashboard Deployment

1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Go to Cloudflare Dashboard:**
   - Visit: https://dash.cloudflare.com/
   - Navigate to "Pages" in the sidebar
   - Click "Create a project"

3. **Upload the build folder:**
   - Choose "Upload assets"
   - Upload the entire `dist/public` folder
   - Set project name: `polo-exchange`

4. **Configure build settings:**
   - Build command: `npm run build`
   - Build output directory: `dist/public`
   - Root directory: `/`

### Option 3: Git Integration (Best for continuous deployment)

1. **Push your code to GitHub/GitLab**

2. **Connect to Cloudflare Pages:**
   - Go to Cloudflare Dashboard > Pages
   - Click "Connect to Git"
   - Select your repository
   - Configure build settings:
     - Framework preset: `Vite`
     - Build command: `npm run build`
     - Build output directory: `dist/public`

3. **Environment Variables (Optional):**
   ```
   NODE_ENV=production
   VITE_APP_NAME=Marko Polo Capital
   ```

## 🌐 Your Live Website

After deployment, your Polo Exchange will be available at:
- **Production:** `https://polo-exchange.pages.dev`
- **Custom domain:** Configure in Cloudflare Pages settings

## 🔧 Features Included

✅ **Security Headers** - XSS protection, CSRF protection
✅ **SPA Routing** - Client-side routing support
✅ **Asset Caching** - Optimized caching for performance
✅ **SSL/HTTPS** - Automatic SSL certificates
✅ **Global CDN** - Fast loading worldwide
✅ **Auto-scaling** - Handles traffic spikes automatically

## 🎯 What You'll See Live

- **Complete Polo Exchange interface**
- **Live price checking** for all cryptocurrencies
- **"Waiting for deposit" flow** after Execute Swap
- **Real-time transaction monitoring**
- **Professional dark theme** with animations
- **Mobile-responsive design**
- **50+ cryptocurrency support**

## 🚀 Quick Commands

```bash
# Deploy to production
npm run deploy:production

# Deploy to staging
npm run deploy:staging

# Preview locally
npm run preview

# Build only
npm run build
```

## 🔗 Useful Links

- **Cloudflare Pages Docs:** https://developers.cloudflare.com/pages/
- **Wrangler CLI Docs:** https://developers.cloudflare.com/workers/wrangler/
- **Your Dashboard:** https://dash.cloudflare.com/

---

**🎉 Your Polo Exchange will be live on the internet with professional hosting!**
