/**
 * Polo Exchange Orchestrator
 * Main service that coordinates all automated exchange operations
 */

import { EventEmitter } from 'events';
import AutomatedWalletManager, { WalletSetupConfig } from './AutomatedWalletManager';
import DepositMonitoringService, { SwapRequest } from './DepositMonitoringService';
import AutomaticSwapExecutor from './AutomaticSwapExecutor';
import AutomaticPayoutService from './AutomaticPayoutService';
import TrustWalletLiFiService from './TrustWalletLiFiService';

export interface ExchangeConfig {
  walletSetup: WalletSetupConfig;
  feeRate: number; // 0.03 = 3%
  autoStart: boolean;
  monitoring: {
    enabled: boolean;
    pollInterval: number;
  };
  execution: {
    enabled: boolean;
    autoExecute: boolean;
  };
  payouts: {
    enabled: boolean;
    batchSize: number;
  };
}

export interface ExchangeStatus {
  isInitialized: boolean;
  isRunning: boolean;
  walletStatus: any;
  monitoringStatus: any;
  executionStats: any;
  payoutStats: any;
  totalSwapsProcessed: number;
  totalVolumeUSD: string;
  uptime: number; // milliseconds
}

export interface SwapRequestData {
  fromCurrency: string;
  toCurrency: string;
  amount: string;
  userReceiveAddress: string;
  userEmail?: string;
  referralCode?: string;
}

export class PoloExchangeOrchestrator extends EventEmitter {
  private walletManager: AutomatedWalletManager;
  private depositMonitor: DepositMonitoringService;
  private swapExecutor: AutomaticSwapExecutor;
  private payoutService: AutomaticPayoutService;
  private lifiService: TrustWalletLiFiService;
  
  private config: ExchangeConfig;
  private isInitialized = false;
  private isRunning = false;
  private startTime: Date | null = null;
  private swapCounter = 0;

  constructor(config: ExchangeConfig) {
    super();
    this.config = config;
    
    // Initialize services
    this.lifiService = new TrustWalletLiFiService();
    this.walletManager = new AutomatedWalletManager();
    this.depositMonitor = new DepositMonitoringService(this.walletManager);
    this.swapExecutor = new AutomaticSwapExecutor(this.lifiService, this.walletManager);
    this.payoutService = new AutomaticPayoutService(this.walletManager);
    
    console.log('🎯 Polo Exchange Orchestrator initialized');
    this.setupEventListeners();
  }

  /**
   * Initialize the complete exchange system
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing Polo Exchange...');
      
      // Step 1: Setup wallet
      console.log('1️⃣ Setting up automated wallet...');
      const walletStatus = await this.walletManager.setupWallet(this.config.walletSetup);
      console.log(`✅ Wallet ready with ${walletStatus.totalAddresses} addresses`);
      
      // Step 2: Configure services
      console.log('2️⃣ Configuring services...');
      this.depositMonitor.updateConfig({
        enabled: this.config.monitoring.enabled,
        pollInterval: this.config.monitoring.pollInterval,
        confirmationsRequired: {},
        rpcEndpoints: {}
      });
      
      this.swapExecutor.updateConfig({
        feeRate: this.config.feeRate,
        enabled: this.config.execution.enabled,
        autoExecute: this.config.execution.autoExecute,
        maxSlippage: 0.05,
        maxRetries: 3,
        retryDelay: 5000
      });
      
      this.payoutService.updateConfig({
        enabled: this.config.payouts.enabled,
        batchSize: this.config.payouts.batchSize,
        maxRetries: 3,
        retryDelay: 10000,
        gasLimits: {},
        gasPrices: {},
        confirmationsRequired: {}
      });
      
      // Step 3: Connect services
      console.log('3️⃣ Connecting services...');
      this.swapExecutor.setupDepositListener(this.depositMonitor);
      this.payoutService.setupSwapExecutorListener(this.swapExecutor);
      
      this.isInitialized = true;
      console.log('✅ Polo Exchange initialized successfully!');
      
      this.emit('exchange_initialized');
      
      if (this.config.autoStart) {
        await this.start();
      }
      
    } catch (error) {
      console.error('❌ Failed to initialize Polo Exchange:', error);
      throw error;
    }
  }

  /**
   * Start the exchange operations
   */
  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Exchange not initialized');
    }
    
    if (this.isRunning) {
      console.log('⚠️ Exchange already running');
      return;
    }

    try {
      console.log('🚀 Starting Polo Exchange operations...');
      
      // Start monitoring
      if (this.config.monitoring.enabled) {
        await this.depositMonitor.startMonitoring();
        console.log('👁️ Deposit monitoring started');
      }
      
      // Enable automatic execution
      if (this.config.execution.enabled) {
        this.swapExecutor.setAutoExecute(this.config.execution.autoExecute);
        console.log('🤖 Automatic swap execution enabled');
      }
      
      // Enable automatic payouts
      if (this.config.payouts.enabled) {
        this.payoutService.setEnabled(true);
        console.log('💸 Automatic payouts enabled');
      }
      
      this.isRunning = true;
      this.startTime = new Date();
      
      console.log('✅ Polo Exchange is now running!');
      console.log('🎯 Ready to process swaps automatically');
      
      this.emit('exchange_started');
      
    } catch (error) {
      console.error('❌ Failed to start Polo Exchange:', error);
      throw error;
    }
  }

  /**
   * Stop the exchange operations
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️ Exchange not running');
      return;
    }

    try {
      console.log('🛑 Stopping Polo Exchange operations...');
      
      // Stop monitoring
      await this.depositMonitor.stopMonitoring();
      console.log('🔴 Deposit monitoring stopped');
      
      // Disable automatic execution
      this.swapExecutor.setAutoExecute(false);
      console.log('🔴 Automatic swap execution disabled');
      
      // Disable automatic payouts
      this.payoutService.setEnabled(false);
      console.log('🔴 Automatic payouts disabled');
      
      this.isRunning = false;
      
      console.log('✅ Polo Exchange stopped');
      this.emit('exchange_stopped');
      
    } catch (error) {
      console.error('❌ Failed to stop Polo Exchange:', error);
      throw error;
    }
  }

  /**
   * Create new swap request
   */
  async createSwapRequest(swapData: SwapRequestData): Promise<{
    swapRequest: SwapRequest;
    depositAddress: string;
    estimatedOutput: string;
  }> {
    if (!this.isInitialized) {
      throw new Error('Exchange not initialized');
    }

    console.log(`🔄 Creating swap request: ${swapData.amount} ${swapData.fromCurrency} → ${swapData.toCurrency}`);
    
    // Get deposit address
    const depositAddress = this.walletManager.getDepositAddress(swapData.fromCurrency);
    if (!depositAddress) {
      throw new Error(`Unsupported currency: ${swapData.fromCurrency}`);
    }
    
    // Get LiFi quote for estimation
    const quote = await this.lifiService.getLiFiQuote(
      swapData.fromCurrency,
      swapData.toCurrency,
      swapData.amount
    );
    
    // Create swap request
    const swapRequest: SwapRequest = {
      id: `swap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromCurrency: swapData.fromCurrency,
      toCurrency: swapData.toCurrency,
      amount: swapData.amount,
      userReceiveAddress: swapData.userReceiveAddress,
      expectedAmount: quote.toAmount,
      status: 'pending',
      createdAt: new Date()
    };
    
    // Register with deposit monitor
    this.depositMonitor.registerSwapRequest(swapRequest);
    
    console.log(`✅ Swap request created: ${swapRequest.id}`);
    console.log(`📍 Deposit to: ${depositAddress}`);
    console.log(`💰 Expected output: ${quote.toAmount} ${swapData.toCurrency}`);
    
    this.emit('swap_request_created', swapRequest);
    
    return {
      swapRequest,
      depositAddress,
      estimatedOutput: quote.toAmount
    };
  }

  /**
   * Get exchange status
   */
  async getStatus(): Promise<ExchangeStatus> {
    const walletStatus = await this.walletManager.getWalletStatus();
    const monitoringStatus = this.depositMonitor.getMonitoringStatus();
    const executionStats = this.swapExecutor.getExecutionStats();
    const payoutStats = this.payoutService.getStats();
    
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      walletStatus,
      monitoringStatus,
      executionStats,
      payoutStats,
      totalSwapsProcessed: this.swapCounter,
      totalVolumeUSD: '0.0', // TODO: Calculate from completed swaps
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0
    };
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return this.walletManager.getSupportedCurrencies();
  }

  /**
   * Get deposit addresses
   */
  getDepositAddresses(): Record<string, string> {
    return this.walletManager.getAllDepositAddresses();
  }

  /**
   * Test the complete system
   */
  async testSystem(): Promise<boolean> {
    try {
      console.log('🧪 Testing complete Polo Exchange system...');
      
      // Test wallet
      const walletTest = await this.walletManager.testWallet();
      if (!walletTest) {
        console.log('❌ Wallet test failed');
        return false;
      }
      
      // Test LiFi connection
      try {
        const testQuote = await this.lifiService.getLiFiQuote('ETH', 'USDC', '1');
        console.log('✅ LiFi connection test passed');
      } catch (error) {
        console.log('⚠️ LiFi connection test failed:', error);
      }
      
      // Test monitoring status
      const monitoringStatus = this.depositMonitor.getMonitoringStatus();
      console.log(`📊 Monitoring status: ${monitoringStatus.isActive ? 'Active' : 'Inactive'}`);
      
      console.log('✅ System test completed successfully!');
      return true;
      
    } catch (error) {
      console.error('❌ System test failed:', error);
      return false;
    }
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Swap execution events
    this.swapExecutor.on('execution_completed', (execution) => {
      this.swapCounter++;
      console.log(`🎉 Swap completed: ${execution.id}`);
      this.emit('swap_completed', execution);
    });
    
    this.swapExecutor.on('execution_failed', (execution) => {
      console.log(`❌ Swap failed: ${execution.id}`);
      this.emit('swap_failed', execution);
    });
    
    // Payout events
    this.payoutService.on('payout_completed', (payout) => {
      console.log(`💸 Payout completed: ${payout.id}`);
      this.emit('payout_completed', payout);
    });
    
    // Deposit events
    this.depositMonitor.on('deposit_confirmed', (deposit) => {
      console.log(`💰 Deposit confirmed: ${deposit.txHash}`);
      this.emit('deposit_confirmed', deposit);
    });
    
    // Error handling
    this.depositMonitor.on('monitoring_error', (error) => {
      console.error('❌ Monitoring error:', error);
      this.emit('system_error', error);
    });
  }

  /**
   * Generate configuration for environment variables
   */
  generateEnvConfig(): string {
    if (!this.isInitialized) {
      throw new Error('Exchange not initialized');
    }
    
    return this.walletManager.generateEnvConfig();
  }

  /**
   * Update exchange configuration
   */
  updateConfig(newConfig: Partial<ExchangeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Exchange configuration updated');
  }
}

export default PoloExchangeOrchestrator;
