/**
 * Automatic Swap Executor
 * Handles automated LiFi swap execution when deposits are detected
 */

import { EventEmitter } from 'events';
import TrustWalletLiFiService from './TrustWalletLiFiService';
import DepositMonitoringService, { DepositEvent, SwapRequest } from './DepositMonitoringService';
import AutomatedWalletManager from './AutomatedWalletManager';

export interface SwapExecution {
  id: string;
  depositTxHash: string;
  swapTxHash?: string;
  fromCurrency: string;
  toCurrency: string;
  amountIn: string;
  amountOut?: string;
  feeDeducted: string;
  userReceiveAddress: string;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  error?: string;
  startTime: Date;
  completionTime?: Date;
  lifiRoute?: any;
}

export interface ExecutorConfig {
  feeRate: number; // 0.03 = 3%
  maxSlippage: number; // 0.05 = 5%
  maxRetries: number;
  retryDelay: number; // milliseconds
  enabled: boolean;
  autoExecute: boolean;
}

export class AutomaticSwapExecutor extends EventEmitter {
  private lifiService: TrustWalletLiFiService;
  private walletManager: AutomatedWalletManager;
  private config: ExecutorConfig;
  private activeExecutions: Map<string, SwapExecution> = new Map();
  private executionQueue: SwapExecution[] = [];
  private isProcessing = false;

  constructor(
    lifiService: TrustWalletLiFiService,
    walletManager: AutomatedWalletManager
  ) {
    super();
    this.lifiService = lifiService;
    this.walletManager = walletManager;
    
    this.config = {
      feeRate: 0.03, // 3%
      maxSlippage: 0.05, // 5%
      maxRetries: 3,
      retryDelay: 5000, // 5 seconds
      enabled: true,
      autoExecute: true
    };

    console.log('🤖 Automatic Swap Executor initialized');
    console.log(`💰 Fee rate: ${this.config.feeRate * 100}%`);
  }

  /**
   * Setup automatic execution when deposits are detected
   */
  setupDepositListener(depositMonitor: DepositMonitoringService): void {
    console.log('🔗 Setting up deposit listener...');
    
    depositMonitor.on('swap_deposit_received', async ({ deposit, swapRequest }) => {
      if (this.config.autoExecute) {
        await this.executeSwapFromDeposit(deposit, swapRequest);
      }
    });
    
    console.log('✅ Deposit listener configured');
  }

  /**
   * Execute swap from detected deposit
   */
  async executeSwapFromDeposit(deposit: DepositEvent, swapRequest: SwapRequest): Promise<SwapExecution> {
    console.log(`🚀 Executing swap from deposit: ${deposit.txHash}`);
    
    const execution: SwapExecution = {
      id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      depositTxHash: deposit.txHash,
      fromCurrency: deposit.currency,
      toCurrency: swapRequest.toCurrency,
      amountIn: deposit.amount,
      feeDeducted: this.calculateFee(deposit.amount),
      userReceiveAddress: swapRequest.userReceiveAddress,
      status: 'pending',
      startTime: new Date()
    };
    
    this.activeExecutions.set(execution.id, execution);
    this.executionQueue.push(execution);
    
    this.emit('execution_started', execution);
    
    if (!this.isProcessing) {
      this.processExecutionQueue();
    }
    
    return execution;
  }

  /**
   * Process execution queue
   */
  private async processExecutionQueue(): Promise<void> {
    if (this.isProcessing || this.executionQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`⚙️ Processing ${this.executionQueue.length} swap executions...`);
    
    while (this.executionQueue.length > 0) {
      const execution = this.executionQueue.shift()!;
      await this.executeSwap(execution);
    }
    
    this.isProcessing = false;
    console.log('✅ Execution queue processed');
  }

  /**
   * Execute individual swap
   */
  private async executeSwap(execution: SwapExecution): Promise<void> {
    try {
      console.log(`🔄 Executing swap: ${execution.id}`);
      console.log(`💱 ${execution.amountIn} ${execution.fromCurrency} → ${execution.toCurrency}`);
      
      execution.status = 'executing';
      this.emit('execution_progress', execution);
      
      // Calculate amount after fee
      const amountAfterFee = this.calculateAmountAfterFee(execution.amountIn);
      
      // Get LiFi quote
      console.log('📊 Getting LiFi quote...');
      const quote = await this.lifiService.getLiFiQuote(
        execution.fromCurrency,
        execution.toCurrency,
        amountAfterFee
      );
      
      execution.lifiRoute = quote;
      execution.amountOut = quote.toAmount;
      
      console.log(`💰 Expected output: ${execution.amountOut} ${execution.toCurrency}`);
      
      // Execute the swap
      console.log('🚀 Executing LiFi swap...');
      const swapResult = await this.lifiService.executeSwap({
        fromToken: execution.fromCurrency,
        toToken: execution.toCurrency,
        amount: amountAfterFee,
        userReceiveAddress: execution.userReceiveAddress,
        fromChain: undefined,
        toChain: undefined
      });
      
      if (swapResult.success) {
        execution.status = 'completed';
        execution.swapTxHash = swapResult.txHash;
        execution.completionTime = new Date();
        execution.amountOut = swapResult.amountOut;
        
        console.log(`✅ Swap completed: ${execution.id}`);
        console.log(`📍 TX: ${execution.swapTxHash}`);
        console.log(`💰 Output: ${execution.amountOut} ${execution.toCurrency}`);
        
        this.emit('execution_completed', execution);
        
        // Trigger automatic payout
        await this.triggerAutomaticPayout(execution);
        
      } else {
        throw new Error(swapResult.error || 'Swap execution failed');
      }
      
    } catch (error) {
      console.error(`❌ Swap execution failed: ${execution.id}`, error);
      
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.completionTime = new Date();
      
      this.emit('execution_failed', execution);
      
      // Retry logic
      await this.handleExecutionRetry(execution);
    }
  }

  /**
   * Handle execution retry
   */
  private async handleExecutionRetry(execution: SwapExecution): Promise<void> {
    const retryCount = (execution as any).retryCount || 0;
    
    if (retryCount < this.config.maxRetries) {
      console.log(`🔄 Retrying swap execution: ${execution.id} (attempt ${retryCount + 1}/${this.config.maxRetries})`);
      
      (execution as any).retryCount = retryCount + 1;
      execution.status = 'pending';
      execution.error = undefined;
      
      // Add back to queue with delay
      setTimeout(() => {
        this.executionQueue.push(execution);
        if (!this.isProcessing) {
          this.processExecutionQueue();
        }
      }, this.config.retryDelay);
      
    } else {
      console.error(`❌ Max retries exceeded for swap: ${execution.id}`);
      this.emit('execution_max_retries_exceeded', execution);
    }
  }

  /**
   * Trigger automatic payout to user
   */
  private async triggerAutomaticPayout(execution: SwapExecution): Promise<void> {
    console.log(`💸 Triggering automatic payout for: ${execution.id}`);
    
    // This would integrate with your automated payout system
    // For now, just emit an event
    this.emit('payout_required', {
      executionId: execution.id,
      currency: execution.toCurrency,
      amount: execution.amountOut,
      toAddress: execution.userReceiveAddress,
      txHash: execution.swapTxHash
    });
    
    console.log(`✅ Payout triggered: ${execution.amountOut} ${execution.toCurrency} → ${execution.userReceiveAddress}`);
  }

  /**
   * Calculate fee amount
   */
  private calculateFee(amount: string): string {
    const amountNum = parseFloat(amount);
    const feeAmount = amountNum * this.config.feeRate;
    return feeAmount.toString();
  }

  /**
   * Calculate amount after fee deduction
   */
  private calculateAmountAfterFee(amount: string): string {
    const amountNum = parseFloat(amount);
    const amountAfterFee = amountNum * (1 - this.config.feeRate);
    return amountAfterFee.toString();
  }

  /**
   * Get execution status
   */
  getExecution(id: string): SwapExecution | undefined {
    return this.activeExecutions.get(id);
  }

  /**
   * Get all active executions
   */
  getActiveExecutions(): SwapExecution[] {
    return Array.from(this.activeExecutions.values());
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    total: number;
    pending: number;
    executing: number;
    completed: number;
    failed: number;
    queueLength: number;
  } {
    const executions = this.getActiveExecutions();
    
    return {
      total: executions.length,
      pending: executions.filter(e => e.status === 'pending').length,
      executing: executions.filter(e => e.status === 'executing').length,
      completed: executions.filter(e => e.status === 'completed').length,
      failed: executions.filter(e => e.status === 'failed').length,
      queueLength: this.executionQueue.length
    };
  }

  /**
   * Update executor configuration
   */
  updateConfig(newConfig: Partial<ExecutorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Executor configuration updated');
    console.log(`💰 New fee rate: ${this.config.feeRate * 100}%`);
  }

  /**
   * Enable/disable automatic execution
   */
  setAutoExecute(enabled: boolean): void {
    this.config.autoExecute = enabled;
    console.log(`🤖 Auto-execution ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Manual swap execution (for testing)
   */
  async executeManualSwap(
    fromCurrency: string,
    toCurrency: string,
    amount: string,
    userReceiveAddress: string
  ): Promise<SwapExecution> {
    console.log(`🔧 Manual swap execution: ${amount} ${fromCurrency} → ${toCurrency}`);
    
    const execution: SwapExecution = {
      id: `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      depositTxHash: 'manual_execution',
      fromCurrency,
      toCurrency,
      amountIn: amount,
      feeDeducted: this.calculateFee(amount),
      userReceiveAddress,
      status: 'pending',
      startTime: new Date()
    };
    
    this.activeExecutions.set(execution.id, execution);
    this.executionQueue.push(execution);
    
    if (!this.isProcessing) {
      this.processExecutionQueue();
    }
    
    return execution;
  }

  /**
   * Clear completed executions (cleanup)
   */
  clearCompletedExecutions(): number {
    const completed = Array.from(this.activeExecutions.entries())
      .filter(([_, execution]) => execution.status === 'completed' || execution.status === 'failed');
    
    for (const [id, _] of completed) {
      this.activeExecutions.delete(id);
    }
    
    console.log(`🧹 Cleared ${completed.length} completed executions`);
    return completed.length;
  }
}

export default AutomaticSwapExecutor;
