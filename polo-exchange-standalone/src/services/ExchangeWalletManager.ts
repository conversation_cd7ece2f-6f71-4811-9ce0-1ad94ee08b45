/**
 * Exchange Wallet Manager
 * Secure management of operational wallets for Polo Exchange
 * Handles multi-chain wallet operations for LiFi swap execution
 */

import { ethers } from 'ethers';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import * as crypto from 'crypto';

export interface WalletInfo {
  address: string;
  privateKey: string;
  publicKey?: string;
  network: string;
  chainId?: number;
}

export interface ChainConfig {
  name: string;
  chainId: number;
  rpcUrl: string;
  nativeCurrency: string;
  type: 'evm' | 'solana' | 'bitcoin' | 'other';
}

export interface SwapWalletConfig {
  fromWallet: WalletInfo;
  toWallet: WalletInfo;
  fromChain: ChainConfig;
  toChain: ChainConfig;
}

export class ExchangeWalletManager {
  private wallets: Map<string, WalletInfo> = new Map();
  private chainConfigs: Map<string, ChainConfig> = new Map();
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.WALLET_ENCRYPTION_PASSWORD || 'default_key_change_this';
    this.initializeChainConfigs();
    this.loadWalletsFromEnv();
    console.log('🔐 Exchange Wallet Manager initialized');
  }

  /**
   * Initialize supported blockchain configurations
   */
  private initializeChainConfigs() {
    const chains: ChainConfig[] = [
      {
        name: 'ethereum',
        chainId: 1,
        rpcUrl: process.env.ETHEREUM_RPC_URL || 'https://mainnet.infura.io/v3/********************************',
        nativeCurrency: 'ETH',
        type: 'evm'
      },
      {
        name: 'bsc',
        chainId: 56,
        rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org/',
        nativeCurrency: 'BNB',
        type: 'evm'
      },
      {
        name: 'polygon',
        chainId: 137,
        rpcUrl: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com/',
        nativeCurrency: 'MATIC',
        type: 'evm'
      },
      {
        name: 'avalanche',
        chainId: 43114,
        rpcUrl: process.env.AVALANCHE_RPC_URL || 'https://api.avax.network/ext/bc/C/rpc',
        nativeCurrency: 'AVAX',
        type: 'evm'
      },
      {
        name: 'arbitrum',
        chainId: 42161,
        rpcUrl: process.env.ARBITRUM_RPC_URL || 'https://arb1.arbitrum.io/rpc',
        nativeCurrency: 'ETH',
        type: 'evm'
      },
      {
        name: 'optimism',
        chainId: 10,
        rpcUrl: process.env.OPTIMISM_RPC_URL || 'https://mainnet.optimism.io',
        nativeCurrency: 'ETH',
        type: 'evm'
      },
      {
        name: 'solana',
        chainId: 101,
        rpcUrl: process.env.SOLANA_RPC_URL || 'https://go.getblock.io/657dc00c73c84600906bfb1c02953e32',
        nativeCurrency: 'SOL',
        type: 'solana'
      },
      {
        name: 'hyperliquid',
        chainId: 999,
        rpcUrl: process.env.HYPERLIQUID_RPC_URL || 'https://rpc.hyperliquid.xyz/evm',
        nativeCurrency: 'HYPE',
        type: 'evm'
      }
    ];

    chains.forEach(chain => {
      this.chainConfigs.set(chain.name, chain);
    });

    console.log(`🌐 Loaded ${chains.length} blockchain configurations`);
  }

  /**
   * Load wallet configurations from environment variables
   */
  private loadWalletsFromEnv() {
    const walletConfigs = [
      { network: 'ethereum', privateKeyEnv: 'ETHEREUM_PRIVATE_KEY', addressEnv: 'ETHEREUM_ADDRESS' },
      { network: 'bsc', privateKeyEnv: 'BSC_PRIVATE_KEY', addressEnv: 'BSC_ADDRESS' },
      { network: 'polygon', privateKeyEnv: 'POLYGON_PRIVATE_KEY', addressEnv: 'POLYGON_ADDRESS' },
      { network: 'avalanche', privateKeyEnv: 'AVALANCHE_PRIVATE_KEY', addressEnv: 'AVALANCHE_ADDRESS' },
      { network: 'arbitrum', privateKeyEnv: 'ARBITRUM_PRIVATE_KEY', addressEnv: 'ARBITRUM_ADDRESS' },
      { network: 'optimism', privateKeyEnv: 'OPTIMISM_PRIVATE_KEY', addressEnv: 'OPTIMISM_ADDRESS' },
      { network: 'solana', privateKeyEnv: 'SOLANA_PRIVATE_KEY', addressEnv: 'SOLANA_ADDRESS' },
    ];

    walletConfigs.forEach(config => {
      const privateKey = process.env[config.privateKeyEnv];
      let address = process.env[config.addressEnv];

      if (privateKey) {
        try {
          // For EVM chains, derive address from private key if not provided
          if (!address && config.network !== 'solana') {
            const wallet = new ethers.Wallet(privateKey);
            address = wallet.address;
          }

          // For Solana, derive address from private key if not provided
          if (!address && config.network === 'solana') {
            const keypair = Keypair.fromSecretKey(Buffer.from(privateKey, 'base64'));
            address = keypair.publicKey.toString();
          }

          if (address) {
            const walletInfo: WalletInfo = {
              address,
              privateKey,
              network: config.network,
              chainId: this.chainConfigs.get(config.network)?.chainId
            };

            this.wallets.set(config.network, walletInfo);
            console.log(`✅ Loaded ${config.network} wallet: ${address}`);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to load ${config.network} wallet:`, error.message);
        }
      }
    });

    console.log(`🔑 Loaded ${this.wallets.size} operational wallets`);
  }

  /**
   * Get wallet for specific network
   */
  getWallet(network: string): WalletInfo | null {
    return this.wallets.get(network.toLowerCase()) || null;
  }

  /**
   * Get all available wallets
   */
  getAllWallets(): Map<string, WalletInfo> {
    return new Map(this.wallets);
  }

  /**
   * Get chain configuration
   */
  getChainConfig(network: string): ChainConfig | null {
    return this.chainConfigs.get(network.toLowerCase()) || null;
  }

  /**
   * Get wallet configuration for a swap
   */
  getSwapWalletConfig(fromChain: string, toChain: string): SwapWalletConfig | null {
    const fromWallet = this.getWallet(fromChain);
    const toWallet = this.getWallet(toChain);
    const fromChainConfig = this.getChainConfig(fromChain);
    const toChainConfig = this.getChainConfig(toChain);

    if (!fromWallet || !toWallet || !fromChainConfig || !toChainConfig) {
      console.error(`❌ Missing wallet or chain config for ${fromChain} → ${toChain}`);
      return null;
    }

    return {
      fromWallet,
      toWallet,
      fromChain: fromChainConfig,
      toChain: toChainConfig
    };
  }

  /**
   * Create ethers wallet instance for EVM chains
   */
  createEthersWallet(network: string): ethers.Wallet | null {
    const walletInfo = this.getWallet(network);
    const chainConfig = this.getChainConfig(network);

    if (!walletInfo || !chainConfig || chainConfig.type !== 'evm') {
      return null;
    }

    try {
      const provider = new ethers.JsonRpcProvider(chainConfig.rpcUrl);
      return new ethers.Wallet(walletInfo.privateKey, provider);
    } catch (error) {
      console.error(`❌ Failed to create ethers wallet for ${network}:`, error);
      return null;
    }
  }

  /**
   * Create Solana keypair instance
   */
  createSolanaKeypair(): Keypair | null {
    const walletInfo = this.getWallet('solana');

    if (!walletInfo) {
      return null;
    }

    try {
      return Keypair.fromSecretKey(Buffer.from(walletInfo.privateKey, 'base64'));
    } catch (error) {
      console.error('❌ Failed to create Solana keypair:', error);
      return null;
    }
  }

  /**
   * Get deposit address for a specific currency
   */
  getDepositAddress(currency: string): string | null {
    // Map currencies to their respective networks
    const currencyToNetwork: Record<string, string> = {
      // Ethereum and ERC20 tokens
      'ETH': 'ethereum',
      'USDC': 'ethereum',
      'USDT': 'ethereum',
      'DAI': 'ethereum',
      'LINK': 'ethereum',
      'UNI': 'ethereum',
      'CRV': 'ethereum',
      'AAVE': 'ethereum',
      'COMP': 'ethereum',
      'MKR': 'ethereum',
      'WBTC': 'ethereum',

      // BSC tokens
      'BNB': 'bsc',
      'BUSD': 'bsc',

      // Polygon tokens
      'MATIC': 'polygon',

      // Avalanche tokens
      'AVAX': 'avalanche',

      // Solana tokens
      'SOL': 'solana',
      'HYPE': 'solana',

      // Other networks
      'BTC': 'bitcoin',
      'LTC': 'litecoin',
      'ADA': 'cardano',
      'TON': 'ton',
      'TAO': 'bittensor',
      'XMR': 'monero'
    };

    const network = currencyToNetwork[currency.toUpperCase()];
    if (!network) {
      console.warn(`⚠️ No network mapping for currency: ${currency}`);
      return null;
    }

    const wallet = this.getWallet(network);
    return wallet?.address || null;
  }

  /**
   * Validate wallet configuration
   */
  validateWalletConfig(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check if we have wallets for major networks
    const requiredNetworks = ['ethereum', 'bsc', 'polygon', 'solana'];
    
    requiredNetworks.forEach(network => {
      const wallet = this.getWallet(network);
      if (!wallet) {
        issues.push(`Missing wallet configuration for ${network}`);
      } else {
        // Validate address format
        if (network === 'solana') {
          try {
            new PublicKey(wallet.address);
          } catch {
            issues.push(`Invalid Solana address format: ${wallet.address}`);
          }
        } else {
          if (!ethers.isAddress(wallet.address)) {
            issues.push(`Invalid ${network} address format: ${wallet.address}`);
          }
        }
      }
    });

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get wallet status summary
   */
  getWalletStatus(): {
    totalWallets: number;
    configuredNetworks: string[];
    missingNetworks: string[];
    validationIssues: string[];
  } {
    const validation = this.validateWalletConfig();
    const configuredNetworks = Array.from(this.wallets.keys());
    const allNetworks = Array.from(this.chainConfigs.keys());
    const missingNetworks = allNetworks.filter(network => !configuredNetworks.includes(network));

    return {
      totalWallets: this.wallets.size,
      configuredNetworks,
      missingNetworks,
      validationIssues: validation.issues
    };
  }
}

export default ExchangeWalletManager;
