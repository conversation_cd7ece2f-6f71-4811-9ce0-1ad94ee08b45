import fs from 'fs';
import path from 'path';

// Types for swap tracking
export interface SwapTransaction {
  id: string;
  timestamp: number;
  fromToken: string;
  toToken: string;
  fromAmount: number;
  toAmount: number;
  usdValue: number;
  fee: number;
  feeUSD: number;
  provider: string;
  userAddress: string;
  txHash?: string;
  status: 'pending' | 'completed' | 'failed';
  executionTime?: number;
}

export interface LiquidityMetrics {
  token: string;
  totalLiquidity: number;
  liquidityUSD: number;
  volume24h: number;
  volume7d: number;
  volume30d: number;
  swapCount24h: number;
  swapCount7d: number;
  swapCount30d: number;
  avgSwapSize: number;
  lastUpdated: number;
}

export interface VolumeMetrics {
  totalVolumeUSD: number;
  volume24h: number;
  volume7d: number;
  volume30d: number;
  totalSwaps: number;
  swaps24h: number;
  swaps7d: number;
  swaps30d: number;
  totalFeesCollected: number;
  fees24h: number;
  fees7d: number;
  fees30d: number;
  topTokenPairs: Array<{
    pair: string;
    volume24h: number;
    swapCount: number;
  }>;
  topProviders: Array<{
    provider: string;
    volume24h: number;
    swapCount: number;
    reliability: number;
  }>;
}

export class SwapAnalytics {
  private swapHistory: SwapTransaction[] = [];
  private liquidityData: Map<string, LiquidityMetrics> = new Map();
  private dataFile: string;
  private liquidityFile: string;

  constructor() {
    this.dataFile = path.join(process.cwd(), 'data', 'swap-analytics.json');
    this.liquidityFile = path.join(process.cwd(), 'data', 'liquidity-metrics.json');
    this.ensureDataDirectory();
    this.loadData();
  }

  // Track a new swap transaction
  async trackSwap(swap: Omit<SwapTransaction, 'id' | 'timestamp'>): Promise<string> {
    const swapId = this.generateSwapId();
    const swapTransaction: SwapTransaction = {
      ...swap,
      id: swapId,
      timestamp: Date.now()
    };

    this.swapHistory.push(swapTransaction);
    await this.saveData();

    console.log(`📊 Swap tracked: ${swap.fromAmount} ${swap.fromToken} → ${swap.toAmount} ${swap.toToken} ($${swap.usdValue.toFixed(2)})`);
    
    return swapId;
  }

  // Update swap status (for pending swaps)
  async updateSwapStatus(swapId: string, status: 'completed' | 'failed', txHash?: string, executionTime?: number): Promise<void> {
    const swap = this.swapHistory.find(s => s.id === swapId);
    if (swap) {
      swap.status = status;
      if (txHash) swap.txHash = txHash;
      if (executionTime) swap.executionTime = executionTime;
      await this.saveData();
      
      console.log(`📊 Swap ${swapId} updated: ${status}`);
    }
  }

  // Update liquidity metrics for a token
  async updateLiquidityMetrics(token: string, liquidityAmount: number, liquidityUSD: number): Promise<void> {
    const existing = this.liquidityData.get(token) || this.createEmptyLiquidityMetrics(token);
    
    existing.totalLiquidity = liquidityAmount;
    existing.liquidityUSD = liquidityUSD;
    existing.lastUpdated = Date.now();
    
    // Calculate volume metrics from recent swaps
    this.calculateVolumeMetrics(existing, token);
    
    this.liquidityData.set(token, existing);
    await this.saveLiquidityData();
    
    console.log(`📊 Liquidity updated: ${token} = $${liquidityUSD.toLocaleString()}`);
  }

  // Get comprehensive volume metrics
  getVolumeMetrics(): VolumeMetrics {
    const now = Date.now();
    const day = 24 * 60 * 60 * 1000;
    const week = 7 * day;
    const month = 30 * day;

    const completedSwaps = this.swapHistory.filter(s => s.status === 'completed');
    
    const swaps24h = completedSwaps.filter(s => now - s.timestamp <= day);
    const swaps7d = completedSwaps.filter(s => now - s.timestamp <= week);
    const swaps30d = completedSwaps.filter(s => now - s.timestamp <= month);

    const volume24h = swaps24h.reduce((sum, s) => sum + s.usdValue, 0);
    const volume7d = swaps7d.reduce((sum, s) => sum + s.usdValue, 0);
    const volume30d = swaps30d.reduce((sum, s) => sum + s.usdValue, 0);
    const totalVolumeUSD = completedSwaps.reduce((sum, s) => sum + s.usdValue, 0);

    const fees24h = swaps24h.reduce((sum, s) => sum + s.feeUSD, 0);
    const fees7d = swaps7d.reduce((sum, s) => sum + s.feeUSD, 0);
    const fees30d = swaps30d.reduce((sum, s) => sum + s.feeUSD, 0);
    const totalFeesCollected = completedSwaps.reduce((sum, s) => sum + s.feeUSD, 0);

    // Calculate top token pairs
    const pairVolumes = new Map<string, { volume: number; count: number }>();
    swaps24h.forEach(swap => {
      const pair = `${swap.fromToken}/${swap.toToken}`;
      const existing = pairVolumes.get(pair) || { volume: 0, count: 0 };
      existing.volume += swap.usdValue;
      existing.count += 1;
      pairVolumes.set(pair, existing);
    });

    const topTokenPairs = Array.from(pairVolumes.entries())
      .map(([pair, data]) => ({ pair, volume24h: data.volume, swapCount: data.count }))
      .sort((a, b) => b.volume24h - a.volume24h)
      .slice(0, 10);

    // Calculate top providers
    const providerVolumes = new Map<string, { volume: number; count: number; successful: number }>();
    swaps24h.forEach(swap => {
      const existing = providerVolumes.get(swap.provider) || { volume: 0, count: 0, successful: 0 };
      existing.volume += swap.usdValue;
      existing.count += 1;
      if (swap.status === 'completed') existing.successful += 1;
      providerVolumes.set(swap.provider, existing);
    });

    const topProviders = Array.from(providerVolumes.entries())
      .map(([provider, data]) => ({
        provider,
        volume24h: data.volume,
        swapCount: data.count,
        reliability: data.count > 0 ? data.successful / data.count : 0
      }))
      .sort((a, b) => b.volume24h - a.volume24h)
      .slice(0, 10);

    return {
      totalVolumeUSD,
      volume24h,
      volume7d,
      volume30d,
      totalSwaps: completedSwaps.length,
      swaps24h: swaps24h.length,
      swaps7d: swaps7d.length,
      swaps30d: swaps30d.length,
      totalFeesCollected,
      fees24h,
      fees7d,
      fees30d,
      topTokenPairs,
      topProviders
    };
  }

  // Get liquidity metrics for all tokens
  getLiquidityMetrics(): LiquidityMetrics[] {
    return Array.from(this.liquidityData.values())
      .sort((a, b) => b.liquidityUSD - a.liquidityUSD);
  }

  // Get top tokens by liquidity
  getTopTokensByLiquidity(limit: number = 10): LiquidityMetrics[] {
    return this.getLiquidityMetrics().slice(0, limit);
  }

  // Get top tokens by volume
  getTopTokensByVolume(limit: number = 10): LiquidityMetrics[] {
    return Array.from(this.liquidityData.values())
      .sort((a, b) => b.volume24h - a.volume24h)
      .slice(0, limit);
  }

  // Get swap history with filters
  getSwapHistory(filters?: {
    token?: string;
    provider?: string;
    timeframe?: number;
    status?: 'pending' | 'completed' | 'failed';
    limit?: number;
  }): SwapTransaction[] {
    let filtered = [...this.swapHistory];

    if (filters?.token) {
      filtered = filtered.filter(s => 
        s.fromToken === filters.token || s.toToken === filters.token
      );
    }

    if (filters?.provider) {
      filtered = filtered.filter(s => s.provider === filters.provider);
    }

    if (filters?.status) {
      filtered = filtered.filter(s => s.status === filters.status);
    }

    if (filters?.timeframe) {
      const cutoff = Date.now() - filters.timeframe;
      filtered = filtered.filter(s => s.timestamp >= cutoff);
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp - a.timestamp);

    if (filters?.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  // Generate analytics report
  generateReport(): string {
    const metrics = this.getVolumeMetrics();
    const topLiquidity = this.getTopTokensByLiquidity(5);
    const topVolume = this.getTopTokensByVolume(5);

    return `
📊 MARKO POLO CAPITAL - ANALYTICS REPORT
Generated: ${new Date().toISOString()}

💰 VOLUME METRICS:
  Total Volume: $${metrics.totalVolumeUSD.toLocaleString()}
  24h Volume: $${metrics.volume24h.toLocaleString()}
  7d Volume: $${metrics.volume7d.toLocaleString()}
  30d Volume: $${metrics.volume30d.toLocaleString()}

🔄 SWAP METRICS:
  Total Swaps: ${metrics.totalSwaps.toLocaleString()}
  24h Swaps: ${metrics.swaps24h.toLocaleString()}
  7d Swaps: ${metrics.swaps7d.toLocaleString()}
  30d Swaps: ${metrics.swaps30d.toLocaleString()}

💸 REVENUE METRICS:
  Total Fees: $${metrics.totalFeesCollected.toLocaleString()}
  24h Fees: $${metrics.fees24h.toLocaleString()}
  7d Fees: $${metrics.fees7d.toLocaleString()}
  30d Fees: $${metrics.fees30d.toLocaleString()}

🏆 TOP TOKEN PAIRS (24h):
${metrics.topTokenPairs.map((pair, i) => 
  `  ${i + 1}. ${pair.pair}: $${pair.volume24h.toLocaleString()} (${pair.swapCount} swaps)`
).join('\n')}

🌊 TOP LIQUIDITY PROVIDERS:
${metrics.topProviders.map((provider, i) => 
  `  ${i + 1}. ${provider.provider}: $${provider.volume24h.toLocaleString()} (${(provider.reliability * 100).toFixed(1)}% success)`
).join('\n')}

💧 TOP TOKENS BY LIQUIDITY:
${topLiquidity.map((token, i) => 
  `  ${i + 1}. ${token.token}: $${token.liquidityUSD.toLocaleString()}`
).join('\n')}

📈 TOP TOKENS BY VOLUME (24h):
${topVolume.map((token, i) => 
  `  ${i + 1}. ${token.token}: $${token.volume24h.toLocaleString()}`
).join('\n')}
`;
  }

  // Private helper methods
  private calculateVolumeMetrics(metrics: LiquidityMetrics, token: string): void {
    const now = Date.now();
    const day = 24 * 60 * 60 * 1000;
    const week = 7 * day;
    const month = 30 * day;

    const tokenSwaps = this.swapHistory.filter(s => 
      (s.fromToken === token || s.toToken === token) && s.status === 'completed'
    );

    const swaps24h = tokenSwaps.filter(s => now - s.timestamp <= day);
    const swaps7d = tokenSwaps.filter(s => now - s.timestamp <= week);
    const swaps30d = tokenSwaps.filter(s => now - s.timestamp <= month);

    metrics.volume24h = swaps24h.reduce((sum, s) => sum + s.usdValue, 0);
    metrics.volume7d = swaps7d.reduce((sum, s) => sum + s.usdValue, 0);
    metrics.volume30d = swaps30d.reduce((sum, s) => sum + s.usdValue, 0);
    
    metrics.swapCount24h = swaps24h.length;
    metrics.swapCount7d = swaps7d.length;
    metrics.swapCount30d = swaps30d.length;
    
    metrics.avgSwapSize = swaps24h.length > 0 ? metrics.volume24h / swaps24h.length : 0;
  }

  private createEmptyLiquidityMetrics(token: string): LiquidityMetrics {
    return {
      token,
      totalLiquidity: 0,
      liquidityUSD: 0,
      volume24h: 0,
      volume7d: 0,
      volume30d: 0,
      swapCount24h: 0,
      swapCount7d: 0,
      swapCount30d: 0,
      avgSwapSize: 0,
      lastUpdated: Date.now()
    };
  }

  private generateSwapId(): string {
    return `swap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private ensureDataDirectory(): void {
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  private async saveData(): Promise<void> {
    try {
      const data = {
        swapHistory: this.swapHistory,
        lastUpdated: Date.now()
      };
      fs.writeFileSync(this.dataFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('❌ Failed to save swap analytics data:', error);
    }
  }

  private async saveLiquidityData(): Promise<void> {
    try {
      const data = {
        liquidityMetrics: Array.from(this.liquidityData.entries()),
        lastUpdated: Date.now()
      };
      fs.writeFileSync(this.liquidityFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('❌ Failed to save liquidity data:', error);
    }
  }

  private loadData(): void {
    try {
      if (fs.existsSync(this.dataFile)) {
        const data = JSON.parse(fs.readFileSync(this.dataFile, 'utf8'));
        this.swapHistory = data.swapHistory || [];
      }

      if (fs.existsSync(this.liquidityFile)) {
        const data = JSON.parse(fs.readFileSync(this.liquidityFile, 'utf8'));
        this.liquidityData = new Map(data.liquidityMetrics || []);
      }
    } catch (error) {
      console.error('❌ Failed to load analytics data:', error);
    }
  }
}

export default SwapAnalytics;
