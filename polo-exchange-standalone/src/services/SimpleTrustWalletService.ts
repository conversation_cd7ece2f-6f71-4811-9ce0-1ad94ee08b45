/**
 * Simple Trust Wallet Service
 * Easy integration without complex dependencies
 * Perfect for getting started quickly!
 */

export interface WalletAddresses {
  BTC?: string;
  ETH?: string;
  SOL?: string;
  BNB?: string;
  MATIC?: string;
  AVAX?: string;
  LTC?: string;
  ADA?: string;
  TON?: string;
  ALGO?: string;
  ATOM?: string;
  DOT?: string;
  [key: string]: string | undefined;
}

export interface WalletConfig {
  addresses: WalletAddresses;
  mnemonic?: string;
  isGenerated: boolean;
}

export class SimpleTrustWalletService {
  private addresses: WalletAddresses = {};
  private mnemonic?: string;
  private isInitialized = false;

  constructor() {
    console.log('🔗 Simple Trust Wallet Service initialized');
  }

  /**
   * Setup wallet with manual addresses (EASIEST METHOD)
   */
  setupWithAddresses(addresses: WalletAddresses): WalletConfig {
    console.log('📍 Setting up wallet with provided addresses...');
    
    this.addresses = { ...addresses };
    this.isInitialized = true;
    
    const supportedCount = Object.keys(addresses).filter(key => addresses[key]).length;
    console.log(`✅ Wallet setup complete with ${supportedCount} addresses`);
    
    return {
      addresses: this.addresses,
      isGenerated: false
    };
  }

  /**
   * Generate new wallet (MOCK - for development)
   */
  generateNewWallet(): WalletConfig {
    console.log('🎲 Generating new wallet...');
    
    // Generate mock mnemonic
    this.mnemonic = 'abandon ability able about above absent absorb abstract absurd abuse access accident';
    
    // Generate mock addresses
    this.addresses = {
      BTC: 'bc1q' + this.generateRandomString(39),
      ETH: '0x' + this.generateRandomString(40),
      SOL: this.generateRandomString(44),
      BNB: 'bnb1' + this.generateRandomString(38),
      MATIC: '0x' + this.generateRandomString(40),
      AVAX: '0x' + this.generateRandomString(40),
      LTC: 'ltc1q' + this.generateRandomString(35),
      ADA: 'addr1' + this.generateRandomString(98),
      TON: 'UQ' + this.generateRandomString(46),
      ALGO: this.generateRandomString(58).toUpperCase(),
      ATOM: 'cosmos1' + this.generateRandomString(32),
      DOT: this.generateRandomString(47)
    };
    
    this.isInitialized = true;
    
    console.log('✅ New wallet generated successfully!');
    console.log(`🔐 Mnemonic: ${this.mnemonic}`);
    console.log('⚠️ SAVE YOUR MNEMONIC PHRASE SECURELY!');
    
    return {
      addresses: this.addresses,
      mnemonic: this.mnemonic,
      isGenerated: true
    };
  }

  /**
   * Import wallet from mnemonic (MOCK - for development)
   */
  importFromMnemonic(mnemonic: string): WalletConfig {
    console.log('🔐 Importing wallet from mnemonic...');
    
    if (!this.validateMnemonic(mnemonic)) {
      throw new Error('Invalid mnemonic phrase');
    }
    
    this.mnemonic = mnemonic;
    
    // Generate deterministic addresses from mnemonic (mock)
    const seed = this.hashMnemonic(mnemonic);
    this.addresses = {
      BTC: 'bc1q' + this.generateDeterministicString(seed, 39),
      ETH: '0x' + this.generateDeterministicString(seed + '1', 40),
      SOL: this.generateDeterministicString(seed + '2', 44),
      BNB: 'bnb1' + this.generateDeterministicString(seed + '3', 38),
      MATIC: '0x' + this.generateDeterministicString(seed + '4', 40),
      AVAX: '0x' + this.generateDeterministicString(seed + '5', 40),
      LTC: 'ltc1q' + this.generateDeterministicString(seed + '6', 35),
      ADA: 'addr1' + this.generateDeterministicString(seed + '7', 98),
      TON: 'UQ' + this.generateDeterministicString(seed + '8', 46),
      ALGO: this.generateDeterministicString(seed + '9', 58).toUpperCase(),
      ATOM: 'cosmos1' + this.generateDeterministicString(seed + '10', 32),
      DOT: this.generateDeterministicString(seed + '11', 47)
    };
    
    this.isInitialized = true;
    
    console.log('✅ Wallet imported successfully!');
    
    return {
      addresses: this.addresses,
      mnemonic: this.mnemonic,
      isGenerated: true
    };
  }

  /**
   * Get deposit address for specific currency
   */
  getDepositAddress(currency: string): string | null {
    if (!this.isInitialized) {
      console.warn('⚠️ Wallet not initialized');
      return null;
    }
    
    const address = this.addresses[currency.toUpperCase()];
    if (!address) {
      console.warn(`⚠️ No address found for ${currency}`);
      return null;
    }
    
    return address;
  }

  /**
   * Get all addresses
   */
  getAllAddresses(): WalletAddresses {
    return { ...this.addresses };
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return Object.keys(this.addresses).filter(key => this.addresses[key]);
  }

  /**
   * Check if wallet is ready
   */
  isReady(): boolean {
    return this.isInitialized && Object.keys(this.addresses).length > 0;
  }

  /**
   * Get wallet status
   */
  getWalletStatus(): {
    isInitialized: boolean;
    totalAddresses: number;
    supportedCurrencies: string[];
    hasMnemonic: boolean;
  } {
    return {
      isInitialized: this.isInitialized,
      totalAddresses: Object.keys(this.addresses).filter(key => this.addresses[key]).length,
      supportedCurrencies: this.getSupportedCurrencies(),
      hasMnemonic: !!this.mnemonic
    };
  }

  /**
   * Validate mnemonic phrase
   */
  private validateMnemonic(mnemonic: string): boolean {
    const words = mnemonic.trim().split(/\s+/);
    return words.length === 12 || words.length === 24;
  }

  /**
   * Generate random string
   */
  private generateRandomString(length: number): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Hash mnemonic for deterministic generation
   */
  private hashMnemonic(mnemonic: string): string {
    let hash = 0;
    for (let i = 0; i < mnemonic.length; i++) {
      const char = mnemonic.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Generate deterministic string from seed
   */
  private generateDeterministicString(seed: string, length: number): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    let seedNum = parseInt(seed, 36) || 12345;
    
    for (let i = 0; i < length; i++) {
      seedNum = (seedNum * 9301 + 49297) % 233280;
      result += chars.charAt(seedNum % chars.length);
    }
    return result;
  }

  /**
   * Generate environment configuration
   */
  generateEnvConfig(): string {
    if (!this.isInitialized) {
      throw new Error('Wallet not initialized');
    }

    const envLines = [
      '# Trust Wallet Configuration',
      `# Generated on ${new Date().toISOString()}`,
      ''
    ];

    if (this.mnemonic) {
      envLines.push('# Mnemonic phrase (KEEP SECURE!)');
      envLines.push(`TRUST_WALLET_MNEMONIC="${this.mnemonic}"`);
      envLines.push('');
    }

    envLines.push('# Wallet addresses');
    for (const [currency, address] of Object.entries(this.addresses)) {
      if (address) {
        envLines.push(`TRUST_WALLET_${currency}=${address}`);
      }
    }

    envLines.push('');
    envLines.push('# Exchange settings');
    envLines.push('EXCHANGE_FEE_RATE=0.03');
    envLines.push('AUTO_MONITORING=true');

    return envLines.join('\n');
  }

  /**
   * Test wallet functionality
   */
  async testWallet(): Promise<boolean> {
    try {
      console.log('🧪 Testing wallet functionality...');
      
      if (!this.isInitialized) {
        console.log('❌ Wallet not initialized');
        return false;
      }
      
      const addressCount = this.getSupportedCurrencies().length;
      if (addressCount === 0) {
        console.log('❌ No addresses configured');
        return false;
      }
      
      console.log(`✅ Wallet test passed - ${addressCount} addresses ready`);
      return true;
      
    } catch (error) {
      console.error('❌ Wallet test failed:', error);
      return false;
    }
  }
}

export default SimpleTrustWalletService;
