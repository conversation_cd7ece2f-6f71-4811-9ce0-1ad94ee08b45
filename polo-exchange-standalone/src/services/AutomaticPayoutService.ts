/**
 * Automatic Payout Service
 * Handles automated sending of swapped tokens to user addresses
 */

import { EventEmitter } from 'events';
import AutomatedWalletManager from './AutomatedWalletManager';
import AutomaticSwapExecutor from './AutomaticSwapExecutor';

export interface PayoutRequest {
  id: string;
  executionId: string;
  currency: string;
  amount: string;
  toAddress: string;
  fromAddress: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  txHash?: string;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
  gasUsed?: string;
  gasFee?: string;
  retryCount?: number;
}

export interface PayoutConfig {
  enabled: boolean;
  maxRetries: number;
  retryDelay: number; // milliseconds
  gasLimits: Record<string, string>;
  gasPrices: Record<string, string>;
  confirmationsRequired: Record<string, number>;
  batchSize: number; // max payouts to process simultaneously
}

export interface PayoutStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  totalVolume: Record<string, string>; // currency -> total amount
  totalFees: Record<string, string>; // currency -> total gas fees
}

export class AutomaticPayoutService extends EventEmitter {
  private walletManager: AutomatedWalletManager;
  private config: PayoutConfig;
  private payoutQueue: PayoutRequest[] = [];
  private activePayouts: Map<string, PayoutRequest> = new Map();
  private isProcessing = false;
  private stats: PayoutStats;

  constructor(walletManager: AutomatedWalletManager) {
    super();
    this.walletManager = walletManager;
    
    this.config = {
      enabled: true,
      maxRetries: 3,
      retryDelay: 10000, // 10 seconds
      gasLimits: {
        'ETH': '21000',
        'BNB': '21000',
        'MATIC': '21000',
        'AVAX': '21000',
        'BTC': '250', // bytes
        'SOL': '200000', // compute units
        'LTC': '250',
        'DOGE': '250'
      },
      gasPrices: {
        'ETH': '20000000000', // 20 gwei
        'BNB': '5000000000', // 5 gwei
        'MATIC': '30000000000', // 30 gwei
        'AVAX': '25000000000', // 25 gwei
        'BTC': '10', // sat/byte
        'SOL': '5000', // lamports
        'LTC': '10',
        'DOGE': '100'
      },
      confirmationsRequired: {
        'ETH': 12,
        'BNB': 15,
        'MATIC': 20,
        'AVAX': 15,
        'BTC': 3,
        'SOL': 32,
        'LTC': 6,
        'DOGE': 6
      },
      batchSize: 5
    };

    this.stats = {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      totalVolume: {},
      totalFees: {}
    };

    console.log('💸 Automatic Payout Service initialized');
  }

  /**
   * Setup payout listener for swap executor
   */
  setupSwapExecutorListener(swapExecutor: AutomaticSwapExecutor): void {
    console.log('🔗 Setting up swap executor listener...');
    
    swapExecutor.on('payout_required', async (payoutData) => {
      await this.createPayoutRequest(payoutData);
    });
    
    console.log('✅ Swap executor listener configured');
  }

  /**
   * Create new payout request
   */
  async createPayoutRequest(payoutData: {
    executionId: string;
    currency: string;
    amount: string;
    toAddress: string;
    txHash?: string;
  }): Promise<PayoutRequest> {
    console.log(`💸 Creating payout request: ${payoutData.amount} ${payoutData.currency}`);
    
    const fromAddress = this.walletManager.getDepositAddress(payoutData.currency);
    if (!fromAddress) {
      throw new Error(`No wallet address found for ${payoutData.currency}`);
    }

    const payoutRequest: PayoutRequest = {
      id: `payout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      executionId: payoutData.executionId,
      currency: payoutData.currency,
      amount: payoutData.amount,
      toAddress: payoutData.toAddress,
      fromAddress,
      status: 'pending',
      createdAt: new Date(),
      retryCount: 0
    };

    this.activePayouts.set(payoutRequest.id, payoutRequest);
    this.payoutQueue.push(payoutRequest);
    this.updateStats();
    
    console.log(`📝 Payout request created: ${payoutRequest.id}`);
    console.log(`👤 To: ${payoutRequest.toAddress}`);
    
    this.emit('payout_created', payoutRequest);
    
    if (this.config.enabled && !this.isProcessing) {
      this.processPayoutQueue();
    }
    
    return payoutRequest;
  }

  /**
   * Process payout queue
   */
  private async processPayoutQueue(): Promise<void> {
    if (this.isProcessing || this.payoutQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`⚙️ Processing ${this.payoutQueue.length} payouts...`);
    
    // Process payouts in batches
    while (this.payoutQueue.length > 0) {
      const batch = this.payoutQueue.splice(0, this.config.batchSize);
      await this.processBatch(batch);
    }
    
    this.isProcessing = false;
    console.log('✅ Payout queue processed');
  }

  /**
   * Process batch of payouts
   */
  private async processBatch(batch: PayoutRequest[]): Promise<void> {
    console.log(`📦 Processing batch of ${batch.length} payouts...`);
    
    const promises = batch.map(payout => this.executePayout(payout));
    await Promise.allSettled(promises);
    
    console.log(`✅ Batch processed`);
  }

  /**
   * Execute individual payout
   */
  private async executePayout(payout: PayoutRequest): Promise<void> {
    try {
      console.log(`💸 Executing payout: ${payout.id}`);
      console.log(`💰 ${payout.amount} ${payout.currency} → ${payout.toAddress}`);
      
      payout.status = 'processing';
      this.updateStats();
      this.emit('payout_processing', payout);
      
      // Execute the transaction based on currency type
      const txResult = await this.executeTransaction(payout);
      
      if (txResult.success) {
        payout.status = 'completed';
        payout.txHash = txResult.txHash;
        payout.gasUsed = txResult.gasUsed;
        payout.gasFee = txResult.gasFee;
        payout.completedAt = new Date();
        
        console.log(`✅ Payout completed: ${payout.id}`);
        console.log(`📍 TX: ${payout.txHash}`);
        console.log(`⛽ Gas used: ${payout.gasUsed}`);
        
        this.updateStats();
        this.emit('payout_completed', payout);
        
      } else {
        throw new Error(txResult.error || 'Transaction failed');
      }
      
    } catch (error) {
      console.error(`❌ Payout failed: ${payout.id}`, error);
      
      payout.status = 'failed';
      payout.error = error instanceof Error ? error.message : 'Unknown error';
      
      this.updateStats();
      this.emit('payout_failed', payout);
      
      // Handle retry
      await this.handlePayoutRetry(payout);
    }
  }

  /**
   * Execute transaction for specific currency
   */
  private async executeTransaction(payout: PayoutRequest): Promise<{
    success: boolean;
    txHash?: string;
    gasUsed?: string;
    gasFee?: string;
    error?: string;
  }> {
    const currency = payout.currency.toUpperCase();
    
    switch (currency) {
      case 'ETH':
      case 'BNB':
      case 'MATIC':
      case 'AVAX':
        return await this.executeEVMTransaction(payout);
      
      case 'BTC':
      case 'LTC':
      case 'DOGE':
        return await this.executeBitcoinTransaction(payout);
      
      case 'SOL':
        return await this.executeSolanaTransaction(payout);
      
      default:
        return await this.executeGenericTransaction(payout);
    }
  }

  /**
   * Execute EVM transaction
   */
  private async executeEVMTransaction(payout: PayoutRequest): Promise<any> {
    console.log(`🔗 Executing EVM transaction for ${payout.currency}`);
    
    // This would use ethers.js or web3.js to send the transaction
    // For now, return a mock successful transaction
    
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate network delay
    
    return {
      success: true,
      txHash: `0x${Math.random().toString(16).substr(2, 64)}`,
      gasUsed: this.config.gasLimits[payout.currency],
      gasFee: (parseFloat(this.config.gasLimits[payout.currency]) * parseFloat(this.config.gasPrices[payout.currency]) / 1e18).toString()
    };
  }

  /**
   * Execute Bitcoin-like transaction
   */
  private async executeBitcoinTransaction(payout: PayoutRequest): Promise<any> {
    console.log(`₿ Executing Bitcoin transaction for ${payout.currency}`);
    
    // This would use bitcoin libraries to create and broadcast transaction
    // For now, return a mock successful transaction
    
    await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate network delay
    
    return {
      success: true,
      txHash: Math.random().toString(16).substr(2, 64),
      gasUsed: this.config.gasLimits[payout.currency],
      gasFee: (parseFloat(this.config.gasLimits[payout.currency]) * parseFloat(this.config.gasPrices[payout.currency]) / 1e8).toString()
    };
  }

  /**
   * Execute Solana transaction
   */
  private async executeSolanaTransaction(payout: PayoutRequest): Promise<any> {
    console.log(`☀️ Executing Solana transaction`);
    
    // This would use @solana/web3.js to send the transaction
    // For now, return a mock successful transaction
    
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    
    return {
      success: true,
      txHash: Math.random().toString(16).substr(2, 88), // Solana tx signatures are longer
      gasUsed: this.config.gasLimits[payout.currency],
      gasFee: (parseFloat(this.config.gasLimits[payout.currency]) * 0.000005).toString() // ~5000 lamports
    };
  }

  /**
   * Execute generic transaction
   */
  private async executeGenericTransaction(payout: PayoutRequest): Promise<any> {
    console.log(`🔗 Executing generic transaction for ${payout.currency}`);
    
    // This would integrate with the specific blockchain's SDK
    // For now, return a mock successful transaction
    
    await new Promise(resolve => setTimeout(resolve, 2500)); // Simulate network delay
    
    return {
      success: true,
      txHash: Math.random().toString(16).substr(2, 64),
      gasUsed: '0',
      gasFee: '0'
    };
  }

  /**
   * Handle payout retry
   */
  private async handlePayoutRetry(payout: PayoutRequest): Promise<void> {
    const retryCount = payout.retryCount || 0;
    
    if (retryCount < this.config.maxRetries) {
      console.log(`🔄 Retrying payout: ${payout.id} (attempt ${retryCount + 1}/${this.config.maxRetries})`);
      
      payout.retryCount = retryCount + 1;
      payout.status = 'pending';
      payout.error = undefined;
      
      // Add back to queue with delay
      setTimeout(() => {
        this.payoutQueue.push(payout);
        if (!this.isProcessing) {
          this.processPayoutQueue();
        }
      }, this.config.retryDelay);
      
    } else {
      console.error(`❌ Max retries exceeded for payout: ${payout.id}`);
      this.emit('payout_max_retries_exceeded', payout);
    }
  }

  /**
   * Update statistics
   */
  private updateStats(): void {
    const payouts = Array.from(this.activePayouts.values());
    
    this.stats = {
      total: payouts.length,
      pending: payouts.filter(p => p.status === 'pending').length,
      processing: payouts.filter(p => p.status === 'processing').length,
      completed: payouts.filter(p => p.status === 'completed').length,
      failed: payouts.filter(p => p.status === 'failed').length,
      totalVolume: {},
      totalFees: {}
    };
    
    // Calculate volume and fees by currency
    for (const payout of payouts.filter(p => p.status === 'completed')) {
      const currency = payout.currency;
      
      this.stats.totalVolume[currency] = (
        parseFloat(this.stats.totalVolume[currency] || '0') + parseFloat(payout.amount)
      ).toString();
      
      if (payout.gasFee) {
        this.stats.totalFees[currency] = (
          parseFloat(this.stats.totalFees[currency] || '0') + parseFloat(payout.gasFee)
        ).toString();
      }
    }
  }

  /**
   * Get payout by ID
   */
  getPayout(id: string): PayoutRequest | undefined {
    return this.activePayouts.get(id);
  }

  /**
   * Get all active payouts
   */
  getActivePayouts(): PayoutRequest[] {
    return Array.from(this.activePayouts.values());
  }

  /**
   * Get payout statistics
   */
  getStats(): PayoutStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PayoutConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Payout configuration updated');
  }

  /**
   * Enable/disable automatic payouts
   */
  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    console.log(`💸 Automatic payouts ${enabled ? 'enabled' : 'disabled'}`);
    
    if (enabled && !this.isProcessing && this.payoutQueue.length > 0) {
      this.processPayoutQueue();
    }
  }

  /**
   * Clear completed payouts (cleanup)
   */
  clearCompletedPayouts(): number {
    const completed = Array.from(this.activePayouts.entries())
      .filter(([_, payout]) => payout.status === 'completed' || payout.status === 'failed');
    
    for (const [id, _] of completed) {
      this.activePayouts.delete(id);
    }
    
    this.updateStats();
    console.log(`🧹 Cleared ${completed.length} completed payouts`);
    return completed.length;
  }
}

export default AutomaticPayoutService;
