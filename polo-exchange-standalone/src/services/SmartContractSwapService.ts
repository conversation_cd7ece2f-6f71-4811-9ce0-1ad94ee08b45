/**
 * Smart Contract Swap Service
 * Integrates with deployed Polo Swap contracts for automated swaps
 */

import { ethers } from 'ethers';
import { LiFi } from '@lifi/sdk';

interface ContractConfig {
  address: string;
  chainId: number;
  rpcUrl: string;
  lifiDiamond: string;
}

interface SwapRequest {
  fromToken: string;
  toToken: string;
  amount: string;
  userAddress: string;
  recipientAddress: string;
  fromChain: string;
  toChain: string;
}

interface SwapResult {
  success: boolean;
  txHash?: string;
  swapId?: number;
  error?: string;
}

export class SmartContractSwapService {
  private contracts: Map<string, ContractConfig> = new Map();
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private lifi: LiFi;
  private wallet: ethers.Wallet;

  // Contract ABI (simplified)
  private contractABI = [
    "function requestSwap(address fromToken, address toToken, uint256 amount, address recipientAddress) external",
    "function executeSwap(uint256 swapId, bytes calldata lifiCalldata) external",
    "function getSwapRequest(uint256 swapId) external view returns (tuple(address user, address fromToken, address toToken, uint256 amount, address recipientAddress, uint256 feeAmount, bool completed, uint256 timestamp))",
    "function addSupportedToken(address token) external",
    "function isTokenSupported(address token) external view returns (bool)",
    "function withdrawFees(address token, uint256 amount) external",
    "function getCollectedFees(address token) external view returns (uint256)",
    "event SwapRequested(address indexed user, address indexed fromToken, address indexed toToken, uint256 amount, address recipientAddress, uint256 swapId)"
  ];

  constructor() {
    // Initialize LiFi
    this.lifi = new LiFi({
      apiKey: process.env.LIFI_API_KEY || 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e',
      integrator: 'marko-polo-capital'
    });

    // Initialize wallet (you'll need to set this securely)
    this.wallet = new ethers.Wallet(process.env.EXECUTOR_PRIVATE_KEY || '');

    this.initializeContracts();
    console.log('🔗 Smart Contract Swap Service initialized');
  }

  private initializeContracts() {
    // Load contract configurations
    const contracts = {
      ethereum: {
        address: process.env.ETHEREUM_CONTRACT_ADDRESS || '',
        chainId: 1,
        rpcUrl: process.env.ETHEREUM_RPC_URL || '',
        lifiDiamond: '******************************************'
      },
      bsc: {
        address: process.env.BSC_CONTRACT_ADDRESS || '',
        chainId: 56,
        rpcUrl: process.env.BSC_RPC_URL || '',
        lifiDiamond: '******************************************'
      },
      polygon: {
        address: process.env.POLYGON_CONTRACT_ADDRESS || '',
        chainId: 137,
        rpcUrl: process.env.POLYGON_RPC_URL || '',
        lifiDiamond: '******************************************'
      },
      avalanche: {
        address: process.env.AVALANCHE_CONTRACT_ADDRESS || '',
        chainId: 43114,
        rpcUrl: process.env.AVALANCHE_RPC_URL || '',
        lifiDiamond: '******************************************'
      },
      arbitrum: {
        address: process.env.ARBITRUM_CONTRACT_ADDRESS || '',
        chainId: 42161,
        rpcUrl: process.env.ARBITRUM_RPC_URL || '',
        lifiDiamond: '******************************************'
      },
      optimism: {
        address: process.env.OPTIMISM_CONTRACT_ADDRESS || '',
        chainId: 10,
        rpcUrl: process.env.OPTIMISM_RPC_URL || '',
        lifiDiamond: '******************************************'
      }
    };

    // Initialize providers and contracts
    Object.entries(contracts).forEach(([network, config]) => {
      if (config.address && config.rpcUrl) {
        this.contracts.set(network, config);
        this.providers.set(network, new ethers.JsonRpcProvider(config.rpcUrl));
        console.log(`✅ ${network} contract configured: ${config.address}`);
      }
    });
  }

  /**
   * Execute a swap using smart contracts
   */
  async executeSwap(swapRequest: SwapRequest): Promise<SwapResult> {
    try {
      console.log(`🚀 Executing smart contract swap: ${swapRequest.amount} ${swapRequest.fromToken} → ${swapRequest.toToken}`);

      // Get contract for source chain
      const contractConfig = this.contracts.get(swapRequest.fromChain);
      if (!contractConfig) {
        throw new Error(`Contract not configured for chain: ${swapRequest.fromChain}`);
      }

      const provider = this.providers.get(swapRequest.fromChain);
      if (!provider) {
        throw new Error(`Provider not configured for chain: ${swapRequest.fromChain}`);
      }

      // Connect wallet to provider
      const connectedWallet = this.wallet.connect(provider);
      
      // Get contract instance
      const contract = new ethers.Contract(
        contractConfig.address,
        this.contractABI,
        connectedWallet
      );

      // Get token addresses
      const fromTokenAddress = this.getTokenAddress(swapRequest.fromToken, swapRequest.fromChain);
      const toTokenAddress = this.getTokenAddress(swapRequest.toToken, swapRequest.toChain);

      // Convert amount to wei
      const amount = ethers.parseUnits(swapRequest.amount, this.getTokenDecimals(swapRequest.fromToken));

      // Check if user has approved the contract
      if (fromTokenAddress !== ethers.ZeroAddress) {
        const tokenContract = new ethers.Contract(
          fromTokenAddress,
          ['function allowance(address owner, address spender) view returns (uint256)'],
          provider
        );
        
        const allowance = await tokenContract.allowance(swapRequest.userAddress, contractConfig.address);
        if (allowance < amount) {
          return {
            success: false,
            error: `Insufficient allowance. User needs to approve ${swapRequest.amount} ${swapRequest.fromToken}`
          };
        }
      }

      // Request swap on contract
      const tx = await contract.requestSwap(
        fromTokenAddress,
        toTokenAddress,
        amount,
        swapRequest.recipientAddress
      );

      const receipt = await tx.wait();
      
      // Parse swap ID from events
      const swapRequestedEvent = receipt.logs.find((log: any) => {
        try {
          const parsed = contract.interface.parseLog(log);
          return parsed?.name === 'SwapRequested';
        } catch {
          return false;
        }
      });

      if (!swapRequestedEvent) {
        throw new Error('SwapRequested event not found');
      }

      const parsedEvent = contract.interface.parseLog(swapRequestedEvent);
      const swapId = parsedEvent?.args?.swapId;

      console.log(`✅ Swap requested on-chain. Swap ID: ${swapId}`);

      // Now execute the swap via LiFi
      await this.executeLiFiSwap(swapRequest.fromChain, swapId, swapRequest);

      return {
        success: true,
        txHash: tx.hash,
        swapId: Number(swapId)
      };

    } catch (error) {
      console.error('❌ Smart contract swap failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute LiFi swap for a contract swap request
   */
  private async executeLiFiSwap(network: string, swapId: number, swapRequest: SwapRequest) {
    try {
      console.log(`🔄 Executing LiFi swap for contract swap ID: ${swapId}`);

      // Get LiFi quote
      const quote = await this.lifi.getQuote({
        fromChain: this.getChainId(swapRequest.fromChain),
        toChain: this.getChainId(swapRequest.toChain),
        fromToken: this.getTokenAddress(swapRequest.fromToken, swapRequest.fromChain),
        toToken: this.getTokenAddress(swapRequest.toToken, swapRequest.toChain),
        fromAmount: ethers.parseUnits(swapRequest.amount, this.getTokenDecimals(swapRequest.fromToken)).toString(),
        fromAddress: this.contracts.get(network)?.address || '',
        toAddress: swapRequest.recipientAddress
      });

      // Execute LiFi swap
      const execution = await this.lifi.executeRoute({
        route: quote,
        settings: {
          updateCallback: (update) => {
            console.log(`📊 LiFi execution update: ${update.status}`);
          }
        }
      });

      // Get contract instance
      const contractConfig = this.contracts.get(network)!;
      const provider = this.providers.get(network)!;
      const connectedWallet = this.wallet.connect(provider);
      const contract = new ethers.Contract(contractConfig.address, this.contractABI, connectedWallet);

      // Execute swap on contract with LiFi calldata
      const executeTx = await contract.executeSwap(swapId, execution.transactionRequest.data);
      await executeTx.wait();

      console.log(`✅ LiFi swap executed for contract swap ID: ${swapId}`);

    } catch (error) {
      console.error(`❌ LiFi execution failed for swap ID ${swapId}:`, error);
      
      // Try emergency completion
      await this.emergencyCompleteSwap(network, swapId, swapRequest);
    }
  }

  /**
   * Emergency completion if LiFi fails
   */
  private async emergencyCompleteSwap(network: string, swapId: number, swapRequest: SwapRequest) {
    try {
      console.log(`🚨 Attempting emergency completion for swap ID: ${swapId}`);
      
      // This would require manual intervention or alternative swap execution
      // For now, we'll log the failure and require manual processing
      
      console.log(`⚠️ Manual intervention required for swap ID: ${swapId}`);
      console.log(`   From: ${swapRequest.amount} ${swapRequest.fromToken}`);
      console.log(`   To: ${swapRequest.toToken}`);
      console.log(`   Recipient: ${swapRequest.recipientAddress}`);
      
    } catch (error) {
      console.error(`❌ Emergency completion failed for swap ID ${swapId}:`, error);
    }
  }

  /**
   * Monitor contract events for new swap requests
   */
  async startEventMonitoring() {
    console.log('👀 Starting contract event monitoring...');

    this.contracts.forEach((config, network) => {
      const provider = this.providers.get(network);
      if (!provider) return;

      const contract = new ethers.Contract(config.address, this.contractABI, provider);

      // Listen for SwapRequested events
      contract.on('SwapRequested', async (user, fromToken, toToken, amount, recipientAddress, swapId, event) => {
        console.log(`📥 New swap request on ${network}:`);
        console.log(`   Swap ID: ${swapId}`);
        console.log(`   User: ${user}`);
        console.log(`   Amount: ${ethers.formatUnits(amount, 18)}`);
        console.log(`   From: ${fromToken} → To: ${toToken}`);
        console.log(`   Recipient: ${recipientAddress}`);

        // Auto-execute the swap
        // Note: In production, you might want to add additional validation
        // await this.executeLiFiSwap(network, Number(swapId), {
        //   fromToken: this.getTokenSymbol(fromToken),
        //   toToken: this.getTokenSymbol(toToken),
        //   amount: ethers.formatUnits(amount, 18),
        //   userAddress: user,
        //   recipientAddress,
        //   fromChain: network,
        //   toChain: network // This would need to be determined from the swap request
        // });
      });

      console.log(`✅ Monitoring events on ${network}: ${config.address}`);
    });
  }

  /**
   * Helper methods
   */
  private getTokenAddress(symbol: string, chain: string): string {
    // This would map token symbols to addresses for each chain
    // For now, return zero address for native tokens
    if (symbol === 'ETH' || symbol === 'BNB' || symbol === 'MATIC' || symbol === 'AVAX') {
      return ethers.ZeroAddress;
    }
    
    // Return actual token addresses based on your token configuration
    return '******************************************';
  }

  private getTokenDecimals(symbol: string): number {
    const decimals: Record<string, number> = {
      'ETH': 18, 'BTC': 8, 'USDC': 6, 'USDT': 6, 'BNB': 18,
      'MATIC': 18, 'AVAX': 18, 'SOL': 9
    };
    return decimals[symbol] || 18;
  }

  private getChainId(chain: string): number {
    const chainIds: Record<string, number> = {
      'ethereum': 1, 'bsc': 56, 'polygon': 137, 'avalanche': 43114,
      'arbitrum': 42161, 'optimism': 10
    };
    return chainIds[chain] || 1;
  }

  /**
   * Get contract deployment status
   */
  getDeploymentStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    this.contracts.forEach((config, network) => {
      status[network] = !!config.address;
    });
    return status;
  }

  /**
   * Get collected fees for all contracts
   */
  async getCollectedFees(): Promise<Record<string, Record<string, string>>> {
    const fees: Record<string, Record<string, string>> = {};
    
    for (const [network, config] of this.contracts) {
      const provider = this.providers.get(network);
      if (!provider) continue;

      const contract = new ethers.Contract(config.address, this.contractABI, provider);
      fees[network] = {};

      // Check fees for major tokens
      const tokens = ['USDC', 'USDT', 'ETH', 'BTC'];
      for (const token of tokens) {
        try {
          const tokenAddress = this.getTokenAddress(token, network);
          const feeAmount = await contract.getCollectedFees(tokenAddress);
          fees[network][token] = ethers.formatUnits(feeAmount, this.getTokenDecimals(token));
        } catch (error) {
          fees[network][token] = '0';
        }
      }
    }

    return fees;
  }
}

export default SmartContractSwapService;
