import { useState, useEffect } from 'react';

interface MobileDetection {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  isSlowConnection: boolean;
  devicePixelRatio: number;
}

export const useMobileDetection = (): MobileDetection => {
  const [detection, setDetection] = useState<MobileDetection>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenWidth: 1920,
    isSlowConnection: false,
    devicePixelRatio: 1
  });

  useEffect(() => {
    const updateDetection = () => {
      const width = window.innerWidth;
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
      const isTabletUA = /ipad|android(?!.*mobile)/i.test(userAgent);
      
      // Network detection for performance optimization
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      const isSlowConnection = connection ? 
        (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' || connection.effectiveType === '3g') : 
        false;

      setDetection({
        isMobile: width <= 768 || isMobileUA,
        isTablet: (width > 768 && width <= 1024) || isTabletUA,
        isDesktop: width > 1024 && !isMobileUA,
        screenWidth: width,
        isSlowConnection,
        devicePixelRatio: window.devicePixelRatio || 1
      });
    };

    updateDetection();
    window.addEventListener('resize', updateDetection);
    window.addEventListener('orientationchange', updateDetection);

    return () => {
      window.removeEventListener('resize', updateDetection);
      window.removeEventListener('orientationchange', updateDetection);
    };
  }, []);

  return detection;
};
