import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "./components/ui/toaster";
import Home from "./pages/Home";
import ExchangePage from "./pages/ExchangePage";
import ServicesPage from "./pages/ServicesPage";
import TradingBotMarketplace from "./pages/TradingBotMarketplace";
import InvestPage from "./pages/InvestPage";
import FundingPage from "./pages/FundingPage";
import PortfolioPage from "./pages/PortfolioPage";
import ContractsPage from "./pages/ContractsPage";
import SolanaPage from "./pages/SolanaPage";
import SecurityPage from "./pages/SecurityPage";
import WhitepaperPage from "./pages/WhitepaperPage";
import FaqPage from "./pages/FaqPage";
import AdminDashboard from "./pages/AdminDashboard";
import AdminLogin from "./components/AdminLogin";
import NEARTest from "./components/NEARTest";
import { LanguageProvider } from "./lib/LanguageContext";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/exchange" component={() => {
        console.log('🎯 Exchange route matched!');
        return <ExchangePage />;
      }} />
      <Route path="/services" component={ServicesPage} />
      <Route path="/trading-bots" component={TradingBotMarketplace} />
      <Route path="/invest" component={InvestPage} />
      <Route path="/funding" component={FundingPage} />
      <Route path="/portfolio" component={PortfolioPage} />
      <Route path="/contracts" component={ContractsPage} />
      <Route path="/solana" component={SolanaPage} />
      <Route path="/security" component={SecurityPage} />
      <Route path="/whitepaper" component={WhitepaperPage} />
      <Route path="/faq" component={FaqPage} />
      <Route path="/test-near" component={NEARTest} />
      <Route path="/admin/login" component={AdminLogin} />
      <Route path="/admin" component={AdminDashboard} />
      <Route component={Home} />
    </Switch>
  );
}

function App() {
  return (
    <div className="overflow-x-hidden w-full relative">
      <QueryClientProvider client={queryClient}>
        <LanguageProvider>
          <Router />
          <Toaster />
        </LanguageProvider>
      </QueryClientProvider>
    </div>
  );
}

export default App;
