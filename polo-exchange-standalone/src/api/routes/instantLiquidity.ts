import express from 'express';
import { instantLiquidityService } from '../instantLiquidity';

const router = express.Router();

// GET /api/instant-liquidity/quote
router.get('/quote', async (req, res) => {
  try {
    const { fromToken, toToken, amount, fromChain, toChain, userAddress } = req.query;

    // Validate required parameters
    if (!fromToken || !toToken || !amount) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: fromToken, toToken, amount'
      });
    }

    const amountNum = parseFloat(amount as string);
    if (isNaN(amountNum) || amountNum <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount'
      });
    }

    const quote = await instantLiquidityService.getInstantQuote({
      fromToken: fromToken as string,
      toToken: toToken as string,
      amount: amountNum,
      fromChain: from<PERSON>hain as string,
      toChain: to<PERSON>hain as string,
      userAddress: userAddress as string
    });

    res.json(quote);
  } catch (error) {
    console.error('❌ Instant liquidity quote error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get instant liquidity quote'
    });
  }
});

// POST /api/instant-liquidity/execute
router.post('/execute', async (req, res) => {
  try {
    const { quoteId, userAddress, recipientAddress, maxSlippage, deadline } = req.body;

    // Validate required parameters
    if (!quoteId || !userAddress || !recipientAddress) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: quoteId, userAddress, recipientAddress'
      });
    }

    const result = await instantLiquidityService.executeInstantSwap({
      quoteId,
      userAddress,
      recipientAddress,
      maxSlippage: maxSlippage || 1.0,
      deadline
    });

    res.json(result);
  } catch (error) {
    console.error('❌ Instant liquidity execution error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute instant swap'
    });
  }
});

// GET /api/instant-liquidity/providers
router.get('/providers', async (req, res) => {
  try {
    const status = await instantLiquidityService.getProviderStatus();
    res.json(status);
  } catch (error) {
    console.error('❌ Provider status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get provider status'
    });
  }
});

// GET /api/instant-liquidity/depth
router.get('/depth', async (req, res) => {
  try {
    const { fromToken, toToken } = req.query;

    if (!fromToken || !toToken) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: fromToken, toToken'
      });
    }

    const depth = await instantLiquidityService.getLiquidityDepth({
      fromToken: fromToken as string,
      toToken: toToken as string
    });

    res.json(depth);
  } catch (error) {
    console.error('❌ Liquidity depth error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get liquidity depth'
    });
  }
});

export default router;
