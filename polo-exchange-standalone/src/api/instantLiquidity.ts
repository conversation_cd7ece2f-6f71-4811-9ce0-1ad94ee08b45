import { InstantLiquidityAggregator, AggregatedQuote, InstantQuote } from '../services/InstantLiquidityAggregator';

// Initialize the instant liquidity aggregator
const liquidityAggregator = new InstantLiquidityAggregator();

export interface InstantLiquidityAPI {
  getInstantQuote: (params: QuoteParams) => Promise<InstantQuoteResponse>;
  executeInstantSwap: (params: SwapParams) => Promise<SwapResponse>;
  getProviderStatus: () => Promise<ProviderStatusResponse>;
  getLiquidityDepth: (params: DepthParams) => Promise<LiquidityDepthResponse>;
}

export interface QuoteParams {
  fromToken: string;
  toToken: string;
  amount: number;
  fromChain?: string;
  toChain?: string;
  slippage?: number;
  userAddress?: string;
}

export interface SwapParams {
  quoteId: string;
  userAddress: string;
  recipientAddress: string;
  maxSlippage: number;
  deadline?: number;
}

export interface InstantQuoteResponse {
  success: boolean;
  quote?: {
    id: string;
    fromToken: string;
    toToken: string;
    fromAmount: number;
    toAmount: number;
    rate: number;
    priceImpact: number;
    fee: number;
    feePercentage: number;
    provider: string;
    executionTime: number;
    confidence: number;
    expires: number;
    alternatives: Array<{
      provider: string;
      toAmount: number;
      executionTime: number;
      fee: number;
    }>;
  };
  error?: string;
  timestamp: number;
}

export interface SwapResponse {
  success: boolean;
  result?: {
    transactionId: string;
    status: 'pending' | 'confirmed' | 'failed';
    fromAmount: number;
    toAmount: number;
    actualRate: number;
    fee: number;
    provider: string;
    estimatedTime: number;
    confirmations: number;
    blockExplorer?: string;
  };
  error?: string;
  timestamp: number;
}

export interface ProviderStatusResponse {
  success: boolean;
  data: {
    totalProviders: number;
    activeProviders: number;
    avgReliability: number;
    providers: Array<{
      name: string;
      status: 'online' | 'offline' | 'degraded';
      reliability: number;
      avgExecutionTime: number;
      supportedPairs: number;
      lastUpdate: number;
    }>;
  };
  timestamp: number;
}

export interface LiquidityDepthResponse {
  success: boolean;
  data: {
    fromToken: string;
    toToken: string;
    totalLiquidity: number;
    providers: Array<{
      name: string;
      liquidity: number;
      rate: number;
      maxAmount: number;
    }>;
    priceImpact: Array<{
      amount: number;
      impact: number;
    }>;
  };
  timestamp: number;
}

// Quote cache for instant responses
const quoteCache = new Map<string, { quote: AggregatedQuote; expires: number }>();
const QUOTE_CACHE_DURATION = 15000; // 15 seconds

export class InstantLiquidityService implements InstantLiquidityAPI {
  
  // Get instant quote with multiple provider options
  async getInstantQuote(params: QuoteParams): Promise<InstantQuoteResponse> {
    try {
      console.log(`🔍 Getting instant quote: ${params.amount} ${params.fromToken} → ${params.toToken}`);
      
      // Validate parameters
      if (!params.fromToken || !params.toToken || !params.amount || params.amount <= 0) {
        return {
          success: false,
          error: 'Invalid parameters: fromToken, toToken, and amount are required',
          timestamp: Date.now()
        };
      }

      // Check cache first
      const cacheKey = `${params.fromToken}-${params.toToken}-${params.amount}`;
      const cached = quoteCache.get(cacheKey);
      if (cached && cached.expires > Date.now()) {
        console.log('📋 Returning cached quote');
        return this.formatQuoteResponse(cached.quote, true);
      }

      // Get fresh quotes from all providers
      const aggregatedQuote = await liquidityAggregator.getInstantQuotes(
        params.fromToken,
        params.toToken,
        params.amount,
        params.fromChain
      );

      // Cache the result
      quoteCache.set(cacheKey, {
        quote: aggregatedQuote,
        expires: Date.now() + QUOTE_CACHE_DURATION
      });

      return this.formatQuoteResponse(aggregatedQuote, false);

    } catch (error) {
      console.error('❌ Failed to get instant quote:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get quote',
        timestamp: Date.now()
      };
    }
  }

  // Execute instant swap
  async executeInstantSwap(params: SwapParams): Promise<SwapResponse> {
    try {
      console.log(`⚡ Executing instant swap with quote ${params.quoteId}`);

      // Find the quote (in real implementation, you'd store quotes with IDs)
      // For now, we'll simulate execution
      const mockQuote: InstantQuote = {
        provider: '1inch',
        fromAmount: 1,
        toAmount: 0.95,
        rate: 0.95,
        fee: 0.003,
        executionTime: 15000,
        slippage: 0.5,
        valid: true,
        expires: Date.now() + 30000
      };

      const result = await liquidityAggregator.executeInstantSwap(
        mockQuote,
        params.userAddress,
        params.recipientAddress
      );

      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Swap execution failed',
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        result: {
          transactionId: result.transactionId || '',
          status: 'pending',
          fromAmount: mockQuote.fromAmount,
          toAmount: mockQuote.toAmount,
          actualRate: mockQuote.rate,
          fee: mockQuote.fee,
          provider: mockQuote.provider,
          estimatedTime: result.estimatedTime,
          confirmations: 0,
          blockExplorer: this.getBlockExplorerUrl(result.transactionId || '', mockQuote.provider)
        },
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('❌ Failed to execute instant swap:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Swap execution failed',
        timestamp: Date.now()
      };
    }
  }

  // Get provider status
  async getProviderStatus(): Promise<ProviderStatusResponse> {
    try {
      const stats = liquidityAggregator.getProviderStats();
      
      return {
        success: true,
        data: {
          totalProviders: stats.totalProviders,
          activeProviders: stats.enabledProviders,
          avgReliability: stats.avgReliability,
          providers: stats.providers.map(p => ({
            name: p.name,
            status: p.enabled ? (p.reliability > 0.9 ? 'online' : 'degraded') : 'offline',
            reliability: p.reliability,
            avgExecutionTime: p.avgExecutionTime,
            supportedPairs: p.supportedChains * p.supportedTokens,
            lastUpdate: Date.now()
          }))
        },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('❌ Failed to get provider status:', error);
      return {
        success: false,
        data: {
          totalProviders: 0,
          activeProviders: 0,
          avgReliability: 0,
          providers: []
        },
        timestamp: Date.now()
      };
    }
  }

  // Get liquidity depth analysis
  async getLiquidityDepth(params: DepthParams): Promise<LiquidityDepthResponse> {
    try {
      // Simulate liquidity depth analysis
      const mockDepth = {
        fromToken: params.fromToken,
        toToken: params.toToken,
        totalLiquidity: 1000000, // $1M total liquidity
        providers: [
          { name: '1inch', liquidity: 400000, rate: 0.95, maxAmount: 100000 },
          { name: 'Jupiter', liquidity: 300000, rate: 0.94, maxAmount: 75000 },
          { name: 'ParaSwap', liquidity: 200000, rate: 0.93, maxAmount: 50000 },
          { name: 'KyberSwap', liquidity: 100000, rate: 0.92, maxAmount: 25000 }
        ],
        priceImpact: [
          { amount: 1000, impact: 0.1 },
          { amount: 5000, impact: 0.3 },
          { amount: 10000, impact: 0.7 },
          { amount: 25000, impact: 1.5 },
          { amount: 50000, impact: 3.2 }
        ]
      };

      return {
        success: true,
        data: mockDepth,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('❌ Failed to get liquidity depth:', error);
      return {
        success: false,
        data: {
          fromToken: params.fromToken,
          toToken: params.toToken,
          totalLiquidity: 0,
          providers: [],
          priceImpact: []
        },
        timestamp: Date.now()
      };
    }
  }

  // Format quote response
  private formatQuoteResponse(aggregatedQuote: AggregatedQuote, fromCache: boolean): InstantQuoteResponse {
    const bestQuote = aggregatedQuote.bestQuote;
    const quoteId = `quote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      success: true,
      quote: {
        id: quoteId,
        fromToken: 'BTC', // Would come from bestQuote
        toToken: 'ETH',   // Would come from bestQuote
        fromAmount: bestQuote.fromAmount,
        toAmount: bestQuote.toAmount,
        rate: bestQuote.rate,
        priceImpact: this.calculatePriceImpact(bestQuote),
        fee: bestQuote.fee,
        feePercentage: (bestQuote.fee / bestQuote.fromAmount) * 100,
        provider: bestQuote.provider,
        executionTime: bestQuote.executionTime,
        confidence: aggregatedQuote.confidence,
        expires: bestQuote.expires,
        alternatives: aggregatedQuote.allQuotes.slice(1, 4).map(quote => ({
          provider: quote.provider,
          toAmount: quote.toAmount,
          executionTime: quote.executionTime,
          fee: quote.fee
        }))
      },
      timestamp: Date.now()
    };
  }

  // Calculate price impact
  private calculatePriceImpact(quote: InstantQuote): number {
    // Simplified price impact calculation
    return Math.max(0, (1 - quote.rate) * 100);
  }

  // Get block explorer URL
  private getBlockExplorerUrl(txId: string, provider: string): string {
    const explorers: Record<string, string> = {
      '1inch': 'https://etherscan.io/tx/',
      'jupiter': 'https://solscan.io/tx/',
      'changenow': 'https://blockchair.com/search/',
      'thorchain': 'https://viewblock.io/thorchain/tx/'
    };
    
    return explorers[provider] ? `${explorers[provider]}${txId}` : '';
  }
}

export interface DepthParams {
  fromToken: string;
  toToken: string;
  amounts?: number[];
}

// Export singleton instance
export const instantLiquidityService = new InstantLiquidityService();
export default instantLiquidityService;
