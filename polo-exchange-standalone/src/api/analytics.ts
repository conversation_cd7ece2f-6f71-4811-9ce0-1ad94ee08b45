import express from 'express';
import InstantLiquidityAggregator from '../services/InstantLiquidityAggregator.js';

const router = express.Router();
const aggregator = new InstantLiquidityAggregator();

// Get comprehensive volume metrics
router.get('/volume', async (req, res) => {
  try {
    const metrics = aggregator.getVolumeMetrics();
    res.json({
      success: true,
      data: metrics,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get liquidity metrics for all tokens
router.get('/liquidity', async (req, res) => {
  try {
    const metrics = aggregator.getLiquidityMetrics();
    res.json({
      success: true,
      data: metrics,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get top tokens by liquidity
router.get('/liquidity/top', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const topTokens = aggregator.getTopTokensByLiquidity(limit);
    res.json({
      success: true,
      data: topTokens,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get top tokens by volume
router.get('/volume/top', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const topTokens = aggregator.getTopTokensByVolume(limit);
    res.json({
      success: true,
      data: topTokens,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get swap history with filters
router.get('/swaps', async (req, res) => {
  try {
    const filters: any = {};
    
    if (req.query.token) filters.token = req.query.token as string;
    if (req.query.provider) filters.provider = req.query.provider as string;
    if (req.query.status) filters.status = req.query.status as string;
    if (req.query.limit) filters.limit = parseInt(req.query.limit as string);
    if (req.query.timeframe) filters.timeframe = parseInt(req.query.timeframe as string);

    const swaps = aggregator.getSwapHistory(filters);
    res.json({
      success: true,
      data: swaps,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get provider statistics
router.get('/providers', async (req, res) => {
  try {
    const stats = aggregator.getProviderStats();
    res.json({
      success: true,
      data: stats,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get comprehensive analytics report
router.get('/report', async (req, res) => {
  try {
    const report = aggregator.generateAnalyticsReport();
    res.json({
      success: true,
      data: {
        report,
        generated: new Date().toISOString()
      },
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get analytics dashboard data (all metrics in one call)
router.get('/dashboard', async (req, res) => {
  try {
    const volumeMetrics = aggregator.getVolumeMetrics();
    const liquidityMetrics = aggregator.getLiquidityMetrics();
    const topLiquidityTokens = aggregator.getTopTokensByLiquidity(5);
    const topVolumeTokens = aggregator.getTopTokensByVolume(5);
    const providerStats = aggregator.getProviderStats();
    const recentSwaps = aggregator.getSwapHistory({ limit: 10 });

    res.json({
      success: true,
      data: {
        volume: volumeMetrics,
        liquidity: {
          all: liquidityMetrics,
          topByLiquidity: topLiquidityTokens,
          topByVolume: topVolumeTokens
        },
        providers: providerStats,
        recentSwaps,
        summary: {
          totalVolume: volumeMetrics.totalVolumeUSD,
          volume24h: volumeMetrics.volume24h,
          totalSwaps: volumeMetrics.totalSwaps,
          swaps24h: volumeMetrics.swaps24h,
          totalFees: volumeMetrics.totalFeesCollected,
          fees24h: volumeMetrics.fees24h,
          activeProviders: providerStats.enabledProviders,
          avgReliability: providerStats.avgReliability
        }
      },
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update liquidity for a token (admin endpoint)
router.post('/liquidity/update', async (req, res) => {
  try {
    const { token, amount, usdValue } = req.body;
    
    if (!token || amount === undefined || usdValue === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: token, amount, usdValue'
      });
    }

    await aggregator.updateTokenLiquidity(token, amount, usdValue);
    
    res.json({
      success: true,
      message: `Liquidity updated for ${token}`,
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get specific token analytics
router.get('/token/:symbol', async (req, res) => {
  try {
    const symbol = req.params.symbol.toUpperCase();
    const liquidityMetrics = aggregator.getLiquidityMetrics();
    const tokenMetrics = liquidityMetrics.find(m => m.token === symbol);
    
    if (!tokenMetrics) {
      return res.status(404).json({
        success: false,
        error: `Token ${symbol} not found`
      });
    }

    // Get recent swaps for this token
    const recentSwaps = aggregator.getSwapHistory({ 
      token: symbol, 
      limit: 20 
    });

    res.json({
      success: true,
      data: {
        token: symbol,
        metrics: tokenMetrics,
        recentSwaps,
        rank: {
          byLiquidity: liquidityMetrics.findIndex(m => m.token === symbol) + 1,
          byVolume: liquidityMetrics
            .sort((a, b) => b.volume24h - a.volume24h)
            .findIndex(m => m.token === symbol) + 1
        }
      },
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get analytics for a specific time period
router.get('/timeframe/:period', async (req, res) => {
  try {
    const period = req.params.period;
    let timeframe: number;

    switch (period) {
      case '1h':
        timeframe = 60 * 60 * 1000;
        break;
      case '24h':
        timeframe = 24 * 60 * 60 * 1000;
        break;
      case '7d':
        timeframe = 7 * 24 * 60 * 60 * 1000;
        break;
      case '30d':
        timeframe = 30 * 24 * 60 * 60 * 1000;
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid period. Use: 1h, 24h, 7d, or 30d'
        });
    }

    const swaps = aggregator.getSwapHistory({ timeframe });
    const totalVolume = swaps.reduce((sum, swap) => sum + swap.usdValue, 0);
    const totalFees = swaps.reduce((sum, swap) => sum + swap.feeUSD, 0);

    res.json({
      success: true,
      data: {
        period,
        timeframe,
        swapCount: swaps.length,
        totalVolume,
        totalFees,
        avgSwapSize: swaps.length > 0 ? totalVolume / swaps.length : 0,
        swaps
      },
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
