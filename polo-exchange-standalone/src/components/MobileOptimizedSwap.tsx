import React, { useState, useEffect, useMemo, lazy, Suspense } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRightLeft, Loader2 } from 'lucide-react';

// Lazy load heavy components for better mobile performance
const HoudiniSwapWidget = lazy(() => import('./HoudiniSwapWidget'));

// Mobile-optimized currency list (top 20 most popular)
const MOBILE_CURRENCIES = [
  'BTC', 'ETH', 'SOL', 'USDC', 'USDT', 'BNB', 'ADA', 'DOT', 'MATIC', 'AVAX',
  'LINK', 'UNI', 'LTC', 'BCH', 'XRP', 'DOGE', 'SHIB', 'ATOM', 'NEAR', 'FIL'
];

interface MobileOptimizedSwapProps {
  isMobile?: boolean;
}

const MobileOptimizedSwap: React.FC<MobileOptimizedSwapProps> = ({ isMobile = false }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [useFullVersion, setUseFullVersion] = useState(false);

  // Detect if user wants full version
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('full') === 'true') {
      setUseFullVersion(true);
    }
    setIsLoading(false);
  }, []);

  // Mobile-specific optimizations
  const mobileOptimizations = useMemo(() => ({
    // Reduce animations on mobile for better performance
    reduceMotion: isMobile,
    // Lazy load images
    lazyImages: isMobile,
    // Simplified currency list
    limitedCurrencies: isMobile ? MOBILE_CURRENCIES : undefined,
    // Debounce API calls more aggressively
    debounceMs: isMobile ? 1000 : 500,
  }), [isMobile]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-[#080c15] to-[#0e1b38]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#20c4cb] mx-auto mb-4" />
          <div className="text-white text-sm">Loading Polo Exchange...</div>
        </div>
      </div>
    );
  }

  // Show full version if requested or on desktop
  if (useFullVersion || !isMobile) {
    return (
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-[#080c15] to-[#0e1b38]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-[#20c4cb] mx-auto mb-4" />
            <div className="text-white text-sm">Loading Full Exchange...</div>
          </div>
        </div>
      }>
        <HoudiniSwapWidget />
      </Suspense>
    );
  }

  // Mobile-optimized lightweight version
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#080c15] to-[#0e1b38] p-2">
      <Card className="w-full max-w-sm mx-auto border-0 bg-gradient-to-br from-[#0a0e1a] to-[#0e1b38] text-white">
        <CardHeader className="pb-4">
          <CardTitle className="text-center text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#20c4cb] to-[#9d55ff]">
            POLO SWAP
          </CardTitle>
          <div className="text-center text-xs text-gray-400">
            Mobile Quick Swap
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Quick Swap Interface */}
          <div className="space-y-3">
            <div>
              <Label className="text-xs text-[#20c4cb]">From</Label>
              <Select>
                <SelectTrigger className="bg-[#121a2a] border-[rgba(32,196,203,0.3)]">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {MOBILE_CURRENCIES.slice(0, 10).map(currency => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs text-[#20c4cb]">Amount</Label>
              <Input 
                type="number" 
                placeholder="0.00"
                className="bg-[#121a2a] border-[rgba(32,196,203,0.3)] text-white"
              />
            </div>

            <div className="flex justify-center">
              <Button size="sm" variant="ghost" className="text-[#20c4cb]">
                <ArrowRightLeft className="h-4 w-4" />
              </Button>
            </div>

            <div>
              <Label className="text-xs text-[#9d55ff]">To</Label>
              <Select>
                <SelectTrigger className="bg-[#121a2a] border-[rgba(157,85,255,0.3)]">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {MOBILE_CURRENCIES.slice(0, 10).map(currency => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs text-gray-400">Wallet Address</Label>
              <Input 
                placeholder="Enter recipient address"
                className="bg-[#121a2a] border-[rgba(32,196,203,0.3)] text-white text-xs"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2 pt-4">
            <Button className="w-full bg-gradient-to-r from-[#20c4cb] to-[#9d55ff] hover:from-[#9d55ff] hover:to-[#20c4cb] text-black font-bold">
              Quick Swap
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full border-[rgba(32,196,203,0.3)] text-[#20c4cb] text-xs"
              onClick={() => setUseFullVersion(true)}
            >
              Use Full Exchange
            </Button>
          </div>

          {/* Mobile-specific features */}
          <div className="text-center text-xs text-gray-500 pt-2">
            <div>⚡ Optimized for mobile</div>
            <div>🔒 3% privacy fee</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MobileOptimizedSwap;
