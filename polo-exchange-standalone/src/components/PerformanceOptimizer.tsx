import React, { useEffect, useState } from 'react';
import { useMobileDetection } from '../hooks/useMobileDetection';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
}

const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = ({ children }) => {
  const { isMobile, isSlowConnection } = useMobileDetection();
  const [isOptimized, setIsOptimized] = useState(false);

  useEffect(() => {
    // Apply mobile-specific optimizations
    if (isMobile || isSlowConnection) {
      // Reduce animation duration for mobile
      document.documentElement.style.setProperty('--animation-duration', '0.2s');
      
      // Disable heavy animations on slow connections
      if (isSlowConnection) {
        document.documentElement.style.setProperty('--animation-duration', '0s');
        document.documentElement.classList.add('reduce-motion');
      }

      // Optimize images for mobile
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.loading) {
          img.loading = 'lazy';
        }
      });

      // Reduce particle effects on mobile
      document.documentElement.classList.add('mobile-optimized');
      
      setIsOptimized(true);
    }

    // Preload critical resources
    const preloadCritical = () => {
      // Preload fonts
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.as = 'font';
      fontLink.type = 'font/woff2';
      fontLink.crossOrigin = 'anonymous';
      document.head.appendChild(fontLink);
    };

    preloadCritical();
  }, [isMobile, isSlowConnection]);

  // Add mobile-specific CSS optimizations
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Mobile Performance Optimizations */
      .mobile-optimized * {
        will-change: auto !important;
      }
      
      .mobile-optimized .animate-pulse {
        animation-duration: 1s !important;
      }
      
      .mobile-optimized .animate-spin {
        animation-duration: 0.5s !important;
      }
      
      .reduce-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
      
      /* Optimize gradients for mobile */
      @media (max-width: 768px) {
        .bg-gradient-to-br,
        .bg-gradient-to-r {
          background: #0e1b38 !important;
        }
        
        /* Simplify shadows on mobile */
        .shadow-lg,
        .shadow-xl {
          box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
        
        /* Reduce backdrop blur on mobile */
        .backdrop-blur-sm {
          backdrop-filter: none !important;
          background: rgba(14, 27, 56, 0.95) !important;
        }
      }
      
      /* Optimize for slow connections */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return <>{children}</>;
};

export default PerformanceOptimizer;
