import React from 'react';

const NEARTest: React.FC = () => {
  console.log('🔍 NEARTest component loaded');

  const currencies = [
    { value: 'BTC', label: 'Bitcoin (BTC)', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1.png' },
    { value: 'ETH', label: 'Ethereum (ETH)', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png' },
    { value: 'NEAR', label: 'Near Protocol (NEAR)', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6535.png' },
    { value: 'USDC', label: 'USD Coin (USDC)', logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png' }
  ];

  console.log('🎯 Currencies array:', currencies);
  console.log('🔍 NEAR found:', currencies.find(c => c.value === 'NEAR'));

  return (
    <div className="min-h-screen bg-red-900 text-white p-8">
      <div className="max-w-md mx-auto">
        <h1 className="text-4xl font-bold mb-6 text-center text-yellow-400">🚨 NEAR PROTOCOL TEST 🚨</h1>

        <div className="bg-green-800 p-4 rounded-lg mb-6 text-center">
          <h2 className="text-2xl font-bold text-white">THIS PAGE SHOULD BE VISIBLE</h2>
          <p className="text-lg">If you can see this, the test page is working</p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6 space-y-4">
          <h2 className="text-lg font-semibold">Available Currencies:</h2>
          
          <div className="space-y-2">
            {currencies.map(currency => (
              <div key={currency.value} className="flex items-center gap-3 p-3 bg-gray-700 rounded-md">
                <img 
                  src={currency.logo} 
                  alt={currency.value} 
                  className="h-8 w-8 object-contain" 
                />
                <div>
                  <div className="font-medium">{currency.label}</div>
                  <div className="text-sm text-gray-400">{currency.value}</div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-blue-900 rounded-md">
            <h3 className="font-semibold mb-2">Test Results:</h3>
            <div className="text-sm space-y-1">
              <div>✅ NEAR logo loading: <img src="https://s2.coinmarketcap.com/static/img/coins/64x64/6535.png" alt="NEAR" className="inline h-4 w-4 ml-1" /></div>
              <div>✅ NEAR in list: {currencies.find(c => c.value === 'NEAR') ? 'FOUND' : 'NOT FOUND'}</div>
              <div>✅ Total currencies: {currencies.length}</div>
            </div>
          </div>
          
          <div className="mt-4">
            <label className="block text-sm font-medium mb-2">Select Currency:</label>
            <select className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white">
              <option value="">Choose a currency...</option>
              {currencies.map(currency => (
                <option key={currency.value} value={currency.value}>
                  {currency.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NEARTest;
