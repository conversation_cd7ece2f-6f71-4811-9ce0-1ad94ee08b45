// Complete token configuration for Ghost Swap Cross-Chain Bridge
// All 35+ supported tokens across 6 chains

export interface TokenConfig {
  symbol: string;
  name: string;
  decimals: number;
  logo: string;
  addresses: {
    ethereum?: string;
    polygon?: string;
    bsc?: string;
    arbitrum?: string;
    optimism?: string;
    avalanche?: string;
    solana?: string;
    hyperliquid?: string;
  };
  priority: number;
  category: string;
}

// TIER 1: Highest Volume Tokens (Stablecoins & Major Crypto)
export const TIER_1_TOKENS: Record<string, TokenConfig> = {
  USDC: {
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************',
      solana: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    },
    priority: 1,
    category: 'Stablecoin'
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/825.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************',
      solana: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
    },
    priority: 1,
    category: 'Stablecoin'
  },
  BTC: {
    symbol: 'BTC',
    name: 'Bitcoin',
    decimals: 8,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************',
      solana: '9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E'
    },
    priority: 1,
    category: 'Major Crypto'
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************',
      solana: '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs'
    },
    priority: 1,
    category: 'Major Crypto'
  },
  SOL: {
    symbol: 'SOL',
    name: 'Solana',
    decimals: 9,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      solana: 'So11111111111111111111111111111111111111112'
    },
    priority: 1,
    category: 'Major Crypto'
  },
  BNB: {
    symbol: 'BNB',
    name: 'BNB',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      arbitrum: '******************************************',
      avalanche: '******************************************',
      bsc: '******************************************' // Native on BSC
    },
    priority: 1,
    category: 'Major Crypto'
  },
  HYPE: {
    symbol: 'HYPE',
    name: 'Hyperliquid',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/32196.png',
    addresses: {
      hyperliquid: '******************************************', // Native gas token on HyperEVM
      ethereum: '******************************************',
      arbitrum: '******************************************',
      avalanche: '******************************************' // Wrapped HYPE on Avalanche
    },
    priority: 1,
    category: 'Major Crypto'
  },
  KAS: {
    symbol: 'KAS',
    name: 'Kaspa',
    decimals: 8,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/20396.png',
    addresses: {
      kaspa: 'kaspa:native', // Native KAS on Kaspa network
      ethereum: '******************************************', // Wrapped KAS on Ethereum
      bsc: '******************************************', // Wrapped KAS on BSC
      arbitrum: '******************************************' // Wrapped KAS on Arbitrum
    },
    priority: 1,
    category: 'Major Crypto'
  }
};

// TIER 2: High Volume Tokens (Layer 1 Natives)
export const TIER_2_TOKENS: Record<string, TokenConfig> = {
  MATIC: {
    symbol: 'MATIC',
    name: 'Polygon',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3890.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      avalanche: '******************************************'
    },
    priority: 2,
    category: 'Layer 1'
  },
  AVAX: {
    symbol: 'AVAX',
    name: 'Avalanche',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5805.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************'
    },
    priority: 2,
    category: 'Layer 1'
  },
  FTM: {
    symbol: 'FTM',
    name: 'Fantom',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3513.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 2,
    category: 'Layer 1'
  },
  OP: {
    symbol: 'OP',
    name: 'Optimism',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11840.png',
    addresses: {
      ethereum: '******************************************',
      optimism: '******************************************', // Native on Optimism
      arbitrum: '******************************************',
      polygon: '******************************************'
    },
    priority: 2,
    category: 'Layer 2'
  },
  BLAST: {
    symbol: 'BLAST',
    name: 'Blast',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/28480.png',
    addresses: {
      ethereum: '******************************************',
      blast: '******************************************', // Native on Blast
      arbitrum: '******************************************'
    },
    priority: 2,
    category: 'Layer 2'
  }
};

// TIER 3: DeFi & Meme Coins (High Volume Potential)
export const TIER_3_TOKENS: Record<string, TokenConfig> = {
  LINK: {
    symbol: 'LINK',
    name: 'Chainlink',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1975.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'DeFi'
  },
  UNI: {
    symbol: 'UNI',
    name: 'Uniswap',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7083.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'DeFi'
  },
  AAVE: {
    symbol: 'AAVE',
    name: 'Aave',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/7278.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'DeFi'
  },
  CRV: {
    symbol: 'CRV',
    name: 'Curve DAO Token',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6538.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************',
      bsc: '******************************************'
    },
    priority: 3,
    category: 'DeFi'
  },
  DOGE: {
    symbol: 'DOGE',
    name: 'Dogecoin',
    decimals: 8,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/74.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'Meme'
  },
  SHIB: {
    symbol: 'SHIB',
    name: 'Shiba Inu',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5994.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'Meme'
  },
  PEPE: {
    symbol: 'PEPE',
    name: 'Pepe',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/24478.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 3,
    category: 'Meme'
  },
  DARKPINO: {
    symbol: 'DARKPINO',
    name: 'Dark Pino',
    decimals: 9,
    logo: 'https://assets.coingecko.com/coins/images/56032/standard/darkpinooriginal.jpg?1748077000',
    addresses: {
      solana: '4fwCUiZ8qaddK3WFLXazXRtpYpHc39iYLnEfF7KjmoEy', // Official Solana contract
      ethereum: '******************************************', // Bridged versions
      bsc: '******************************************',
      polygon: '******************************************'
    },
    priority: 3,
    category: 'Solana Meme'
  },
  IOTA: {
    symbol: 'IOTA',
    name: 'IOTA',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1720.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************',
      iota: 'iota:native' // Native IOTA network
    },
    priority: 3,
    category: 'Layer 1'
  },
  LINEA: {
    symbol: 'LINEA',
    name: 'Linea',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/27657.png',
    addresses: {
      ethereum: '******************************************',
      linea: '******************************************', // Native on Linea
      arbitrum: '******************************************'
    },
    priority: 3,
    category: 'Layer 2'
  },
  MANTLE: {
    symbol: 'MNT',
    name: 'Mantle',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/27075.png',
    addresses: {
      ethereum: '******************************************',
      mantle: '******************************************', // Native on Mantle
      bsc: '******************************************'
    },
    priority: 3,
    category: 'Layer 2'
  },
  METIS: {
    symbol: 'METIS',
    name: 'Metis',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/9640.png',
    addresses: {
      ethereum: '******************************************',
      metis: '******************************************', // Native on Metis
      polygon: '******************************************'
    },
    priority: 3,
    category: 'Layer 2'
  },
  TAO: {
    symbol: 'TAO',
    name: 'Bittensor',
    decimals: 9,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/22974.png',
    addresses: {
      ethereum: '******************************************', // Wrapped TAO on Ethereum
      bsc: '******************************************', // Wrapped TAO on BSC
      arbitrum: '******************************************', // Wrapped TAO on Arbitrum
      polygon: '******************************************', // Wrapped TAO on Polygon
      bittensor: 'tao:native' // Native TAO on Bittensor network
    },
    priority: 3,
    category: 'AI/ML'
  },
  NEAR: {
    symbol: 'NEAR',
    name: 'Near Protocol',
    decimals: 24,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6535.png',
    addresses: {
      ethereum: '******************************************', // Wrapped NEAR on Ethereum
      bsc: '******************************************', // Wrapped NEAR on BSC
      aurora: '******************************************', // Native NEAR on Aurora
      near: 'near:native', // Native NEAR on Near Protocol
      polygon: '******************************************', // Wrapped NEAR on Polygon
      arbitrum: '******************************************' // Wrapped NEAR on Arbitrum
    },
    priority: 3,
    category: 'Layer 1'
  }
};

// TIER 4: Layer 1 Blockchains
export const TIER_4_TOKENS: Record<string, TokenConfig> = {
  TRX: {
    symbol: 'TRX',
    name: 'TRON',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1958.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      optimism: '******************************************',
      avalanche: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  ADA: {
    symbol: 'ADA',
    name: 'Cardano',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/2010.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  DOT: {
    symbol: 'DOT',
    name: 'Polkadot',
    decimals: 10,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6636.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  ATOM: {
    symbol: 'ATOM',
    name: 'Cosmos',
    decimals: 6,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3794.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  BERA: {
    symbol: 'BERA',
    name: 'Berachain',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/30220.png',
    addresses: {
      ethereum: '******************************************', // Testnet only currently
      berachain: 'bera:native', // Native on Berachain
      arbitrum: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  PLS: {
    symbol: 'PLS',
    name: 'PulseChain',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/11145.png',
    addresses: {
      ethereum: '******************************************',
      pulsechain: 'pls:native', // Native on PulseChain
      bsc: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  SONIC: {
    symbol: 'SONIC',
    name: 'Sonic',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/32370.png',
    addresses: {
      ethereum: '******************************************',
      sonic: 'sonic:native', // Native on Sonic network
      fantom: '******************************************'
    },
    priority: 4,
    category: 'Layer 1'
  },
  BEAM: {
    symbol: 'BEAM',
    name: 'Beam',
    decimals: 8,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3702.png',
    addresses: {
      ethereum: '******************************************',
      beam: 'beam:native', // Native on Beam network
      bsc: '******************************************'
    },
    priority: 4,
    category: 'Privacy'
  }
};

// TIER 5: DeFi, Gaming & Enterprise
export const TIER_5_TOKENS: Record<string, TokenConfig> = {
  COMP: {
    symbol: 'COMP',
    name: 'Compound',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/5692.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************',
      arbitrum: '******************************************',
      avalanche: '******************************************'
    },
    priority: 5,
    category: 'DeFi'
  },
  SAND: {
    symbol: 'SAND',
    name: 'The Sandbox',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/6210.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************'
    },
    priority: 5,
    category: 'Gaming'
  },
  MANA: {
    symbol: 'MANA',
    name: 'Decentraland',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1966.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      bsc: '******************************************'
    },
    priority: 5,
    category: 'Gaming'
  },
  APE: {
    symbol: 'APE',
    name: 'ApeCoin',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/18876.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
      arbitrum: '******************************************'
    },
    priority: 5,
    category: 'Gaming'
  },
  WLD: {
    symbol: 'WLD',
    name: 'Worldcoin',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/13502.png',
    addresses: {
      ethereum: '******************************************',
      worldchain: 'wld:native', // Native on World Chain
      optimism: '******************************************',
      polygon: '******************************************'
    },
    priority: 5,
    category: 'AI'
  },
  SAI: {
    symbol: 'SAI',
    name: 'Sai',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/1455.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************'
    },
    priority: 5,
    category: 'Stablecoin'
  },
  SPX: {
    symbol: 'SPX6900',
    name: 'SPX6900',
    decimals: 8,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/32797.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 5,
    category: 'Meme'
  },
  ABSTRACT: {
    symbol: 'ABS',
    name: 'Abstract',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/32000.png',
    addresses: {
      ethereum: '******************************************',
      abstract: 'abs:native', // Native on Abstract network
      arbitrum: '******************************************'
    },
    priority: 5,
    category: 'Layer 2'
  },

};

// TIER 6: Enterprise & Privacy
export const TIER_6_TOKENS: Record<string, TokenConfig> = {
  VET: {
    symbol: 'VET',
    name: 'VeChain',
    decimals: 18,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/3077.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 6,
    category: 'Enterprise'
  },
  FLOKI: {
    symbol: 'FLOKI',
    name: 'FLOKI',
    decimals: 9,
    logo: 'https://s2.coinmarketcap.com/static/img/coins/64x64/10804.png',
    addresses: {
      ethereum: '******************************************',
      bsc: '******************************************'
    },
    priority: 6,
    category: 'Meme'
  }
};

// Combine all tokens
export const ALL_TOKENS = {
  ...TIER_1_TOKENS,
  ...TIER_2_TOKENS,
  ...TIER_3_TOKENS,
  ...TIER_4_TOKENS,
  ...TIER_5_TOKENS,
  ...TIER_6_TOKENS
};

// Get tokens by category
export const getTokensByCategory = () => {
  const tokens = Object.values(ALL_TOKENS);
  return {
    stablecoins: tokens.filter(t => t.category === 'Stablecoin'),
    majorCrypto: tokens.filter(t => t.category === 'Major Crypto'),
    layer1: tokens.filter(t => t.category === 'Layer 1'),
    defi: tokens.filter(t => t.category === 'DeFi'),
    meme: tokens.filter(t => t.category === 'Meme'),
    gaming: tokens.filter(t => t.category === 'Gaming'),
    enterprise: tokens.filter(t => t.category === 'Enterprise'),
    privacy: tokens.filter(t => t.category === 'Privacy')
  };
};

// Get tokens for specific chain
export const getTokensForChain = (chainKey: string) => {
  return Object.entries(ALL_TOKENS)
    .filter(([_, token]) => token.addresses[chainKey as keyof typeof token.addresses])
    .reduce((acc, [symbol, token]) => {
      acc[symbol] = {
        ...token,
        address: token.addresses[chainKey as keyof typeof token.addresses]
      };
      return acc;
    }, {} as Record<string, TokenConfig & { address: string }>);
};

export default ALL_TOKENS;
