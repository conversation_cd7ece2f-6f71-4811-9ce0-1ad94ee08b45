# 🔐 Polo Exchange Wallet Configuration Guide

## Overview

Your Polo Exchange uses a **custodial model** where users send crypto to your operational wallets, and you execute swaps using LiFi's unlimited liquidity network. This guide helps you set up and configure these operational wallets.

## 🚀 Quick Setup

### Step 1: Generate Wallets
```bash
npm run setup-wallets
```

This script will:
- ✅ Check your current wallet configuration
- 🎲 Generate missing wallets automatically
- 📝 Update your `.env` file with new private keys
- 📋 Display funding instructions

### Step 2: Test Configuration
```bash
npm run test-wallets
```

This script will:
- 🔗 Test connectivity to all blockchain networks
- 💰 Check wallet balances
- 🌉 Verify LiFi API access
- 📊 Generate a comprehensive test report

### Step 3: Start LiFi Backend
```bash
npm run lifi-backend
```

This starts your swap execution server that:
- 🚀 Executes real LiFi swaps using your wallets
- 💱 Handles cross-chain transactions
- 📡 Provides API endpoints for your frontend

## 🌐 Supported Networks

Your exchange supports these blockchain networks:

| Network | Native Token | Purpose | RPC Endpoint |
|---------|-------------|---------|--------------|
| **Ethereum** | ETH | ETH, USDC, USDT, LINK, UNI, etc. | Infura/Alchemy |
| **BSC** | BNB | BNB, BUSD, etc. | Binance RPC |
| **Polygon** | MATIC | MATIC, USDC.e, etc. | Polygon RPC |
| **Avalanche** | AVAX | AVAX, USDC.e, etc. | Avalanche RPC |
| **Arbitrum** | ETH | ETH, USDC, etc. | Arbitrum RPC |
| **Optimism** | ETH | ETH, USDC, etc. | Optimism RPC |
| **Solana** | SOL | SOL, HYPE, etc. | GetBlock/Solana RPC |

## 💰 Funding Your Wallets

After generating wallets, fund them with operational amounts:

### Recommended Starting Amounts:

```
🔹 Ethereum: 0.5 ETH + 1,000 USDC
   Purpose: Gas fees + ERC20 token swaps

🔹 BSC: 1 BNB + 500 BUSD  
   Purpose: Gas fees + BEP20 token swaps

🔹 Polygon: 100 MATIC + 500 USDC
   Purpose: Gas fees + Polygon token swaps

🔹 Avalanche: 10 AVAX + 500 USDC
   Purpose: Gas fees + Avalanche token swaps

🔹 Arbitrum: 0.1 ETH + 500 USDC
   Purpose: Gas fees + Arbitrum token swaps

🔹 Optimism: 0.1 ETH + 500 USDC
   Purpose: Gas fees + Optimism token swaps

🔹 Solana: 5 SOL
   Purpose: Gas fees + SPL token swaps
```

## 🔒 Security Best Practices

### 1. Private Key Security
- 🚨 **NEVER** share private keys with anyone
- 🔐 Keep your `.env` file secure
- 🚫 **NEVER** commit `.env` to version control
- 🛡️ Consider hardware wallets for large amounts

### 2. Operational Security
- 📊 Monitor wallet balances regularly
- 🔄 Set up automated balance alerts
- 💰 Keep only operational amounts in hot wallets
- 🏦 Store larger amounts in cold storage

### 3. Access Control
- 🔑 Limit server access to authorized personnel
- 🌐 Use secure RPC endpoints
- 📡 Monitor API usage and rate limits
- 🔍 Log all swap transactions

## 🛠️ Manual Configuration

If you prefer manual setup, edit your `.env` file:

```bash
# Ethereum & ERC20 tokens
ETHEREUM_PRIVATE_KEY=0x...
ETHEREUM_ADDRESS=0x...

# BSC & BEP20 tokens  
BSC_PRIVATE_KEY=0x...
BSC_ADDRESS=0x...

# Polygon & Polygon tokens
POLYGON_PRIVATE_KEY=0x...
POLYGON_ADDRESS=0x...

# Avalanche & Avalanche tokens
AVALANCHE_PRIVATE_KEY=0x...
AVALANCHE_ADDRESS=0x...

# Arbitrum & Arbitrum tokens
ARBITRUM_PRIVATE_KEY=0x...
ARBITRUM_ADDRESS=0x...

# Optimism & Optimism tokens
OPTIMISM_PRIVATE_KEY=0x...
OPTIMISM_ADDRESS=0x...

# Solana & SPL tokens
SOLANA_PRIVATE_KEY=base64_encoded_key
SOLANA_ADDRESS=solana_public_key
```

## 🔧 Troubleshooting

### Common Issues:

**❌ "Private key not configured"**
- Run `npm run setup-wallets` to generate missing keys
- Check your `.env` file for missing entries

**❌ "RPC connection failed"**
- Verify RPC URLs in `.env` file
- Check if you need API keys (Infura, Alchemy)
- Test with public RPC endpoints first

**❌ "Insufficient balance for gas"**
- Fund your wallets with native tokens for gas fees
- Check minimum balance requirements

**❌ "LiFi API key invalid"**
- Verify `LIFI_API_KEY` in `.env` file
- Check API key format and permissions

### Getting Help:

1. Run `npm run test-wallets` for diagnostic information
2. Check console logs for specific error messages
3. Verify all environment variables are set correctly
4. Test with small amounts first

## 📊 Monitoring & Maintenance

### Daily Tasks:
- ✅ Check wallet balances
- ✅ Monitor swap success rates
- ✅ Review transaction logs

### Weekly Tasks:
- ✅ Analyze swap volume and profits
- ✅ Rebalance wallet funds if needed
- ✅ Update RPC endpoints if necessary

### Monthly Tasks:
- ✅ Security audit of wallet access
- ✅ Review and optimize fee structures
- ✅ Update to latest LiFi SDK version

## 🚀 Next Steps

1. **Generate Wallets**: `npm run setup-wallets`
2. **Fund Wallets**: Send operational crypto to generated addresses
3. **Test Configuration**: `npm run test-wallets`
4. **Start Backend**: `npm run lifi-backend`
5. **Test Swaps**: Execute small test swaps through your frontend
6. **Monitor Operations**: Set up balance alerts and logging
7. **Scale Up**: Increase wallet funding as volume grows

Your exchange is now ready to execute real swaps with LiFi's $2B+ liquidity network! 🎉
