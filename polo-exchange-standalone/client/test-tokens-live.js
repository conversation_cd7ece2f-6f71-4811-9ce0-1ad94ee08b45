// Test tokens are loading correctly in the live server
import dotenv from 'dotenv';
dotenv.config();

console.log('🔍 TESTING LIVE TOKEN CONFIGURATION');
console.log('=' .repeat(50));

async function testLiveTokens() {
  try {
    console.log('📡 Fetching live website...');
    const response = await fetch('http://localhost:3000');
    
    if (!response.ok) {
      console.log('❌ Server error:', response.status);
      return;
    }
    
    const html = await response.text();
    
    console.log('\n🔍 Searching for tokens in HTML...');
    
    // Check for CRV
    const hasCRV = html.includes('CRV') || html.includes('Curve DAO');
    const hasCRVSymbol = html.includes('"CRV"') || html.includes("'CRV'");
    
    // Check for INK (should be removed)
    const hasINK = html.includes('INK') && html.includes('Kraken');
    const hasINKSymbol = html.includes('"INK"') || html.includes("'INK'");
    
    // Check for other tokens
    const hasLINK = html.includes('LINK') || html.includes('Chainlink');
    const hasSOL = html.includes('SOL') || html.includes('Solana');
    const hasETH = html.includes('ETH') || html.includes('Ethereum');
    
    console.log('\n📊 Token Detection Results:');
    console.log(`${hasCRV ? '✅' : '❌'} CRV Found: ${hasCRV}`);
    console.log(`${hasCRVSymbol ? '✅' : '❌'} CRV Symbol: ${hasCRVSymbol}`);
    console.log(`${hasINK ? '❌' : '✅'} INK Removed: ${!hasINK}`);
    console.log(`${hasINKSymbol ? '❌' : '✅'} INK Symbol Removed: ${!hasINKSymbol}`);
    console.log(`${hasLINK ? '✅' : '❌'} LINK (Chainlink) Found: ${hasLINK}`);
    console.log(`${hasSOL ? '✅' : '❌'} SOL Found: ${hasSOL}`);
    console.log(`${hasETH ? '✅' : '❌'} ETH Found: ${hasETH}`);
    
    // Check for token configuration patterns
    const hasTokenConfig = html.includes('ALL_TOKENS') || html.includes('AVAILABLE_CURRENCIES');
    const hasTokenImport = html.includes('config/tokens');
    
    console.log('\n🔧 Configuration Check:');
    console.log(`${hasTokenConfig ? '✅' : '❌'} Token Config: ${hasTokenConfig}`);
    console.log(`${hasTokenImport ? '✅' : '✅'} Token Import: ${hasTokenImport}`);
    
    // Look for specific CRV patterns
    if (html.includes('curve-dao-token')) {
      console.log('✅ CRV CoinGecko ID found: curve-dao-token');
    }
    
    if (html.includes('6538')) {
      console.log('✅ CRV CoinMarketCap ID found: 6538');
    }
    
    // Check bundle size and content
    const bundleMatches = html.match(/assets\/index-[a-zA-Z0-9]+\.js/g);
    if (bundleMatches) {
      console.log(`\n📦 JavaScript Bundles: ${bundleMatches.length} found`);
      bundleMatches.forEach(bundle => console.log(`   - ${bundle}`));
    }
    
    console.log('\n🎯 Recommendations:');
    if (!hasCRV) {
      console.log('❌ CRV not found - try hard refresh (Cmd+Shift+R)');
      console.log('💡 Clear browser cache completely');
      console.log('💡 Try incognito/private browsing mode');
    } else {
      console.log('✅ CRV should be visible in dropdowns');
    }
    
    if (hasINK) {
      console.log('❌ INK still present - server restart needed');
    } else {
      console.log('✅ INK successfully removed');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('💡 Make sure server is running on localhost:3000');
  }
}

// Run the test
testLiveTokens().catch(console.error);
