// Test if CRV is available in the UI
import dotenv from 'dotenv';
dotenv.config();

console.log('🔵 TESTING CRV AVAILABILITY ON WEBSITE');
console.log('=' .repeat(50));

async function testCRVAvailability() {
  console.log('🔍 Checking if CRV appears in currency dropdowns...');
  
  try {
    // Test the localhost server
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      const html = await response.text();
      
      // Check if CRV is mentioned in the HTML
      const hasCRV = html.includes('CRV') || html.includes('Curve DAO');
      const hasTokenConfig = html.includes('curve-dao-token');
      
      console.log('\n📊 CRV Detection Results:');
      console.log(`✅ Server Response: ${response.status} OK`);
      console.log(`${hasCRV ? '✅' : '❌'} CRV Symbol Found: ${hasCRV}`);
      console.log(`${hasTokenConfig ? '✅' : '❌'} Token Config Found: ${hasTokenConfig}`);
      
      if (hasCRV) {
        console.log('\n🎉 SUCCESS: CRV is available on your website!');
        console.log('✅ CRV should appear in the currency dropdowns');
        console.log('✅ You can now select CRV → USDC or ETH → CRV');
        console.log('✅ Price should show around $0.85 per CRV');
      } else {
        console.log('\n⚠️ CRV not detected in HTML response');
        console.log('💡 The token config might need a browser refresh');
        console.log('💡 Try hard refresh (Cmd+Shift+R) on the website');
      }
      
    } else {
      console.log(`❌ Server error: ${response.status}`);
    }
    
  } catch (error) {
    console.log('❌ Connection error:', error.message);
    console.log('💡 Make sure the server is running on localhost:3000');
  }
}

async function printInstructions() {
  console.log('\n📋 MANUAL VERIFICATION STEPS:');
  console.log('1. Go to: https://healing-uh-legislative-brighton.trycloudflare.com');
  console.log('2. Click the "From" currency dropdown');
  console.log('3. Look for "CRV" in the list');
  console.log('4. If not visible, try hard refresh (Cmd+Shift+R)');
  console.log('5. Select CRV → USDC');
  console.log('6. Enter amount: 1000');
  console.log('7. Should show ~$850 USD value');
  
  console.log('\n🔧 If CRV is still missing:');
  console.log('• Check browser console for errors');
  console.log('• Verify token config was saved properly');
  console.log('• Try incognito/private browsing mode');
  console.log('• Clear browser cache and cookies');
  
  console.log('\n🎯 Expected CRV Features:');
  console.log('✅ CRV in both From/To dropdowns');
  console.log('✅ Price display: ~$0.85');
  console.log('✅ Logo: Curve DAO official logo');
  console.log('✅ Instant liquidity quotes work');
  console.log('✅ Cross-chain swaps available');
}

// Run the test
testCRVAvailability().then(() => {
  printInstructions();
}).catch(console.error);
