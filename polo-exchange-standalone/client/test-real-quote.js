// Real quote test - actually fetch quotes from providers
import dotenv from 'dotenv';
dotenv.config();

console.log('🔥 MARKO POLO CAPITAL - REAL QUOTE TEST');
console.log('=' .repeat(50));
console.log('Testing actual API calls to liquidity providers...\n');

// Test Jupiter real quote
async function testJupiterRealQuote() {
  console.log('⚡ Testing Jupiter (Solana) Real Quote...');
  
  try {
    // SOL to USDC quote
    const solMint = 'So11111111111111111111111111111111111111112';
    const usdcMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    const amount = **********; // 1 SOL in lamports
    
    const response = await fetch(
      `https://quote-api.jup.ag/v6/quote?inputMint=${solMint}&outputMint=${usdcMint}&amount=${amount}&slippageBps=50`
    );
    
    if (response.ok) {
      const data = await response.json();
      const outputAmount = parseFloat(data.outAmount) / 1e6; // Convert to USDC
      const rate = outputAmount / 1; // 1 SOL
      
      console.log('✅ Jupiter Quote Success!');
      console.log(`   Input: 1 SOL`);
      console.log(`   Output: ${outputAmount.toFixed(2)} USDC`);
      console.log(`   Rate: ${rate.toFixed(2)} USDC per SOL`);
      console.log(`   Price Impact: ${data.priceImpactPct || 'N/A'}%`);
      console.log(`   Route: ${data.routePlan?.length || 1} step(s)`);
      
      return {
        success: true,
        provider: 'Jupiter',
        fromAmount: 1,
        toAmount: outputAmount,
        rate: rate,
        executionTime: 8000
      };
    } else {
      console.log('❌ Jupiter API failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ Jupiter error:', error.message);
    return null;
  }
}

// Test LiFi real quote
async function testLiFiRealQuote() {
  console.log('\n🌉 Testing LiFi (Cross-chain) Real Quote...');
  
  if (!process.env.LIFI_API_KEY) {
    console.log('⚠️ LiFi API key not configured');
    return null;
  }
  
  try {
    // ETH to BNB cross-chain quote
    const response = await fetch(
      'https://li.quest/v1/quote?' + new URLSearchParams({
        fromChain: '1',    // Ethereum
        toChain: '56',     // BSC
        fromToken: '******************************************', // ETH
        toToken: '******************************************',   // BNB
        fromAmount: '**********0000000', // 0.01 ETH
        fromAddress: '******************************************',
        toAddress: '******************************************'
      }),
      {
        headers: {
          'x-lifi-api-key': process.env.LIFI_API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.ok) {
      const data = await response.json();
      
      if (data.estimate) {
        const inputAmount = 0.01; // ETH
        const outputAmount = parseFloat(data.estimate.toAmount) / 1e18; // BNB
        const rate = outputAmount / inputAmount;
        const executionTime = data.estimate.executionDuration || 45000;
        
        console.log('✅ LiFi Quote Success!');
        console.log(`   Input: ${inputAmount} ETH`);
        console.log(`   Output: ${outputAmount.toFixed(4)} BNB`);
        console.log(`   Rate: ${rate.toFixed(2)} BNB per ETH`);
        console.log(`   Execution Time: ~${Math.round(executionTime/1000)}s`);
        console.log(`   Route: ${data.estimate.tool || 'Cross-chain'}`);
        
        return {
          success: true,
          provider: 'LiFi',
          fromAmount: inputAmount,
          toAmount: outputAmount,
          rate: rate,
          executionTime: executionTime
        };
      } else {
        console.log('⚠️ LiFi: No estimate available');
        return null;
      }
    } else {
      const errorText = await response.text();
      console.log('❌ LiFi API failed:', response.status);
      console.log('   Error:', errorText.substring(0, 100));
      return null;
    }
  } catch (error) {
    console.log('❌ LiFi error:', error.message);
    return null;
  }
}

// Test ParaSwap real quote
async function testParaSwapRealQuote() {
  console.log('\n🔷 Testing ParaSwap (Ethereum) Real Quote...');
  
  try {
    // ETH to USDC quote
    const response = await fetch(
      'https://apiv5.paraswap.io/prices/?' + new URLSearchParams({
        srcToken: '******************************************', // ETH
        destToken: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505', // USDC (placeholder)
        amount: '**********00000000', // 0.1 ETH
        srcDecimals: '18',
        destDecimals: '6',
        network: '1'
      })
    );
    
    if (response.ok) {
      const data = await response.json();
      
      if (data.priceRoute) {
        const inputAmount = 0.1; // ETH
        const outputAmount = parseFloat(data.priceRoute.destAmount) / 1e6; // USDC
        const rate = outputAmount / inputAmount;
        
        console.log('✅ ParaSwap Quote Success!');
        console.log(`   Input: ${inputAmount} ETH`);
        console.log(`   Output: ${outputAmount.toFixed(2)} USDC`);
        console.log(`   Rate: ${rate.toFixed(2)} USDC per ETH`);
        console.log(`   DEXs: ${data.priceRoute.bestRoute?.length || 1} route(s)`);
        
        return {
          success: true,
          provider: 'ParaSwap',
          fromAmount: inputAmount,
          toAmount: outputAmount,
          rate: rate,
          executionTime: 12000
        };
      } else {
        console.log('⚠️ ParaSwap: No route available');
        return null;
      }
    } else {
      console.log('❌ ParaSwap API failed:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ ParaSwap error:', error.message);
    return null;
  }
}

// Compare all quotes
async function compareQuotes(quotes) {
  console.log('\n📊 QUOTE COMPARISON');
  console.log('=' .repeat(50));
  
  const validQuotes = quotes.filter(q => q && q.success);
  
  if (validQuotes.length === 0) {
    console.log('❌ No valid quotes received');
    return;
  }
  
  console.log(`✅ Received ${validQuotes.length} valid quote(s):\n`);
  
  validQuotes.forEach((quote, index) => {
    console.log(`${index + 1}. ${quote.provider}:`);
    console.log(`   Rate: ${quote.rate.toFixed(4)}`);
    console.log(`   Output: ${quote.toAmount.toFixed(6)}`);
    console.log(`   Speed: ~${Math.round(quote.executionTime/1000)}s`);
    console.log('');
  });
  
  // Find best quote by output amount
  const bestQuote = validQuotes.reduce((best, current) => 
    current.toAmount > best.toAmount ? current : best
  );
  
  console.log(`🏆 Best Quote: ${bestQuote.provider}`);
  console.log(`   Best Rate: ${bestQuote.rate.toFixed(4)}`);
  console.log(`   Best Output: ${bestQuote.toAmount.toFixed(6)}`);
}

// Main test function
async function runRealQuoteTests() {
  console.log('🚀 Starting real quote tests...\n');
  
  // Test all providers
  const quotes = await Promise.all([
    testJupiterRealQuote(),
    testLiFiRealQuote(),
    testParaSwapRealQuote()
  ]);
  
  // Compare results
  await compareQuotes(quotes);
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Go to: https://healing-uh-legislative-brighton.trycloudflare.com');
  console.log('2. Test the orange "Get Instant Liquidity Quotes" button');
  console.log('3. Verify quotes match the rates shown above');
  console.log('4. Test execution with small amounts');
  
  console.log('\n🌊 Real quote testing complete!');
  console.log('Your Marko Polo Capital exchange has live liquidity! 🎉');
}

runRealQuoteTests().catch(console.error);
