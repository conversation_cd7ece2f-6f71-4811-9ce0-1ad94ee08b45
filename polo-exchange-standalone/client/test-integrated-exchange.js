#!/usr/bin/env node

/**
 * Test Integrated Exchange
 * Tests all 16+ working pairs on the complete Marko Polo Capital website
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 TESTING INTEGRATED MARKO POLO CAPITAL EXCHANGE');
console.log('=' .repeat(70));

async function testIntegratedExchange() {
  console.log('🧪 Testing complete website integration...\n');

  const results = {
    websiteTests: [],
    exchangeTests: [],
    workingPairs: [],
    failedPairs: []
  };

  // Test 1: Website Accessibility
  console.log('1️⃣ Testing website accessibility...');
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('   ✅ Website accessible on localhost:3000');
      results.websiteTests.push({ test: 'Website Access', status: 'PASS' });
    } else {
      console.log(`   ❌ Website error: ${response.status}`);
      results.websiteTests.push({ test: 'Website Access', status: 'FAIL' });
    }
  } catch (error) {
    console.log(`   ❌ Website not accessible: ${error.message}`);
    results.websiteTests.push({ test: 'Website Access', status: 'FAIL' });
  }

  // Test 2: Exchange Component Integration
  console.log('\n2️⃣ Testing exchange component integration...');
  try {
    const response = await fetch('http://localhost:3000');
    const html = await response.text();
    
    if (html.includes('Marko Polo Capital Exchange')) {
      console.log('   ✅ Exchange component integrated');
      results.websiteTests.push({ test: 'Exchange Integration', status: 'PASS' });
    } else {
      console.log('   ❌ Exchange component not found');
      results.websiteTests.push({ test: 'Exchange Integration', status: 'FAIL' });
    }
  } catch (error) {
    console.log(`   ❌ Integration test failed: ${error.message}`);
    results.websiteTests.push({ test: 'Exchange Integration', status: 'FAIL' });
  }

  // Test 3: Our Tested Pairs
  console.log('\n3️⃣ Testing our 16+ working pairs...');
  
  const testedPairs = [
    // Exotic pairs (1.2% fees)
    { from: 'BNB', to: 'HYPE', category: 'exotic', expectedRate: 17.36 },
    { from: 'ETH', to: 'HYPE', category: 'exotic', expectedRate: 68.08 },
    { from: 'HYPE', to: 'ETH', category: 'exotic', expectedRate: 0.0141 },
    { from: 'HYPE', to: 'BNB', category: 'exotic', expectedRate: 0.0538 },
    
    // Cross-chain pairs (0.8% fees)
    { from: 'ETH', to: 'BNB', category: 'cross-chain', expectedRate: 3.90 },
    { from: 'BNB', to: 'ETH', category: 'cross-chain', expectedRate: 0.25 },
    { from: 'ETH', to: 'MATIC', category: 'cross-chain', expectedRate: 13392 },
    { from: 'MATIC', to: 'ETH', category: 'cross-chain', expectedRate: 0.0001 },
    { from: 'ETH', to: 'AVAX', category: 'cross-chain', expectedRate: 139.44 },
    { from: 'AVAX', to: 'ETH', category: 'cross-chain', expectedRate: 0.0067 },
    
    // Solana pairs (0.4% fees)
    { from: 'SOL', to: 'USDC', category: 'solana', expectedRate: 147 },
    { from: 'SOL', to: 'USDT', category: 'solana', expectedRate: 147 },
    { from: 'USDC', to: 'USDT', category: 'solana', expectedRate: 1.0 },
    { from: 'SOL', to: 'RAY', category: 'solana', expectedRate: 67 }
  ];

  for (const pair of testedPairs) {
    process.stdout.write(`   Testing ${pair.from} → ${pair.to}... `);
    
    const result = await testPair(pair.from, pair.to);
    if (result.success) {
      console.log(`✅ ${result.rate.toFixed(4)} rate`);
      results.workingPairs.push({
        ...pair,
        actualRate: result.rate,
        provider: result.provider
      });
    } else {
      console.log(`❌ ${result.error}`);
      results.failedPairs.push({
        ...pair,
        error: result.error
      });
    }
  }

  return results;
}

async function testPair(fromToken, toToken) {
  try {
    // Test Jupiter for Solana pairs
    const solanaTokens = ['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA'];
    
    if (solanaTokens.includes(fromToken) && solanaTokens.includes(toToken)) {
      return await testJupiterPair(fromToken, toToken);
    }
    
    // Test LiFi for other pairs
    return await testLiFiPair(fromToken, toToken);
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiterPair(fromToken, toToken) {
  try {
    const tokenMints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R'
    };

    const inputMint = tokenMints[fromToken];
    const outputMint = tokenMints[toToken];

    if (!inputMint || !outputMint) {
      return { success: false, error: 'Token not supported' };
    }

    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint,
      outputMint,
      amount: '**********', // 1 SOL
      slippageBps: '50'
    }));

    if (response.ok) {
      const quote = await response.json();
      const decimals = toToken === 'SOL' ? 9 : 6;
      const rate = parseFloat(quote.outAmount) / Math.pow(10, decimals);
      return { success: true, rate, provider: 'jupiter' };
    }
    
    return { success: false, error: `Jupiter API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFiPair(fromToken, toToken) {
  try {
    if (!process.env.LIFI_API_KEY) {
      return { success: false, error: 'No LiFi API key' };
    }

    const chainMappings = {
      'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1,
      'BNB': 56,
      'MATIC': 137,
      'AVAX': 43114,
      'HYPE': 999
    };

    const fromChain = chainMappings[fromToken] || 1;
    const toChain = chainMappings[toToken] || 1;

    const quoteParams = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '**********000000000',
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: ['HYPE'].includes(fromToken) || ['HYPE'].includes(toToken) ? '0.05' : '0.03'
    });

    const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      }
    });

    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate) {
        const rate = parseFloat(quote.estimate.toAmount) / 1e18;
        return { success: true, rate, provider: 'lifi' };
      }
    }
    
    return { success: false, error: `LiFi API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function analyzeResults(results) {
  console.log('\n' + '=' .repeat(70));
  console.log('🏆 INTEGRATED EXCHANGE TEST RESULTS');
  console.log('=' .repeat(70));

  // Website tests
  console.log('\n🌐 Website Integration Tests:');
  results.websiteTests.forEach(test => {
    const status = test.status === 'PASS' ? '✅' : '❌';
    console.log(`   ${status} ${test.test}: ${test.status}`);
  });

  // Exchange tests
  const workingCount = results.workingPairs.length;
  const failedCount = results.failedPairs.length;
  const totalTested = workingCount + failedCount;
  const successRate = totalTested > 0 ? (workingCount / totalTested * 100).toFixed(1) : '0.0';

  console.log('\n💱 Exchange Functionality Tests:');
  console.log(`   🔄 Total pairs tested: ${totalTested}`);
  console.log(`   ✅ Working pairs: ${workingCount}`);
  console.log(`   ❌ Failed pairs: ${failedCount}`);
  console.log(`   📈 Success rate: ${successRate}%`);

  // Category breakdown
  const categories = {
    exotic: results.workingPairs.filter(p => p.category === 'exotic'),
    'cross-chain': results.workingPairs.filter(p => p.category === 'cross-chain'),
    solana: results.workingPairs.filter(p => p.category === 'solana')
  };

  console.log('\n📂 Working Pairs by Category:');
  Object.entries(categories).forEach(([category, pairs]) => {
    console.log(`   🔥 ${category}: ${pairs.length} pairs`);
    pairs.forEach(pair => {
      console.log(`     • ${pair.from} → ${pair.to}: ${pair.actualRate.toFixed(4)} rate (${pair.provider})`);
    });
  });

  if (results.failedPairs.length > 0) {
    console.log('\n❌ Failed Pairs:');
    results.failedPairs.forEach(pair => {
      console.log(`   • ${pair.from} → ${pair.to}: ${pair.error}`);
    });
  }

  // Revenue analysis
  console.log('\n💰 Revenue Analysis:');
  const exoticCount = categories.exotic.length;
  const crossChainCount = categories['cross-chain'].length;
  const solanaCount = categories.solana.length;

  console.log(`   🔥 Exotic pairs (1.2% fee): ${exoticCount}`);
  console.log(`   🌉 Cross-chain pairs (0.8% fee): ${crossChainCount}`);
  console.log(`   🚀 Solana pairs (0.4% fee): ${solanaCount}`);

  const dailyRevenue = (exoticCount * 120) + (crossChainCount * 80) + (solanaCount * 40);
  console.log(`   💎 Potential daily revenue (per $10K volume): $${dailyRevenue}`);

  // Final assessment
  console.log('\n🎯 Integration Assessment:');
  const websitePassing = results.websiteTests.every(t => t.status === 'PASS');
  
  if (websitePassing && successRate >= 80) {
    console.log('   🎉 EXCELLENT! Complete integration successful!');
    console.log('   ✅ Website fully functional');
    console.log('   ✅ Exchange working perfectly');
    console.log('   ✅ All major pairs operational');
  } else if (websitePassing && successRate >= 60) {
    console.log('   ✅ GOOD! Integration mostly successful!');
    console.log('   ✅ Website functional');
    console.log('   ⚠️ Some exchange pairs need attention');
  } else {
    console.log('   ⚠️ NEEDS WORK! Integration issues detected');
    if (!websitePassing) console.log('   ❌ Website integration problems');
    if (successRate < 60) console.log('   ❌ Exchange functionality issues');
  }

  return {
    websiteWorking: websitePassing,
    exchangeSuccessRate: parseFloat(successRate),
    workingPairs: workingCount,
    totalRevenuePairs: exoticCount + crossChainCount + solanaCount
  };
}

// Run the comprehensive test
async function runIntegratedTest() {
  console.log('🧪 Starting integrated exchange test...\n');

  const startTime = Date.now();
  const results = await testIntegratedExchange();
  const endTime = Date.now();
  
  console.log(`\n⏱️ Test completed in ${Math.round((endTime - startTime) / 1000)} seconds\n`);
  
  const analysis = await analyzeResults(results);

  console.log('\n🌐 Your complete website: http://localhost:3000');
  console.log(`💰 Ready to earn from ${analysis.workingPairs} integrated pairs!`);
  console.log(`🏆 Integration success: ${analysis.websiteWorking ? 'COMPLETE' : 'PARTIAL'}`);
}

runIntegratedTest().catch(console.error);
