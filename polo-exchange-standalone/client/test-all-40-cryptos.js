#!/usr/bin/env node

/**
 * Test All 40+ Cryptos Coverage
 * Tests how many of your displayed tokens actually have working swap routes
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 TESTING ALL 40+ CRYPTO COVERAGE');
console.log('=' .repeat(70));

// Your complete token list (from frontend)
const ALL_TOKENS = [
  // Major cryptocurrencies
  'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK',
  'UNI', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX', 'ETC', 'XLM',
  
  // Stablecoins
  'USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'USDD', 'GUSD',
  
  // DeFi tokens
  'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL', '1INCH',
  
  // Layer 1s & 2s
  'FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA',
  
  // Exotic/New tokens
  'HYPE', 'SUI', 'APT', 'OP', 'ARB', 'IMX', 'LDO', 'GMX', 'DYDX',
  
  // Solana ecosystem
  'RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS'
];

async function testTokenCoverage() {
  console.log(`🧪 Testing coverage for ${ALL_TOKENS.length} tokens...\n`);

  const results = {
    totalTokens: ALL_TOKENS.length,
    workingAsFrom: [],
    workingAsTo: [],
    workingPairs: [],
    failedTokens: []
  };

  // Test each token as "from" token against popular "to" tokens
  const popularTargets = ['ETH', 'BNB', 'USDC', 'USDT', 'SOL'];
  
  for (const token of ALL_TOKENS) {
    console.log(`🔍 Testing ${token}...`);
    
    let tokenWorking = false;
    
    for (const target of popularTargets) {
      if (token === target) continue;
      
      // Test token → target
      const result1 = await testTokenPair(token, target);
      if (result1.success) {
        results.workingPairs.push(`${token} → ${target}`);
        if (!results.workingAsFrom.includes(token)) {
          results.workingAsFrom.push(token);
        }
        if (!results.workingAsTo.includes(target)) {
          results.workingAsTo.push(target);
        }
        tokenWorking = true;
      }
      
      // Test target → token
      const result2 = await testTokenPair(target, token);
      if (result2.success) {
        results.workingPairs.push(`${target} → ${token}`);
        if (!results.workingAsFrom.includes(target)) {
          results.workingAsFrom.push(target);
        }
        if (!results.workingAsTo.includes(token)) {
          results.workingAsTo.push(token);
        }
        tokenWorking = true;
      }
    }
    
    if (!tokenWorking) {
      results.failedTokens.push(token);
    }
    
    // Show progress
    const progress = ((ALL_TOKENS.indexOf(token) + 1) / ALL_TOKENS.length * 100).toFixed(1);
    process.stdout.write(`\r   Progress: ${progress}% (${token} ${tokenWorking ? '✅' : '❌'})`);
  }
  
  console.log('\n');
  return results;
}

async function testTokenPair(fromToken, toToken) {
  try {
    // Try Jupiter first (Solana tokens)
    if (['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA', 'MNGO'].includes(fromToken) &&
        ['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA', 'MNGO'].includes(toToken)) {
      return await testJupiterPair(fromToken, toToken);
    }
    
    // Try LiFi for cross-chain
    return await testLiFiPair(fromToken, toToken);
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiterPair(fromToken, toToken) {
  try {
    const tokenMints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
      'SRM': 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
      'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      'MNGO': 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac'
    };

    const inputMint = tokenMints[fromToken];
    const outputMint = tokenMints[toToken];

    if (!inputMint || !outputMint) {
      return { success: false, error: 'Token not supported by Jupiter' };
    }

    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint,
      outputMint,
      amount: '**********', // 1 SOL or equivalent
      slippageBps: '50'
    }));

    if (response.ok) {
      const quote = await response.json();
      return { success: true, provider: 'jupiter', rate: quote.outAmount };
    }
    
    return { success: false, error: `Jupiter API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFiPair(fromToken, toToken) {
  try {
    if (!process.env.LIFI_API_KEY) {
      return { success: false, error: 'No LiFi API key' };
    }

    // Chain mappings
    const chainMappings = {
      'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1, 'DAI': 1, 'LINK': 1, 'UNI': 1,
      'BNB': 56, 'BUSD': 56,
      'MATIC': 137,
      'AVAX': 43114,
      'FTM': 250,
      'HYPE': 999,
      'SUI': 101,
      'SOL': 101
    };

    const fromChain = chainMappings[fromToken] || 1;
    const toChain = chainMappings[toToken] || 1;

    const quoteParams = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '**********000000000', // 1 token
      fromAddress: '******************************************',
      toAddress: '******************************************'
    });

    const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      }
    });

    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate) {
        return { success: true, provider: 'lifi', rate: quote.estimate.toAmount };
      }
    }
    
    return { success: false, error: `LiFi API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function analyzeResults(results) {
  console.log('\n' + '=' .repeat(70));
  console.log('🏆 COMPLETE TOKEN COVERAGE ANALYSIS');
  console.log('=' .repeat(70));

  console.log(`\n📊 Coverage Statistics:`);
  console.log(`   🪙 Total tokens displayed: ${results.totalTokens}`);
  console.log(`   ✅ Tokens working as FROM: ${results.workingAsFrom.length}`);
  console.log(`   ✅ Tokens working as TO: ${results.workingAsTo.length}`);
  console.log(`   🔄 Total working pairs: ${results.workingPairs.length}`);
  console.log(`   ❌ Non-working tokens: ${results.failedTokens.length}`);

  const coveragePercent = ((results.workingAsFrom.length / results.totalTokens) * 100).toFixed(1);
  console.log(`   📈 Coverage percentage: ${coveragePercent}%`);

  console.log(`\n✅ Working Tokens (${results.workingAsFrom.length}):`);
  const workingTokens = [...new Set([...results.workingAsFrom, ...results.workingAsTo])];
  workingTokens.sort().forEach((token, index) => {
    if (index % 10 === 0) console.log('');
    process.stdout.write(`   ${token.padEnd(6)}`);
  });

  if (results.failedTokens.length > 0) {
    console.log(`\n\n❌ Non-Working Tokens (${results.failedTokens.length}):`);
    results.failedTokens.sort().forEach((token, index) => {
      if (index % 10 === 0) console.log('');
      process.stdout.write(`   ${token.padEnd(6)}`);
    });
  }

  console.log(`\n\n🔄 Sample Working Pairs:`);
  results.workingPairs.slice(0, 20).forEach((pair, index) => {
    console.log(`   ${index + 1}. ${pair}`);
  });
  
  if (results.workingPairs.length > 20) {
    console.log(`   ... and ${results.workingPairs.length - 20} more pairs`);
  }

  console.log(`\n💡 Analysis:`);
  
  if (coveragePercent >= 80) {
    console.log('   🎉 EXCELLENT coverage! Most tokens have working routes');
  } else if (coveragePercent >= 60) {
    console.log('   ✅ GOOD coverage! Majority of tokens work');
  } else if (coveragePercent >= 40) {
    console.log('   ⚠️ MODERATE coverage! Many tokens need more providers');
  } else {
    console.log('   ❌ LIMITED coverage! Need more liquidity providers');
  }

  console.log(`\n🔧 Recommendations:`);
  
  if (results.failedTokens.includes('BTC')) {
    console.log('   1. Add ChangeNOW API for BTC support');
  }
  
  if (results.failedTokens.some(t => ['AAVE', 'COMP', 'MKR'].includes(t))) {
    console.log('   2. Add 1inch API for DeFi token support');
  }
  
  if (results.failedTokens.some(t => ['ADA', 'XRP', 'DOT'].includes(t))) {
    console.log('   3. Add more cross-chain providers for alt L1s');
  }

  console.log(`\n🎯 Business Impact:`);
  console.log(`   📈 Actual working pairs: ${results.workingPairs.length}`);
  console.log(`   💰 Revenue potential: ${results.workingPairs.length} × average fees`);
  console.log(`   🎪 Marketing claim: "${workingTokens.length}+ supported cryptocurrencies"`);
  console.log(`   🔥 Unique pairs: Focus on exotic combinations`);

  return {
    totalDisplayed: results.totalTokens,
    actualWorking: workingTokens.length,
    workingPairs: results.workingPairs.length,
    coveragePercent: parseFloat(coveragePercent)
  };
}

// Run comprehensive coverage test
async function runCoverageTest() {
  console.log('🧪 Starting comprehensive token coverage test...\n');
  console.log('⚠️ This will take several minutes to test all combinations...\n');

  const results = await testTokenCoverage();
  const analysis = await analyzeResults(results);

  console.log('\n🌐 Your exchange: http://localhost:5001');
  console.log(`💰 Ready to earn from ${analysis.workingPairs} actual working pairs!`);
}

runCoverageTest().catch(console.error);
