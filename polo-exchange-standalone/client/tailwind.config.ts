import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/index.html", "./client/src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "bubble-rise": {
          "0%": {
            transform: "translateY(0) translateX(0)",
            opacity: "0.8",
          },
          "50%": {
            transform: "translateY(-100px) translateX(10px)",
          },
          "100%": {
            transform: "translateY(-200px) translateX(-10px)",
            opacity: "0",
          },
        },
        "pulse-glow": {
          "0%, 100%": {
            boxShadow: "0 0 15px 2px rgba(74, 222, 128, 0.7), 0 0 30px 5px rgba(74, 222, 128, 0.4), 0 0 45px 8px rgba(74, 222, 128, 0.2)",
          },
          "50%": {
            boxShadow: "0 0 20px 5px rgba(74, 222, 128, 0.9), 0 0 40px 10px rgba(74, 222, 128, 0.6), 0 0 60px 15px rgba(74, 222, 128, 0.3)",
          },
        },
        "ghost-float": {
          "0%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-5px)",
          },
          "100%": {
            transform: "translateY(0)",
          },
        },
        "ghost-wave": {
          "0%": {
            transform: "translateY(0)",
          },
          "25%": {
            transform: "translateY(-2px)",
          },
          "50%": {
            transform: "translateY(0)",
          },
          "75%": {
            transform: "translateY(1px)",
          },
          "100%": {
            transform: "translateY(0)",
          },
        },
        "ghost-blink": {
          "0%, 48%, 52%, 100%": {
            transform: "scaleY(1)",
            opacity: "1",
          },
          "50%": {
            transform: "scaleY(0.1)",
            opacity: "0.8",
          },
        },
        "ghost-glow": {
          "0%, 100%": {
            filter: "blur(8px) brightness(1.2)",
            opacity: "0.7",
          },
          "50%": {
            filter: "blur(12px) brightness(1.5)",
            opacity: "0.9",
          },
        },
        "circuit-pulse": {
          "0%": {
            opacity: "0.1",
            boxShadow: "0 0 2px 1px rgba(32, 196, 203, 0.3)",
          },
          "50%": {
            opacity: "0.6",
            boxShadow: "0 0 8px 2px rgba(32, 196, 203, 0.5)",
          },
          "100%": {
            opacity: "0.1",
            boxShadow: "0 0 2px 1px rgba(32, 196, 203, 0.3)",
          },
        },
        "cyber-flash": {
          "0%": {
            opacity: "0.1",
            boxShadow: "0 0 3px 1px rgba(157, 85, 255, 0.3)",
          },
          "30%": {
            opacity: "0.8",
            boxShadow: "0 0 10px 3px rgba(157, 85, 255, 0.7), 0 0 15px 5px rgba(32, 196, 203, 0.5)",
          },
          "60%": {
            opacity: "0.3",
            boxShadow: "0 0 5px 2px rgba(32, 196, 203, 0.4)",
          },
          "100%": {
            opacity: "0.1",
            boxShadow: "0 0 3px 1px rgba(157, 85, 255, 0.3)",
          },
        },
        "cyber-scan": {
          "0%": {
            backgroundPosition: "0% 0%",
            opacity: "0.05",
          },
          "50%": {
            backgroundPosition: "100% 100%",
            opacity: "0.15",
          },
          "100%": {
            backgroundPosition: "0% 0%",
            opacity: "0.05",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "bubble-slow": "bubble-rise 8s ease-in infinite",
        "bubble-medium": "bubble-rise 6s ease-in infinite", 
        "bubble-fast": "bubble-rise 4s ease-in infinite",
        "pulse-slow": "pulse-glow 3s ease-in-out infinite",
        "float": "ghost-float 4s ease-in-out infinite",
        "wave": "ghost-wave 2s ease-in-out infinite",
        "wave-offset": "ghost-wave 2s ease-in-out 0.6s infinite",
        "wave-delay": "ghost-wave 2s ease-in-out 1.2s infinite",
        "blink": "ghost-blink 6s ease infinite",
        "glow": "ghost-glow 3s ease-in-out infinite",
        "circuit": "circuit-pulse 2s linear infinite",
        "cyber-glow": "cyber-flash 4s ease-in-out infinite",
        "cyber-scan": "cyber-scan 8s linear infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;
