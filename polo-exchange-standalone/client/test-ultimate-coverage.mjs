/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> COVERAGE TEST - EVERY TOKEN + EVERY COMBINATION + CORRECT ADDRESSES
 * This will test ALL 4,032 combinations with proper token contract addresses
 */

import { 
  COMPLETE_TOKEN_ADDRESSES, 
  TOKEN_CHAIN_MAPPING, 
  SOL<PERSON><PERSON>_TOKEN_MINTS, 
  TOKEN_DECIMALS,
  getTokenAddress,
  getT<PERSON><PERSON><PERSON><PERSON>,
  getSolanaTokenMint,
  getTokenDecimals,
  isSolanaToken
} from './complete-token-mapping.js';

console.log('🚀 ULTIMATE COVERAGE TEST - EVERY TOKEN + EVERY COMBINATION');
console.log('=' .repeat(80));

const LIFI_API_KEY = 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e';

// ALL 64 TOKENS
const ALL_TOKENS = [
  // Major cryptocurrencies (20)
  'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK',
  'UNI', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX', 'ETC', 'XLM',
  
  // Stablecoins (8)
  'USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'USDD', 'GUSD',
  
  // DeFi tokens (9)
  'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL', '1INCH',
  
  // Layer 1s & 2s (8)
  'FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA',
  
  // Exotic/New tokens (9)
  'HYPE', 'SUI', 'APT', 'OP', 'ARB', 'IMX', 'LDO', 'GMX', 'DYDX',
  
  // Solana ecosystem (8)
  'RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS',
  
  // Special tokens (2)
  'KAS', 'BERA'
];

const totalTokens = ALL_TOKENS.length;
const totalCombinations = totalTokens * (totalTokens - 1);

console.log(`📊 Testing ${totalTokens} tokens with CORRECT addresses`);
console.log(`🔄 Total combinations: ${totalCombinations.toLocaleString()}`);
console.log(`⏱️ Estimated time: ${Math.round(totalCombinations / 20 / 60)} minutes\n`);

const results = {
  totalTested: 0,
  workingPairs: [],
  failedPairs: [],
  providerStats: { jupiter: 0, lifi: 0, failed: 0 },
  tokenStats: {},
  categoryStats: {
    major: { working: 0, total: 0 },
    stablecoin: { working: 0, total: 0 },
    defi: { working: 0, total: 0 },
    layer1: { working: 0, total: 0 },
    exotic: { working: 0, total: 0 },
    solana: { working: 0, total: 0 },
    special: { working: 0, total: 0 },
    crossChain: { working: 0, total: 0 }
  }
};

// Initialize token stats
ALL_TOKENS.forEach(token => {
  results.tokenStats[token] = { asFrom: 0, asTo: 0, total: 0 };
});

async function testUltimateCoverage() {
  let tested = 0;
  const startTime = Date.now();
  
  console.log('🧪 Testing EVERY combination with CORRECT addresses...\n');
  
  for (let i = 0; i < ALL_TOKENS.length; i++) {
    const fromToken = ALL_TOKENS[i];
    
    for (let j = 0; j < ALL_TOKENS.length; j++) {
      if (i === j) continue;
      
      const toToken = ALL_TOKENS[j];
      tested++;
      results.totalTested++;
      
      // Show progress every 100 tests
      if (tested % 100 === 0) {
        const elapsed = (Date.now() - startTime) / 1000;
        const rate = tested / elapsed;
        const remaining = (totalCombinations - tested) / rate;
        const progress = (tested / totalCombinations * 100).toFixed(1);
        
        console.log(`📊 Progress: ${progress}% (${tested.toLocaleString()}/${totalCombinations.toLocaleString()}) - ${rate.toFixed(1)} tests/sec - ${Math.round(remaining/60)}min remaining`);
      }
      
      const result = await testPairWithCorrectAddresses(fromToken, toToken);
      const category = categorizeTokenPair(fromToken, toToken);
      
      results.categoryStats[category].total++;
      
      if (result.success) {
        results.workingPairs.push({
          from: fromToken,
          to: toToken,
          provider: result.provider,
          rate: result.rate,
          category
        });
        
        // Update stats
        results.tokenStats[fromToken].asFrom++;
        results.tokenStats[fromToken].total++;
        results.tokenStats[toToken].asTo++;
        results.tokenStats[toToken].total++;
        results.providerStats[result.provider]++;
        results.categoryStats[category].working++;
      } else {
        results.failedPairs.push({
          from: fromToken,
          to: toToken,
          error: result.error,
          category
        });
        results.providerStats.failed++;
      }
      
      // Rate limiting - be gentle with APIs
      if (tested % 10 === 0) {
        await sleep(100); // 100ms every 10 requests
      }
    }
  }
  
  return results;
}

async function testPairWithCorrectAddresses(fromToken, toToken) {
  try {
    // Test Jupiter for Solana ecosystem
    if (isSolanaToken(fromToken) && isSolanaToken(toToken)) {
      return await testJupiterWithCorrectMints(fromToken, toToken);
    } else {
      // Test LiFi with correct addresses
      return await testLiFiWithCorrectAddresses(fromToken, toToken);
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiterWithCorrectMints(fromToken, toToken) {
  try {
    const inputMint = getSolanaTokenMint(fromToken);
    const outputMint = getSolanaTokenMint(toToken);
    
    if (!inputMint || !outputMint) {
      return { success: false, error: 'Solana mint not found' };
    }
    
    const amount = fromToken === 'SOL' ? '1000000000' : '1000000'; // 1 SOL or 1 token
    
    const response = await fetch(`https://quote-api.jup.ag/v6/quote?${new URLSearchParams({
      inputMint,
      outputMint,
      amount,
      slippageBps: '50'
    })}`);
    
    if (!response.ok) {
      return { success: false, error: `Jupiter API error ${response.status}` };
    }
    
    const quote = await response.json();
    
    if (!quote.outAmount) {
      return { success: false, error: 'No output amount' };
    }
    
    const decimals = getTokenDecimals(toToken);
    const rate = parseFloat(quote.outAmount) / Math.pow(10, decimals);
    
    return { success: true, rate, provider: 'jupiter' };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFiWithCorrectAddresses(fromToken, toToken) {
  try {
    const fromChain = getTokenChain(fromToken);
    const toChain = getTokenChain(toToken);
    
    // Skip if Solana tokens (should use Jupiter)
    if (fromChain === 'solana' || toChain === 'solana') {
      return { success: false, error: 'Use Jupiter for Solana' };
    }
    
    const fromAddress = getTokenAddress(fromToken, fromChain);
    const toAddress = getTokenAddress(toToken, toChain);
    
    if (!fromAddress || !toAddress) {
      return { success: false, error: 'Token address not found' };
    }
    
    const isExotic = ['HYPE', 'SUI', 'KAS'].includes(fromToken) || ['HYPE', 'SUI', 'KAS'].includes(toToken);
    const slippage = isExotic ? '0.05' : '0.03';
    
    const params = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: fromAddress,
      toToken: toAddress,
      fromAmount: '1000000000000000000', // 1 token in wei
      fromAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      toAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      slippage
    });
    
    const response = await fetch(`https://li.quest/v1/quote?${params}`, {
      headers: {
        'x-lifi-api-key': LIFI_API_KEY
      }
    });
    
    if (!response.ok) {
      return { success: false, error: `LiFi API error ${response.status}` };
    }
    
    const quote = await response.json();
    
    if (!quote.estimate || !quote.estimate.toAmount) {
      return { success: false, error: 'No estimate' };
    }
    
    const decimals = getTokenDecimals(toToken);
    const rate = parseFloat(quote.estimate.toAmount) / Math.pow(10, decimals);
    
    return { success: true, rate, provider: 'lifi' };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function categorizeTokenPair(fromToken, toToken) {
  const categories = {
    major: ['BTC', 'ETH', 'BNB', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX', 'ETC', 'XLM'],
    stablecoin: ['USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'USDD', 'GUSD'],
    defi: ['AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL', '1INCH'],
    layer1: ['FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA'],
    exotic: ['HYPE', 'SUI', 'APT', 'OP', 'ARB', 'IMX', 'LDO', 'GMX', 'DYDX'],
    solana: ['SOL', 'RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS'],
    special: ['KAS', 'BERA']
  };

  for (const [category, tokens] of Object.entries(categories)) {
    if (tokens.includes(fromToken) || tokens.includes(toToken)) {
      return category;
    }
  }
  
  const fromChain = getTokenChain(fromToken);
  const toChain = getTokenChain(toToken);
  if (fromChain !== toChain && fromChain !== 'solana' && toChain !== 'solana') {
    return 'crossChain';
  }
  
  return 'major';
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function showUltimateResults(results) {
  console.log('\n' + '=' .repeat(80));
  console.log('🏆 ULTIMATE COVERAGE TEST RESULTS - EVERY TOKEN + EVERY COMBINATION');
  console.log('=' .repeat(80));

  const workingCount = results.workingPairs.length;
  const totalTested = results.totalTested;
  const successRate = (workingCount / totalTested * 100).toFixed(1);

  console.log(`\n📊 ULTIMATE STATISTICS:`);
  console.log(`   🔄 Total combinations tested: ${totalTested.toLocaleString()}`);
  console.log(`   ✅ Total working pairs: ${workingCount.toLocaleString()}`);
  console.log(`   ❌ Total failed pairs: ${(totalTested - workingCount).toLocaleString()}`);
  console.log(`   📈 Overall success rate: ${successRate}%`);

  console.log(`\n🌊 Provider Performance:`);
  console.log(`   🚀 Jupiter (Solana): ${results.providerStats.jupiter.toLocaleString()}`);
  console.log(`   🌉 LiFi (Cross-chain): ${results.providerStats.lifi.toLocaleString()}`);
  console.log(`   ❌ Failed: ${results.providerStats.failed.toLocaleString()}`);

  console.log(`\n📂 Category Performance:`);
  Object.entries(results.categoryStats).forEach(([category, stats]) => {
    const rate = stats.total > 0 ? (stats.working / stats.total * 100).toFixed(1) : '0.0';
    console.log(`   ${category.padEnd(12)}: ${stats.working.toLocaleString()}/${stats.total.toLocaleString()} (${rate}%)`);
  });

  console.log(`\n🏅 Top Performing Tokens:`);
  const sortedTokens = Object.entries(results.tokenStats)
    .sort(([,a], [,b]) => b.total - a.total)
    .slice(0, 20);
  
  sortedTokens.forEach(([token, stats], index) => {
    console.log(`   ${(index + 1).toString().padStart(2)}. ${token.padEnd(6)}: ${stats.total.toString().padStart(3)} working pairs`);
  });

  console.log(`\n💰 ULTIMATE Revenue Analysis:`);
  const exotic = results.categoryStats.exotic.working;
  const crossChain = results.categoryStats.crossChain.working;
  const solana = results.categoryStats.solana.working;
  const stablecoin = results.categoryStats.stablecoin.working;
  const defi = results.categoryStats.defi.working;
  const major = results.categoryStats.major.working;

  console.log(`   🔥 Exotic pairs (1.2% fee): ${exotic.toLocaleString()} pairs`);
  console.log(`   🌉 Cross-chain pairs (0.8% fee): ${crossChain.toLocaleString()} pairs`);
  console.log(`   🚀 Solana pairs (0.4% fee): ${solana.toLocaleString()} pairs`);
  console.log(`   💰 Stablecoin pairs (0.4% fee): ${stablecoin.toLocaleString()} pairs`);
  console.log(`   🏦 DeFi pairs (0.6% fee): ${defi.toLocaleString()} pairs`);
  console.log(`   💎 Major pairs (0.5% fee): ${major.toLocaleString()} pairs`);

  const dailyRevenue = (exotic * 120) + (crossChain * 80) + (solana * 40) + (stablecoin * 40) + (defi * 60) + (major * 50);
  console.log(`\n💎 ULTIMATE daily revenue potential (per $10K volume): $${dailyRevenue.toLocaleString()}`);

  console.log(`\n🏆 FINAL ASSESSMENT:`);
  if (successRate >= 80) {
    console.log('   🎉 EXCEPTIONAL! World-class comprehensive exchange!');
  } else if (successRate >= 60) {
    console.log('   ✅ EXCELLENT! Outstanding comprehensive coverage!');
  } else if (successRate >= 40) {
    console.log('   👍 GOOD! Solid comprehensive performance!');
  } else if (successRate >= 20) {
    console.log('   ⚠️ MODERATE! Decent coverage, room for improvement!');
  } else {
    console.log('   🔧 DEVELOPING! Foundation in place, needs optimization!');
  }

  console.log(`\n🌐 Your complete exchange: http://localhost:3000`);
  console.log(`💰 Ready to earn from ${workingCount.toLocaleString()} confirmed working pairs!`);
  console.log(`🚀 Out of ${totalTested.toLocaleString()} total possible combinations!`);
  console.log(`💎 ULTIMATE COVERAGE ACHIEVED - EVERY TOKEN TESTED WITH CORRECT ADDRESSES!`);
}

// Run the ultimate test
async function runUltimateTest() {
  console.log('🧪 Starting ULTIMATE coverage test...\n');
  const startTime = Date.now();
  const results = await testUltimateCoverage();
  const endTime = Date.now();
  console.log(`\n⏱️ ULTIMATE test completed in ${Math.round((endTime - startTime) / 60000)} minutes\n`);
  await showUltimateResults(results);
}

runUltimateTest().catch(console.error);
