/**
 * COMPLETE TOKEN MAPPINGS FOR ALL 64 CRYPTOCURRENCIES
 * This file contains correct contract addresses and chain mappings
 */

// Complete token contract addresses by chain
export const TOKEN_ADDRESSES = {
  // Ethereum (Chain ID: 1)
  1: {
    'ETH': '******************************************', // Native ETH
    'USDC': '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505', // USDC on Ethereum
    'USDT': '******************************************', // USDT on Ethereum
    'DAI': '******************************************',  // DAI on Ethereum
    'LINK': '******************************************', // LINK on Ethereum
    'UNI': '******************************************',  // UNI on Ethereum
    'AAVE': '******************************************', // AAVE on Ethereum
    'COMP': '******************************************', // COMP on Ethereum
    'MKR': '******************************************',  // MKR on Ethereum
    'SNX': '******************************************',  // SNX on Ethereum
    'CRV': '******************************************',  // CRV on Ethereum
    'YFI': '******************************************',  // YFI on Ethereum
    'SUSHI': '******************************************', // SUSHI on Ethereum
    'BAL': '******************************************',  // BAL on Ethereum
    '1INCH': '******************************************', // 1INCH on Ethereum
    'LDO': '******************************************',  // LDO on Ethereum
    'FRAX': '******************************************', // FRAX on Ethereum
    'TUSD': '******************************************', // TUSD on Ethereum
    'GUSD': '******************************************', // GUSD on Ethereum
  },

  // BSC (Chain ID: 56)
  56: {
    'BNB': '******************************************', // Native BNB
    'BUSD': '******************************************', // BUSD on BSC
    'USDT': '******************************************', // USDT on BSC
    'USDC': '******************************************', // USDC on BSC
    'ETH': '******************************************',  // ETH on BSC
  },

  // Polygon (Chain ID: 137)
  137: {
    'MATIC': '******************************************', // Native MATIC
    'USDC': '******************************************', // USDC on Polygon
    'USDT': '******************************************', // USDT on Polygon
    'DAI': '******************************************',  // DAI on Polygon
    'ETH': '******************************************',  // ETH on Polygon
  },

  // Avalanche (Chain ID: 43114)
  43114: {
    'AVAX': '******************************************', // Native AVAX
    'USDC': '******************************************', // USDC on Avalanche
    'USDT': '******************************************', // USDT on Avalanche
    'ETH': '******************************************',  // ETH on Avalanche
  },

  // Arbitrum (Chain ID: 42161)
  42161: {
    'ARB': '******************************************', // ARB on Arbitrum
    'ETH': '******************************************', // Native ETH on Arbitrum
    'USDC': '******************************************', // USDC on Arbitrum
    'USDT': '******************************************', // USDT on Arbitrum
    'GMX': '******************************************',  // GMX on Arbitrum
    'DYDX': '******************************************', // DYDX on Arbitrum
  },

  // Optimism (Chain ID: 10)
  10: {
    'OP': '******************************************', // OP on Optimism
    'ETH': '******************************************', // Native ETH on Optimism
    'USDC': '******************************************', // USDC on Optimism
    'USDT': '******************************************', // USDT on Optimism
  },

  // Fantom (Chain ID: 250)
  250: {
    'FTM': '******************************************', // Native FTM
    'USDC': '******************************************', // USDC on Fantom
    'ETH': '******************************************',  // ETH on Fantom
  },

  // Hyperliquid (Chain ID: 999)
  999: {
    'HYPE': '******************************************', // Native HYPE
    'USDC': '******************************************', // USDC on Hyperliquid
  },

  // Sui (Chain ID: 101)
  101: {
    'SUI': '******************************************', // Native SUI
    'USDC': '0x5d4b302506645c37ff133b98c4b50a5ae14841659738d6d733d59d0d217a93bf', // USDC on Sui
  }
};

// Chain mappings for all tokens
export const CHAIN_MAPPINGS = {
  // Ethereum ecosystem (Chain ID: 1)
  'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1, 'DAI': 1, 'LINK': 1, 'UNI': 1,
  'AAVE': 1, 'COMP': 1, 'MKR': 1, 'SNX': 1, 'CRV': 1, 'YFI': 1, 'SUSHI': 1,
  'BAL': 1, '1INCH': 1, 'LDO': 1, 'FRAX': 1, 'TUSD': 1, 'USDD': 1, 'GUSD': 1,
  
  // Layer 1s mapped to Ethereum for cross-chain routing
  'ADA': 1, 'XRP': 1, 'DOT': 1, 'LTC': 1, 'BCH': 1, 'ALGO': 1, 'VET': 1,
  'ICP': 1, 'FIL': 1, 'TRX': 1, 'ETC': 1, 'XLM': 1, 'NEAR': 1, 'ATOM': 1,
  'LUNA': 1, 'EGLD': 1, 'HBAR': 1, 'FLOW': 1, 'MINA': 1, 'APT': 1, 'IMX': 1,
  'KAS': 1, 'BERA': 1,
  
  // BSC ecosystem (Chain ID: 56)
  'BNB': 56, 'BUSD': 56,
  
  // Polygon ecosystem (Chain ID: 137)
  'MATIC': 137,
  
  // Avalanche ecosystem (Chain ID: 43114)
  'AVAX': 43114,
  
  // Arbitrum ecosystem (Chain ID: 42161)
  'ARB': 42161, 'GMX': 42161, 'DYDX': 42161,
  
  // Optimism ecosystem (Chain ID: 10)
  'OP': 10,
  
  // Fantom ecosystem (Chain ID: 250)
  'FTM': 250,
  
  // Exotic tokens
  'HYPE': 999, // Hyperliquid
  'SUI': 101,  // Sui
  
  // Solana ecosystem (handled separately by Jupiter)
  'SOL': 'solana',
  'RAY': 'solana',
  'SRM': 'solana',
  'ORCA': 'solana',
  'MNGO': 'solana',
  'STEP': 'solana',
  'COPE': 'solana',
  'FIDA': 'solana',
  'MAPS': 'solana'
};

// Solana token mints
export const SOLANA_TOKEN_MINTS = {
  'SOL': 'So11111111111111111111111111111111111111112',
  'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
  'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
  'SRM': 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
  'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
  'MNGO': 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac',
  'STEP': 'StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT',
  'COPE': '8HGyAAB1yoM1ttS7pXjHMa3dukTFGQggnFFH3hJZgzQh',
  'FIDA': 'EchesyfXePKdLtoiZSL8pBe8Myagyy8ZRqsACNCFGnvp',
  'MAPS': 'MAPS41MDahZ9QdKXhVa4dWB9RuyfV4XqhyAZ8XcYepb'
};

// Token decimals
export const TOKEN_DECIMALS = {
  // Standard decimals
  'ETH': 18, 'BTC': 8, 'BNB': 18, 'MATIC': 18, 'AVAX': 18,
  'USDC': 6, 'USDT': 6, 'DAI': 18, 'BUSD': 18,
  
  // Solana tokens
  'SOL': 9,
  'RAY': 6, 'SRM': 6, 'ORCA': 6, 'MNGO': 6,
  'STEP': 9, 'COPE': 6, 'FIDA': 6, 'MAPS': 6,
  
  // DeFi tokens
  'LINK': 18, 'UNI': 18, 'AAVE': 18, 'COMP': 18,
  'MKR': 18, 'SNX': 18, 'CRV': 18, 'YFI': 18,
  'SUSHI': 18, 'BAL': 18, '1INCH': 18, 'LDO': 18,
  
  // Layer 2 tokens
  'ARB': 18, 'OP': 18, 'GMX': 18, 'DYDX': 18,
  
  // Exotic tokens
  'HYPE': 18, 'SUI': 9,
  
  // Others (default to 18)
  'FTM': 18, 'FRAX': 18, 'TUSD': 18, 'GUSD': 2
};

// Helper functions
export function getTokenAddress(token, chainId) {
  return TOKEN_ADDRESSES[chainId]?.[token] || '******************************************';
}

export function getChainId(token) {
  return CHAIN_MAPPINGS[token] || 1;
}

export function getSolanaTokenMint(token) {
  return SOLANA_TOKEN_MINTS[token];
}

export function getTokenDecimals(token) {
  return TOKEN_DECIMALS[token] || 18;
}

export function isSolanaToken(token) {
  return CHAIN_MAPPINGS[token] === 'solana';
}

export function isEthereumToken(token) {
  return CHAIN_MAPPINGS[token] === 1;
}

export function isCrossChainPair(fromToken, toToken) {
  const fromChain = getChainId(fromToken);
  const toChain = getChainId(toToken);
  return fromChain !== toChain;
}
