#!/usr/bin/env node

/**
 * Test Real LiFi Execution
 * Tests the actual LiFi SDK integration for real swap execution
 */

import dotenv from 'dotenv';
import InstantLiquidityAggregator from './src/services/InstantLiquidityAggregator.js';

dotenv.config();

async function testRealLiFiExecution() {
  console.log('🧪 Testing Real LiFi Execution Integration\n');

  try {
    // Initialize the aggregator with real LiFi SDK
    const aggregator = new InstantLiquidityAggregator();
    
    console.log('✅ InstantLiquidityAggregator initialized with LiFi SDK');
    
    // Test 1: Get LiFi quote with route data
    console.log('\n🔍 Test 1: Getting LiFi quote with route data...');
    
    const quotes = await aggregator.getInstantQuotes('ETH', 'USDC', 0.01);
    
    if (quotes.bestQuote && quotes.bestQuote.provider === 'lifi') {
      console.log('✅ LiFi quote received successfully!');
      console.log(`   Rate: ${quotes.bestQuote.rate.toFixed(4)} USDC per ETH`);
      console.log(`   Output: ${quotes.bestQuote.toAmount.toFixed(6)} USDC`);
      console.log(`   Execution time: ${Math.round(quotes.bestQuote.executionTime / 1000)}s`);
      console.log(`   Fee: ${quotes.bestQuote.fee.toFixed(6)} ETH`);
      
      // Check if route data is attached
      if (quotes.bestQuote.routeData) {
        console.log('✅ Route data attached for execution');
        console.log(`   Route steps: ${quotes.bestQuote.routeData.steps?.length || 0}`);
        console.log(`   From chain: ${quotes.bestQuote.routeData.fromChainId}`);
        console.log(`   To chain: ${quotes.bestQuote.routeData.toChainId}`);
      } else {
        console.log('⚠️ No route data attached - execution will fail');
      }
      
      // Test 2: Execute the LiFi swap (simulation)
      console.log('\n🚀 Test 2: Testing LiFi execution...');
      
      const testUserAddress = '******************************************';
      const testRecipientAddress = '******************************************';
      
      console.log('⚠️ Note: This is a test execution - no real funds will be moved');
      
      const executionResult = await aggregator.executeInstantSwap(
        quotes.bestQuote,
        testUserAddress,
        testRecipientAddress
      );
      
      if (executionResult.success) {
        console.log('✅ LiFi execution completed successfully!');
        console.log(`   Transaction ID: ${executionResult.transactionId}`);
        console.log(`   Estimated time: ${Math.round(executionResult.estimatedTime / 1000)}s`);
      } else {
        console.log('❌ LiFi execution failed:');
        console.log(`   Error: ${executionResult.error}`);
      }
      
    } else {
      console.log('⚠️ LiFi not selected as best provider');
      console.log(`   Best provider: ${quotes.bestQuote?.provider || 'none'}`);
      console.log(`   Available providers: ${quotes.allQuotes.map(q => q.provider).join(', ')}`);
    }
    
    // Test 3: Provider statistics
    console.log('\n📊 Test 3: Provider statistics...');
    const stats = aggregator.getProviderStats();
    console.log(`✅ Total providers: ${stats.totalProviders}`);
    console.log(`✅ Enabled providers: ${stats.enabledProviders}`);
    console.log(`✅ Average reliability: ${(stats.avgReliability * 100).toFixed(1)}%`);
    
    const lifiProvider = stats.providers.find(p => p.id === 'lifi');
    if (lifiProvider) {
      console.log(`✅ LiFi provider status: ${lifiProvider.enabled ? 'Enabled' : 'Disabled'}`);
      console.log(`   Reliability: ${(lifiProvider.reliability * 100).toFixed(1)}%`);
      console.log(`   Supported chains: ${lifiProvider.supportedChains}`);
      console.log(`   Supported tokens: ${lifiProvider.supportedTokens}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('   Stack:', error.stack);
  }
}

// Test different scenarios
async function testMultipleScenarios() {
  console.log('\n🔬 Testing Multiple LiFi Scenarios\n');
  
  const aggregator = new InstantLiquidityAggregator();
  
  const scenarios = [
    { from: 'ETH', to: 'BNB', amount: 0.1, description: 'ETH → BNB (Cross-chain)' },
    { from: 'BTC', to: 'ETH', amount: 0.01, description: 'BTC → ETH (Cross-chain)' },
    { from: 'SUI', to: 'HYPE', amount: 10, description: 'SUI → HYPE (Exotic pair)' },
    { from: 'USDC', to: 'USDT', amount: 100, description: 'USDC → USDT (Stablecoin)' }
  ];
  
  for (const scenario of scenarios) {
    try {
      console.log(`\n🧪 Testing: ${scenario.description}`);
      
      const quotes = await aggregator.getInstantQuotes(
        scenario.from, 
        scenario.to, 
        scenario.amount
      );
      
      if (quotes.bestQuote) {
        console.log(`✅ Best quote: ${quotes.bestQuote.provider}`);
        console.log(`   Rate: ${quotes.bestQuote.rate.toFixed(4)}`);
        console.log(`   Output: ${quotes.bestQuote.toAmount.toFixed(6)} ${scenario.to}`);
        
        if (quotes.bestQuote.provider === 'lifi') {
          console.log('🌉 LiFi selected as best provider!');
        }
      } else {
        console.log('❌ No quotes available');
      }
      
    } catch (error) {
      console.log(`❌ Scenario failed: ${error.message}`);
    }
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Real LiFi Integration Tests\n');
  console.log('=' .repeat(60));
  
  // Check API key
  if (!process.env.LIFI_API_KEY) {
    console.log('❌ LIFI_API_KEY not configured in .env file');
    console.log('   Please add your LiFi API key to test real execution');
    return;
  }
  
  console.log('✅ LiFi API key configured');
  console.log(`   Key: ${process.env.LIFI_API_KEY.substring(0, 20)}...`);
  
  await testRealLiFiExecution();
  await testMultipleScenarios();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🎉 All LiFi integration tests completed!');
}

// Execute if run directly
runAllTests().catch(console.error);

export { testRealLiFiExecution, testMultipleScenarios };
