{"name": "polo-exchange-standalone", "version": "1.0.0", "description": "Polo Swap - Standalone Cryptocurrency Exchange", "type": "module", "license": "MIT", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "start": "npm run dev", "deploy": "npm run build && npx wrangler pages deploy dist/public --project-name polo-exchange", "deploy:staging": "npm run build && npx wrangler pages deploy dist/public --project-name polo-exchange --env staging", "deploy:production": "npm run build && npx wrangler pages deploy dist/public --project-name polo-exchange --env production", "server": "cd ../polo-exchange-standalone-server && node admin-login-server.js", "tunnel": "npx cloudflared tunnel --url http://localhost:3000", "cf:login": "npx wrangler login", "cf:init": "npx wrangler pages project create polo-exchange"}, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.0", "@hookform/resolvers": "^3.9.1", "@lifi/sdk": "^3.7.9", "@lifi/types": "^17.18.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@solana/web3.js": "^1.98.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "ethers": "^6.14.4", "framer-motion": "^11.13.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.453.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-i18next": "^15.5.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "wouter": "^3.3.5"}, "devDependencies": {"@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "vite": "^5.4.14", "wrangler": "^4.20.3"}}