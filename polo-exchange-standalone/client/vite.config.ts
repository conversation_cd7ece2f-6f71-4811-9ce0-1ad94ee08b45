import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import themePlugin from "@replit/vite-plugin-shadcn-theme-json";
import path, { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export default defineConfig(async () => ({
  plugins: [
    react(),
    // themePlugin(), // Temporarily disabled due to theme.json parsing error
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@shared": path.resolve(__dirname, "../shared"),
      "@assets": path.resolve(__dirname, "../attached_assets"),
    },
  },
  server: {
    host: '0.0.0.0', // Allow access from any device on network
    port: 3000,
    strictPort: true,
    allowedHosts: [
      'localhost',
      '.trycloudflare.com', // Allow all Cloudflare tunnel domains
      '.vercel.app',
      '.netlify.app'
    ],
    // Removed backend proxy - exchange is now fully frontend-based
  },
  build: {
    outDir: path.resolve(__dirname, "dist/public"),
    emptyOutDir: true,
    sourcemap: false,
    // Mobile performance optimizations
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true
      }
    },
    // Reduce chunk size warning limit
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      external: ["@solana/web3.js", "@solana/web3.js/web3.js"],
      output: {
        manualChunks: {
          // Separate vendor chunks for better mobile caching
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-select', '@radix-ui/react-tabs'],
          crypto: ['ethers'],
          utils: ['axios', 'date-fns', 'clsx', 'framer-motion']
        }
      }
    },
  },
  // Mobile-specific optimizations
  optimizeDeps: {
    include: ['react', 'react-dom', 'axios']
  },
}));
