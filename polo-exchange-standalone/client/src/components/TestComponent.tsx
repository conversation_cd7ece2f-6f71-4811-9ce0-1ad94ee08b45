import React from 'react';

export default function TestComponent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-white text-center">
        <h1 className="text-4xl font-bold mb-4">🎉 Marko Polo Capital</h1>
        <p className="text-xl mb-4">Exchange is Loading...</p>
        <div className="space-y-2 text-sm">
          <div>✅ React: Working</div>
          <div>✅ TypeScript: Working</div>
          <div>✅ Tailwind: Working</div>
          <div>✅ Components: Loading</div>
        </div>
      </div>
    </div>
  );
}
