import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

/**
 * Secure API Key Management System
 * Encrypts and manages all external API keys for maximum security
 */

export interface EncryptedApiKey {
  encrypted: string;
  iv: string;
  provider: string;
  createdAt: number;
  lastUsed?: number;
}

export interface ApiKeyConfig {
  provider: string;
  key: string;
  description: string;
  permissions?: string[];
}

export class ApiKeyManager {
  private readonly ALGORITHM = 'aes-256-gcm';
  private readonly KEY_LENGTH = 32;
  private readonly IV_LENGTH = 16;
  private readonly TAG_LENGTH = 16;
  
  private masterKey: Buffer;
  private keyStore: Map<string, EncryptedApiKey> = new Map();
  private keyStorePath: string;

  constructor(masterPassword?: string) {
    this.keyStorePath = path.join(process.cwd(), 'secure', 'encrypted-keys.json');
    this.masterKey = this.deriveMasterKey(masterPassword || process.env.MASTER_PASSWORD || 'default-key-change-in-production');
    this.ensureSecureDirectory();
    this.loadEncryptedKeys();
  }

  /**
   * Encrypt and store an API key securely
   */
  async storeApiKey(config: ApiKeyConfig): Promise<void> {
    try {
      const iv = crypto.randomBytes(this.IV_LENGTH);
      const cipher = crypto.createCipher(this.ALGORITHM, this.masterKey);
      cipher.setAAD(Buffer.from(config.provider));

      let encrypted = cipher.update(config.key, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      const encryptedData = encrypted + authTag.toString('hex');

      const encryptedKey: EncryptedApiKey = {
        encrypted: encryptedData,
        iv: iv.toString('hex'),
        provider: config.provider,
        createdAt: Date.now()
      };

      this.keyStore.set(config.provider, encryptedKey);
      await this.saveEncryptedKeys();

      console.log(`🔐 API key for ${config.provider} encrypted and stored securely`);
    } catch (error) {
      console.error(`❌ Failed to encrypt API key for ${config.provider}:`, error);
      throw new Error(`API key encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve and decrypt an API key
   */
  async getApiKey(provider: string): Promise<string | null> {
    try {
      const encryptedKey = this.keyStore.get(provider);
      if (!encryptedKey) {
        console.warn(`⚠️ No encrypted key found for provider: ${provider}`);
        return null;
      }

      const iv = Buffer.from(encryptedKey.iv, 'hex');
      const encryptedData = encryptedKey.encrypted;
      
      // Split encrypted data and auth tag
      const encrypted = encryptedData.slice(0, -this.TAG_LENGTH * 2);
      const authTag = Buffer.from(encryptedData.slice(-this.TAG_LENGTH * 2), 'hex');

      const decipher = crypto.createDecipher(this.ALGORITHM, this.masterKey);
      decipher.setAAD(Buffer.from(provider));
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Update last used timestamp
      encryptedKey.lastUsed = Date.now();
      await this.saveEncryptedKeys();

      console.log(`🔓 API key for ${provider} decrypted successfully`);
      return decrypted;
    } catch (error) {
      console.error(`❌ Failed to decrypt API key for ${provider}:`, error);
      return null;
    }
  }

  /**
   * Check if API key exists for provider
   */
  hasApiKey(provider: string): boolean {
    return this.keyStore.has(provider);
  }

  /**
   * List all stored providers (without keys)
   */
  getStoredProviders(): string[] {
    return Array.from(this.keyStore.keys());
  }

  /**
   * Remove an API key
   */
  async removeApiKey(provider: string): Promise<boolean> {
    if (this.keyStore.has(provider)) {
      this.keyStore.delete(provider);
      await this.saveEncryptedKeys();
      console.log(`🗑️ API key for ${provider} removed`);
      return true;
    }
    return false;
  }

  /**
   * Rotate master key (re-encrypt all keys with new master key)
   */
  async rotateMasterKey(newMasterPassword: string): Promise<void> {
    console.log('🔄 Starting master key rotation...');
    
    // Decrypt all keys with old master key
    const decryptedKeys: Map<string, string> = new Map();
    for (const [provider, encryptedKey] of this.keyStore) {
      const decryptedKey = await this.getApiKey(provider);
      if (decryptedKey) {
        decryptedKeys.set(provider, decryptedKey);
      }
    }

    // Generate new master key
    this.masterKey = this.deriveMasterKey(newMasterPassword);
    this.keyStore.clear();

    // Re-encrypt all keys with new master key
    for (const [provider, key] of decryptedKeys) {
      await this.storeApiKey({
        provider,
        key,
        description: `Re-encrypted during master key rotation`
      });
    }

    console.log(`✅ Master key rotation completed for ${decryptedKeys.size} API keys`);
  }

  /**
   * Get API key usage statistics
   */
  getUsageStats(): Array<{provider: string; createdAt: Date; lastUsed?: Date; daysSinceCreated: number}> {
    const now = Date.now();
    return Array.from(this.keyStore.entries()).map(([provider, key]) => ({
      provider,
      createdAt: new Date(key.createdAt),
      lastUsed: key.lastUsed ? new Date(key.lastUsed) : undefined,
      daysSinceCreated: Math.floor((now - key.createdAt) / (1000 * 60 * 60 * 24))
    }));
  }

  /**
   * Validate API key format for specific providers
   */
  validateApiKeyFormat(provider: string, key: string): boolean {
    const patterns: Record<string, RegExp> = {
      'lifi': /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,
      'oneinch': /^[A-Za-z0-9]{32,64}$/,
      'changenow': /^[a-f0-9]{32,64}$/,
      'paraswap': /^[A-Za-z0-9]{40,80}$/,
      'zerox': /^[A-Za-z0-9]{32,64}$/
    };

    const pattern = patterns[provider.toLowerCase()];
    if (!pattern) {
      console.warn(`⚠️ No validation pattern for provider: ${provider}`);
      return true; // Allow if no pattern defined
    }

    const isValid = pattern.test(key);
    if (!isValid) {
      console.error(`❌ Invalid API key format for ${provider}`);
    }

    return isValid;
  }

  /**
   * Backup encrypted keys to secure location
   */
  async backupKeys(backupPath?: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const defaultBackupPath = path.join(process.cwd(), 'backups', `api-keys-backup-${timestamp}.json`);
    const finalBackupPath = backupPath || defaultBackupPath;

    // Ensure backup directory exists
    const backupDir = path.dirname(finalBackupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const backupData = {
      timestamp: Date.now(),
      version: '1.0',
      keyCount: this.keyStore.size,
      keys: Array.from(this.keyStore.entries())
    };

    fs.writeFileSync(finalBackupPath, JSON.stringify(backupData, null, 2), { mode: 0o600 });
    console.log(`💾 API keys backed up to: ${finalBackupPath}`);
    
    return finalBackupPath;
  }

  /**
   * Restore keys from backup
   */
  async restoreFromBackup(backupPath: string): Promise<void> {
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }

    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));
    
    if (!backupData.keys || !Array.isArray(backupData.keys)) {
      throw new Error('Invalid backup file format');
    }

    this.keyStore.clear();
    this.keyStore = new Map(backupData.keys);
    
    await this.saveEncryptedKeys();
    console.log(`📥 Restored ${backupData.keyCount} API keys from backup`);
  }

  // Private helper methods
  private deriveMasterKey(password: string): Buffer {
    const salt = Buffer.from('marko-polo-capital-salt', 'utf8');
    return crypto.pbkdf2Sync(password, salt, 100000, this.KEY_LENGTH, 'sha256');
  }

  private ensureSecureDirectory(): void {
    const secureDir = path.dirname(this.keyStorePath);
    if (!fs.existsSync(secureDir)) {
      fs.mkdirSync(secureDir, { recursive: true, mode: 0o700 });
    }
  }

  private async saveEncryptedKeys(): Promise<void> {
    try {
      const data = {
        version: '1.0',
        lastUpdated: Date.now(),
        keys: Array.from(this.keyStore.entries())
      };

      fs.writeFileSync(this.keyStorePath, JSON.stringify(data, null, 2), { mode: 0o600 });
    } catch (error) {
      console.error('❌ Failed to save encrypted keys:', error);
      throw error;
    }
  }

  private loadEncryptedKeys(): void {
    try {
      if (fs.existsSync(this.keyStorePath)) {
        const data = JSON.parse(fs.readFileSync(this.keyStorePath, 'utf8'));
        if (data.keys && Array.isArray(data.keys)) {
          this.keyStore = new Map(data.keys);
          console.log(`🔐 Loaded ${this.keyStore.size} encrypted API keys`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to load encrypted keys:', error);
      // Continue with empty key store
    }
  }
}

// Singleton instance for global use
let apiKeyManagerInstance: ApiKeyManager | null = null;

export const getApiKeyManager = (masterPassword?: string): ApiKeyManager => {
  if (!apiKeyManagerInstance) {
    apiKeyManagerInstance = new ApiKeyManager(masterPassword);
  }
  return apiKeyManagerInstance;
};

export default ApiKeyManager;
