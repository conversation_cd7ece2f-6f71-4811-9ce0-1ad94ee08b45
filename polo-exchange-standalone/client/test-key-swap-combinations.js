#!/usr/bin/env node

/**
 * Test Key Swap Combinations
 * Fast test of the most important and profitable swap combinations
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 TESTING KEY SWAP COMBINATIONS (FAST)');
console.log('=' .repeat(70));

// Key tokens that matter most for revenue
const KEY_TOKENS = {
  major: ['BTC', 'ETH', 'BNB', 'SOL', 'AVAX', 'MATIC', 'ADA', 'DOT'],
  stablecoins: ['USDC', 'USDT', 'DAI', 'BUSD'],
  defi: ['AAVE', 'UNI', 'LINK', 'CRV', 'SUSHI', 'COMP'],
  exotic: ['HYPE', 'SUI', 'GMX', 'ARB', 'OP'],
  solana: ['RAY', 'SRM', 'ORCA', 'MNGO']
};

async function testKeySwapCombinations() {
  console.log('🧪 Testing most important swap combinations...\n');

  const results = {
    totalTested: 0,
    workingPairs: [],
    failedPairs: [],
    categoryResults: {},
    providerStats: { jupiter: 0, lifi: 0, failed: 0 }
  };

  // Test within each category
  for (const [category, tokens] of Object.entries(KEY_TOKENS)) {
    console.log(`📂 Testing ${category.toUpperCase()} category (${tokens.length} tokens)...`);
    
    const categoryResults = {
      working: [],
      failed: [],
      totalTested: 0
    };

    // Test all combinations within category
    for (let i = 0; i < tokens.length; i++) {
      for (let j = 0; j < tokens.length; j++) {
        if (i === j) continue;
        
        const fromToken = tokens[i];
        const toToken = tokens[j];
        
        categoryResults.totalTested++;
        results.totalTested++;
        
        const result = await testSwapPair(fromToken, toToken);
        
        if (result.success) {
          const pair = {
            from: fromToken,
            to: toToken,
            provider: result.provider,
            rate: result.rate,
            category
          };
          categoryResults.working.push(pair);
          results.workingPairs.push(pair);
          results.providerStats[result.provider]++;
        } else {
          categoryResults.failed.push({ from: fromToken, to: toToken, error: result.error });
          results.failedPairs.push({ from: fromToken, to: toToken, error: result.error });
          results.providerStats.failed++;
        }
      }
    }
    
    const successRate = (categoryResults.working.length / categoryResults.totalTested * 100).toFixed(1);
    console.log(`   ✅ ${categoryResults.working.length}/${categoryResults.totalTested} working (${successRate}%)`);
    
    results.categoryResults[category] = categoryResults;
  }

  // Test cross-category combinations (most profitable)
  console.log('\n🌉 Testing CROSS-CATEGORY combinations...');
  
  const crossCategoryPairs = [
    // Major to exotic (high revenue)
    ...KEY_TOKENS.major.flatMap(major => 
      KEY_TOKENS.exotic.flatMap(exotic => [
        [major, exotic], [exotic, major]
      ])
    ),
    
    // Major to stablecoins (high volume)
    ...KEY_TOKENS.major.flatMap(major => 
      KEY_TOKENS.stablecoins.flatMap(stable => [
        [major, stable], [stable, major]
      ])
    ),
    
    // Stablecoin arbitrage
    ...KEY_TOKENS.stablecoins.flatMap(stable1 => 
      KEY_TOKENS.stablecoins.filter(stable2 => stable1 !== stable2).map(stable2 => [stable1, stable2])
    ),
    
    // Solana ecosystem
    ...KEY_TOKENS.major.slice(0, 3).flatMap(major => 
      KEY_TOKENS.solana.flatMap(solana => [
        [major, solana], [solana, major]
      ])
    )
  ];

  let crossCategoryWorking = 0;
  let crossCategoryTested = 0;

  for (const [fromToken, toToken] of crossCategoryPairs) {
    crossCategoryTested++;
    results.totalTested++;
    
    const result = await testSwapPair(fromToken, toToken);
    
    if (result.success) {
      crossCategoryWorking++;
      results.workingPairs.push({
        from: fromToken,
        to: toToken,
        provider: result.provider,
        rate: result.rate,
        category: 'cross-category'
      });
      results.providerStats[result.provider]++;
    } else {
      results.failedPairs.push({ from: fromToken, to: toToken, error: result.error });
      results.providerStats.failed++;
    }
  }

  const crossSuccessRate = (crossCategoryWorking / crossCategoryTested * 100).toFixed(1);
  console.log(`   ✅ ${crossCategoryWorking}/${crossCategoryTested} working (${crossSuccessRate}%)`);

  return results;
}

async function testSwapPair(fromToken, toToken) {
  try {
    // Try Jupiter first for Solana tokens
    const solanaTokens = ['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA', 'MNGO'];
    
    if (solanaTokens.includes(fromToken) && solanaTokens.includes(toToken)) {
      const jupiterResult = await testJupiterSwap(fromToken, toToken);
      if (jupiterResult.success) {
        return { ...jupiterResult, provider: 'jupiter' };
      }
    }
    
    // Try LiFi for everything else
    const lifiResult = await testLiFiSwap(fromToken, toToken);
    if (lifiResult.success) {
      return { ...lifiResult, provider: 'lifi' };
    }
    
    return { success: false, error: 'No provider supports this pair' };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiterSwap(fromToken, toToken) {
  try {
    const tokenMints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
      'SRM': 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
      'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      'MNGO': 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac'
    };

    const inputMint = tokenMints[fromToken];
    const outputMint = tokenMints[toToken];

    if (!inputMint || !outputMint) {
      return { success: false, error: 'Token not supported by Jupiter' };
    }

    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint,
      outputMint,
      amount: '**********', // 1 SOL or equivalent
      slippageBps: '50'
    }));

    if (response.ok) {
      const quote = await response.json();
      if (quote.outAmount) {
        const decimals = getTokenDecimals(toToken);
        const rate = parseFloat(quote.outAmount) / Math.pow(10, decimals);
        return { success: true, rate };
      }
    }
    
    return { success: false, error: `Jupiter API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFiSwap(fromToken, toToken) {
  try {
    if (!process.env.LIFI_API_KEY) {
      return { success: false, error: 'No LiFi API key' };
    }

    // Chain mappings
    const chainMappings = {
      'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1, 'DAI': 1, 'LINK': 1, 'UNI': 1,
      'AAVE': 1, 'COMP': 1, 'CRV': 1, 'SUSHI': 1,
      'BNB': 56, 'BUSD': 56,
      'MATIC': 137,
      'AVAX': 43114,
      'ADA': 1, 'DOT': 1,
      'HYPE': 999,
      'SUI': 101,
      'GMX': 42161,
      'ARB': 42161,
      'OP': 10
    };

    const fromChain = chainMappings[fromToken] || 1;
    const toChain = chainMappings[toToken] || 1;

    // Dynamic slippage based on token type
    const exoticTokens = ['HYPE', 'SUI', 'GMX'];
    const isExotic = exoticTokens.includes(fromToken) || exoticTokens.includes(toToken);
    const slippage = isExotic ? '0.05' : '0.03';

    const quoteParams = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '**********000000000', // 1 token
      fromAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      toAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      slippage
    });

    const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      }
    });

    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate && quote.estimate.toAmount) {
        const rate = parseFloat(quote.estimate.toAmount) / 1e18;
        return { success: true, rate };
      }
    }
    
    return { success: false, error: `LiFi API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function getTokenDecimals(token) {
  const decimals = {
    'SOL': 9, 'RAY': 6, 'SRM': 6, 'ORCA': 6, 'MNGO': 6
  };
  return decimals[token] || 6;
}

async function analyzeKeyResults(results) {
  console.log('\n' + '=' .repeat(70));
  console.log('🏆 KEY SWAP COMBINATIONS ANALYSIS');
  console.log('=' .repeat(70));

  const workingCount = results.workingPairs.length;
  const totalTested = results.totalTested;
  const successRate = (workingCount / totalTested * 100).toFixed(1);

  console.log(`\n📊 Overall Results:`);
  console.log(`   🔄 Key combinations tested: ${totalTested}`);
  console.log(`   ✅ Working pairs: ${workingCount}`);
  console.log(`   📈 Success rate: ${successRate}%`);

  console.log(`\n🌊 Provider Breakdown:`);
  console.log(`   🚀 Jupiter (Solana): ${results.providerStats.jupiter} pairs`);
  console.log(`   🌉 LiFi (Cross-chain): ${results.providerStats.lifi} pairs`);
  console.log(`   ❌ Failed: ${results.providerStats.failed} pairs`);

  console.log(`\n📂 Category Performance:`);
  Object.entries(results.categoryResults).forEach(([category, data]) => {
    const rate = (data.working.length / data.totalTested * 100).toFixed(1);
    console.log(`   ${category.padEnd(12)}: ${data.working.length}/${data.totalTested} (${rate}%)`);
  });

  // Analyze by fee tier
  const exoticPairs = results.workingPairs.filter(p => 
    ['HYPE', 'SUI', 'GMX'].includes(p.from) || ['HYPE', 'SUI', 'GMX'].includes(p.to)
  );
  
  const crossChainPairs = results.workingPairs.filter(p => 
    p.provider === 'lifi' && !['USDC', 'USDT', 'DAI', 'BUSD'].includes(p.from) && !['USDC', 'USDT', 'DAI', 'BUSD'].includes(p.to)
  );
  
  const stablecoinPairs = results.workingPairs.filter(p => 
    ['USDC', 'USDT', 'DAI', 'BUSD'].includes(p.from) || ['USDC', 'USDT', 'DAI', 'BUSD'].includes(p.to)
  );
  
  const solanaPairs = results.workingPairs.filter(p => p.provider === 'jupiter');

  console.log(`\n💰 Revenue Analysis:`);
  console.log(`   🔥 Exotic pairs (1.2% fee): ${exoticPairs.length}`);
  console.log(`   🌉 Cross-chain pairs (0.8% fee): ${crossChainPairs.length}`);
  console.log(`   💰 Stablecoin pairs (0.4% fee): ${stablecoinPairs.length}`);
  console.log(`   🚀 Solana pairs (0.4% fee): ${solanaPairs.length}`);

  console.log(`\n🎯 Key Working Pairs:`);
  
  // Show best exotic pairs
  if (exoticPairs.length > 0) {
    console.log(`   🔥 Exotic pairs (${exoticPairs.length}):`);
    exoticPairs.slice(0, 5).forEach(pair => {
      console.log(`     • ${pair.from} → ${pair.to}: ${pair.rate.toFixed(4)} rate`);
    });
  }
  
  // Show best cross-chain pairs
  if (crossChainPairs.length > 0) {
    console.log(`   🌉 Cross-chain pairs (${crossChainPairs.length}):`);
    crossChainPairs.slice(0, 5).forEach(pair => {
      console.log(`     • ${pair.from} → ${pair.to}: ${pair.rate.toFixed(4)} rate`);
    });
  }

  console.log(`\n🏆 Business Impact:`);
  if (successRate >= 80) {
    console.log(`   🎉 EXCEPTIONAL! ${successRate}% success rate on key pairs!`);
  } else if (successRate >= 60) {
    console.log(`   ✅ EXCELLENT! ${successRate}% success rate on key pairs!`);
  } else {
    console.log(`   👍 GOOD! ${successRate}% success rate on key pairs!`);
  }

  console.log(`   💎 Total working pairs: ${workingCount}`);
  console.log(`   🔥 Exotic revenue potential: ${exoticPairs.length} × 1.2% fees`);
  console.log(`   🌉 Cross-chain revenue: ${crossChainPairs.length} × 0.8% fees`);
  console.log(`   💰 High-volume potential: ${stablecoinPairs.length + solanaPairs.length} pairs`);

  return {
    totalPairs: workingCount,
    successRate: parseFloat(successRate),
    exoticPairs: exoticPairs.length,
    crossChainPairs: crossChainPairs.length,
    stablecoinPairs: stablecoinPairs.length,
    solanaPairs: solanaPairs.length
  };
}

// Run the key combinations test
async function runKeyTest() {
  console.log('🧪 Starting key swap combinations test...\n');

  const startTime = Date.now();
  const results = await testKeySwapCombinations();
  const endTime = Date.now();
  
  console.log(`\n⏱️ Test completed in ${Math.round((endTime - startTime) / 1000)} seconds\n`);
  
  const analysis = await analyzeKeyResults(results);

  console.log('\n🌐 Your exchange: http://localhost:5001');
  console.log(`💰 Ready to earn from ${analysis.totalPairs} key working pairs!`);
  console.log(`🏆 Success rate: ${analysis.successRate}% - ${analysis.successRate >= 60 ? 'EXCELLENT!' : 'GOOD!'}`);
}

runKeyTest().catch(console.error);
