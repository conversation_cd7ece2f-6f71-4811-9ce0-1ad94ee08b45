# Polo Exchange Smart Contract Deployment Configuration
# Copy this to .env and fill in your actual values

# ===== DEPLOYMENT WALLET =====
# Create a new wallet specifically for deployment (NOT your main Trust Wallet)
DEPLOYER_PRIVATE_KEY=0xYourDeploymentWalletPrivateKey
DEPLOYER_ADDRESS=0xYourDeploymentWalletAddress

# ===== RPC ENDPOINTS =====
# Get these from Infura, Alchemy, or other RPC providers

# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY

# BSC (Binance Smart Chain)
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# Polygon
POLYGON_RPC_URL=https://polygon-rpc.com/
POLYGON_MUMBAI_RPC_URL=https://rpc-mumbai.maticvigil.com/

# Avalanche
AVALANCHE_RPC_URL=https://api.avax.network/ext/bc/C/rpc

# Arbitrum
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# Optimism
OPTIMISM_RPC_URL=https://mainnet.optimism.io

# ===== BLOCK EXPLORER API KEYS =====
# For contract verification
ETHERSCAN_API_KEY=YourEtherscanApiKey
BSCSCAN_API_KEY=YourBscscanApiKey
POLYGONSCAN_API_KEY=YourPolygonscanApiKey
SNOWTRACE_API_KEY=YourSnowtraceApiKey
ARBISCAN_API_KEY=YourArbiscanApiKey
OPTIMISTIC_ETHERSCAN_API_KEY=YourOptimisticEtherscanApiKey

# ===== LIFI CONFIGURATION =====
LIFI_API_KEY=e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e
LIFI_INTEGRATOR=marko-polo-capital

# ===== DEPLOYMENT SETTINGS =====
# Fee collector address (where 3% fees will be sent)
FEE_COLLECTOR_ADDRESS=0xYourFeeCollectorAddress

# Gas settings
REPORT_GAS=true
GAS_PRICE_GWEI=20

# ===== CONTRACT ADDRESSES (will be filled after deployment) =====
ETHEREUM_CONTRACT_ADDRESS=
BSC_CONTRACT_ADDRESS=
POLYGON_CONTRACT_ADDRESS=
AVALANCHE_CONTRACT_ADDRESS=
ARBITRUM_CONTRACT_ADDRESS=
OPTIMISM_CONTRACT_ADDRESS=

# ===== LIFI DIAMOND ADDRESSES =====
# These are the official LiFi Diamond contract addresses
ETHEREUM_LIFI_DIAMOND=******************************************
BSC_LIFI_DIAMOND=******************************************
POLYGON_LIFI_DIAMOND=******************************************
AVALANCHE_LIFI_DIAMOND=******************************************
ARBITRUM_LIFI_DIAMOND=******************************************
OPTIMISM_LIFI_DIAMOND=******************************************

# ===== SECURITY SETTINGS =====
# Enable for production
VERIFY_CONTRACTS=true
PAUSE_BETWEEN_DEPLOYMENTS=10000

# ===== INSTRUCTIONS =====
# 1. Create a new wallet for deployment (don't use your main wallet)
# 2. Fund it with enough ETH/BNB/MATIC for gas fees
# 3. Get RPC URLs from Infura, Alchemy, or similar
# 4. Get API keys from block explorers for verification
# 5. Set your fee collector address
# 6. Test on testnets first!

# ===== TESTNET DEPLOYMENT FIRST =====
# Always test on testnets before mainnet:
# - Sepolia (Ethereum testnet)
# - BSC Testnet
# - Polygon Mumbai
# 
# Commands:
# npx hardhat run scripts/deploy.js --network sepolia
# npx hardhat run scripts/deploy.js --network bscTestnet
# npx hardhat run scripts/deploy.js --network polygonMumbai

# ===== MAINNET DEPLOYMENT =====
# Only after successful testnet deployment:
# npx hardhat run scripts/deploy.js --network ethereum
# npx hardhat run scripts/deploy.js --network bsc
# npx hardhat run scripts/deploy.js --network polygon
# npx hardhat run scripts/deploy.js --network avalanche
# npx hardhat run scripts/deploy.js --network arbitrum
# npx hardhat run scripts/deploy.js --network optimism
