#!/bin/bash

# 🚀 Polo Exchange - Cloudflare Pages Deployment Script
# This script will deploy your Polo Exchange to Cloudflare Pages

echo "🎯 Polo Exchange - Cloudflare Deployment"
echo "========================================"
echo ""

# Check if wrangler is available
if ! command -v npx &> /dev/null; then
    echo "❌ npx is not available. Please install Node.js"
    exit 1
fi

echo "📦 Building the application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

echo "✅ Build successful!"
echo ""

# Copy deployment files
echo "📋 Copying deployment configuration..."
cp _headers _redirects dist/public/ 2>/dev/null || true

echo "🔐 Logging into Cloudflare..."
echo "This will open your browser for authentication."
read -p "Press Enter to continue..."

npx wrangler login

echo ""
echo "🏗️  Creating Cloudflare Pages project..."
npx wrangler pages project create polo-exchange

echo ""
echo "🚀 Deploying to Cloudflare Pages..."
npx wrangler pages deploy dist/public --project-name polo-exchange

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "Your Polo Exchange is now live at:"
echo "https://polo-exchange.pages.dev"
echo ""
echo "You can also access it through your custom domain if configured."
echo ""
echo "📊 To view deployment status and manage your site:"
echo "https://dash.cloudflare.com/"
echo ""
echo "🔧 To deploy updates in the future, just run:"
echo "npm run deploy:production"
