console.log('🚀 TESTING INTEGRATED MARKO POLO CAPITAL EXCHANGE');
console.log('=' .repeat(70));

async function testWebsite() {
  console.log('🧪 Testing website integration...\n');
  
  // Test 1: Website accessibility
  console.log('1️⃣ Testing website accessibility...');
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('   ✅ Website accessible on localhost:3000');
      const html = await response.text();
      
      if (html.includes('Marko Polo Capital')) {
        console.log('   ✅ Marko Polo Capital branding found');
      }
      
      if (html.includes('Exchange') || html.includes('exchange')) {
        console.log('   ✅ Exchange component integrated');
      }
      
    } else {
      console.log(`   ❌ Website error: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ Website not accessible: ${error.message}`);
  }
  
  // Test 2: LiFi API test
  console.log('\n2️⃣ Testing LiFi API (BNB → HYPE)...');
  try {
    const response = await fetch('https://li.quest/v1/quote?' + new URLSearchParams({
      fromChain: '56',
      to<PERSON>hain: '999', 
      fromToken: '0x0000000000000000000000000000000000000000',
      toToken: '0x0000000000000000000000000000000000000000',
      fromAmount: '1000000000000000000',
      fromAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      toAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      slippage: '0.05'
    }), {
      headers: process.env.LIFI_API_KEY ? {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      } : {}
    });
    
    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate) {
        const rate = parseFloat(quote.estimate.toAmount) / 1e18;
        console.log(`   ✅ BNB → HYPE working: ${rate.toFixed(4)} rate`);
      } else {
        console.log('   ⚠️ BNB → HYPE: No estimate available');
      }
    } else {
      console.log(`   ❌ LiFi API error: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ LiFi test failed: ${error.message}`);
  }
  
  // Test 3: Jupiter API test
  console.log('\n3️⃣ Testing Jupiter API (SOL → USDC)...');
  try {
    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint: 'So11111111111111111111111111111111111111112',
      outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      amount: '1000000000',
      slippageBps: '50'
    }));
    
    if (response.ok) {
      const quote = await response.json();
      const rate = parseFloat(quote.outAmount) / 1e6;
      console.log(`   ✅ SOL → USDC working: ${rate.toFixed(2)} rate`);
    } else {
      console.log(`   ❌ Jupiter API error: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ Jupiter test failed: ${error.message}`);
  }
  
  console.log('\n🏆 INTEGRATION TEST COMPLETE');
  console.log('🌐 Your complete website: http://localhost:3000');
  console.log('💰 Ready for real trading with integrated exchange!');
}

testWebsite().catch(console.error);
