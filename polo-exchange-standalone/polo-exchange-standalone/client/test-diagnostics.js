console.log('🔍 DIAGNOSTIC TEST - WHY COMBINATIONS ARE FAILING');
console.log('=' .repeat(60));

// Load environment
require('dotenv').config();

console.log('🔑 Environment Check:');
console.log('   LiFi API Key:', process.env.LIFI_API_KEY ? 'LOADED ✅' : 'MISSING ❌');
console.log('   Current Directory:', process.cwd());
console.log('');

async function runDiagnostics() {
  console.log('🧪 Testing specific failure cases...\n');
  
  // Test 1: Simple Jupiter pair (should work)
  console.log('1️⃣ Testing Jupiter (SOL → USDC):');
  try {
    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint: 'So11111111111111111111111111111111111111112',
      outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      amount: '1000000000',
      slippageBps: '50'
    }));
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      const rate = parseFloat(data.outAmount) / 1e6;
      console.log('   ✅ SUCCESS: Rate =', rate.toFixed(2));
    } else {
      const error = await response.text();
      console.log('   ❌ FAILED:', error.substring(0, 100));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  // Test 2: LiFi with API key (should work)
  console.log('\n2️⃣ Testing LiFi (BNB → HYPE):');
  try {
    const params = new URLSearchParams({
      fromChain: '56',
      toChain: '999',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000',
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: '0.05'
    });
    
    const response = await fetch('https://li.quest/v1/quote?' + params, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY || 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e'
      }
    });
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      if (data.estimate && data.estimate.toAmount) {
        const rate = parseFloat(data.estimate.toAmount) / 1e18;
        console.log('   ✅ SUCCESS: Rate =', rate.toFixed(6));
      } else {
        console.log('   ⚠️ No estimate in response');
        console.log('   Response keys:', Object.keys(data));
      }
    } else {
      const error = await response.text();
      console.log('   ❌ FAILED:', error.substring(0, 200));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  // Test 3: Common failure case (unsupported token)
  console.log('\n3️⃣ Testing unsupported token (BTC → ADA):');
  try {
    const params = new URLSearchParams({
      fromChain: '1',
      toChain: '1',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000',
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: '0.03'
    });
    
    const response = await fetch('https://li.quest/v1/quote?' + params, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY || 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e'
      }
    });
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      console.log('   ⚠️ Unexpected success');
    } else {
      const error = await response.text();
      console.log('   ❌ Expected failure:', error.substring(0, 100));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  console.log('\n🏆 DIAGNOSTIC SUMMARY:');
  console.log('   🚀 Jupiter should work for Solana pairs');
  console.log('   🌉 LiFi should work for cross-chain pairs with API key');
  console.log('   ❌ Many tokens fail because they need specific contract addresses');
  console.log('   🔧 Solution: Use correct token contract addresses instead of 0x000...');
  console.log('\n💡 Main issue: Using native token placeholder instead of actual token contracts!');
}

runDiagnostics().catch(console.error);
