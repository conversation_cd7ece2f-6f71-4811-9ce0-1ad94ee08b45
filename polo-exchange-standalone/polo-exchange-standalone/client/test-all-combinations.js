console.log('🚀 TESTING ALL SWAP COMBINATIONS - INTEGRATED EXCHANGE');
console.log('=' .repeat(80));

// All tokens from your integrated exchange
const ALL_TOKENS = [
  // Major cryptocurrencies
  'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK',
  'UNI', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX', 'ETC', 'XLM',
  
  // Stablecoins
  'USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD',
  
  // DeFi tokens
  'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL',
  
  // Layer 1s & 2s
  'FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA',
  
  // Exotic/New tokens
  'HYPE', 'SUI', 'APT', 'OP', 'ARB', 'IMX', 'LDO', 'GMX', 'DYDX',
  
  // Solana ecosystem
  'RAY', 'SRM', 'ORCA', 'MNGO'
];

async function testAllCombinations() {
  const totalTokens = ALL_TOKENS.length;
  const totalCombinations = totalTokens * (totalTokens - 1);
  
  console.log(`🧪 Testing ${totalTokens} tokens`);
  console.log(`🔄 Total combinations: ${totalCombinations.toLocaleString()}`);
  console.log(`⏱️ Estimated time: ${Math.round(totalCombinations / 20)} seconds\n`);

  const results = {
    totalTested: 0,
    workingPairs: [],
    failedPairs: [],
    providerStats: { jupiter: 0, lifi: 0, failed: 0 },
    categoryStats: {
      exotic: { working: 0, total: 0 },
      crossChain: { working: 0, total: 0 },
      solana: { working: 0, total: 0 },
      stablecoin: { working: 0, total: 0 },
      major: { working: 0, total: 0 }
    }
  };

  let tested = 0;
  const startTime = Date.now();

  // Test every combination
  for (let i = 0; i < ALL_TOKENS.length; i++) {
    const fromToken = ALL_TOKENS[i];
    
    for (let j = 0; j < ALL_TOKENS.length; j++) {
      if (i === j) continue;
      
      const toToken = ALL_TOKENS[j];
      tested++;
      results.totalTested++;
      
      // Show progress every 50 tests
      if (tested % 50 === 0) {
        const elapsed = (Date.now() - startTime) / 1000;
        const rate = tested / elapsed;
        const remaining = (totalCombinations - tested) / rate;
        const progress = (tested / totalCombinations * 100).toFixed(1);
        
        console.log(`📊 Progress: ${progress}% (${tested}/${totalCombinations}) - ${rate.toFixed(1)} tests/sec - ${Math.round(remaining)}s remaining`);
      }
      
      const result = await testPair(fromToken, toToken);
      const category = getCategory(fromToken, toToken);
      
      results.categoryStats[category].total++;
      
      if (result.success) {
        results.workingPairs.push({
          from: fromToken,
          to: toToken,
          provider: result.provider,
          rate: result.rate,
          category
        });
        results.providerStats[result.provider]++;
        results.categoryStats[category].working++;
      } else {
        results.failedPairs.push({
          from: fromToken,
          to: toToken,
          error: result.error,
          category
        });
        results.providerStats.failed++;
      }
    }
  }

  return results;
}

async function testPair(fromToken, toToken) {
  try {
    // Try Jupiter for Solana tokens
    const solanaTokens = ['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA', 'MNGO'];
    
    if (solanaTokens.includes(fromToken) && solanaTokens.includes(toToken)) {
      const result = await testJupiter(fromToken, toToken);
      if (result.success) return { ...result, provider: 'jupiter' };
    }
    
    // Try LiFi for everything else
    const result = await testLiFi(fromToken, toToken);
    if (result.success) return { ...result, provider: 'lifi' };
    
    return { success: false, error: 'No provider supports this pair' };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiter(fromToken, toToken) {
  try {
    const mints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
      'SRM': 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
      'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      'MNGO': 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac'
    };

    const inputMint = mints[fromToken];
    const outputMint = mints[toToken];

    if (!inputMint || !outputMint) return { success: false, error: 'Token not supported' };

    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint, outputMint, amount: '**********', slippageBps: '50'
    }));

    if (response.ok) {
      const quote = await response.json();
      const decimals = toToken === 'SOL' ? 9 : 6;
      const rate = parseFloat(quote.outAmount) / Math.pow(10, decimals);
      return { success: true, rate };
    }
    
    return { success: false, error: `API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFi(fromToken, toToken) {
  try {
    const chains = {
      'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1, 'DAI': 1, 'LINK': 1, 'UNI': 1,
      'AAVE': 1, 'COMP': 1, 'MKR': 1, 'SNX': 1, 'CRV': 1, 'YFI': 1, 'SUSHI': 1, 'BAL': 1,
      'BNB': 56, 'BUSD': 56,
      'MATIC': 137,
      'AVAX': 43114,
      'FTM': 250,
      'HYPE': 999,
      'SUI': 101,
      'ARB': 42161, 'GMX': 42161, 'DYDX': 42161,
      'OP': 10,
      'ADA': 1, 'XRP': 1, 'DOT': 1, 'LTC': 1, 'BCH': 1, 'ALGO': 1, 'VET': 1,
      'ICP': 1, 'FIL': 1, 'TRX': 1, 'ETC': 1, 'XLM': 1, 'NEAR': 1, 'ATOM': 1,
      'LUNA': 1, 'EGLD': 1, 'HBAR': 1, 'FLOW': 1, 'MINA': 1, 'APT': 1, 'IMX': 1, 'LDO': 1
    };

    const fromChain = chains[fromToken] || 1;
    const toChain = chains[toToken] || 1;
    const isExotic = ['HYPE', 'SUI'].includes(fromToken) || ['HYPE', 'SUI'].includes(toToken);

    const params = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '**********000000000',
      fromAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      toAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
      slippage: isExotic ? '0.05' : '0.03'
    });

    const headers = {};
    if (process.env.LIFI_API_KEY) {
      headers['x-lifi-api-key'] = process.env.LIFI_API_KEY;
    }

    const response = await fetch(`https://li.quest/v1/quote?${params}`, { headers });

    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate && quote.estimate.toAmount) {
        const rate = parseFloat(quote.estimate.toAmount) / 1e18;
        return { success: true, rate };
      }
    }
    
    return { success: false, error: `API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function getCategory(fromToken, toToken) {
  const exotic = ['HYPE', 'SUI'];
  const solana = ['SOL', 'RAY', 'SRM', 'ORCA', 'MNGO'];
  const stablecoin = ['USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD'];
  const major = ['BTC', 'ETH', 'BNB', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI'];

  if (exotic.includes(fromToken) || exotic.includes(toToken)) return 'exotic';
  if (solana.includes(fromToken) && solana.includes(toToken)) return 'solana';
  if (stablecoin.includes(fromToken) || stablecoin.includes(toToken)) return 'stablecoin';
  
  const chains = { 'ETH': 1, 'BNB': 56, 'MATIC': 137, 'AVAX': 43114, 'FTM': 250 };
  const fromChain = chains[fromToken] || 1;
  const toChain = chains[toToken] || 1;
  if (fromChain !== toChain) return 'crossChain';
  
  return 'major';
}

async function showResults(results) {
  console.log('\n' + '=' .repeat(80));
  console.log('🏆 COMPLETE SWAP COMBINATION RESULTS');
  console.log('=' .repeat(80));

  const workingCount = results.workingPairs.length;
  const totalTested = results.totalTested;
  const successRate = (workingCount / totalTested * 100).toFixed(1);

  console.log(`\n📊 Overall Statistics:`);
  console.log(`   🔄 Total combinations tested: ${totalTested.toLocaleString()}`);
  console.log(`   ✅ Working pairs: ${workingCount.toLocaleString()}`);
  console.log(`   ❌ Failed pairs: ${(totalTested - workingCount).toLocaleString()}`);
  console.log(`   📈 Success rate: ${successRate}%`);

  console.log(`\n🌊 Provider Statistics:`);
  console.log(`   🚀 Jupiter (Solana): ${results.providerStats.jupiter.toLocaleString()}`);
  console.log(`   🌉 LiFi (Cross-chain): ${results.providerStats.lifi.toLocaleString()}`);
  console.log(`   ❌ Failed: ${results.providerStats.failed.toLocaleString()}`);

  console.log(`\n📂 Category Performance:`);
  Object.entries(results.categoryStats).forEach(([category, stats]) => {
    const rate = stats.total > 0 ? (stats.working / stats.total * 100).toFixed(1) : '0.0';
    console.log(`   ${category.padEnd(12)}: ${stats.working.toLocaleString()}/${stats.total.toLocaleString()} (${rate}%)`);
  });

  console.log(`\n💰 Revenue Analysis:`);
  const exotic = results.categoryStats.exotic.working;
  const crossChain = results.categoryStats.crossChain.working;
  const solana = results.categoryStats.solana.working;
  const stablecoin = results.categoryStats.stablecoin.working;

  console.log(`   🔥 Exotic pairs (1.2% fee): ${exotic.toLocaleString()}`);
  console.log(`   🌉 Cross-chain pairs (0.8% fee): ${crossChain.toLocaleString()}`);
  console.log(`   🚀 Solana pairs (0.4% fee): ${solana.toLocaleString()}`);
  console.log(`   💰 Stablecoin pairs (0.4% fee): ${stablecoin.toLocaleString()}`);

  const dailyRevenue = (exotic * 120) + (crossChain * 80) + (solana * 40) + (stablecoin * 40);
  console.log(`   💎 Daily revenue potential (per $10K volume): $${dailyRevenue.toLocaleString()}`);

  console.log(`\n🎯 Top Working Pairs:`);
  const topPairs = results.workingPairs
    .sort((a, b) => b.rate - a.rate)
    .slice(0, 10);
  
  topPairs.forEach((pair, index) => {
    console.log(`   ${index + 1}. ${pair.from} → ${pair.to}: ${pair.rate.toFixed(4)} (${pair.provider})`);
  });

  console.log(`\n🏆 Final Assessment:`);
  if (successRate >= 80) {
    console.log('   🎉 EXCEPTIONAL! World-class exchange coverage!');
  } else if (successRate >= 60) {
    console.log('   ✅ EXCELLENT! Comprehensive exchange coverage!');
  } else if (successRate >= 40) {
    console.log('   👍 GOOD! Solid exchange coverage!');
  } else {
    console.log('   ⚠️ MODERATE! Consider adding more providers!');
  }

  console.log(`\n🌐 Your integrated exchange: http://localhost:3000`);
  console.log(`💰 Ready to earn from ${workingCount.toLocaleString()} working pairs!`);
}

// Run the test
async function runTest() {
  console.log('🧪 Starting comprehensive combination test...\n');
  const startTime = Date.now();
  const results = await testAllCombinations();
  const endTime = Date.now();
  console.log(`\n⏱️ Test completed in ${Math.round((endTime - startTime) / 1000)} seconds\n`);
  await showResults(results);
}

runTest().catch(console.error);
