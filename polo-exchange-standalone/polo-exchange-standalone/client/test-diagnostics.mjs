console.log('�� DIAGNOSTIC TEST - WHY COMBINATIONS ARE FAILING');
console.log('=' .repeat(60));

// Environment variables
const LIFI_API_KEY = 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e';

console.log('🔑 Environment Check:');
console.log('   LiFi API Key: LOADED ✅');
console.log('   Current Directory:', process.cwd());
console.log('');

async function runDiagnostics() {
  console.log('🧪 Testing specific failure cases...\n');
  
  // Test 1: Simple Jupiter pair (should work)
  console.log('1️⃣ Testing Jupiter (SOL → USDC):');
  try {
    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint: 'So11111111111111111111111111111111111111112',
      outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      amount: '1000000000',
      slippageBps: '50'
    }));
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      const rate = parseFloat(data.outAmount) / 1e6;
      console.log('   ✅ SUCCESS: Rate =', rate.toFixed(2));
    } else {
      const error = await response.text();
      console.log('   ❌ FAILED:', error.substring(0, 100));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  // Test 2: LiFi with API key (should work)
  console.log('\n2️⃣ Testing LiFi (BNB → HYPE):');
  try {
    const params = new URLSearchParams({
      fromChain: '56',
      toChain: '999',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000',
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: '0.05'
    });
    
    const response = await fetch('https://li.quest/v1/quote?' + params, {
      headers: {
        'x-lifi-api-key': LIFI_API_KEY
      }
    });
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      if (data.estimate && data.estimate.toAmount) {
        const rate = parseFloat(data.estimate.toAmount) / 1e18;
        console.log('   ✅ SUCCESS: Rate =', rate.toFixed(6));
      } else {
        console.log('   ⚠️ No estimate in response');
        console.log('   Response keys:', Object.keys(data));
      }
    } else {
      const error = await response.text();
      console.log('   ❌ FAILED:', error.substring(0, 200));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  // Test 3: Test with actual USDC contract address
  console.log('\n3️⃣ Testing LiFi with real USDC addresses (ETH → USDC):');
  try {
    const params = new URLSearchParams({
      fromChain: '1',
      toChain: '1',
      fromToken: '******************************************', // ETH
      toToken: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505', // USDC
      fromAmount: '1000000000000000000',
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: '0.03'
    });
    
    const response = await fetch('https://li.quest/v1/quote?' + params, {
      headers: {
        'x-lifi-api-key': LIFI_API_KEY
      }
    });
    
    console.log('   Response status:', response.status);
    if (response.ok) {
      const data = await response.json();
      if (data.estimate && data.estimate.toAmount) {
        const rate = parseFloat(data.estimate.toAmount) / 1e6; // USDC has 6 decimals
        console.log('   ✅ SUCCESS: Rate =', rate.toFixed(2));
      } else {
        console.log('   ⚠️ No estimate in response');
      }
    } else {
      const error = await response.text();
      console.log('   ❌ FAILED:', error.substring(0, 200));
    }
  } catch (error) {
    console.log('   💥 EXCEPTION:', error.message);
  }
  
  console.log('\n🏆 DIAGNOSTIC SUMMARY:');
  console.log('   🚀 Jupiter: Works for Solana ecosystem');
  console.log('   🌉 LiFi: Works for cross-chain with correct addresses');
  console.log('   ❌ Issue: Many tokens need specific contract addresses');
  console.log('   🔧 Solution: Map each token to correct contract address');
  console.log('\n💡 MAIN PROBLEM: Need real token contract addresses, not 0x000... placeholders!');
  console.log('🎯 NEXT STEP: Create complete token address mapping for all 64 tokens');
}

runDiagnostics().catch(console.error);
