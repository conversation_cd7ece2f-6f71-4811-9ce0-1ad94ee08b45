// Simple test for instant liquidity providers
import dotenv from 'dotenv';
dotenv.config();

console.log('🧪 MARKO POLO CAPITAL - Quick Liquidity Test');
console.log('=' .repeat(50));

// Test 1: Environment Check
console.log('\n🔧 Environment Check:');
console.log('LiFi API Key:', process.env.LIFI_API_KEY ? '✅ Configured' : '❌ Missing');
console.log('Solana RPC:', process.env.SOLANA_RPC_URL ? '✅ Configured' : '❌ Missing');
console.log('Node ENV:', process.env.NODE_ENV || 'development');

// Test 2: Provider APIs
async function testAPIs() {
  console.log('\n🌐 Testing Provider APIs:');
  
  // Test Jupiter (Free)
  try {
    const response = await fetch('https://quote-api.jup.ag/v6/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=**********');
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Jupiter API: Working');
      console.log(`   1 SOL = ${(parseFloat(data.outAmount) / 1e6).toFixed(2)} USDC`);
    } else {
      console.log('❌ Jupiter API: Failed');
    }
  } catch (error) {
    console.log('❌ Jupiter API: Error -', error.message);
  }
  
  // Test LiFi (Your API Key)
  if (process.env.LIFI_API_KEY) {
    try {
      const response = await fetch('https://li.quest/v1/chains', {
        headers: { 'x-lifi-api-key': process.env.LIFI_API_KEY }
      });
      if (response.ok) {
        const data = await response.json();
        console.log('✅ LiFi API: Working');
        console.log(`   Supports ${data.chains?.length || 'many'} chains`);
      } else {
        console.log('❌ LiFi API: Failed -', response.status);
      }
    } catch (error) {
      console.log('❌ LiFi API: Error -', error.message);
    }
  } else {
    console.log('⚠️ LiFi API: No key configured');
  }
}

// Test 3: Server Check
async function testServer() {
  console.log('\n🏥 Testing Local Server:');
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Server: Running on localhost:3000');
    } else {
      console.log('❌ Server: Error -', response.status);
    }
  } catch (error) {
    console.log('❌ Server: Not running');
    console.log('💡 Run: npm run dev');
  }
}

// Run tests
async function runTests() {
  await testAPIs();
  await testServer();
  
  console.log('\n🎯 Manual Test Instructions:');
  console.log('1. Go to: https://healing-uh-legislative-brighton.trycloudflare.com');
  console.log('2. Select: SOL → USDC');
  console.log('3. Amount: 1');
  console.log('4. Click: "Get Instant Liquidity Quotes" (orange button)');
  console.log('5. Should see: Real quotes from Jupiter');
  
  console.log('\n🌊 Test Complete!');
}

runTests().catch(console.error);
