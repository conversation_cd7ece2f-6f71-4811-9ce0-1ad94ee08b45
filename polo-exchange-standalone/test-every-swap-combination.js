#!/usr/bin/env node

/**
 * Test Every Single Swap Combination
 * Comprehensive test of ALL possible token pair combinations
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 TESTING EVERY SINGLE SWAP COMBINATION');
console.log('=' .repeat(80));

// Complete token list from your exchange
const ALL_TOKENS = [
  // Major cryptocurrencies
  'BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK',
  'UNI', 'LTC', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX', 'ETC', 'XLM',
  
  // Stablecoins
  'USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'USDD', 'GUSD',
  
  // DeFi tokens
  'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL', '1INCH',
  
  // Layer 1s & 2s
  'FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA',
  
  // Exotic/New tokens
  'HYPE', 'SUI', 'APT', 'OP', 'ARB', 'IMX', 'LDO', 'GMX', 'DYDX',
  
  // Solana ecosystem
  'RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS'
];

async function testEverySwapCombination() {
  const totalTokens = ALL_TOKENS.length;
  const totalCombinations = totalTokens * (totalTokens - 1); // A→B and B→A are different
  
  console.log(`🧪 Testing ${totalTokens} tokens`);
  console.log(`🔄 Total combinations to test: ${totalCombinations.toLocaleString()}`);
  console.log(`⏱️ Estimated time: ${Math.round(totalCombinations / 10)} seconds\n`);

  const results = {
    totalCombinations,
    workingPairs: [],
    failedPairs: [],
    providerStats: {
      jupiter: 0,
      lifi: 0,
      failed: 0
    },
    tokenStats: {},
    categoryStats: {
      major: { working: 0, total: 0 },
      stablecoin: { working: 0, total: 0 },
      defi: { working: 0, total: 0 },
      layer1: { working: 0, total: 0 },
      exotic: { working: 0, total: 0 },
      solana: { working: 0, total: 0 }
    }
  };

  // Initialize token stats
  ALL_TOKENS.forEach(token => {
    results.tokenStats[token] = { asFrom: 0, asTo: 0, total: 0 };
  });

  let tested = 0;
  const startTime = Date.now();

  // Test every combination
  for (let i = 0; i < ALL_TOKENS.length; i++) {
    const fromToken = ALL_TOKENS[i];
    
    for (let j = 0; j < ALL_TOKENS.length; j++) {
      if (i === j) continue; // Skip same token
      
      const toToken = ALL_TOKENS[j];
      tested++;
      
      // Show progress every 100 tests
      if (tested % 100 === 0) {
        const elapsed = (Date.now() - startTime) / 1000;
        const rate = tested / elapsed;
        const remaining = (totalCombinations - tested) / rate;
        const progress = (tested / totalCombinations * 100).toFixed(1);
        
        console.log(`📊 Progress: ${progress}% (${tested}/${totalCombinations}) - ${rate.toFixed(1)} tests/sec - ${Math.round(remaining)}s remaining`);
      }
      
      // Test the pair
      const result = await testSwapPair(fromToken, toToken);
      
      if (result.success) {
        results.workingPairs.push({
          from: fromToken,
          to: toToken,
          provider: result.provider,
          rate: result.rate,
          category: getTokenCategory(fromToken, toToken)
        });
        
        // Update stats
        results.tokenStats[fromToken].asFrom++;
        results.tokenStats[fromToken].total++;
        results.tokenStats[toToken].asTo++;
        results.tokenStats[toToken].total++;
        results.providerStats[result.provider]++;
        
        // Update category stats
        const category = getTokenCategory(fromToken, toToken);
        results.categoryStats[category].working++;
        
      } else {
        results.failedPairs.push({
          from: fromToken,
          to: toToken,
          error: result.error
        });
        results.providerStats.failed++;
      }
      
      // Update category totals
      const category = getTokenCategory(fromToken, toToken);
      results.categoryStats[category].total++;
    }
  }

  return results;
}

async function testSwapPair(fromToken, toToken) {
  try {
    // Try Jupiter first for Solana tokens
    const solanaTokens = ['SOL', 'USDC', 'USDT', 'RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS'];
    
    if (solanaTokens.includes(fromToken) && solanaTokens.includes(toToken)) {
      const jupiterResult = await testJupiterSwap(fromToken, toToken);
      if (jupiterResult.success) {
        return { ...jupiterResult, provider: 'jupiter' };
      }
    }
    
    // Try LiFi for everything else
    const lifiResult = await testLiFiSwap(fromToken, toToken);
    if (lifiResult.success) {
      return { ...lifiResult, provider: 'lifi' };
    }
    
    return { success: false, error: 'No provider supports this pair' };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testJupiterSwap(fromToken, toToken) {
  try {
    const tokenMints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'RAY': '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
      'SRM': 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
      'ORCA': 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      'MNGO': 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac',
      'STEP': 'StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT',
      'COPE': '8HGyAAB1yoM1ttS7pXjHMa3dukTFGQggnFFH3hJZgzQh',
      'FIDA': 'EchesyfXePKdLtoiZSL8pBe8Myagyy8ZRqsACNCFGnvp',
      'MAPS': 'MAPS41MDahZ9QdKXhVa4dWB9RuyfV4XqhyAZ8XcYepb'
    };

    const inputMint = tokenMints[fromToken];
    const outputMint = tokenMints[toToken];

    if (!inputMint || !outputMint) {
      return { success: false, error: 'Token not supported by Jupiter' };
    }

    const response = await fetch('https://quote-api.jup.ag/v6/quote?' + new URLSearchParams({
      inputMint,
      outputMint,
      amount: '1000000000', // 1 SOL or equivalent
      slippageBps: '50'
    }));

    if (response.ok) {
      const quote = await response.json();
      if (quote.outAmount) {
        const decimals = getTokenDecimals(toToken);
        const rate = parseFloat(quote.outAmount) / Math.pow(10, decimals);
        return { success: true, rate };
      }
    }
    
    return { success: false, error: `Jupiter API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testLiFiSwap(fromToken, toToken) {
  try {
    if (!process.env.LIFI_API_KEY) {
      return { success: false, error: 'No LiFi API key' };
    }

    // Chain mappings for LiFi
    const chainMappings = {
      // Ethereum ecosystem
      'BTC': 1, 'ETH': 1, 'USDC': 1, 'USDT': 1, 'DAI': 1, 'LINK': 1, 'UNI': 1,
      'AAVE': 1, 'COMP': 1, 'MKR': 1, 'SNX': 1, 'CRV': 1, 'YFI': 1, 'SUSHI': 1,
      'BAL': 1, '1INCH': 1, 'LDO': 1,
      
      // BSC ecosystem
      'BNB': 56, 'BUSD': 56,
      
      // Polygon ecosystem
      'MATIC': 137,
      
      // Avalanche ecosystem
      'AVAX': 43114,
      
      // Arbitrum ecosystem
      'ARB': 42161, 'GMX': 42161, 'DYDX': 42161,
      
      // Optimism ecosystem
      'OP': 10,
      
      // Other L1s (mapped to Ethereum for cross-chain)
      'ADA': 1, 'XRP': 1, 'DOT': 1, 'LTC': 1, 'BCH': 1, 'ALGO': 1, 'VET': 1,
      'ICP': 1, 'FIL': 1, 'TRX': 1, 'ETC': 1, 'XLM': 1, 'NEAR': 1, 'ATOM': 1,
      'LUNA': 1, 'EGLD': 1, 'HBAR': 1, 'FLOW': 1, 'MINA': 1, 'APT': 1, 'IMX': 1,
      
      // Exotic tokens
      'HYPE': 999,
      'SUI': 101,
      'FTM': 250
    };

    const fromChain = chainMappings[fromToken] || 1;
    const toChain = chainMappings[toToken] || 1;

    const quoteParams = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000', // 1 token
      fromAddress: '******************************************',
      toAddress: '******************************************',
      slippage: getTokenCategory(fromToken, toToken) === 'exotic' ? '0.05' : '0.03'
    });

    const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      }
    });

    if (response.ok) {
      const quote = await response.json();
      if (quote.estimate && quote.estimate.toAmount) {
        const rate = parseFloat(quote.estimate.toAmount) / 1e18;
        return { success: true, rate };
      }
    }
    
    return { success: false, error: `LiFi API error ${response.status}` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

function getTokenCategory(fromToken, toToken) {
  const categories = {
    major: ['BTC', 'ETH', 'BNB', 'SOL', 'ADA', 'XRP', 'DOT', 'AVAX', 'MATIC', 'LINK', 'UNI', 'LTC', 'BCH'],
    stablecoin: ['USDC', 'USDT', 'DAI', 'BUSD', 'FRAX', 'TUSD', 'USDD', 'GUSD'],
    defi: ['AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'YFI', 'SUSHI', 'BAL', '1INCH'],
    layer1: ['FTM', 'NEAR', 'ATOM', 'LUNA', 'EGLD', 'HBAR', 'FLOW', 'MINA', 'APT', 'OP', 'ARB'],
    exotic: ['HYPE', 'SUI', 'IMX', 'LDO', 'GMX', 'DYDX'],
    solana: ['RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'MAPS']
  };

  // Check if both tokens are in the same category
  for (const [category, tokens] of Object.entries(categories)) {
    if (tokens.includes(fromToken) || tokens.includes(toToken)) {
      return category;
    }
  }
  
  return 'major'; // Default
}

function getTokenDecimals(token) {
  const decimals = {
    'SOL': 9, 'RAY': 6, 'SRM': 6, 'ORCA': 6, 'MNGO': 6,
    'STEP': 9, 'COPE': 6, 'FIDA': 6, 'MAPS': 6
  };
  return decimals[token] || 6;
}

async function analyzeResults(results) {
  console.log('\n' + '=' .repeat(80));
  console.log('🏆 COMPLETE SWAP COMBINATION ANALYSIS');
  console.log('=' .repeat(80));

  const workingCount = results.workingPairs.length;
  const failedCount = results.failedPairs.length;
  const successRate = (workingCount / results.totalCombinations * 100).toFixed(1);

  console.log(`\n📊 Overall Statistics:`);
  console.log(`   🔄 Total combinations tested: ${results.totalCombinations.toLocaleString()}`);
  console.log(`   ✅ Working pairs: ${workingCount.toLocaleString()}`);
  console.log(`   ❌ Failed pairs: ${failedCount.toLocaleString()}`);
  console.log(`   📈 Success rate: ${successRate}%`);

  console.log(`\n🌊 Provider Statistics:`);
  console.log(`   🚀 Jupiter (Solana): ${results.providerStats.jupiter.toLocaleString()} pairs`);
  console.log(`   🌉 LiFi (Cross-chain): ${results.providerStats.lifi.toLocaleString()} pairs`);
  console.log(`   ❌ Failed: ${results.providerStats.failed.toLocaleString()} pairs`);

  console.log(`\n📂 Category Statistics:`);
  Object.entries(results.categoryStats).forEach(([category, stats]) => {
    const rate = stats.total > 0 ? (stats.working / stats.total * 100).toFixed(1) : '0.0';
    console.log(`   ${category.padEnd(12)}: ${stats.working.toLocaleString()}/${stats.total.toLocaleString()} (${rate}%)`);
  });

  console.log(`\n🏅 Top Performing Tokens:`);
  const sortedTokens = Object.entries(results.tokenStats)
    .sort(([,a], [,b]) => b.total - a.total)
    .slice(0, 10);
  
  sortedTokens.forEach(([token, stats], index) => {
    console.log(`   ${(index + 1).toString().padStart(2)}. ${token.padEnd(6)}: ${stats.total.toLocaleString()} working pairs`);
  });

  console.log(`\n💰 Revenue Analysis:`);
  const exoticPairs = results.workingPairs.filter(p => p.category === 'exotic').length;
  const crossChainPairs = results.workingPairs.filter(p => p.category !== 'solana' && p.category !== 'stablecoin').length;
  const stablecoinPairs = results.workingPairs.filter(p => p.category === 'stablecoin').length;
  const solanaPairs = results.workingPairs.filter(p => p.category === 'solana').length;

  console.log(`   🔥 Exotic pairs (1.2% fee): ${exoticPairs.toLocaleString()}`);
  console.log(`   🌉 Cross-chain pairs (0.8% fee): ${crossChainPairs.toLocaleString()}`);
  console.log(`   💰 Stablecoin pairs (0.4% fee): ${stablecoinPairs.toLocaleString()}`);
  console.log(`   🚀 Solana pairs (0.4% fee): ${solanaPairs.toLocaleString()}`);

  console.log(`\n🎯 Business Impact:`);
  console.log(`   📈 Total addressable pairs: ${workingCount.toLocaleString()}`);
  console.log(`   💎 Unique market position: ${exoticPairs} exotic pairs`);
  console.log(`   🌐 Cross-chain capability: ${crossChainPairs} pairs`);
  console.log(`   🚀 Solana ecosystem: ${solanaPairs} pairs`);

  if (successRate >= 80) {
    console.log(`\n🎉 EXCEPTIONAL! Your exchange has world-class coverage!`);
  } else if (successRate >= 60) {
    console.log(`\n✅ EXCELLENT! Your exchange has comprehensive coverage!`);
  } else if (successRate >= 40) {
    console.log(`\n👍 GOOD! Your exchange has solid coverage!`);
  } else {
    console.log(`\n⚠️ MODERATE! Consider adding more providers for better coverage!`);
  }

  return {
    totalPairs: workingCount,
    successRate: parseFloat(successRate),
    exoticPairs,
    crossChainPairs,
    stablecoinPairs,
    solanaPairs
  };
}

// Run the comprehensive test
async function runCompleteTest() {
  console.log('🧪 Starting complete swap combination test...\n');
  console.log('⚠️ This will test EVERY possible combination - may take 10-15 minutes\n');

  const startTime = Date.now();
  const results = await testEverySwapCombination();
  const endTime = Date.now();
  
  console.log(`\n⏱️ Test completed in ${Math.round((endTime - startTime) / 1000)} seconds\n`);
  
  const analysis = await analyzeResults(results);

  console.log('\n🌐 Your exchange: http://localhost:5001');
  console.log(`💰 Ready to earn from ${analysis.totalPairs.toLocaleString()} working pairs!`);
  console.log(`🏆 Success rate: ${analysis.successRate}% - ${analysis.successRate >= 60 ? 'EXCELLENT!' : 'GOOD!'}`);
}

runCompleteTest().catch(console.error);
