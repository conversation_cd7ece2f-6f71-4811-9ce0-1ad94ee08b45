#!/usr/bin/env node

/**
 * Test API Encryption System
 * Validates the security implementation for all API keys
 */

import { getApiKeyManager } from './src/security/ApiKeyManager.js';
import InstantLiquidityAggregator from './src/services/InstantLiquidityAggregator.js';
import crypto from 'crypto';

console.log('🔐 Testing API Encryption System\n');

async function testEncryptionSecurity() {
  console.log('🧪 Security Test Suite');
  console.log('=' .repeat(50));

  try {
    // Test 1: API Key Manager Initialization
    console.log('\n1️⃣ Testing API Key Manager Initialization...');
    const apiKeyManager = getApiKeyManager();
    console.log('✅ API Key Manager initialized successfully');

    // Test 2: Encryption/Decryption Cycle
    console.log('\n2️⃣ Testing Encryption/Decryption Cycle...');
    const testApiKey = 'test-api-key-' + crypto.randomBytes(16).toString('hex');
    
    await apiKeyManager.storeApiKey({
      provider: 'test-provider',
      key: testApi<PERSON>ey,
      description: 'Test API Key for Security Validation'
    });
    console.log('✅ Test API key encrypted and stored');

    const retrievedKey = await apiKeyManager.getApiKey('test-provider');
    if (retrievedKey === testApiKey) {
      console.log('✅ Test API key decrypted successfully');
    } else {
      throw new Error('Decrypted key does not match original');
    }

    // Clean up test key
    await apiKeyManager.removeApiKey('test-provider');
    console.log('✅ Test API key removed');

    // Test 3: Key Validation
    console.log('\n3️⃣ Testing API Key Validation...');
    const validLiFiKey = 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e';
    const isValid = apiKeyManager.validateApiKeyFormat('lifi', validLiFiKey);
    if (isValid) {
      console.log('✅ LiFi API key format validation passed');
    } else {
      console.log('❌ LiFi API key format validation failed');
    }

    // Test 4: Provider Auto-Configuration
    console.log('\n4️⃣ Testing Provider Auto-Configuration...');
    const aggregator = new InstantLiquidityAggregator();
    
    // Wait for async initialization
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const providerStats = aggregator.getProviderStats();
    console.log(`✅ ${providerStats.totalProviders} providers initialized`);
    console.log(`✅ ${providerStats.enabledProviders} providers enabled`);
    
    // Test 5: Secure Key Retrieval
    console.log('\n5️⃣ Testing Secure Key Retrieval...');
    const storedProviders = apiKeyManager.getStoredProviders();
    console.log(`✅ ${storedProviders.length} encrypted keys stored`);
    
    for (const provider of storedProviders) {
      const key = await apiKeyManager.getApiKey(provider);
      if (key) {
        console.log(`✅ ${provider}: Key retrieved (${key.length} chars)`);
      } else {
        console.log(`❌ ${provider}: Failed to retrieve key`);
      }
    }

    // Test 6: Usage Statistics
    console.log('\n6️⃣ Testing Usage Statistics...');
    const usageStats = apiKeyManager.getUsageStats();
    console.log(`✅ Usage statistics for ${usageStats.length} providers`);
    
    usageStats.forEach(stat => {
      console.log(`   📊 ${stat.provider}: Created ${stat.daysSinceCreated} days ago`);
    });

    // Test 7: Backup System
    console.log('\n7️⃣ Testing Backup System...');
    const backupPath = await apiKeyManager.backupKeys();
    console.log(`✅ Backup created: ${backupPath}`);

    console.log('\n🎉 All Security Tests Passed!');
    return true;

  } catch (error) {
    console.error('\n❌ Security test failed:', error.message);
    return false;
  }
}

async function testProviderSecurity() {
  console.log('\n🌊 Provider Security Test');
  console.log('=' .repeat(50));

  try {
    const aggregator = new InstantLiquidityAggregator();
    
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test provider security
    const providers = ['lifi', 'oneinch', 'changenow', 'paraswap', 'zerox'];
    
    for (const provider of providers) {
      console.log(`\n🔍 Testing ${provider} security...`);
      
      try {
        // This should use encrypted keys
        const quotes = await aggregator.getInstantQuotes('ETH', 'USDC', 0.01);
        
        const providerQuote = quotes.allQuotes.find(q => q.provider === provider);
        if (providerQuote) {
          console.log(`✅ ${provider}: Secure quote retrieved`);
        } else {
          console.log(`ℹ️ ${provider}: No quote (may need API key)`);
        }
      } catch (error) {
        console.log(`⚠️ ${provider}: ${error.message}`);
      }
    }

    console.log('\n✅ Provider security test completed');
    return true;

  } catch (error) {
    console.error('\n❌ Provider security test failed:', error.message);
    return false;
  }
}

async function testSecurityVulnerabilities() {
  console.log('\n🛡️ Security Vulnerability Test');
  console.log('=' .repeat(50));

  const vulnerabilities = [];

  // Test 1: Check for plain text API keys in environment
  console.log('\n🔍 Checking for plain text API keys...');
  const dangerousEnvVars = [
    'LIFI_API_KEY',
    'ONEINCH_API_KEY', 
    'CHANGENOW_API_KEY',
    'PARASWAP_API_KEY',
    'ZEROX_API_KEY'
  ];

  dangerousEnvVars.forEach(envVar => {
    if (process.env[envVar] && process.env.USE_ENCRYPTED_KEYS === 'true') {
      vulnerabilities.push(`Plain text ${envVar} found in environment while using encrypted keys`);
      console.log(`⚠️ ${envVar}: Found in environment (should be removed)`);
    } else if (process.env[envVar]) {
      console.log(`ℹ️ ${envVar}: Found (fallback mode)`);
    } else {
      console.log(`✅ ${envVar}: Not in environment (secure)`);
    }
  });

  // Test 2: Check master password security
  console.log('\n🔑 Checking master password security...');
  if (!process.env.MASTER_PASSWORD) {
    vulnerabilities.push('No master password configured');
    console.log('❌ MASTER_PASSWORD: Not configured');
  } else if (process.env.MASTER_PASSWORD.length < 32) {
    vulnerabilities.push('Master password too short (< 32 characters)');
    console.log(`⚠️ MASTER_PASSWORD: Too short (${process.env.MASTER_PASSWORD.length} chars)`);
  } else {
    console.log(`✅ MASTER_PASSWORD: Configured (${process.env.MASTER_PASSWORD.length} chars)`);
  }

  // Test 3: Check file permissions
  console.log('\n📁 Checking file permissions...');
  try {
    const fs = await import('fs');
    const path = await import('path');
    
    const keyStorePath = path.join(process.cwd(), 'secure', 'encrypted-keys.json');
    if (fs.existsSync(keyStorePath)) {
      const stats = fs.statSync(keyStorePath);
      const permissions = (stats.mode & parseInt('777', 8)).toString(8);
      
      if (permissions === '600') {
        console.log('✅ Key store file permissions: Secure (600)');
      } else {
        vulnerabilities.push(`Insecure key store permissions: ${permissions}`);
        console.log(`⚠️ Key store file permissions: ${permissions} (should be 600)`);
      }
    } else {
      console.log('ℹ️ Key store file: Not found (no keys stored yet)');
    }
  } catch (error) {
    console.log(`⚠️ Could not check file permissions: ${error.message}`);
  }

  // Test 4: Check encryption configuration
  console.log('\n🔐 Checking encryption configuration...');
  if (process.env.USE_ENCRYPTED_KEYS === 'true') {
    console.log('✅ USE_ENCRYPTED_KEYS: Enabled');
  } else {
    vulnerabilities.push('Encrypted keys not enabled');
    console.log('⚠️ USE_ENCRYPTED_KEYS: Not enabled');
  }

  // Summary
  console.log('\n📊 Security Vulnerability Summary');
  console.log('=' .repeat(40));
  
  if (vulnerabilities.length === 0) {
    console.log('🎉 No security vulnerabilities found!');
    console.log('✅ Your API encryption system is secure');
    return true;
  } else {
    console.log(`❌ Found ${vulnerabilities.length} security issues:`);
    vulnerabilities.forEach((vuln, index) => {
      console.log(`   ${index + 1}. ${vuln}`);
    });
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive API Encryption Tests\n');
  console.log('=' .repeat(60));

  const results = {
    encryption: false,
    providers: false,
    security: false
  };

  // Run all test suites
  results.encryption = await testEncryptionSecurity();
  results.providers = await testProviderSecurity();
  results.security = await testSecurityVulnerabilities();

  // Final summary
  console.log('\n' + '=' .repeat(60));
  console.log('🏆 FINAL TEST RESULTS');
  console.log('=' .repeat(60));

  console.log(`🔐 Encryption Tests: ${results.encryption ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🌊 Provider Tests: ${results.providers ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🛡️ Security Tests: ${results.security ? '✅ PASSED' : '❌ FAILED'}`);

  const allPassed = results.encryption && results.providers && results.security;
  
  if (allPassed) {
    console.log('\n🎉 ALL TESTS PASSED - YOUR API ENCRYPTION IS SECURE!');
    console.log('\n✅ Your Marko Polo Capital exchange has enterprise-grade API security');
    console.log('✅ All API keys are encrypted with military-grade AES-256-GCM');
    console.log('✅ No security vulnerabilities detected');
    console.log('✅ Ready for production deployment');
  } else {
    console.log('\n⚠️ SOME TESTS FAILED - REVIEW SECURITY CONFIGURATION');
    console.log('\n📋 Next Steps:');
    if (!results.encryption) console.log('   1. Run: node setup-secure-api-keys.js setup');
    if (!results.providers) console.log('   2. Configure missing API keys');
    if (!results.security) console.log('   3. Fix security vulnerabilities listed above');
  }

  console.log('\n💡 For help: node setup-secure-api-keys.js --help');
}

// Execute tests
runAllTests().catch(console.error);
