import dotenv from 'dotenv';
dotenv.config();

console.log('🧪 Testing LiFi SDK Integration');

// Test LiFi SDK import
try {
  const lifiModule = await import('@lifi/sdk');
  console.log('✅ LiFi SDK imported successfully');
  console.log('   Available exports:', Object.keys(lifiModule));

  // Try different import patterns
  const LiFi = lifiModule.LiFi || lifiModule.default || lifiModule;

  if (typeof LiFi !== 'function') {
    throw new Error('LiFi constructor not found in module exports');
  }

  // Test LiFi initialization
  const lifi = new LiFi({
    apiKey: process.env.LIFI_API_KEY,
    integrator: 'marko-polo-capital'
  });
  
  console.log('✅ LiFi SDK initialized');
  console.log(`   API Key: ${process.env.LIFI_API_KEY ? 'Configured' : 'Missing'}`);
  
  // Test getting chains
  try {
    const chains = await lifi.getChains();
    console.log(`✅ LiFi chains loaded: ${chains.length} chains available`);
    
    // Show some popular chains
    const popularChains = chains.filter(chain => 
      ['Ethereum', 'Polygon', 'BSC', 'Arbitrum', 'Optimism'].includes(chain.name)
    );
    
    console.log('   Popular chains:');
    popularChains.forEach(chain => {
      console.log(`     ${chain.name} (ID: ${chain.id})`);
    });
    
  } catch (error) {
    console.log('❌ Failed to get chains:', error.message);
  }
  
  // Test getting tokens
  try {
    const tokens = await lifi.getTokens({ chains: [1, 56] }); // Ethereum and BSC
    console.log(`✅ LiFi tokens loaded: ${Object.keys(tokens).length} chain(s) with tokens`);
    
    if (tokens[1]) {
      console.log(`   Ethereum tokens: ${tokens[1].length}`);
    }
    if (tokens[56]) {
      console.log(`   BSC tokens: ${tokens[56].length}`);
    }
    
  } catch (error) {
    console.log('❌ Failed to get tokens:', error.message);
  }
  
  // Test getting a simple quote
  try {
    console.log('\n🔍 Testing LiFi quote...');
    
    const routeRequest = {
      fromChainId: 1, // Ethereum
      toChainId: 56,  // BSC
      fromTokenAddress: '******************************************', // ETH
      toTokenAddress: '******************************************',   // BNB
      fromAmount: '10000000000000000', // 0.01 ETH
      fromAddress: '******************************************',
      toAddress: '******************************************',
      options: {
        slippage: 0.005, // 0.5%
        allowSwitchChain: true
      }
    };
    
    const routes = await lifi.getRoutes(routeRequest);
    
    if (routes && routes.length > 0) {
      const bestRoute = routes[0];
      const inputAmount = 0.01;
      const outputAmount = parseFloat(bestRoute.toAmount) / 1e18;
      const rate = outputAmount / inputAmount;
      
      console.log('✅ LiFi quote successful!');
      console.log(`   Input: ${inputAmount} ETH`);
      console.log(`   Output: ${outputAmount.toFixed(4)} BNB`);
      console.log(`   Rate: ${rate.toFixed(2)} BNB per ETH`);
      console.log(`   Steps: ${bestRoute.steps.length}`);
      console.log(`   Estimated time: ${Math.round(bestRoute.steps.reduce((total, step) => total + (step.estimate.executionDuration || 30000), 0) / 1000)}s`);
      
      // Show route details
      console.log('   Route:');
      bestRoute.steps.forEach((step, index) => {
        console.log(`     ${index + 1}. ${step.tool} (${step.action.fromChainId} → ${step.action.toChainId})`);
      });
      
    } else {
      console.log('⚠️ No routes available for this pair');
    }
    
  } catch (error) {
    console.log('❌ Failed to get quote:', error.message);
  }
  
} catch (error) {
  console.log('❌ Failed to import LiFi SDK:', error.message);
  console.log('   Make sure @lifi/sdk is installed: npm install @lifi/sdk @lifi/types');
}

console.log('\n🎉 LiFi SDK test completed!');
