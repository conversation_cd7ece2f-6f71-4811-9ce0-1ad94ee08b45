import{r as e,R as t,a as n,b as r,c as o}from"./vendor-DH5OV8M2.js";var i={exports:{}},a={},s=e,c=Symbol.for("react.element"),l=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(e,t,n){var r,o={},i=null,a=null;for(r in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(a=t.ref),t)u.call(t,r)&&!f.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:c,type:e,key:i,ref:a,props:o,_owner:d.current}}a.Fragment=l,a.jsx=p,a.jsxs=p,i.exports=a;var m=i.exports;function h(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function v(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function g(...e){return t=>{let n=!1;const r=e.map((e=>{const r=v(e,t);return n||"function"!=typeof r||(n=!0),r}));if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():v(e[t],null)}}}}function y(...t){return e.useCallback(g(...t),t)}function w(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=r?.[t]||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;r=[...r,o];const s=n=>{const{scope:r,children:o,...s}=n,c=r?.[t]?.[a]||i,l=e.useMemo((()=>s),Object.values(s));return m.jsx(c.Provider,{value:l,children:o})};return s.displayName=n+"Provider",[s,function(r,s){const c=s?.[t]?.[a]||i,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},b(o,...n)]}function b(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function x(t){const n=S(t),r=e.forwardRef(((t,r)=>{const{children:o,...i}=t,a=e.Children.toArray(o),s=a.find(P);if(s){const t=s.props.children,o=a.map((n=>n===s?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return m.jsx(n,{...i,ref:r,children:e.isValidElement(t)?e.cloneElement(t,void 0,o):null})}return m.jsx(n,{...i,ref:r,children:o})}));return r.displayName=`${t}.Slot`,r}var E=x("Slot");function S(t){const n=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?g(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null}));return n.displayName=`${t}.SlotClone`,n}var C=Symbol("radix.slottable");function R(e){const t=({children:e})=>m.jsx(m.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=C,t}function P(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===C}function T(e){const n=e+"CollectionProvider",[r,o]=w(n),[i,a]=r(n,{collectionRef:{current:null},itemMap:new Map}),s=e=>{const{scope:n,children:r}=e,o=t.useRef(null),a=t.useRef(new Map).current;return m.jsx(i,{scope:n,itemMap:a,collectionRef:o,children:r})};s.displayName=n;const c=e+"CollectionSlot",l=x(c),u=t.forwardRef(((e,t)=>{const{scope:n,children:r}=e,o=y(t,a(c,n).collectionRef);return m.jsx(l,{ref:o,children:r})}));u.displayName=c;const d=e+"CollectionItemSlot",f="data-radix-collection-item",p=x(d),h=t.forwardRef(((e,n)=>{const{scope:r,children:o,...i}=e,s=t.useRef(null),c=y(n,s),l=a(d,r);return t.useEffect((()=>(l.itemMap.set(s,{ref:s,...i}),()=>{l.itemMap.delete(s)}))),m.jsx(p,{[f]:"",ref:c,children:o})}));return h.displayName=d,[{Provider:s,Slot:u,ItemSlot:h},function(n){const r=a(e+"CollectionConsumer",n);return t.useCallback((()=>{const e=r.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[r.collectionRef,r.itemMap])},o]}var D=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((t,n)=>{const r=x(`Primitive.${n}`),o=e.forwardRef(((e,t)=>{const{asChild:o,...i}=e,a=o?r:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),m.jsx(a,{...i,ref:t})}));return o.displayName=`Primitive.${n}`,{...t,[n]:o}}),{});function _(e,t){e&&n.flushSync((()=>e.dispatchEvent(t)))}function A(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>n.current?.(...e)),[])}var N,O="dismissableLayer.update",j="dismissableLayer.pointerDownOutside",I="dismissableLayer.focusOutside",k=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),L=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:s,onDismiss:c,...l}=t,u=e.useContext(k),[d,f]=e.useState(null),p=d?.ownerDocument??globalThis?.document,[,v]=e.useState({}),g=y(n,(e=>f(e))),w=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=w.indexOf(b),E=d?w.indexOf(d):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=E>=x,R=function(t,n=globalThis?.document){const r=A(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){W(j,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...u.branches].some((e=>e.contains(t)));C&&!n&&(i?.(e),s?.(e),e.defaultPrevented||c?.())}),p),P=function(t,n=globalThis?.document){const r=A(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){W(I,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...u.branches].some((e=>e.contains(t)))||(a?.(e),s?.(e),e.defaultPrevented||c?.())}),p);return function(t,n=globalThis?.document){const r=A(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{E===u.layers.size-1&&(o?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))}),p),e.useEffect((()=>{if(d)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(N=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),F(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=N)}}),[d,p,r,u]),e.useEffect((()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),F())}),[d,u]),e.useEffect((()=>{const e=()=>v({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)}),[]),m.jsx(D.div,{...l,ref:g,style:{pointerEvents:S?C?"auto":"none":void 0,...t.style},onFocusCapture:h(t.onFocusCapture,P.onFocusCapture),onBlurCapture:h(t.onBlurCapture,P.onBlurCapture),onPointerDownCapture:h(t.onPointerDownCapture,R.onPointerDownCapture)})}));L.displayName="DismissableLayer";var M=e.forwardRef(((t,n)=>{const r=e.useContext(k),o=e.useRef(null),i=y(n,o);return e.useEffect((()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),m.jsx(D.div,{...t,ref:i})}));function F(){const e=new CustomEvent(O);document.dispatchEvent(e)}function W(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?_(o,i):o.dispatchEvent(i)}M.displayName="DismissableLayerBranch";var B=L,H=M,V=globalThis?.document?e.useLayoutEffect:()=>{},$=e.forwardRef(((t,n)=>{const{container:o,...i}=t,[a,s]=e.useState(!1);V((()=>s(!0)),[]);const c=o||a&&globalThis?.document?.body;return c?r.createPortal(m.jsx(D.div,{...i,ref:n}),c):null}));$.displayName="Portal";var K=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),a=e.useRef("none"),s=t?"mounted":"unmounted",[c,l]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=z(o.current);a.current="mounted"===c?e:"none"}),[c]),V((()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=z(e);if(t)l("MOUNT");else if("none"===o||"none"===e?.display)l("UNMOUNT");else{l(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}}),[t,l]),V((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=z(o.current).includes(r.animationName);if(r.target===n&&a&&(l("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},s=e=>{e.target===n&&(a.current=z(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}l("ANIMATION_END")}),[n,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback((e=>{o.current=e?getComputedStyle(e):null,r(e)}),[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=y(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function z(e){return e?.animationName||"none"}K.displayName="Presence";var U=o[" useInsertionEffect ".trim().toString()]||V;function Y({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,a,s]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),a=e.useRef(n);return U((()=>{a.current=n}),[n]),e.useEffect((()=>{i.current!==r&&(a.current?.(r),i.current=r)}),[r,i]),[r,o,a]}({defaultProp:n,onChange:r}),c=void 0!==t,l=c?t:i;{const n=e.useRef(void 0!==t);e.useEffect((()=>{const e=n.current;if(e!==c){}n.current=c}),[c,o])}const u=e.useCallback((e=>{if(c){const n=function(e){return"function"==typeof e}(e)?e(t):e;n!==t&&s.current?.(n)}else a(e)}),[c,t,a,s]);return[l,u]}var X=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),q=e.forwardRef(((e,t)=>m.jsx(D.span,{...e,ref:t,style:{...X,...e.style}})));q.displayName="VisuallyHidden";var G=q,Z=e.createContext(void 0);function J(t){const n=e.useContext(Z);return t||n||"ltr"}var Q=0;function ee(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??te()),document.body.insertAdjacentElement("beforeend",e[1]??te()),Q++,()=>{1===Q&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),Q--}}),[])}function te(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ne="focusScope.autoFocusOnMount",re="focusScope.autoFocusOnUnmount",oe={bubbles:!1,cancelable:!0},ie=e.forwardRef(((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...s}=t,[c,l]=e.useState(null),u=A(i),d=A(a),f=e.useRef(null),p=y(n,(e=>l(e))),h=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(o){let e=function(e){if(h.paused||!c)return;const t=e.target;c.contains(t)?f.current=t:le(f.current,{select:!0})},t=function(e){if(h.paused||!c)return;const t=e.relatedTarget;null!==t&&(c.contains(t)||le(f.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&le(c)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,c,h.paused]),e.useEffect((()=>{if(c){ue.add(h);const t=document.activeElement;if(!c.contains(t)){const n=new CustomEvent(ne,oe);c.addEventListener(ne,u),c.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(le(r,{select:t}),document.activeElement!==n)return}((e=ae(c),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&le(c))}return()=>{c.removeEventListener(ne,u),setTimeout((()=>{const e=new CustomEvent(re,oe);c.addEventListener(re,d),c.dispatchEvent(e),e.defaultPrevented||le(t??document.body,{select:!0}),c.removeEventListener(re,d),ue.remove(h)}),0)}}var e}),[c,u,d,h]);const v=e.useCallback((e=>{if(!r&&!o)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,i]=function(e){const t=ae(e),n=se(t,e),r=se(t.reverse(),e);return[n,r]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&le(i,{select:!0})):(e.preventDefault(),r&&le(o,{select:!0})):n===t&&e.preventDefault()}}),[r,o,h.paused]);return m.jsx(D.div,{tabIndex:-1,...s,ref:p,onKeyDown:v})}));function ae(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function se(e,t){for(const n of e)if(!ce(n,{upTo:t}))return n}function ce(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function le(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}ie.displayName="FocusScope";var ue=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=de(e,t),e.unshift(t)},remove(t){e=de(e,t),e[0]?.resume()}}}();function de(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var fe=o[" useId ".trim().toString()]||(()=>{}),pe=0;function me(t){const[n,r]=e.useState(fe());return V((()=>{r((e=>e??String(pe++)))}),[t]),n?`radix-${n}`:""}const he=["top","right","bottom","left"],ve=Math.min,ge=Math.max,ye=Math.round,we=Math.floor,be=e=>({x:e,y:e}),xe={left:"right",right:"left",bottom:"top",top:"bottom"},Ee={start:"end",end:"start"};function Se(e,t,n){return ge(e,ve(t,n))}function Ce(e,t){return"function"==typeof e?e(t):e}function Re(e){return e.split("-")[0]}function Pe(e){return e.split("-")[1]}function Te(e){return"x"===e?"y":"x"}function De(e){return"y"===e?"height":"width"}function _e(e){return["top","bottom"].includes(Re(e))?"y":"x"}function Ae(e){return Te(_e(e))}function Ne(e){return e.replace(/start|end/g,(e=>Ee[e]))}function Oe(e){return e.replace(/left|right|bottom|top/g,(e=>xe[e]))}function je(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function Ie(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ke(e,t,n){let{reference:r,floating:o}=e;const i=_e(t),a=Ae(t),s=De(a),c=Re(t),l="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(c){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Pe(t)){case"start":p[a]-=f*(n&&l?-1:1);break;case"end":p[a]+=f*(n&&l?-1:1)}return p}async function Le(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Ce(t,e),m=je(p),h=s[f?"floating"===d?"reference":"floating":d],v=Ie(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:l,rootBoundary:u,strategy:c})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=Ie(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:y,strategy:c}):g);return{top:(v.top-b.top+m.top)/w.y,bottom:(b.bottom-v.bottom+m.bottom)/w.y,left:(v.left-b.left+m.left)/w.x,right:(b.right-v.right+m.right)/w.x}}function Me(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Fe(e){return he.some((t=>e[t]>=0))}function We(){return"undefined"!=typeof window}function Be(e){return $e(e)?(e.nodeName||"").toLowerCase():"#document"}function He(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Ve(e){var t;return null==(t=($e(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function $e(e){return!!We()&&(e instanceof Node||e instanceof He(e).Node)}function Ke(e){return!!We()&&(e instanceof Element||e instanceof He(e).Element)}function ze(e){return!!We()&&(e instanceof HTMLElement||e instanceof He(e).HTMLElement)}function Ue(e){return!(!We()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof He(e).ShadowRoot)}function Ye(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Qe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Xe(e){return["table","td","th"].includes(Be(e))}function qe(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function Ge(e){const t=Ze(),n=Ke(e)?Qe(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Ze(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Je(e){return["html","body","#document"].includes(Be(e))}function Qe(e){return He(e).getComputedStyle(e)}function et(e){return Ke(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function tt(e){if("html"===Be(e))return e;const t=e.assignedSlot||e.parentNode||Ue(e)&&e.host||Ve(e);return Ue(t)?t.host:t}function nt(e){const t=tt(e);return Je(t)?e.ownerDocument?e.ownerDocument.body:e.body:ze(t)&&Ye(t)?t:nt(t)}function rt(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=nt(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=He(o);if(i){const e=ot(a);return t.concat(a,a.visualViewport||[],Ye(o)?o:[],e&&n?rt(e):[])}return t.concat(o,rt(o,[],n))}function ot(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function it(e){const t=Qe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ze(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=ye(n)!==i||ye(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function at(e){return Ke(e)?e:e.contextElement}function st(e){const t=at(e);if(!ze(t))return be(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=it(t);let a=(i?ye(n.width):n.width)/r,s=(i?ye(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const ct=be(0);function lt(e){const t=He(e);return Ze()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ct}function ut(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=at(e);let a=be(1);t&&(r?Ke(r)&&(a=st(r)):a=st(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==He(e))&&t}(i,n,r)?lt(i):be(0);let c=(o.left+s.x)/a.x,l=(o.top+s.y)/a.y,u=o.width/a.x,d=o.height/a.y;if(i){const e=He(i),t=r&&Ke(r)?He(r):r;let n=e,o=ot(n);for(;o&&r&&t!==n;){const e=st(o),t=o.getBoundingClientRect(),r=Qe(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=i,l+=a,n=He(o),o=ot(n)}}return Ie({width:u,height:d,x:c,y:l})}function dt(e,t){const n=et(e).scrollLeft;return t?t.left+n:ut(Ve(e)).left+n}function ft(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:dt(e,r)),y:r.top+t.scrollTop}}function pt(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=He(e),r=Ve(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,c=0;if(o){i=o.width,a=o.height;const e=Ze();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:s,y:c}}(e,n);else if("document"===t)r=function(e){const t=Ve(e),n=et(e),r=e.ownerDocument.body,o=ge(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=ge(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+dt(e);const s=-n.scrollTop;return"rtl"===Qe(r).direction&&(a+=ge(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(Ve(e));else if(Ke(t))r=function(e,t){const n=ut(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ze(e)?st(e):be(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=lt(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Ie(r)}function mt(e,t){const n=tt(e);return!(n===t||!Ke(n)||Je(n))&&("fixed"===Qe(n).position||mt(n,t))}function ht(e,t,n){const r=ze(t),o=Ve(t),i="fixed"===n,a=ut(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const c=be(0);function l(){c.x=dt(o)}if(r||!r&&!i)if(("body"!==Be(t)||Ye(o))&&(s=et(t)),r){const e=ut(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&l();i&&!r&&o&&l();const u=!o||r||i?be(0):ft(o,s);return{x:a.left+s.scrollLeft-c.x-u.x,y:a.top+s.scrollTop-c.y-u.y,width:a.width,height:a.height}}function vt(e){return"static"===Qe(e).position}function gt(e,t){if(!ze(e)||"fixed"===Qe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Ve(e)===n&&(n=n.ownerDocument.body),n}function yt(e,t){const n=He(e);if(qe(e))return n;if(!ze(e)){let t=tt(e);for(;t&&!Je(t);){if(Ke(t)&&!vt(t))return t;t=tt(t)}return n}let r=gt(e,t);for(;r&&Xe(r)&&vt(r);)r=gt(r,t);return r&&Je(r)&&vt(r)&&!Ge(r)?n:r||function(e){let t=tt(e);for(;ze(t)&&!Je(t);){if(Ge(t))return t;if(qe(t))return null;t=tt(t)}return null}(e)||n}const wt={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=Ve(r),s=!!t&&qe(t.floating);if(r===a||s&&i)return n;let c={scrollLeft:0,scrollTop:0},l=be(1);const u=be(0),d=ze(r);if((d||!d&&!i)&&(("body"!==Be(r)||Ye(a))&&(c=et(r)),ze(r))){const e=ut(r);l=st(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const f=!a||d||i?be(0):ft(a,c,!0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x+f.x,y:n.y*l.y-c.scrollTop*l.y+u.y+f.y}},getDocumentElement:Ve,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?qe(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=rt(e,[],!1).filter((e=>Ke(e)&&"body"!==Be(e))),o=null;const i="fixed"===Qe(e).position;let a=i?tt(e):e;for(;Ke(a)&&!Je(a);){const t=Qe(a),n=Ge(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Ye(a)&&!n&&mt(e,a))?r=r.filter((e=>e!==a)):o=t,a=tt(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce(((e,n)=>{const r=pt(t,n,o);return e.top=ge(r.top,e.top),e.right=ve(r.right,e.right),e.bottom=ve(r.bottom,e.bottom),e.left=ge(r.left,e.left),e}),pt(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:yt,getElementRects:async function(e){const t=this.getOffsetParent||yt,n=this.getDimensions,r=await n(e.floating);return{reference:ht(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=it(e);return{width:t,height:n}},getScale:st,isElement:Ke,isRTL:function(e){return"rtl"===Qe(e).direction}};function bt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function xt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=at(e),u=o||i?[...l?rt(l):[],...rt(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=l&&s?function(e,t){let n,r=null;const o=Ve(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),i();const l=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=l;if(s||t(),!f||!p)return;const m={rootMargin:-we(d)+"px "+-we(o.clientWidth-(u+f))+"px "+-we(o.clientHeight-(d+p))+"px "+-we(u)+"px",threshold:ge(0,ve(1,c))||1};let h=!0;function v(t){const r=t[0].intersectionRatio;if(r!==c){if(!h)return a();r?a(!1,r):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==r||bt(l,e.getBoundingClientRect())||a(),h=!1}try{r=new IntersectionObserver(v,{...m,root:o.ownerDocument})}catch(g){r=new IntersectionObserver(v,m)}r.observe(e)}(!0),i}(l,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===l&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),l&&!c&&m.observe(l),m.observe(t));let h=c?ut(e):null;return c&&function t(){const r=ut(e);h&&!bt(h,r)&&n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(f)}}const Et=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,c=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=Re(n),s=Pe(n),c="y"===_e(n),l=["left","top"].includes(a)?-1:1,u=i&&c?-1:1,d=Ce(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}},St=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=Ce(e,t),l={x:n,y:r},u=await Le(t,c),d=_e(Re(o)),f=Te(d);let p=l[f],m=l[d];if(i){const e="y"===f?"bottom":"right";p=Se(p+u["y"===f?"top":"left"],p,p-u[e])}if(a){const e="y"===d?"bottom":"right";m=Se(m+u["y"===d?"top":"left"],m,m-u[e])}const h=s.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:a}}}}}},Ct=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:c,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=Ce(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=Re(o),y=_e(s),w=Re(s)===s,b=await(null==c.isRTL?void 0:c.isRTL(l.floating)),x=f||(w||!h?[Oe(s)]:function(e){const t=Oe(e);return[Ne(e),t,Ne(t)]}(s)),E="none"!==m;!f&&E&&x.push(...function(e,t,n,r){const o=Pe(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(Re(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(Ne)))),i}(s,h,m,b));const S=[s,...x],C=await Le(t,v),R=[];let P=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&R.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=Pe(e),o=Ae(e),i=De(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Oe(a)),[a,Oe(a)]}(o,a,b);R.push(C[e[0]],C[e[1]])}if(P=[...P,{placement:o,overflows:R}],!R.every((e=>e<=0))){var T,D;const e=((null==(T=i.flip)?void 0:T.index)||0)+1,t=S[e];if(t){if(!("alignment"===d&&y!==_e(t))||P.every((e=>e.overflows[0]>0&&_e(e.placement)===y)))return{data:{index:e,overflows:P},reset:{placement:t}}}let n=null==(D=P.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:D.placement;if(!n)switch(p){case"bestFit":{var _;const e=null==(_=P.filter((e=>{if(E){const t=_e(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:_[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},Rt=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:s}=t,{apply:c=()=>{},...l}=Ce(e,t),u=await Le(t,l),d=Re(o),f=Pe(o),p="y"===_e(o),{width:m,height:h}=i.floating;let v,g;"top"===d||"bottom"===d?(v=d,g=f===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(g=d,v="end"===f?"top":"bottom");const y=h-u.top-u.bottom,w=m-u.left-u.right,b=ve(h-u[v],y),x=ve(m-u[g],w),E=!t.middlewareData.shift;let S=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!f){const e=ge(u.left,0),t=ge(u.right,0),n=ge(u.top,0),r=ge(u.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:ge(u.left,u.right)):S=h-2*(0!==n||0!==r?n+r:ge(u.top,u.bottom))}await c({...t,availableWidth:C,availableHeight:S});const R=await a.getDimensions(s.floating);return m!==R.width||h!==R.height?{reset:{rects:!0}}:{}}}},Pt=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Ce(e,t);switch(r){case"referenceHidden":{const e=Me(await Le(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Fe(e)}}}case"escaped":{const e=Me(await Le(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:Fe(e)}}}default:return{}}}}},Tt=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:a,elements:s,middlewareData:c}=t,{element:l,padding:u=0}=Ce(e,t)||{};if(null==l)return{};const d=je(u),f={x:n,y:r},p=Ae(o),m=De(p),h=await a.getDimensions(l),v="y"===p,g=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],x=f[p]-i.reference[p],E=await(null==a.getOffsetParent?void 0:a.getOffsetParent(l));let S=E?E[w]:0;S&&await(null==a.isElement?void 0:a.isElement(E))||(S=s.floating[w]||i.floating[m]);const C=b/2-x/2,R=S/2-h[m]/2-1,P=ve(d[g],R),T=ve(d[y],R),D=P,_=S-h[m]-T,A=S/2-h[m]/2+C,N=Se(D,A,_),O=!c.arrow&&null!=Pe(o)&&A!==N&&i.reference[m]/2-(A<D?P:T)-h[m]/2<0,j=O?A<D?A-D:A-_:0;return{[p]:f[p]+j,data:{[p]:N,centerOffset:A-N-j,...O&&{alignmentOffset:j}},reset:O}}}),Dt=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=Ce(e,t),u={x:n,y:r},d=_e(o),f=Te(d);let p=u[f],m=u[d];const h=Ce(s,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(Re(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},_t=(e,t,n)=>{const r=new Map,o={platform:wt,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),c=await(null==a.isRTL?void 0:a.isRTL(t));let l=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=ke(l,r,c),f=r,p={},m=0;for(let h=0;h<s.length;h++){const{name:n,fn:i}=s[h],{x:v,y:g,data:y,reset:w}=await i({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:l,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(l=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:d}=ke(l,f,c))),h=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var At="undefined"!=typeof document?e.useLayoutEffect:function(){};function Nt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Nt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Nt(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function Ot(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function jt(e,t){const n=Ot(e);return Math.round(t*n)/n}function It(t){const n=e.useRef(t);return At((()=>{n.current=t})),n}const kt=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Tt({element:n.current,padding:r}).fn(t):{}:n?Tt({element:n,padding:r}).fn(t):{};var o}}),Lt=(e,t)=>({...Et(e),options:[e,t]}),Mt=(e,t)=>({...St(e),options:[e,t]}),Ft=(e,t)=>({...Dt(e),options:[e,t]}),Wt=(e,t)=>({...Ct(e),options:[e,t]}),Bt=(e,t)=>({...Rt(e),options:[e,t]}),Ht=(e,t)=>({...Pt(e),options:[e,t]}),Vt=(e,t)=>({...kt(e),options:[e,t]});var $t=e.forwardRef(((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return m.jsx(D.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})}));$t.displayName="Arrow";var Kt=$t;function zt(t){const[n,r]=e.useState(void 0);return V((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}var Ut="Popper",[Yt,Xt]=w(Ut),[qt,Gt]=Yt(Ut),Zt=t=>{const{__scopePopper:n,children:r}=t,[o,i]=e.useState(null);return m.jsx(qt,{scope:n,anchor:o,onAnchorChange:i,children:r})};Zt.displayName=Ut;var Jt="PopperAnchor",Qt=e.forwardRef(((t,n)=>{const{__scopePopper:r,virtualRef:o,...i}=t,a=Gt(Jt,r),s=e.useRef(null),c=y(n,s);return e.useEffect((()=>{a.onAnchorChange(o?.current||s.current)})),o?null:m.jsx(D.div,{...i,ref:c})}));Qt.displayName=Jt;var en="PopperContent",[tn,nn]=Yt(en),rn=e.forwardRef(((t,r)=>{const{__scopePopper:o,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:c=0,arrowPadding:l=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:v="optimized",onPlaced:g,...w}=t,b=Gt(en,o),[x,E]=e.useState(null),S=y(r,(e=>E(e))),[C,R]=e.useState(null),P=zt(C),T=P?.width??0,_=P?.height??0,N=i+("center"!==s?"-"+s:""),O="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},j=Array.isArray(d)?d:[d],I=j.length>0,k={padding:O,boundary:j.filter(cn),altBoundary:I},{refs:L,floatingStyles:M,placement:F,isPositioned:W,middlewareData:B}=function(t){void 0===t&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:s,floating:c}={},transform:l=!0,whileElementsMounted:u,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);Nt(m,i)||h(i);const[v,g]=e.useState(null),[y,w]=e.useState(null),b=e.useCallback((e=>{e!==C.current&&(C.current=e,g(e))}),[]),x=e.useCallback((e=>{e!==R.current&&(R.current=e,w(e))}),[]),E=s||v,S=c||y,C=e.useRef(null),R=e.useRef(null),P=e.useRef(f),T=null!=u,D=It(u),_=It(a),A=It(d),N=e.useCallback((()=>{if(!C.current||!R.current)return;const e={placement:r,strategy:o,middleware:m};_.current&&(e.platform=_.current),_t(C.current,R.current,e).then((e=>{const t={...e,isPositioned:!1!==A.current};O.current&&!Nt(P.current,t)&&(P.current=t,n.flushSync((()=>{p(t)})))}))}),[m,r,o,_,A]);At((()=>{!1===d&&P.current.isPositioned&&(P.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const O=e.useRef(!1);At((()=>(O.current=!0,()=>{O.current=!1})),[]),At((()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(D.current)return D.current(E,S,N);N()}}),[E,S,N,D,T]);const j=e.useMemo((()=>({reference:C,floating:R,setReference:b,setFloating:x})),[b,x]),I=e.useMemo((()=>({reference:E,floating:S})),[E,S]),k=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!I.floating)return e;const t=jt(I.floating,f.x),n=jt(I.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...Ot(I.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,l,I.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:N,refs:j,elements:I,floatingStyles:k})),[f,N,j,I,k])}({strategy:"fixed",placement:N,whileElementsMounted:(...e)=>xt(...e,{animationFrame:"always"===v}),elements:{reference:b.anchor},middleware:[Lt({mainAxis:a+_,alignmentAxis:c}),u&&Mt({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?Ft():void 0,...k}),u&&Wt({...k}),Bt({...k,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&Vt({element:C,padding:l}),ln({arrowWidth:T,arrowHeight:_}),h&&Ht({strategy:"referenceHidden",...k})]}),[H,$]=un(F),K=A(g);V((()=>{W&&K?.()}),[W,K]);const z=B.arrow?.x,U=B.arrow?.y,Y=0!==B.arrow?.centerOffset,[X,q]=e.useState();return V((()=>{x&&q(window.getComputedStyle(x).zIndex)}),[x]),m.jsx("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:W?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:m.jsx(tn,{scope:o,placedSide:H,onArrowChange:R,arrowX:z,arrowY:U,shouldHideArrow:Y,children:m.jsx(D.div,{"data-side":H,"data-align":$,...w,ref:S,style:{...w.style,animation:W?void 0:"none"}})})})}));rn.displayName=en;var on="PopperArrow",an={top:"bottom",right:"left",bottom:"top",left:"right"},sn=e.forwardRef((function(e,t){const{__scopePopper:n,...r}=e,o=nn(on,n),i=an[o.placedSide];return m.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:m.jsx(Kt,{...r,ref:t,style:{...r.style,display:"block"}})})}));function cn(e){return null!==e}sn.displayName=on;var ln=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=0!==o.arrow?.centerOffset,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[c,l]=un(n),u={start:"0%",center:"50%",end:"100%"}[l],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+s/2;let p="",m="";return"bottom"===c?(p=i?u:`${d}px`,m=-s+"px"):"top"===c?(p=i?u:`${d}px`,m=`${r.floating.height+s}px`):"right"===c?(p=-s+"px",m=i?u:`${f}px`):"left"===c&&(p=`${r.floating.width+s}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function un(e){const[t,n="center"]=e.split("-");return[t,n]}var dn=Zt,fn=Qt,pn=rn,mn=sn,hn="rovingFocusGroup.onEntryFocus",vn={bubbles:!1,cancelable:!0},gn="RovingFocusGroup",[yn,wn,bn]=T(gn),[xn,En]=w(gn,[bn]),[Sn,Cn]=xn(gn),Rn=e.forwardRef(((e,t)=>m.jsx(yn.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(yn.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx(Pn,{...e,ref:t})})})));Rn.displayName=gn;var Pn=e.forwardRef(((t,n)=>{const{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:a,currentTabStopId:s,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...f}=t,p=e.useRef(null),v=y(n,p),g=J(a),[w,b]=Y({prop:s,defaultProp:c??null,onChange:l,caller:gn}),[x,E]=e.useState(!1),S=A(u),C=wn(r),R=e.useRef(!1),[P,T]=e.useState(0);return e.useEffect((()=>{const e=p.current;if(e)return e.addEventListener(hn,S),()=>e.removeEventListener(hn,S)}),[S]),m.jsx(Sn,{scope:r,orientation:o,dir:g,loop:i,currentTabStopId:w,onItemFocus:e.useCallback((e=>b(e)),[b]),onItemShiftTab:e.useCallback((()=>E(!0)),[]),onFocusableItemAdd:e.useCallback((()=>T((e=>e+1))),[]),onFocusableItemRemove:e.useCallback((()=>T((e=>e-1))),[]),children:m.jsx(D.div,{tabIndex:x||0===P?-1:0,"data-orientation":o,...f,ref:v,style:{outline:"none",...t.style},onMouseDown:h(t.onMouseDown,(()=>{R.current=!0})),onFocus:h(t.onFocus,(e=>{const t=!R.current;if(e.target===e.currentTarget&&t&&!x){const t=new CustomEvent(hn,vn);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=C().filter((e=>e.focusable));An([e.find((e=>e.active)),e.find((e=>e.id===w)),...e].filter(Boolean).map((e=>e.ref.current)),d)}}R.current=!1})),onBlur:h(t.onBlur,(()=>E(!1)))})})})),Tn="RovingFocusGroupItem",Dn=e.forwardRef(((t,n)=>{const{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:a,children:s,...c}=t,l=me(),u=a||l,d=Cn(Tn,r),f=d.currentTabStopId===u,p=wn(r),{onFocusableItemAdd:v,onFocusableItemRemove:g,currentTabStopId:y}=d;return e.useEffect((()=>{if(o)return v(),()=>g()}),[o,v,g]),m.jsx(yn.ItemSlot,{scope:r,id:u,focusable:o,active:i,children:m.jsx(D.span,{tabIndex:f?0:-1,"data-orientation":d.orientation,...c,ref:n,onMouseDown:h(t.onMouseDown,(e=>{o?d.onItemFocus(u):e.preventDefault()})),onFocus:h(t.onFocus,(()=>d.onItemFocus(u))),onKeyDown:h(t.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void d.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:_n[r]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=p().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const i=o.indexOf(e.currentTarget);o=d.loop?(r=i+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(i+1)}setTimeout((()=>An(o)))}var n,r})),children:"function"==typeof s?s({isCurrentTabStop:f,hasTabStop:null!=y}):s})})}));Dn.displayName=Tn;var _n={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function An(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var Nn=Rn,On=Dn,jn=new WeakMap,In=new WeakMap,kn={},Ln=0,Mn=function(e){return e&&(e.host||Mn(e.parentNode))},Fn=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=Mn(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);kn[n]||(kn[n]=new WeakMap);var i=kn[n],a=[],s=new Set,c=new Set(o),l=function(e){e&&!s.has(e)&&(s.add(e),l(e.parentNode))};o.forEach(l);var u=function(e){e&&!c.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(jn.get(e)||0)+1,l=(i.get(e)||0)+1;jn.set(e,c),i.set(e,l),a.push(e),1===c&&o&&In.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}}))};return u(t),s.clear(),Ln++,function(){a.forEach((function(e){var t=jn.get(e)-1,o=i.get(e)-1;jn.set(e,t),i.set(e,o),t||(In.has(e)||e.removeAttribute(r),In.delete(e)),o||e.removeAttribute(n)})),--Ln||(jn=new WeakMap,jn=new WeakMap,In=new WeakMap,kn={})}},Wn=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Fn(r,o,n,"aria-hidden")):function(){return null}},Bn=function(){return Bn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Bn.apply(this,arguments)};function Hn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var Vn="right-scroll-bar-position",$n="width-before-scroll-bar";function Kn(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var zn="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,Un=new WeakMap;function Yn(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach((function(t){return Kn(t,e)}))},(i=e.useState((function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}}))[0]).callback=o,i.facade);return zn((function(){var e=Un.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach((function(e){r.has(e)||Kn(e,null)})),r.forEach((function(e){n.has(e)||Kn(e,o)}))}Un.set(a,t)}),[t]),a}function Xn(e){return e}var qn=function(t){var n=t.sideCar,r=Hn(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,Bn({},r))};qn.isSideCarExport=!0;var Gn=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=Xn);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=Bn({async:!0,ssr:!1},e),t}(),Zn=function(){},Jn=e.forwardRef((function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:Zn,onWheelCapture:Zn,onTouchMoveCapture:Zn}),i=o[0],a=o[1],s=t.forwardProps,c=t.children,l=t.className,u=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noRelative,h=t.noIsolation,v=t.inert,g=t.allowPinchZoom,y=t.as,w=void 0===y?"div":y,b=t.gapMode,x=Hn(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=p,S=Yn([r,n]),C=Bn(Bn({},x),i);return e.createElement(e.Fragment,null,d&&e.createElement(E,{sideCar:Gn,removeScrollBar:u,shards:f,noRelative:m,noIsolation:h,inert:v,setCallbacks:a,allowPinchZoom:!!g,lockRef:r,gapMode:b}),s?e.cloneElement(e.Children.only(c),Bn(Bn({},C),{ref:S})):e.createElement(w,Bn({},C,{className:l,ref:S}),c))}));Jn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Jn.classNames={fullWidth:$n,zeroRight:Vn};function Qn(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var er=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=Qn())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tr=function(){var t,n=(t=er(),function(n,r){e.useEffect((function(){return t.add(n),function(){t.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},nr={left:0,top:0,right:0,gap:0},rr=function(e){return parseInt(e||"",10)||0},or=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return nr;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[rr(n),rr(r),rr(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ir=tr(),ar="data-scroll-locked",sr=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(ar,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(Vn," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat($n," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(Vn," .").concat(Vn," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat($n," .").concat($n," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ar,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},cr=function(){var e=parseInt(document.body.getAttribute(ar)||"0",10);return isFinite(e)?e:0},lr=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect((function(){return document.body.setAttribute(ar,(cr()+1).toString()),function(){var e=cr()-1;e<=0?document.body.removeAttribute(ar):document.body.setAttribute(ar,e.toString())}}),[]);var a=e.useMemo((function(){return or(i)}),[i]);return e.createElement(ir,{styles:sr(a,!n,i,r?"":"!important")})},ur=!1;if("undefined"!=typeof window)try{var dr=Object.defineProperty({},"passive",{get:function(){return ur=!0,!0}});window.addEventListener("test",dr,dr),window.removeEventListener("test",dr,dr)}catch(Qi){ur=!1}var fr=!!ur&&{passive:!1},pr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},mr=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),hr(e,r)){var o=vr(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},hr=function(e,t){return"v"===e?function(e){return pr(e,"overflowY")}(t):function(e){return pr(e,"overflowX")}(t)},vr=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},gr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},yr=function(e){return[e.deltaX,e.deltaY]},wr=function(e){return e&&"current"in e?e.current:e},br=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},xr=0,Er=[];function Sr(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Cr=(Rr=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(xr++)[0],a=e.useState(tr)[0],s=e.useRef(t);e.useEffect((function(){s.current=t}),[t]),e.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(wr),!0).filter(Boolean);return e.forEach((function(e){return e.classList.add("allow-interactivity-".concat(i))})),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(i))}))}}}),[t.inert,t.lockRef.current,t.shards]);var c=e.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var n,i=gr(e),a=r.current,c="deltaX"in e?e.deltaX:a[0]-i[0],l="deltaY"in e?e.deltaY:a[1]-i[1],u=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=mr(d,u);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=mr(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(c||l)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,s=t.contains(a),c=!1,l=i>0,u=0,d=0;do{if(!a)break;var f=vr(e,a),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&hr(e,a)&&(u+=m,d+=p);var h=a.parentNode;a=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!s&&a!==document.body||s&&(t.contains(a)||t===a));return(l&&Math.abs(u)<1||!l&&Math.abs(d)<1)&&(c=!0),c}(p,t,e,"h"===p?c:l)}),[]),l=e.useCallback((function(e){var t=e;if(Er.length&&Er[Er.length-1]===a){var r="deltaY"in t?yr(t):gr(t),o=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o}))[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(s.current.shards||[]).map(wr).filter(Boolean).filter((function(e){return e.contains(t.target)}));(i.length>0?c(t,i[0]):!s.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),u=e.useCallback((function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:Sr(r)};n.current.push(i),setTimeout((function(){n.current=n.current.filter((function(e){return e!==i}))}),1)}),[]),d=e.useCallback((function(e){r.current=gr(e),o.current=void 0}),[]),f=e.useCallback((function(e){u(e.type,yr(e),e.target,c(e,t.lockRef.current))}),[]),p=e.useCallback((function(e){u(e.type,gr(e),e.target,c(e,t.lockRef.current))}),[]);e.useEffect((function(){return Er.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,fr),document.addEventListener("touchmove",l,fr),document.addEventListener("touchstart",d,fr),function(){Er=Er.filter((function(e){return e!==a})),document.removeEventListener("wheel",l,fr),document.removeEventListener("touchmove",l,fr),document.removeEventListener("touchstart",d,fr)}}),[]);var m=t.removeScrollBar,h=t.inert;return e.createElement(e.Fragment,null,h?e.createElement(a,{styles:br(i)}):null,m?e.createElement(lr,{noRelative:t.noRelative,gapMode:t.gapMode}):null)},Gn.useMedium(Rr),qn);var Rr,Pr=e.forwardRef((function(t,n){return e.createElement(Jn,Bn({},t,{ref:n,sideCar:Cr}))}));Pr.classNames=Jn.classNames;var Tr="Dialog",[Dr,_r]=w(Tr),[Ar,Nr]=Dr(Tr),Or=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=t,c=e.useRef(null),l=e.useRef(null),[u,d]=Y({prop:o,defaultProp:i??!1,onChange:a,caller:Tr});return m.jsx(Ar,{scope:n,triggerRef:c,contentRef:l,contentId:me(),titleId:me(),descriptionId:me(),open:u,onOpenChange:d,onOpenToggle:e.useCallback((()=>d((e=>!e))),[d]),modal:s,children:r})};Or.displayName=Tr;var jr="DialogTrigger",Ir=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nr(jr,n),i=y(t,o.triggerRef);return m.jsx(D.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eo(o.open),...r,ref:i,onClick:h(e.onClick,o.onOpenToggle)})}));Ir.displayName=jr;var kr="DialogPortal",[Lr,Mr]=Dr(kr,{forceMount:void 0}),Fr=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:i}=t,a=Nr(kr,n);return m.jsx(Lr,{scope:n,forceMount:r,children:e.Children.map(o,(e=>m.jsx(K,{present:r||a.open,children:m.jsx($,{asChild:!0,container:i,children:e})})))})};Fr.displayName=kr;var Wr="DialogOverlay",Br=e.forwardRef(((e,t)=>{const n=Mr(Wr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Nr(Wr,e.__scopeDialog);return i.modal?m.jsx(K,{present:r||i.open,children:m.jsx(Vr,{...o,ref:t})}):null}));Br.displayName=Wr;var Hr=x("DialogOverlay.RemoveScroll"),Vr=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nr(Wr,n);return m.jsx(Pr,{as:Hr,allowPinchZoom:!0,shards:[o.contentRef],children:m.jsx(D.div,{"data-state":eo(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})})),$r="DialogContent",Kr=e.forwardRef(((e,t)=>{const n=Mr($r,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Nr($r,e.__scopeDialog);return m.jsx(K,{present:r||i.open,children:i.modal?m.jsx(zr,{...o,ref:t}):m.jsx(Ur,{...o,ref:t})})}));Kr.displayName=$r;var zr=e.forwardRef(((t,n)=>{const r=Nr($r,t.__scopeDialog),o=e.useRef(null),i=y(n,r.contentRef,o);return e.useEffect((()=>{const e=o.current;if(e)return Wn(e)}),[]),m.jsx(Yr,{...t,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:h(t.onCloseAutoFocus,(e=>{e.preventDefault(),r.triggerRef.current?.focus()})),onPointerDownOutside:h(t.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:h(t.onFocusOutside,(e=>e.preventDefault()))})})),Ur=e.forwardRef(((t,n)=>{const r=Nr($r,t.__scopeDialog),o=e.useRef(!1),i=e.useRef(!1);return m.jsx(Yr,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(o.current||r.triggerRef.current?.focus(),e.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));const n=e.target,a=r.triggerRef.current?.contains(n);a&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})})),Yr=e.forwardRef(((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=t,c=Nr($r,r),l=e.useRef(null),u=y(n,l);return ee(),m.jsxs(m.Fragment,{children:[m.jsx(ie,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:m.jsx(L,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":eo(c.open),...s,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(oo,{titleId:c.titleId}),m.jsx(io,{contentRef:l,descriptionId:c.descriptionId})]})]})})),Xr="DialogTitle",qr=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nr(Xr,n);return m.jsx(D.h2,{id:o.titleId,...r,ref:t})}));qr.displayName=Xr;var Gr="DialogDescription",Zr=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nr(Gr,n);return m.jsx(D.p,{id:o.descriptionId,...r,ref:t})}));Zr.displayName=Gr;var Jr="DialogClose",Qr=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nr(Jr,n);return m.jsx(D.button,{type:"button",...r,ref:t,onClick:h(e.onClick,(()=>o.onOpenChange(!1)))})}));function eo(e){return e?"open":"closed"}Qr.displayName=Jr;var to="DialogTitleWarning",[no,ro]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,i=e.useMemo((()=>o),Object.values(o));return m.jsx(r.Provider,{value:i,children:n})};return o.displayName=t+"Provider",[o,function(o){const i=e.useContext(r);if(i)return i;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(to,{contentName:$r,titleName:Xr,docsSlug:"dialog"}),oo=({titleId:t})=>{const n=ro(to),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect((()=>{if(t){document.getElementById(t)}}),[r,t]),null},io=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ro("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{const e=t.current?.getAttribute("aria-describedby");if(n&&e){document.getElementById(n)}}),[r,t,n]),null},ao=Or,so=Ir,co=Fr,lo=Br,uo=Kr,fo=qr,po=Zr,mo=Qr;function ho(e,[t,n]){return Math.min(n,Math.max(t,e))}function vo(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}var go=[" ","Enter","ArrowUp","ArrowDown"],yo=[" ","Enter"],wo="Select",[bo,xo,Eo]=T(wo),[So,Co]=w(wo,[Eo,Xt]),Ro=Xt(),[Po,To]=So(wo),[Do,_o]=So(wo),Ao=t=>{const{__scopeSelect:n,children:r,open:o,defaultOpen:i,onOpenChange:a,value:s,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:f,disabled:p,required:h,form:v}=t,g=Ro(n),[y,w]=e.useState(null),[b,x]=e.useState(null),[E,S]=e.useState(!1),C=J(u),[R,P]=Y({prop:o,defaultProp:i??!1,onChange:a,caller:wo}),[T,D]=Y({prop:s,defaultProp:c,onChange:l,caller:wo}),_=e.useRef(null),A=!y||(v||!!y.closest("form")),[N,O]=e.useState(new Set),j=Array.from(N).map((e=>e.props.value)).join(";");return m.jsx(dn,{...g,children:m.jsxs(Po,{required:h,scope:n,trigger:y,onTriggerChange:w,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:E,onValueNodeHasChildrenChange:S,contentId:me(),value:T,onValueChange:D,open:R,onOpenChange:P,dir:C,triggerPointerDownPosRef:_,disabled:p,children:[m.jsx(bo.Provider,{scope:n,children:m.jsx(Do,{scope:t.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{O((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{O((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:r})}),A?m.jsxs(vi,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:T,onChange:e=>D(e.target.value),disabled:p,form:v,children:[void 0===T?m.jsx("option",{value:""}):null,Array.from(N)]},j):null]})})};Ao.displayName=wo;var No="SelectTrigger",Oo=e.forwardRef(((t,n)=>{const{__scopeSelect:r,disabled:o=!1,...i}=t,a=Ro(r),s=To(No,r),c=s.disabled||o,l=y(n,s.onTriggerChange),u=xo(r),d=e.useRef("touch"),[f,p,v]=yi((e=>{const t=u().filter((e=>!e.disabled)),n=t.find((e=>e.value===s.value)),r=wi(t,e,n);void 0!==r&&s.onValueChange(r.value)})),g=e=>{c||(s.onOpenChange(!0),v()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return m.jsx(fn,{asChild:!0,...a,children:m.jsx(D.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":gi(s.value)?"":void 0,...i,ref:l,onClick:h(i.onClick,(e=>{e.currentTarget.focus(),"mouse"!==d.current&&g(e)})),onPointerDown:h(i.onPointerDown,(e=>{d.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())})),onKeyDown:h(i.onKeyDown,(e=>{const t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),t&&" "===e.key||go.includes(e.key)&&(g(),e.preventDefault())}))})})}));Oo.displayName=No;var jo="SelectValue",Io=e.forwardRef(((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...s}=e,c=To(jo,n),{onValueNodeHasChildrenChange:l}=c,u=void 0!==i,d=y(t,c.onValueNodeChange);return V((()=>{l(u)}),[l,u]),m.jsx(D.span,{...s,ref:d,style:{pointerEvents:"none"},children:gi(c.value)?m.jsx(m.Fragment,{children:a}):i})}));Io.displayName=jo;var ko=e.forwardRef(((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return m.jsx(D.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})}));ko.displayName="SelectIcon";var Lo=e=>m.jsx($,{asChild:!0,...e});Lo.displayName="SelectPortal";var Mo="SelectContent",Fo=e.forwardRef(((t,r)=>{const o=To(Mo,t.__scopeSelect),[i,a]=e.useState();if(V((()=>{a(new DocumentFragment)}),[]),!o.open){const e=i;return e?n.createPortal(m.jsx(Bo,{scope:t.__scopeSelect,children:m.jsx(bo.Slot,{scope:t.__scopeSelect,children:m.jsx("div",{children:t.children})})}),e):null}return m.jsx($o,{...t,ref:r})}));Fo.displayName=Mo;var Wo=10,[Bo,Ho]=So(Mo),Vo=x("SelectContent.RemoveScroll"),$o=e.forwardRef(((t,n)=>{const{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:v,sticky:g,hideWhenDetached:w,avoidCollisions:b,...x}=t,E=To(Mo,r),[S,C]=e.useState(null),[R,P]=e.useState(null),T=y(n,(e=>C(e))),[D,_]=e.useState(null),[A,N]=e.useState(null),O=xo(r),[j,I]=e.useState(!1),k=e.useRef(!1);e.useEffect((()=>{if(S)return Wn(S)}),[S]),ee();const M=e.useCallback((e=>{const[t,...n]=O().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const i of e){if(i===o)return;if(i?.scrollIntoView({block:"nearest"}),i===t&&R&&(R.scrollTop=0),i===r&&R&&(R.scrollTop=R.scrollHeight),i?.focus(),document.activeElement!==o)return}}),[O,R]),F=e.useCallback((()=>M([D,S])),[M,D,S]);e.useEffect((()=>{j&&F()}),[j,F]);const{onOpenChange:W,triggerPointerDownPosRef:B}=E;e.useEffect((()=>{if(S){let e={x:0,y:0};const t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[S,W,B]),e.useEffect((()=>{const e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[W]);const[H,V]=yi((e=>{const t=O().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=wi(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),$=e.useCallback(((e,t,n)=>{const r=!k.current&&!n;(void 0!==E.value&&E.value===t||r)&&(_(e),r&&(k.current=!0))}),[E.value]),K=e.useCallback((()=>S?.focus()),[S]),z=e.useCallback(((e,t,n)=>{const r=!k.current&&!n;(void 0!==E.value&&E.value===t||r)&&N(e)}),[E.value]),U="popper"===o?zo:Ko,Y=U===zo?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:v,sticky:g,hideWhenDetached:w,avoidCollisions:b}:{};return m.jsx(Bo,{scope:r,content:S,viewport:R,onViewportChange:P,itemRefCallback:$,selectedItem:D,onItemLeave:K,itemTextRefCallback:z,focusSelectedItem:F,selectedItemText:A,position:o,isPositioned:j,searchRef:H,children:m.jsx(Pr,{as:Vo,allowPinchZoom:!0,children:m.jsx(ie,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:h(i,(e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()})),children:m.jsx(L,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:m.jsx(U,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...x,...Y,onPlaced:()=>I(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:h(x.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>M(t))),e.preventDefault()}}))})})})})})}));$o.displayName="SelectContentImpl";var Ko=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onPlaced:o,...i}=t,a=To(Mo,r),s=Ho(Mo,r),[c,l]=e.useState(null),[u,d]=e.useState(null),f=y(n,(e=>d(e))),p=xo(r),h=e.useRef(!1),v=e.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=s,E=e.useCallback((()=>{if(a.trigger&&a.valueNode&&c&&u&&g&&w&&b){const e=a.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){const o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,l=Math.max(s,t.width),u=window.innerWidth-Wo,d=ho(i,[Wo,Math.max(Wo,u-l)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{const o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,l=Math.max(s,t.width),u=window.innerWidth-Wo,d=ho(i,[Wo,Math.max(Wo,u-l)]);c.style.minWidth=s+"px",c.style.right=d+"px"}const i=p(),s=window.innerHeight-2*Wo,l=g.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),y=f+m+l+parseInt(d.paddingBottom,10)+v,x=Math.min(5*w.offsetHeight,y),E=window.getComputedStyle(g),S=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),R=e.top+e.height/2-Wo,P=s-R,T=w.offsetHeight/2,D=f+m+(w.offsetTop+T),_=y-D;if(D<=R){const e=i.length>0&&w===i[i.length-1].ref.current;c.style.bottom="0px";const t=u.clientHeight-g.offsetTop-g.offsetHeight,n=D+Math.max(P,T+(e?C:0)+t+v);c.style.height=n+"px"}else{const e=i.length>0&&w===i[0].ref.current;c.style.top="0px";const t=Math.max(R,f+g.offsetTop+(e?S:0)+T)+_;c.style.height=t+"px",g.scrollTop=D-R+g.offsetTop}c.style.margin=`${Wo}px 0`,c.style.minHeight=x+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame((()=>h.current=!0))}}),[p,a.trigger,a.valueNode,c,u,g,w,b,a.dir,o]);V((()=>E()),[E]);const[S,C]=e.useState();V((()=>{u&&C(window.getComputedStyle(u).zIndex)}),[u]);const R=e.useCallback((e=>{e&&!0===v.current&&(E(),x?.(),v.current=!1)}),[E,x]);return m.jsx(Uo,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:R,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:m.jsx(D.div,{...i,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})}));Ko.displayName="SelectItemAlignedPosition";var zo=e.forwardRef(((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=Wo,...i}=e,a=Ro(n);return m.jsx(pn,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));zo.displayName="SelectPopperPosition";var[Uo,Yo]=So(Mo,{}),Xo="SelectViewport",qo=e.forwardRef(((t,n)=>{const{__scopeSelect:r,nonce:o,...i}=t,a=Ho(Xo,r),s=Yo(Xo,r),c=y(n,a.onViewportChange),l=e.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(bo.Slot,{scope:r,children:m.jsx(D.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:h(i.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){const e=Math.abs(l.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*Wo,o=parseFloat(n.style.minHeight),i=parseFloat(n.style.height),a=Math.max(o,i);if(a<r){const o=a+e,i=Math.min(r,o),s=o-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}l.current=t.scrollTop}))})})]})}));qo.displayName=Xo;var Go="SelectGroup",[Zo,Jo]=So(Go);e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=me();return m.jsx(Zo,{scope:n,id:o,children:m.jsx(D.div,{role:"group","aria-labelledby":o,...r,ref:t})})})).displayName=Go;var Qo="SelectLabel",ei=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=Jo(Qo,n);return m.jsx(D.div,{id:o.id,...r,ref:t})}));ei.displayName=Qo;var ti="SelectItem",[ni,ri]=So(ti),oi=e.forwardRef(((t,n)=>{const{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...s}=t,c=To(ti,r),l=Ho(ti,r),u=c.value===o,[d,f]=e.useState(a??""),[p,v]=e.useState(!1),g=y(n,(e=>l.itemRefCallback?.(e,o,i))),w=me(),b=e.useRef("touch"),x=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(ni,{scope:r,value:o,disabled:i,textId:w,isSelected:u,onItemTextChange:e.useCallback((e=>{f((t=>t||(e?.textContent??"").trim()))}),[]),children:m.jsx(bo.ItemSlot,{scope:r,value:o,disabled:i,textValue:d,children:m.jsx(D.div,{role:"option","aria-labelledby":w,"data-highlighted":p?"":void 0,"aria-selected":u&&p,"data-state":u?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:g,onFocus:h(s.onFocus,(()=>v(!0))),onBlur:h(s.onBlur,(()=>v(!1))),onClick:h(s.onClick,(()=>{"mouse"!==b.current&&x()})),onPointerUp:h(s.onPointerUp,(()=>{"mouse"===b.current&&x()})),onPointerDown:h(s.onPointerDown,(e=>{b.current=e.pointerType})),onPointerMove:h(s.onPointerMove,(e=>{b.current=e.pointerType,i?l.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:h(s.onPointerLeave,(e=>{e.currentTarget===document.activeElement&&l.onItemLeave?.()})),onKeyDown:h(s.onKeyDown,(e=>{""!==l.searchRef?.current&&" "===e.key||(yo.includes(e.key)&&x()," "===e.key&&e.preventDefault())}))})})})}));oi.displayName=ti;var ii="SelectItemText",ai=e.forwardRef(((t,r)=>{const{__scopeSelect:o,className:i,style:a,...s}=t,c=To(ii,o),l=Ho(ii,o),u=ri(ii,o),d=_o(ii,o),[f,p]=e.useState(null),h=y(r,(e=>p(e)),u.onItemTextChange,(e=>l.itemTextRefCallback?.(e,u.value,u.disabled))),v=f?.textContent,g=e.useMemo((()=>m.jsx("option",{value:u.value,disabled:u.disabled,children:v},u.value)),[u.disabled,u.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=d;return V((()=>(w(g),()=>b(g))),[w,b,g]),m.jsxs(m.Fragment,{children:[m.jsx(D.span,{id:u.textId,...s,ref:h}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?n.createPortal(s.children,c.valueNode):null]})}));ai.displayName=ii;var si="SelectItemIndicator",ci=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return ri(si,n).isSelected?m.jsx(D.span,{"aria-hidden":!0,...r,ref:t}):null}));ci.displayName=si;var li="SelectScrollUpButton",ui=e.forwardRef(((t,n)=>{const r=Ho(li,t.__scopeSelect),o=Yo(li,t.__scopeSelect),[i,a]=e.useState(!1),s=y(n,o.onScrollButtonChange);return V((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollTop>0;a(e)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),i?m.jsx(pi,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));ui.displayName=li;var di="SelectScrollDownButton",fi=e.forwardRef(((t,n)=>{const r=Ho(di,t.__scopeSelect),o=Yo(di,t.__scopeSelect),[i,a]=e.useState(!1),s=y(n,o.onScrollButtonChange);return V((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;a(n)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),i?m.jsx(pi,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));fi.displayName=di;var pi=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onAutoScroll:o,...i}=t,a=Ho("SelectScrollButton",r),s=e.useRef(null),c=xo(r),l=e.useCallback((()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)}),[]);return e.useEffect((()=>()=>l()),[l]),V((()=>{const e=c().find((e=>e.ref.current===document.activeElement));e?.ref.current?.scrollIntoView({block:"nearest"})}),[c]),m.jsx(D.div,{"aria-hidden":!0,...i,ref:n,style:{flexShrink:0,...i.style},onPointerDown:h(i.onPointerDown,(()=>{null===s.current&&(s.current=window.setInterval(o,50))})),onPointerMove:h(i.onPointerMove,(()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))})),onPointerLeave:h(i.onPointerLeave,(()=>{l()}))})})),mi=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return m.jsx(D.div,{"aria-hidden":!0,...r,ref:t})}));mi.displayName="SelectSeparator";var hi="SelectArrow";e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=Ro(n),i=To(hi,n),a=Ho(hi,n);return i.open&&"popper"===a.position?m.jsx(mn,{...o,...r,ref:t}):null})).displayName=hi;var vi=e.forwardRef((({__scopeSelect:t,value:n,...r},o)=>{const i=e.useRef(null),a=y(o,i),s=vo(n);return e.useEffect((()=>{const e=i.current;if(!e)return;const t=window.HTMLSelectElement.prototype,r=Object.getOwnPropertyDescriptor(t,"value").set;if(s!==n&&r){const t=new Event("change",{bubbles:!0});r.call(e,n),e.dispatchEvent(t)}}),[s,n]),m.jsx(D.select,{...r,style:{...X,...r.style},ref:a,defaultValue:n})}));function gi(e){return""===e||void 0===e}function yi(t){const n=A(t),r=e.useRef(""),o=e.useRef(0),i=e.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),a=e.useCallback((()=>{r.current="",window.clearTimeout(o.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(o.current)),[]),[r,i,a]}function wi(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let i=(a=e,s=Math.max(o,0),a.map(((e,t)=>a[(s+t)%a.length])));var a,s;1===r.length&&(i=i.filter((e=>e!==n)));const c=i.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return c!==n?c:void 0}vi.displayName="SelectBubbleInput";var bi=Ao,xi=Oo,Ei=Io,Si=ko,Ci=Lo,Ri=Fo,Pi=qo,Ti=ei,Di=oi,_i=ai,Ai=ci,Ni=ui,Oi=fi,ji=mi,Ii="Tabs",[ki,Li]=w(Ii,[En]),Mi=En(),[Fi,Wi]=ki(Ii),Bi=e.forwardRef(((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:s,activationMode:c="automatic",...l}=e,u=J(s),[d,f]=Y({prop:r,onChange:o,defaultProp:i??"",caller:Ii});return m.jsx(Fi,{scope:n,baseId:me(),value:d,onValueChange:f,orientation:a,dir:u,activationMode:c,children:m.jsx(D.div,{dir:u,"data-orientation":a,...l,ref:t})})}));Bi.displayName=Ii;var Hi="TabsList",Vi=e.forwardRef(((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,i=Wi(Hi,n),a=Mi(n);return m.jsx(Nn,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:m.jsx(D.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})}));Vi.displayName=Hi;var $i="TabsTrigger",Ki=e.forwardRef(((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,a=Wi($i,n),s=Mi(n),c=Yi(a.baseId,r),l=Xi(a.baseId,r),u=r===a.value;return m.jsx(On,{asChild:!0,...s,focusable:!o,active:u,children:m.jsx(D.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":l,"data-state":u?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:h(e.onMouseDown,(e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(r)})),onKeyDown:h(e.onKeyDown,(e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(r)})),onFocus:h(e.onFocus,(()=>{const e="manual"!==a.activationMode;u||o||!e||a.onValueChange(r)}))})})}));Ki.displayName=$i;var zi="TabsContent",Ui=e.forwardRef(((t,n)=>{const{__scopeTabs:r,value:o,forceMount:i,children:a,...s}=t,c=Wi(zi,r),l=Yi(c.baseId,o),u=Xi(c.baseId,o),d=o===c.value,f=e.useRef(d);return e.useEffect((()=>{const e=requestAnimationFrame((()=>f.current=!1));return()=>cancelAnimationFrame(e)}),[]),m.jsx(K,{present:i||d,children:({present:e})=>m.jsx(D.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!e,id:u,tabIndex:0,...s,ref:n,style:{...t.style,animationDuration:f.current?"0s":void 0},children:e&&a})})}));function Yi(e,t){return`${e}-trigger-${t}`}function Xi(e,t){return`${e}-content-${t}`}Ui.displayName=zi;var qi=Bi,Gi=Vi,Zi=Ki,Ji=Ui;export{bi as $,fn as A,H as B,pn as C,L as D,po as E,ie as F,ao as G,so as H,On as I,xi as J,Si as K,Ni as L,Oi as M,Ci as N,lo as O,D as P,Ri as Q,B as R,E as S,fo as T,Pi as U,q as V,Ti as W,Di as X,Ai as Y,_i as Z,ji as _,T as a,Ei as a0,Gi as a1,Zi as a2,Ji as a3,qi as a4,vo as a5,zt as a6,ho as a7,R as a8,G as a9,_r as aa,no as ab,Y as b,w as c,K as d,h as e,A as f,$ as g,V as h,_ as i,m as j,Xt as k,En as l,g as m,ee as n,Pr as o,x as p,Nn as q,Wn as r,J as s,dn as t,y as u,mn as v,me as w,co as x,uo as y,mo as z};
