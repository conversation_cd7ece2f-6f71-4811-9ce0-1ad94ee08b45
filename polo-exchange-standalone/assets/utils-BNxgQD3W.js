import{j as t}from"./ui-C8FPhe3o.js";import{r as e}from"./vendor-DH5OV8M2.js";function n(t){var e,i,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(e=0;e<o;e++)t[e]&&(i=n(t[e]))&&(s&&(s+=" "),s+=i)}else for(i in t)t[i]&&(s&&(s+=" "),s+=i);return s}function i(){for(var t,e,i=0,s="",o=arguments.length;i<o;i++)(t=arguments[i])&&(e=n(t))&&(s&&(s+=" "),s+=e);return s}const s=e.createContext({});function o(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const r=e.createContext(null),a=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class l extends e.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function u({children:n,isPresent:i}){const s=e.useId(),o=e.useRef(null),r=e.useRef({width:0,height:0,top:0,left:0}),{nonce:u}=e.useContext(a);return e.useInsertionEffect((()=>{const{width:t,height:e,top:n,left:a}=r.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=s;const l=document.createElement("style");return u&&(l.nonce=u),document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`\n          [data-motion-pop-id="${s}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${n}px !important;\n            left: ${a}px !important;\n          }\n        `),()=>{document.head.removeChild(l)}}),[i]),t.jsx(l,{isPresent:i,childRef:o,sizeRef:r,children:e.cloneElement(n,{ref:o})})}const c=({children:n,initial:i,isPresent:s,onExitComplete:a,custom:l,presenceAffectsLayout:c,mode:d})=>{const p=o(h),m=e.useId(),f=e.useCallback((t=>{p.set(t,!0);for(const e of p.values())if(!e)return;a&&a()}),[p,a]),g=e.useMemo((()=>({id:m,initial:i,isPresent:s,custom:l,onExitComplete:f,register:t=>(p.set(t,!1),()=>p.delete(t))})),c?[Math.random(),f]:[s,f]);return e.useMemo((()=>{p.forEach(((t,e)=>p.set(e,!1)))}),[s]),e.useEffect((()=>{!s&&!p.size&&a&&a()}),[s]),"popLayout"===d&&(n=t.jsx(u,{isPresent:s,children:n})),t.jsx(r.Provider,{value:g,children:n})};function h(){return new Map}function d(t=!0){const n=e.useContext(r);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,a=e.useId();e.useEffect((()=>{t&&o(a)}),[t]);const l=e.useCallback((()=>t&&s&&s(a)),[a,s,t]);return!i&&s?[!1,l]:[!0]}const p=t=>t.key||"";function m(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const f="undefined"!=typeof window,g=f?e.useLayoutEffect:e.useEffect,y=({children:n,custom:i,initial:r=!0,onExitComplete:a,presenceAffectsLayout:l=!0,mode:u="sync",propagate:h=!1})=>{const[f,y]=d(h),v=e.useMemo((()=>m(n)),[n]),x=h&&!f?[]:v.map(p),w=e.useRef(!0),b=e.useRef(v),T=o((()=>new Map)),[S,P]=e.useState(v),[E,A]=e.useState(v);g((()=>{w.current=!1,b.current=v;for(let t=0;t<E.length;t++){const e=p(E[t]);x.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}}),[E,x.length,x.join("-")]);const R=[];if(v!==S){let t=[...v];for(let e=0;e<E.length;e++){const n=E[e],i=p(n);x.includes(i)||(t.splice(e,0,n),R.push(n))}return"wait"===u&&R.length&&(t=R),A(m(t)),void P(v)}const{forceRender:C}=e.useContext(s);return t.jsx(t.Fragment,{children:E.map((e=>{const n=p(e),s=!(h&&!f)&&(v===E||x.includes(n));return t.jsx(c,{isPresent:s,initial:!(w.current&&!r)&&void 0,custom:s?void 0:i,presenceAffectsLayout:l,mode:u,onExitComplete:s?void 0:()=>{if(!T.has(n))return;T.set(n,!0);let t=!0;T.forEach((e=>{e||(t=!1)})),t&&(null==C||C(),A(b.current),h&&(null==y||y()),a&&a())},children:e},n)}))})},v=t=>t;let x=v;function w(t){let e;return()=>(void 0===e&&(e=t()),e)}const b=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},T=t=>1e3*t,S=t=>t/1e3,P=!1;const E=["read","resolveKeyframes","update","preRender","render","postRender"];function A(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=E.reduce(((t,e)=>(t[e]=function(t){let e=new Set,n=new Set,i=!1,s=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(e){o.has(e)&&(l.schedule(e),t()),e(r)}const l={schedule:(t,s=!1,r=!1)=>{const a=r&&i?e:n;return s&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{r=t,i?s=!0:(i=!0,[e,n]=[n,e],e.forEach(a),e.clear(),i=!1,s&&(s=!1,l.process(t)))}};return l}(o),t)),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=r,p=()=>{const o=performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(p))};return{schedule:E.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(p)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<E.length;e++)r[E[e]].cancel(t)},state:s,steps:r}}const{schedule:R,cancel:C,state:D,steps:O}=A("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:v,!0),M=e.createContext({strict:!1}),j={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},V={};for(const Pu in j)V[Pu]={isEnabled:t=>j[Pu].some((e=>!!t[e]))};const k=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function L(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||k.has(t)}let F=t=>!L(t);try{(B=require("@emotion/is-prop-valid").default)&&(F=t=>t.startsWith("on")?!L(t):B(t))}catch(Su){}var B;function U(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const N=e.createContext({});function I(t){return"string"==typeof t||Array.isArray(t)}function _(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const W=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],z=["initial",...W];function $(t){return _(t.animate)||z.some((e=>I(t[e])))}function q(t){return Boolean($(t)||t.variants)}function H(t){const{initial:n,animate:i}=function(t,e){if($(t)){const{initial:e,animate:n}=t;return{initial:!1===e||I(e)?e:void 0,animate:I(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(N));return e.useMemo((()=>({initial:n,animate:i})),[K(n),K(i)])}function K(t){return Array.isArray(t)?t.join(" "):t}const X=Symbol.for("motionComponentSymbol");function Y(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function G(t,n,i){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):Y(i)&&(i.current=e))}),[n])}const J=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Z="data-"+J("framerAppearId"),{schedule:Q}=A(queueMicrotask,!1),tt=e.createContext({});function et(t,n,i,s,o){var l,u;const{visualElement:c}=e.useContext(N),h=e.useContext(M),d=e.useContext(r),p=e.useContext(a).reducedMotion,m=e.useRef(null);s=s||h.renderer,!m.current&&s&&(m.current=s(t,{visualState:n,parent:c,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:p}));const f=m.current,y=e.useContext(tt);!f||f.projection||!o||"html"!==f.type&&"svg"!==f.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:nt(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Y(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(m.current,i,o,y);const v=e.useRef(!1);e.useInsertionEffect((()=>{f&&v.current&&f.update(i,d)}));const x=i[Z],w=e.useRef(Boolean(x)&&!(null===(l=window.MotionHandoffIsComplete)||void 0===l?void 0:l.call(window,x))&&(null===(u=window.MotionHasOptimisedAnimation)||void 0===u?void 0:u.call(window,x)));return g((()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Q.render(f.render),w.current&&f.animationState&&f.animationState.animateChanges())})),e.useEffect((()=>{f&&(!w.current&&f.animationState&&f.animationState.animateChanges(),w.current&&(queueMicrotask((()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,x)})),w.current=!1))})),f}function nt(t){if(t)return!1!==t.options.allowProjection?t.projection:nt(t.parent)}function it({preloadedFeatures:n,createVisualElement:i,useRender:s,useVisualState:o,Component:r}){var l,u;function c(n,l){let u;const c={...e.useContext(a),...n,layoutId:st(n)},{isStatic:h}=c,d=H(n),p=o(n,h);if(!h&&f){e.useContext(M).strict;const t=function(t){const{drag:e,layout:n}=V;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(c);u=t.MeasureLayout,d.visualElement=et(r,p,c,i,t.ProjectionNode)}return t.jsxs(N.Provider,{value:d,children:[u&&d.visualElement?t.jsx(u,{visualElement:d.visualElement,...c}):null,s(r,n,G(p,d.visualElement,l),p,h,d.visualElement)]})}n&&function(t){for(const e in t)V[e]={...V[e],...t[e]}}(n),c.displayName=`motion.${"string"==typeof r?r:`create(${null!==(u=null!==(l=r.displayName)&&void 0!==l?l:r.name)&&void 0!==u?u:""})`}`;const h=e.forwardRef(c);return h[X]=r,h}function st({layoutId:t}){const n=e.useContext(s).id;return n&&void 0!==t?n+"-"+t:t}const ot=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rt(t){return"string"==typeof t&&!t.includes("-")&&!!(ot.indexOf(t)>-1||/[A-Z]/u.test(t))}function at(t){const e=[{},{}];return null==t||t.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function lt(t,e,n,i){if("function"==typeof e){const[s,o]=at(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=at(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const ut=t=>Array.isArray(t),ct=t=>Boolean(t&&t.getVelocity);function ht(t){const e=ct(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const dt=t=>(n,i)=>{const s=e.useContext(N),a=e.useContext(r),l=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,s,o){const r={latestValues:pt(i,s,o,t),renderState:e()};return n&&(r.onMount=t=>n({props:i,current:t,...r}),r.onUpdate=t=>n(t)),r}(t,n,s,a);return i?l():o(l)};function pt(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=ht(o[d]);let{initial:r,animate:a}=t;const l=$(t),u=q(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!_(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=lt(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}const mt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ft=new Set(mt),gt=t=>e=>"string"==typeof e&&e.startsWith(t),yt=gt("--"),vt=gt("var(--"),xt=t=>!!vt(t)&&wt.test(t.split("/*")[0].trim()),wt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,bt=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Tt=(t,e,n)=>n>e?e:n<t?t:n,St={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Pt={...St,transform:t=>Tt(0,1,t)},Et={...St,default:1},At=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Rt=At("deg"),Ct=At("%"),Dt=At("px"),Ot=At("vh"),Mt=At("vw"),jt={...Ct,parse:t=>Ct.parse(t)/100,transform:t=>Ct.transform(100*t)},Vt={borderWidth:Dt,borderTopWidth:Dt,borderRightWidth:Dt,borderBottomWidth:Dt,borderLeftWidth:Dt,borderRadius:Dt,radius:Dt,borderTopLeftRadius:Dt,borderTopRightRadius:Dt,borderBottomRightRadius:Dt,borderBottomLeftRadius:Dt,width:Dt,maxWidth:Dt,height:Dt,maxHeight:Dt,top:Dt,right:Dt,bottom:Dt,left:Dt,padding:Dt,paddingTop:Dt,paddingRight:Dt,paddingBottom:Dt,paddingLeft:Dt,margin:Dt,marginTop:Dt,marginRight:Dt,marginBottom:Dt,marginLeft:Dt,backgroundPositionX:Dt,backgroundPositionY:Dt},kt={rotate:Rt,rotateX:Rt,rotateY:Rt,rotateZ:Rt,scale:Et,scaleX:Et,scaleY:Et,scaleZ:Et,skew:Rt,skewX:Rt,skewY:Rt,distance:Dt,translateX:Dt,translateY:Dt,translateZ:Dt,x:Dt,y:Dt,z:Dt,perspective:Dt,transformPerspective:Dt,opacity:Pt,originX:jt,originY:jt,originZ:Dt},Lt={...St,transform:Math.round},Ft={...Vt,...kt,zIndex:Lt,size:Dt,fillOpacity:Pt,strokeOpacity:Pt,numOctaves:Lt},Bt={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ut=mt.length;function Nt(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if(ft.has(l))r=!0;else if(yt(l))s[l]=t;else{const e=bt(t,Ft[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Ut;o++){const r=mt[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=bt(a,Ft[r]);l||(s=!1,i+=`${Bt[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const It={offset:"stroke-dashoffset",array:"stroke-dasharray"},_t={offset:"strokeDashoffset",array:"strokeDasharray"};function Wt(t,e,n){return"string"==typeof t?t:Dt.transform(e+n*t)}function zt(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Nt(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==o||p.transform)&&(p.transformOrigin=function(t,e,n){return`${Wt(e,t.x,t.width)} ${Wt(n,t.y,t.height)}`}(m,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==i&&(d.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?It:_t;t[o.offset]=Dt.transform(-i);const r=Dt.transform(e),a=Dt.transform(n);t[o.array]=`${r} ${a}`}(d,r,a,l,!1)}const $t=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),qt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Ht=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Kt(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}const Xt=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Yt(t,e,n,i){Kt(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(Xt.has(s)?s:J(s),e.attrs[s])}const Gt={};function Jt(t,{layout:e,layoutId:n}){return ft.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Gt[t]||"opacity"===t)}function Zt(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(ct(s[r])||e.style&&ct(e.style[r])||Jt(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}function Qt(t,e,n){const i=Zt(t,e,n);for(const s in t)if(ct(t[s])||ct(e[s])){i[-1!==mt.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const te=["x","y","width","height","cx","cy","r"],ee={useVisualState:dt({scrapeMotionValuesFromProps:Qt,createRenderState:qt,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:s})=>{if(!n)return;let o=!!t.drag;if(!o)for(const a in s)if(ft.has(a)){o=!0;break}if(!o)return;let r=!e;if(e)for(let a=0;a<te.length;a++){const n=te[a];t[n]!==e[n]&&(r=!0)}r&&R.read((()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(n){e.dimensions={x:0,y:0,width:0,height:0}}}(n,i),R.render((()=>{zt(i,s,Ht(n.tagName),t.transformTemplate),Yt(n,i)}))}))}})},ne={useVisualState:dt({scrapeMotionValuesFromProps:Zt,createRenderState:$t})};function ie(t,e,n){for(const i in e)ct(e[i])||Jt(i,n)||(t[i]=e[i])}function se(t,n){const i={};return ie(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Nt(e,n,t),Object.assign({},e.vars,e.style)}),[n])}(t,n)),i}function oe(t,e){const n={},i=se(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}function re(t,n,i,s){const o=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return zt(e,n,Ht(s),t.transformTemplate),{...e.attrs,style:{...e.style}}}),[n]);if(t.style){const e={};ie(e,t.style,t),o.style={...e,...o.style}}return o}function ae(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(rt(n)?re:oe)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(F(s)||!0===n&&L(s)||!e&&!L(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo((()=>ct(c)?c.get():c),[c]);return e.createElement(n,{...u,children:h})}}function le(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return it({...rt(n)?ee:ne,preloadedFeatures:t,useRender:ae(i),createVisualElement:e,Component:n})}}function ue(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function ce(t,e,n){const i=t.getProps();return lt(i,e,void 0!==n?n:i.custom,t)}const he=w((()=>void 0!==window.ScrollTimeline));class de{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>"finished"in t?t.finished:t)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map((n=>he()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0));return()=>{n.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class pe extends de{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function me(t,e){return t?t[e]||t.default||t:void 0}const fe=2e4;function ge(t){let e=0;let n=t.next(e);for(;!n.done&&e<fe;)e+=50,n=t.next(e);return e>=fe?1/0:e}function ye(t){return"function"==typeof t}function ve(t,e){t.timeline=e,t.onfinish=null}const xe=t=>Array.isArray(t)&&"number"==typeof t[0],we={linearEasing:void 0};function be(t,e){const n=w(t);return()=>{var t;return null!==(t=we[e])&&void 0!==t?t:n()}}const Te=be((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Se=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=t(b(0,s-1,o))+", ";return`linear(${i.substring(0,i.length-2)})`};function Pe(t){return Boolean("function"==typeof t&&Te()||!t||"string"==typeof t&&(t in Ae||Te())||xe(t)||Array.isArray(t)&&t.every(Pe))}const Ee=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Ae={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ee([0,.65,.55,1]),circOut:Ee([.55,0,1,.45]),backIn:Ee([.31,.01,.66,-.59]),backOut:Ee([.33,1.53,.69,.99])};function Re(t,e){return t?"function"==typeof t&&Te()?Se(t,e):xe(t)?Ee(t):Array.isArray(t)?t.map((t=>Re(t,e)||Ae.easeOut)):Ae[t]:void 0}const Ce={x:!1,y:!1};function De(){return Ce.x||Ce.y}function Oe(t,e){const n=function(t){if(t instanceof Element)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Me(t){return e=>{"touch"===e.pointerType||De()||t(e)}}const je=(t,e)=>!!e&&(t===e||je(t,e.parentElement)),Ve=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ke=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Le=new WeakSet;function Fe(t){return e=>{"Enter"===e.key&&t(e)}}function Be(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Ue(t){return Ve(t)&&!De()}function Ne(t,e,n={}){const[i,s,o]=Oe(t,n),r=t=>{const i=t.currentTarget;if(!Ue(t)||Le.has(i))return;Le.add(i);const o=e(t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Ue(t)&&Le.has(i)&&(Le.delete(i),"function"==typeof o&&o(t,{success:e}))},a=t=>{r(t,n.useGlobalTarget||je(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{(function(t){return ke.has(t.tagName)||-1!==t.tabIndex})(t)||null!==t.getAttribute("tabindex")||(t.tabIndex=0);(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Fe((()=>{if(Le.has(n))return;Be(n,"down");const t=Fe((()=>{Be(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Be(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s)),s)})),o}const Ie=new Set(["width","height","top","left","right","bottom",...mt]);let _e;function We(){_e=void 0}const ze={now:()=>(void 0===_e&&ze.set(D.isProcessing||P?D.timestamp:performance.now()),_e),set:t=>{_e=t,queueMicrotask(We)}};function $e(t,e){-1===t.indexOf(e)&&t.push(e)}function qe(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class He{constructor(){this.subscriptions=[]}add(t){return $e(this.subscriptions,t),()=>qe(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Ke(t,e){return e?t*(1e3/e):0}class Xe{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=ze.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=ze.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new He);const n=this.events[t].add(e);return"change"===t?()=>{n(),R.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=ze.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Ke(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ye(t,e){return new Xe(t,e)}function Ge(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ye(n))}function Je(t,e){const n=t.getValue("willChange");if(i=n,Boolean(ct(i)&&i.add))return n.add(e);var i}function Ze(t){return t.props[Z]}const Qe=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function tn(t,e,n,i){if(t===e&&n===i)return v;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=Qe(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:Qe(s(t),e,i)}const en=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,nn=t=>e=>1-t(1-e),sn=tn(.33,1.53,.69,.99),on=nn(sn),rn=en(on),an=t=>(t*=2)<1?.5*on(t):.5*(2-Math.pow(2,-10*(t-1))),ln=t=>1-Math.sin(Math.acos(t)),un=nn(ln),cn=en(ln),hn=t=>/^0[^.\s]+$/u.test(t);const dn=t=>Math.round(1e5*t)/1e5,pn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const mn=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,fn=(t,e)=>n=>Boolean("string"==typeof n&&mn.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),gn=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(pn);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},yn={...St,transform:t=>Math.round((t=>Tt(0,255,t))(t))},vn={test:fn("rgb","red"),parse:gn("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+yn.transform(t)+", "+yn.transform(e)+", "+yn.transform(n)+", "+dn(Pt.transform(i))+")"};const xn={test:fn("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:vn.transform},wn={test:fn("hsl","hue"),parse:gn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Ct.transform(dn(e))+", "+Ct.transform(dn(n))+", "+dn(Pt.transform(i))+")"},bn={test:t=>vn.test(t)||xn.test(t)||wn.test(t),parse:t=>vn.test(t)?vn.parse(t):wn.test(t)?wn.parse(t):xn.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?vn.transform(t):wn.transform(t)},Tn=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Sn="number",Pn="color",En=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function An(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(En,(t=>(bn.test(t)?(i.color.push(o),s.push(Pn),n.push(bn.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Sn),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Rn(t){return An(t).values}function Cn(t){const{split:e,types:n}=An(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Sn?dn(t[o]):e===Pn?bn.transform(t[o]):t[o]}return s}}const Dn=t=>"number"==typeof t?0:t;const On={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(pn))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(Tn))||void 0===n?void 0:n.length)||0)>0},parse:Rn,createTransformer:Cn,getAnimatableNone:function(t){const e=Rn(t);return Cn(t)(e.map(Dn))}},Mn=new Set(["brightness","contrast","saturate","opacity"]);function jn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(pn)||[];if(!i)return t;const s=n.replace(i,"");let o=Mn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Vn=/\b([a-z-]*)\(.*?\)/gu,kn={...On,getAnimatableNone:t=>{const e=t.match(Vn);return e?e.map(jn).join(" "):t}},Ln={...Ft,color:bn,backgroundColor:bn,outlineColor:bn,fill:bn,stroke:bn,borderColor:bn,borderTopColor:bn,borderRightColor:bn,borderBottomColor:bn,borderLeftColor:bn,filter:kn,WebkitFilter:kn},Fn=t=>Ln[t];function Bn(t,e){let n=Fn(t);return n!==kn&&(n=On),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Un=new Set(["auto","none","0"]);const Nn=t=>t===St||t===Dt,In=(t,e)=>parseFloat(t.split(", ")[e]),_n=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/u);if(s)return In(s[1],e);{const e=i.match(/^matrix\((.+)\)$/u);return e?In(e[1],t):0}},Wn=new Set(["x","y","z"]),zn=mt.filter((t=>!Wn.has(t)));const $n={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:_n(4,13),y:_n(5,14)};$n.translateX=$n.x,$n.translateY=$n.y;const qn=new Set;let Hn=!1,Kn=!1;function Xn(){if(Kn){const t=Array.from(qn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return zn.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Kn=!1,Hn=!1,qn.forEach((t=>t.complete())),qn.clear()}function Yn(){qn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Kn=!0)}))}class Gn{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(qn.add(this),Hn||(Hn=!0,R.read(Yn),R.resolveKeyframes(Xn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),qn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,qn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Jn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Zn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Qn(t,e,n=1){const[i,s]=function(t){const e=Zn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${null!=n?n:i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return Jn(t)?parseFloat(t):t}return xt(s)?Qn(s,e,n+1):s}const ti=t=>e=>e.test(t),ei=[St,Dt,Ct,Rt,Mt,Ot,{test:t=>"auto"===t,parse:t=>t}],ni=t=>ei.find(ti(t));class ii extends Gn{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),xt(n))){const i=Qn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!Ie.has(n)||2!==t.length)return;const[i,s]=t,o=ni(i),r=ni(s);if(o!==r)if(Nn(o)&&Nn(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)("number"==typeof(i=t[s])?0===i:null===i||"none"===i||"0"===i||hn(i))&&n.push(s);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Un.has(e)&&An(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Bn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=$n[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=$n[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach((([t,n])=>{e.getValue(t).set(n)})),this.resolveNoneKeyframes()}}const si=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!On.test(t)&&"0"!==t||t.startsWith("url(")));const oi=t=>null!==t;function ri(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(oi),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}class ai{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=ze.now(),this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(Yn(),Xn()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=ze.now(),this.hasAttemptedResolve=!0;const{name:n,type:i,velocity:s,delay:o,onComplete:r,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=si(s,e),a=si(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||ye(n))&&i)}(t,n,i,s)){if(!o)return a&&a(ri(t,this.options,e)),r&&r(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise((t=>{this.resolveFinishedPromise=t}))}}const li=(t,e,n)=>t+(e-t)*n;function ui(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function ci(t,e){return n=>n>0?e:t}const hi=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},di=[xn,vn,wn];function pi(t){const e=(n=t,di.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===wn&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=ui(a,i,t+1/3),o=ui(a,i,t),r=ui(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const mi=(t,e)=>{const n=pi(t),i=pi(e);if(!n||!i)return ci(t,e);const s={...n};return t=>(s.red=hi(n.red,i.red,t),s.green=hi(n.green,i.green,t),s.blue=hi(n.blue,i.blue,t),s.alpha=li(n.alpha,i.alpha,t),vn.transform(s))},fi=(t,e)=>n=>e(t(n)),gi=(...t)=>t.reduce(fi),yi=new Set(["none","hidden"]);function vi(t,e){return n=>li(t,e,n)}function xi(t){return"number"==typeof t?vi:"string"==typeof t?xt(t)?ci:bn.test(t)?mi:Ti:Array.isArray(t)?wi:"object"==typeof t?bn.test(t)?mi:bi:ci}function wi(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>xi(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function bi(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=xi(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Ti=(t,e)=>{const n=On.createTransformer(e),i=An(t),s=An(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?yi.has(t)&&!s.values.length||yi.has(e)&&!i.values.length?function(t,e){return yi.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):gi(wi(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):ci(t,e)};function Si(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return li(t,e,n);return xi(t)(t,e)}function Pi(t,e,n){const i=Math.max(e-5,0);return Ke(n-t(i),e-i)}const Ei=100,Ai=10,Ri=1,Ci=0,Di=800,Oi=.3,Mi=.3,ji={granular:.01,default:2},Vi={granular:.005,default:.5},ki=.01,Li=10,Fi=.05,Bi=1,Ui=.001;function Ni({duration:t=Di,bounce:e=Oi,velocity:n=Ci,mass:i=Ri}){let s,o,r=1-e;r=Tt(Fi,Bi,r),t=Tt(ki,Li,S(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=_i(e,r),l=Math.exp(-s);return Ui-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=_i(Math.pow(e,2),r);return(-s(e)+Ui>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<Ii;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=T(t),isNaN(a))return{stiffness:Ei,damping:Ai,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Ii=12;function _i(t,e){return t*Math.sqrt(1-e*e)}const Wi=["duration","bounce"],zi=["stiffness","damping","mass"];function $i(t,e){return e.some((e=>void 0!==t[e]))}function qi(t=Mi,e=Oi){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Ci,stiffness:Ei,damping:Ai,mass:Ri,isResolvedFromDuration:!1,...t};if(!$i(t,zi)&&$i(t,Wi))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*Tt(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:Ri,stiffness:s,damping:o}}else{const n=Ni(t);e={...e,...n,mass:Ri},e.isResolvedFromDuration=!0}return e}({...n,velocity:-S(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=S(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?ji.granular:ji.default),s||(s=v?Vi.granular:Vi.default),f<1){const t=_i(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0;f<1&&(n=0===t?T(m):Pi(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(ge(w),fe),e=Se((e=>w.next(t*e).value),t,30);return t+"ms "+e}};return w}function Hi({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,b;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,b=qi({keyframes:[d.value,p(d.value)],velocity:Pi(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return b||void 0!==w||(e=!0,x(t),T(t)),void 0!==w&&t>=w?b.next(t-w):(!e&&x(t),d)}}}const Ki=tn(.42,0,1,1),Xi=tn(0,0,.58,1),Yi=tn(.42,0,.58,1),Gi={linear:v,easeIn:Ki,easeInOut:Yi,easeOut:Xi,circIn:ln,circInOut:cn,circOut:un,backIn:on,backInOut:rn,backOut:sn,anticipate:an},Ji=t=>{if(xe(t)){x(4===t.length);const[e,n,i,s]=t;return tn(e,n,i,s)}return"string"==typeof t?Gi[t]:t};function Zi(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(x(o===e.length),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||Si,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||v:e;n=gi(t,n)}i.push(n)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=b(t[i],t[i+1],n);return a[i](s)};return n?e=>u(Tt(t[0],t[o-1],e)):u}function Qi(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=b(0,e,i);t.push(li(n,1,s))}}(e,t.length-1),e}function ts({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(Ji):Ji(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:Qi(e),t),a=Zi(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map((()=>u||Yi)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}const es=t=>{const e=({timestamp:e})=>t(e);return{start:()=>R.update(e,!0),stop:()=>C(e),now:()=>D.isProcessing?D.timestamp:ze.now()}},ns={decay:Hi,inertia:Hi,tween:ts,keyframes:ts,spring:qi},is=t=>t/100;class ss extends ai{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:i,keyframes:s}=this.options,o=(null==i?void 0:i.KeyframeResolver)||Gn;this.resolver=new o(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,r=ye(e)?e:ns[e]||ts;let a,l;r!==ts&&"number"!=typeof t[0]&&(a=gi(is,Si(t[0],t[1])),t=[0,100]);const u=r({...this.options,keyframes:t});"mirror"===s&&(l=r({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=ge(u));const{calculatedDuration:c}=u,h=c+i;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-i}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),v=Tt(0,1,n)*c}const w=y?{done:!1,value:a[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;y||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&void 0!==i&&(w.value=ri(a,this.options,i)),f&&f(w.value),T&&this.finish(),w}get duration(){const{resolved:t}=this;return t?S(t.calculatedDuration):0}get time(){return S(this.currentTime)}set time(t){t=T(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=S(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=es,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const os=new Set(["opacity","clipPath","filter","transform"]);function rs(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=Re(a,s);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const as=w((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));const ls={anticipate:an,backInOut:rn,circInOut:cn};class us extends ai{constructor(t){super(t);const{name:e,motionValue:n,element:i,keyframes:s}=this.options;this.resolver=new ii(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:i,ease:s,type:o,motionValue:r,name:a,startTime:l}=this.options;if(!r.owner||!r.owner.current)return!1;var u;if("string"==typeof s&&Te()&&s in ls&&(s=ls[s]),ye((u=this.options).type)||"spring"===u.type||!Pe(u.ease)){const{onComplete:e,onUpdate:r,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new ss({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,i=c.times,s=c.ease,o="keyframes"}const c=rs(r.owner.current,a,t,{...this.options,duration:n,times:i,ease:s});return c.startTime=null!=l?l:this.calcStartTime(),this.pendingTimeline?(ve(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;r.set(ri(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return S(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return S(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=T(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return v;const{animation:n}=e;ve(n,t)}else this.pendingTimeline=t;return v}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:o,times:r}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,c=new ss({...u,keyframes:n,duration:i,type:s,ease:o,times:r,isGenerator:!0}),h=T(this.time);t.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return as()&&n&&os.has(n)&&!a&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}const cs={type:"spring",stiffness:500,damping:25,restSpeed:10},hs={type:"keyframes",duration:.8},ds={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ps=(t,{keyframes:e})=>e.length>2?hs:ft.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:cs:ds;const ms=(t,e,n,i={},s,o)=>r=>{const a=me(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=T(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(c={...c,...ps(t,c)}),c.duration&&(c.duration=T(c.duration)),c.repeatDelay&&(c.repeatDelay=T(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),h&&!o&&void 0!==e.get()){const t=ri(c.keyframes,a);if(void 0!==t)return R.update((()=>{c.onUpdate(t),c.onComplete()})),new pe([])}return!o&&us.supports(c)?new us(c):new ss(c)};function fs({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function gs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(r=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const h in l){const e=t.getValue(h,null!==(o=t.latestValues[h])&&void 0!==o?o:null),i=l[h];if(void 0===i||c&&fs(c,h))continue;const s={delay:n,...me(r||{},h)};let a=!1;if(window.MotionHandoffAnimation){const e=Ze(t);if(e){const t=window.MotionHandoffAnimation(e,h,R);null!==t&&(s.startTime=t,a=!0)}}Je(t,h),e.start(ms(h,e,i,t.shouldReduceMotion&&Ie.has(h)?{type:!1}:s,t,a));const d=e.animation;d&&u.push(d)}return a&&Promise.all(u).then((()=>{R.update((()=>{a&&function(t,e){const n=ce(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const a in o)Ge(t,a,(r=o[a],ut(r)?r[r.length-1]||0:r));var r}(t,a)}))})),u}function ys(t,e,n={}){var i;const s=ce(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(gs(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(vs).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(ys(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then((()=>e()))}return Promise.all([r(),a(n.delay)])}function vs(t,e){return t.sortNodePosition(e)}const xs=z.length;function ws(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&ws(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<xs;n++){const i=z[n],s=t.props[i];(I(s)||!1===s)&&(e[i]=s)}return e}const bs=[...W].reverse(),Ts=W.length;function Ss(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>ys(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=ys(t,e,n);else{const s="function"==typeof e?ce(t,e,n.custom):e;i=Promise.all(gs(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function Ps(t){let e=Ss(t),n=Rs(),i=!0;const s=e=>(n,i)=>{var s;const o=ce(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=ws(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Ts;e++){const d=bs[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=I(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||_(m)||"boolean"==typeof m)continue;const v=Es(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const b=Array.isArray(m)?m:[m];let T=b.reduce(s(d),{});!1===g&&(T={});const{prevResolvedValues:S={}}=p,P={...S,...T},E=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in P){const e=T[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=ut(e)&&ut(n)?!ue(e,n):e!==n,i?null!=e?E(t):u.add(t):void 0!==e&&u.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=T,p.isActive&&(c={...c,...T}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||w)&&l.push(...b.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach((t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)})),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Rs(),i=!0}}}function Es(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!ue(e,t)}function As(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Rs(){return{animate:As(!0),whileInView:As(),whileHover:As(),whileTap:As(),whileDrag:As(),whileFocus:As(),exit:As()}}class Cs{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ds=0;const Os={animation:{Feature:class extends Cs{constructor(t){super(t),t.animationState||(t.animationState=Ps(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();_(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}},exit:{Feature:class extends Cs{constructor(){super(...arguments),this.id=Ds++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>e(this.id)))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}};function Ms(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function js(t){return{point:{x:t.pageX,y:t.pageY}}}function Vs(t,e,n,i){return Ms(t,e,(t=>e=>Ve(e)&&t(e,js(e)))(n),i)}const ks=(t,e)=>Math.abs(t-e);class Ls{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Us(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=ks(t.x,e.x),i=ks(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=D;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Fs(e,this.transformPagePoint),R.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Us("pointercancel"===t.type?this.lastMoveEventInfo:Fs(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Ve(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Fs(js(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=D;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Us(o,this.history)),this.removeListeners=gi(Vs(this.contextWindow,"pointermove",this.handlePointerMove),Vs(this.contextWindow,"pointerup",this.handlePointerUp),Vs(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),C(this.updatePoint)}}function Fs(t,e){return e?{point:e(t.point)}:t}function Bs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Us({point:t},e){return{point:t,delta:Bs(t,Is(e)),offset:Bs(t,Ns(e)),velocity:_s(e,.1)}}function Ns(t){return t[0]}function Is(t){return t[t.length-1]}function _s(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Is(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>T(e)));)n--;if(!i)return{x:0,y:0};const o=S(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ws(t){return t.max-t.min}function zs(t,e,n,i=.5){t.origin=i,t.originPoint=li(e.min,e.max,t.origin),t.scale=Ws(n)/Ws(e),t.translate=li(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function $s(t,e,n,i){zs(t.x,e.x,n.x,i?i.originX:void 0),zs(t.y,e.y,n.y,i?i.originY:void 0)}function qs(t,e,n){t.min=n.min+e.min,t.max=t.min+Ws(e)}function Hs(t,e,n){t.min=e.min-n.min,t.max=t.min+Ws(e)}function Ks(t,e,n){Hs(t.x,e.x,n.x),Hs(t.y,e.y,n.y)}function Xs(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ys(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Gs=.35;function Js(t,e,n){return{min:Zs(t,e),max:Zs(t,n)}}function Zs(t,e){return"number"==typeof t?t:t[e]||0}const Qs=()=>({x:{min:0,max:0},y:{min:0,max:0}});function to(t){return[t("x"),t("y")]}function eo({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function no(t){return void 0===t||1===t}function io({scale:t,scaleX:e,scaleY:n}){return!no(t)||!no(e)||!no(n)}function so(t){return io(t)||oo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function oo(t){return ro(t.x)||ro(t.y)}function ro(t){return t&&"0%"!==t}function ao(t,e,n){return n+e*(t-n)}function lo(t,e,n,i,s){return void 0!==s&&(t=ao(t,s,i)),ao(t,n,i)+e}function uo(t,e=0,n=1,i,s){t.min=lo(t.min,e,n,i,s),t.max=lo(t.max,e,n,i,s)}function co(t,{x:e,y:n}){uo(t.x,e.translate,e.scale,e.originPoint),uo(t.y,n.translate,n.scale,n.originPoint)}const ho=.999999999999,po=1.0000000000001;function mo(t,e){t.min=t.min+e,t.max=t.max+e}function fo(t,e,n,i,s=.5){uo(t,e,n,li(t.min,t.max,s),i)}function go(t,e){fo(t.x,e.x,e.scaleX,e.scale,e.originX),fo(t.y,e.y,e.scaleY,e.scale,e.originY)}function yo(t,e){return eo(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const vo=({current:t})=>t?t.ownerDocument.defaultView:null,xo=new WeakMap;class wo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Ls(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(js(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Ce[o]?null:(Ce[o]=!0,()=>{Ce[o]=!1}):Ce.x||Ce.y?null:(Ce.x=Ce.y=!0,()=>{Ce.x=Ce.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),to((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Ct.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Ws(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&R.postRender((()=>s(t,e))),Je(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>to((t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:vo(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&R.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!bo(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?li(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?li(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&Y(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Xs(t.x,n,s),y:Xs(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Gs){return!1===t?t=0:!0===t&&(t=Gs),{x:Js(t,"left","right"),y:Js(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&to((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Y(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=yo(t,n),{scroll:s}=e;return s&&(mo(i.x,s.offset.x),mo(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ys(t.x,e.x),y:Ys(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=eo(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=to((r=>{if(!bo(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Je(this.visualElement,t),n.start(ms(t,n,0,e,this.visualElement,!1))}stopAnimation(){to((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){to((t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()}))}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){to((e=>{const{drag:n}=this.getProps();if(!bo(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-li(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Y(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};to((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Ws(t),s=Ws(e);return s>i?n=b(e.min,e.max-i,t.min):i>s&&(n=b(t.min,t.max-s,e.min)),Tt(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),to((e=>{if(!bo(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(li(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;xo.set(this.visualElement,this);const t=Vs(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Y(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),R.read(e);const s=Ms(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(to((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Gs,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function bo(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const To=t=>(e,n)=>{t&&R.postRender((()=>t(e,n)))};const So={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Po(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Eo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Dt.test(t))return t;t=parseFloat(t)}return`${Po(t,e.target.x)}% ${Po(t,e.target.y)}%`}},Ao={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=On.parse(t);if(s.length>5)return i;const o=On.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=li(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class Ro extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;var o;o=Do,Object.assign(Gt,o),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),So.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||R.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Q.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Co(n){const[i,o]=d(),r=e.useContext(s);return t.jsx(Ro,{...n,layoutGroup:r,switchLayoutGroup:e.useContext(tt),isPresent:i,safeToRemove:o})}const Do={borderRadius:{...Eo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Eo,borderTopRightRadius:Eo,borderBottomLeftRadius:Eo,borderBottomRightRadius:Eo,boxShadow:Ao};const Oo=(t,e)=>t.depth-e.depth;class Mo{constructor(){this.children=[],this.isDirty=!1}add(t){$e(this.children,t),this.isDirty=!0}remove(t){qe(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Oo),this.isDirty=!1,this.children.forEach(t)}}const jo=["TopLeft","TopRight","BottomLeft","BottomRight"],Vo=jo.length,ko=t=>"string"==typeof t?parseFloat(t):t,Lo=t=>"number"==typeof t||Dt.test(t);function Fo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Bo=No(0,.5,un),Uo=No(.5,.95,v);function No(t,e,n){return i=>i<t?0:i>e?1:n(b(t,e,i))}function Io(t,e){t.min=e.min,t.max=e.max}function _o(t,e){Io(t.x,e.x),Io(t.y,e.y)}function Wo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function zo(t,e,n,i,s){return t=ao(t-=e,1/n,i),void 0!==s&&(t=ao(t,1/s,i)),t}function $o(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Ct.test(e)&&(e=parseFloat(e),e=li(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=li(o.min,o.max,i);t===o&&(a-=e),t.min=zo(t.min,e,n,a,s),t.max=zo(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const qo=["x","scaleX","originX"],Ho=["y","scaleY","originY"];function Ko(t,e,n,i){$o(t.x,e,qo,n?n.x:void 0,i?i.x:void 0),$o(t.y,e,Ho,n?n.y:void 0,i?i.y:void 0)}function Xo(t){return 0===t.translate&&1===t.scale}function Yo(t){return Xo(t.x)&&Xo(t.y)}function Go(t,e){return t.min===e.min&&t.max===e.max}function Jo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Zo(t,e){return Jo(t.x,e.x)&&Jo(t.y,e.y)}function Qo(t){return Ws(t.x)/Ws(t.y)}function tr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class er{constructor(){this.members=[]}add(t){$e(this.members,t),t.scheduleRender()}remove(t){if(qe(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const nr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},ir="undefined"!=typeof window&&void 0!==window.MotionDebug,sr=["","X","Y","Z"],or={visibility:"hidden"};let rr=0;function ar(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function lr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ze(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",R,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&lr(i)}function ur({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=rr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ir&&(nr.totalNodes=nr.resolvedTargetDeltas=nr.recalculatedProjection=0),this.nodes.forEach(dr),this.nodes.forEach(xr),this.nodes.forEach(wr),this.nodes.forEach(pr),ir&&window.MotionDebug.record(nr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Mo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new He),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;var i;this.isSVG=(i=e)instanceof SVGElement&&"svg"!==i.tagName,this.instance=e;const{layoutId:s,layout:o,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||s)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=ze.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(C(i),t(o-e))};return R.read(i,!0),()=>C(i)}(i,250),So.hasAnimatedSinceResize&&(So.hasAnimatedSinceResize=!1,this.nodes.forEach(vr))}))}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||o)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||r.getDefaultTransition()||Ar,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!Zo(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...me(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||vr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,C(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(br),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&lr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(fr);this.isUpdating||this.nodes.forEach(gr),this.isUpdating=!1,this.nodes.forEach(yr),this.nodes.forEach(cr),this.nodes.forEach(hr),this.clearAllSnapshots();const t=ze.now();D.delta=Tt(0,1e3/60,t-D.timestamp),D.timestamp=t,D.isProcessing=!0,O.update.process(D),O.preRender.process(D),O.render.process(D),D.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Q.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(mr),this.sharedNodes.forEach(Tr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,R.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){R.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Yo(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||so(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Dr((i=n).x),Dr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(Mr))){const{scroll:t}=this.root;t&&(mo(n.x,t.offset.x),mo(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(_o(n,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&_o(n,t),mo(n.x,s.offset.x),mo(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};_o(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&go(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),so(t.latestValues)&&go(n,t.latestValues)}return so(this.latestValues)&&go(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};_o(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!so(t.latestValues))continue;io(t.latestValues)&&t.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};_o(i,t.measurePageBox()),Ko(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return so(this.latestValues)&&Ko(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==D.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=D.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ks(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),_o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,qs(r.x,a.x,l.x),qs(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):_o(this.target,this.layout.layoutBox),co(this.target,this.targetDelta)):_o(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ks(this.relativeTargetOrigin,this.target,t.target),_o(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ir&&nr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!io(this.parent.latestValues)&&!oo(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===D.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;_o(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&go(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,co(t,r)),i&&so(o.latestValues)&&go(t,o.latestValues))}e.x<po&&e.x>ho&&(e.x=1),e.y<po&&e.y>ho&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(Wo(this.prevProjectionDelta.x,this.projectionDelta.x),Wo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),$s(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&tr(this.projectionDelta.x,this.prevProjectionDelta.x)&&tr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),ir&&nr.recalculatedProjection++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Er));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;Sr(o.x,t.x,n),Sr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ks(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,g=n,Pr(p.x,m.x,f.x,g),Pr(p.y,m.y,f.y,g),h&&(l=this.relativeTarget,d=h,Go(l.x,d.x)&&Go(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),_o(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=li(0,void 0!==n.opacity?n.opacity:1,Bo(i)),t.opacityExit=li(void 0!==e.opacity?e.opacity:1,0,Uo(i))):o&&(t.opacity=li(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let r=0;r<Vo;r++){const s=`border${jo[r]}Radius`;let o=Fo(e,s),a=Fo(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Lo(o)===Lo(a)?(t[s]=Math.max(li(ko(o),ko(a),i),0),(Ct.test(a)||Ct.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=li(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(C(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=R.update((()=>{So.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,n){const i=ct(t)?t:Ye(t);return i.start(ms("",i,e,n)),i.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Or(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Ws(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Ws(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}_o(e,n),go(e,s),$s(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new er);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&ar("z",t,i,this.animationValues);for(let s=0;s<sr.length;s++)ar(`rotate${sr[s]}`,t,i,this.animationValues),ar(`skew${sr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return or;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=ht(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=ht(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!so(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const u in Gt){if(void 0===r[u])continue;const{correct:t,applyTo:e}=Gt[u],n="none"===i.transform?r[u]:t(r[u],o);if(e){const t=e.length;for(let s=0;s<t;s++)i[e[s]]=n}else i[u]=n}return this.options.layoutId&&(i.pointerEvents=o===this?ht(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()})),this.root.nodes.forEach(fr),this.root.sharedNodes.clear()}}}function cr(t){t.updateLayout()}function hr(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?to((t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Ws(i);i.min=e[t].min,i.max=i.min+s})):Or(s,n.layoutBox,e)&&to((i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Ws(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};$s(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?$s(a,t.applyTransform(i,!0),n.measuredBox):$s(a,e,n.layoutBox);const l=!Yo(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Ks(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Ks(a,e,o.layoutBox),Zo(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function dr(t){ir&&nr.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function pr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function mr(t){t.clearSnapshot()}function fr(t){t.clearMeasurements()}function gr(t){t.isLayoutDirty=!1}function yr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function vr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function xr(t){t.resolveTargetDelta()}function wr(t){t.calcProjection()}function br(t){t.resetSkewAndRotation()}function Tr(t){t.removeLeadSnapshot()}function Sr(t,e,n){t.translate=li(e.translate,0,n),t.scale=li(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Pr(t,e,n,i){t.min=li(e.min,n.min,i),t.max=li(e.max,n.max,i)}function Er(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Ar={duration:.45,ease:[.4,0,.1,1]},Rr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Cr=Rr("applewebkit/")&&!Rr("chrome/")?Math.round:v;function Dr(t){t.min=Cr(t.min),t.max=Cr(t.max)}function Or(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=Qo(e),s=Qo(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Mr(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}const jr=ur({attachResizeListener:(t,e)=>Ms(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Vr={current:void 0},kr=ur({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Vr.current){const t=new jr({});t.mount(window),t.setOptions({layoutScroll:!0}),Vr.current=t}return Vr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Lr={pan:{Feature:class extends Cs{constructor(){super(...arguments),this.removePointerDownListener=v}onPointerDown(t){this.session=new Ls(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:vo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:To(t),onStart:To(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&R.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Vs(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Cs{constructor(t){super(t),this.removeGroupControls=v,this.removeListeners=v,this.controls=new wo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||v}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:kr,MeasureLayout:Co}};function Fr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&R.postRender((()=>s(e,js(e))))}function Br(t,e,n){const{props:i}=t;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&R.postRender((()=>s(e,js(e))))}const Ur=new WeakMap,Nr=new WeakMap,Ir=t=>{const e=Ur.get(t.target);e&&e(t)},_r=t=>{t.forEach(Ir)};function Wr(t,e,n){const i=function({root:t,...e}){const n=t||document;Nr.has(n)||Nr.set(n,{});const i=Nr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(_r,{root:t,...e})),i[s]}(e);return Ur.set(t,n),i.observe(t),()=>{Ur.delete(t),i.unobserve(t)}}const zr={some:0,all:1};const $r={inView:{Feature:class extends Cs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:zr[i]};return Wr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Cs{mount(){const{current:t}=this.node;t&&(this.unmount=Ne(t,(t=>(Br(this.node,t,"Start"),(t,{success:e})=>Br(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Cs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=gi(Ms(this.node.current,"focus",(()=>this.onFocus())),Ms(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Cs{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Oe(t,n),r=Me((t=>{const{target:n}=t,i=e(t);if("function"!=typeof i||!n)return;const o=Me((t=>{i(t),n.removeEventListener("pointerleave",o)}));n.addEventListener("pointerleave",o,s)}));return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}(t,(t=>(Fr(this.node,t,"Start"),t=>Fr(this.node,t,"End")))))}unmount(){}}}},qr={layout:{ProjectionNode:kr,MeasureLayout:Co}},Hr={current:null},Kr={current:!1};const Xr=[...ei,bn,On],Yr=new WeakMap;const Gr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Jr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Gn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=ze.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,R.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=$(e),this.isVariantNode=q(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const d in h){const t=h[d];void 0!==a[d]&&ct(t)&&t.set(a[d],!1)}}mount(t){this.current=t,Yr.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Kr.current||function(){if(Kr.current=!0,f)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Hr.current=t.matches;t.addListener(e),e()}else Hr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Hr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Yr.delete(this.current),this.projection&&this.projection.unmount(),C(this.notifyUpdate),C(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=ft.has(t),i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&R.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in V){const e=V[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<Gr.length;n++){const e=Gr[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(ct(s))t.addValue(i,s);else if(ct(o))t.addValue(i,Ye(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Ye(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Ye(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(Jn(i)||hn(i))?i=parseFloat(i):(s=i,!Xr.find(ti(s))&&On.test(e)&&(i=Bn(t,e))),this.setBaseTarget(t,ct(i)?i.get():i)),ct(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=lt(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||ct(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new He),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Zr extends Jr{constructor(){super(...arguments),this.KeyframeResolver=ii}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ct(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}class Qr extends Zr{constructor(){super(...arguments),this.type="html",this.renderInstance=Kt}readValueFromInstance(t,e){if(ft.has(e)){const t=Fn(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(yt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return yo(t,e)}build(t,e,n){Nt(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Zt(t,e,n)}}class ta extends Zr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Qs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(ft.has(e)){const t=Fn(e);return t&&t.default||0}return e=Xt.has(e)?e:J(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Qt(t,e,n)}build(t,e,n){zt(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,i){Yt(t,e,0,i)}mount(t){this.isSVGTag=Ht(t.tagName),super.mount(t)}}const ea=U(le({...Os,...$r,...Lr,...qr},((t,n)=>rt(t)?new ta(n):new Qr(n,{allowProjection:t!==e.Fragment}))));function na(t,e){return function(){return t.apply(e,arguments)}}const{toString:ia}=Object.prototype,{getPrototypeOf:sa}=Object,{iterator:oa,toStringTag:ra}=Symbol,aa=(t=>e=>{const n=ia.call(e);return t[n]||(t[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),la=t=>(t=t.toLowerCase(),e=>aa(e)===t),ua=t=>e=>typeof e===t,{isArray:ca}=Array,ha=ua("undefined");const da=la("ArrayBuffer");const pa=ua("string"),ma=ua("function"),fa=ua("number"),ga=t=>null!==t&&"object"==typeof t,ya=t=>{if("object"!==aa(t))return!1;const e=sa(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||ra in t||oa in t)},va=la("Date"),xa=la("File"),wa=la("Blob"),ba=la("FileList"),Ta=la("URLSearchParams"),[Sa,Pa,Ea,Aa]=["ReadableStream","Request","Response","Headers"].map(la);function Ra(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let i,s;if("object"!=typeof t&&(t=[t]),ca(t))for(i=0,s=t.length;i<s;i++)e.call(null,t[i],i,t);else{const s=n?Object.getOwnPropertyNames(t):Object.keys(t),o=s.length;let r;for(i=0;i<o;i++)r=s[i],e.call(null,t[r],r,t)}}function Ca(t,e){e=e.toLowerCase();const n=Object.keys(t);let i,s=n.length;for(;s-- >0;)if(i=n[s],e===i.toLowerCase())return i;return null}const Da="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,Oa=t=>!ha(t)&&t!==Da;const Ma=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&sa(Uint8Array)),ja=la("HTMLFormElement"),Va=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),ka=la("RegExp"),La=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),i={};Ra(n,((n,s)=>{let o;!1!==(o=e(n,s,t))&&(i[s]=o||n)})),Object.defineProperties(t,i)};const Fa=la("AsyncFunction"),Ba=(Ua="function"==typeof setImmediate,Na=ma(Da.postMessage),Ua?setImmediate:Na?(Ia=`axios@${Math.random()}`,_a=[],Da.addEventListener("message",(({source:t,data:e})=>{t===Da&&e===Ia&&_a.length&&_a.shift()()}),!1),t=>{_a.push(t),Da.postMessage(Ia,"*")}):t=>setTimeout(t));var Ua,Na,Ia,_a;const Wa="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Da):"undefined"!=typeof process&&process.nextTick||Ba,za={isArray:ca,isArrayBuffer:da,isBuffer:function(t){return null!==t&&!ha(t)&&null!==t.constructor&&!ha(t.constructor)&&ma(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||ma(t.append)&&("formdata"===(e=aa(t))||"object"===e&&ma(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&da(t.buffer),e},isString:pa,isNumber:fa,isBoolean:t=>!0===t||!1===t,isObject:ga,isPlainObject:ya,isReadableStream:Sa,isRequest:Pa,isResponse:Ea,isHeaders:Aa,isUndefined:ha,isDate:va,isFile:xa,isBlob:wa,isRegExp:ka,isFunction:ma,isStream:t=>ga(t)&&ma(t.pipe),isURLSearchParams:Ta,isTypedArray:Ma,isFileList:ba,forEach:Ra,merge:function t(){const{caseless:e}=Oa(this)&&this||{},n={},i=(i,s)=>{const o=e&&Ca(n,s)||s;ya(n[o])&&ya(i)?n[o]=t(n[o],i):ya(i)?n[o]=t({},i):ca(i)?n[o]=i.slice():n[o]=i};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&Ra(arguments[s],i);return n},extend:(t,e,n,{allOwnKeys:i}={})=>(Ra(e,((e,i)=>{n&&ma(e)?t[i]=na(e,n):t[i]=e}),{allOwnKeys:i}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,i)=>{t.prototype=Object.create(e.prototype,i),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,i)=>{let s,o,r;const a={};if(e=e||{},null==t)return e;do{for(s=Object.getOwnPropertyNames(t),o=s.length;o-- >0;)r=s[o],i&&!i(r,t,e)||a[r]||(e[r]=t[r],a[r]=!0);t=!1!==n&&sa(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:aa,kindOfTest:la,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const i=t.indexOf(e,n);return-1!==i&&i===n},toArray:t=>{if(!t)return null;if(ca(t))return t;let e=t.length;if(!fa(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[oa]).call(t);let i;for(;(i=n.next())&&!i.done;){const n=i.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const i=[];for(;null!==(n=t.exec(e));)i.push(n);return i},isHTMLForm:ja,hasOwnProperty:Va,hasOwnProp:Va,reduceDescriptors:La,freezeMethods:t=>{La(t,((e,n)=>{if(ma(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const i=t[n];ma(i)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(t,e)=>{const n={},i=t=>{t.forEach((t=>{n[t]=!0}))};return ca(t)?i(t):i(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,n){return e.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:Ca,global:Da,isContextDefined:Oa,isSpecCompliantForm:function(t){return!!(t&&ma(t.append)&&"FormData"===t[ra]&&t[oa])},toJSONObject:t=>{const e=new Array(10),n=(t,i)=>{if(ga(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[i]=t;const s=ca(t)?[]:{};return Ra(t,((t,e)=>{const o=n(t,i+1);!ha(o)&&(s[e]=o)})),e[i]=void 0,s}}return t};return n(t,0)},isAsyncFn:Fa,isThenable:t=>t&&(ga(t)||ma(t))&&ma(t.then)&&ma(t.catch),setImmediate:Ba,asap:Wa,isIterable:t=>null!=t&&ma(t[oa])};function $a(t,e,n,i,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),i&&(this.request=i),s&&(this.response=s,this.status=s.status?s.status:null)}za.inherits($a,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:za.toJSONObject(this.config),code:this.code,status:this.status}}});const qa=$a.prototype,Ha={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{Ha[t]={value:t}})),Object.defineProperties($a,Ha),Object.defineProperty(qa,"isAxiosError",{value:!0}),$a.from=(t,e,n,i,s,o)=>{const r=Object.create(qa);return za.toFlatObject(t,r,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),$a.call(r,t.message,e,n,i,s),r.cause=t,r.name=t.name,o&&Object.assign(r,o),r};function Ka(t){return za.isPlainObject(t)||za.isArray(t)}function Xa(t){return za.endsWith(t,"[]")?t.slice(0,-2):t}function Ya(t,e,n){return t?t.concat(e).map((function(t,e){return t=Xa(t),!n&&e?"["+t+"]":t})).join(n?".":""):e}const Ga=za.toFlatObject(za,{},null,(function(t){return/^is[A-Z]/.test(t)}));function Ja(t,e,n){if(!za.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const i=(n=za.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!za.isUndefined(e[t])}))).metaTokens,s=n.visitor||u,o=n.dots,r=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&za.isSpecCompliantForm(e);if(!za.isFunction(s))throw new TypeError("visitor must be a function");function l(t){if(null===t)return"";if(za.isDate(t))return t.toISOString();if(za.isBoolean(t))return t.toString();if(!a&&za.isBlob(t))throw new $a("Blob is not supported. Use a Buffer instead.");return za.isArrayBuffer(t)||za.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function u(t,n,s){let a=t;if(t&&!s&&"object"==typeof t)if(za.endsWith(n,"{}"))n=i?n:n.slice(0,-2),t=JSON.stringify(t);else if(za.isArray(t)&&function(t){return za.isArray(t)&&!t.some(Ka)}(t)||(za.isFileList(t)||za.endsWith(n,"[]"))&&(a=za.toArray(t)))return n=Xa(n),a.forEach((function(t,i){!za.isUndefined(t)&&null!==t&&e.append(!0===r?Ya([n],i,o):null===r?n:n+"[]",l(t))})),!1;return!!Ka(t)||(e.append(Ya(s,n,o),l(t)),!1)}const c=[],h=Object.assign(Ga,{defaultVisitor:u,convertValue:l,isVisitable:Ka});if(!za.isObject(t))throw new TypeError("data must be an object");return function t(n,i){if(!za.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+i.join("."));c.push(n),za.forEach(n,(function(n,o){!0===(!(za.isUndefined(n)||null===n)&&s.call(e,n,za.isString(o)?o.trim():o,i,h))&&t(n,i?i.concat(o):[o])})),c.pop()}}(t),e}function Za(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function Qa(t,e){this._pairs=[],t&&Ja(t,this,e)}const tl=Qa.prototype;function el(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function nl(t,e,n){if(!e)return t;const i=n&&n.encode||el;za.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(o=s?s(e,n):za.isURLSearchParams(e)?e.toString():new Qa(e,n).toString(i),o){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}tl.append=function(t,e){this._pairs.push([t,e])},tl.toString=function(t){const e=t?function(e){return t.call(this,e,Za)}:Za;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};class il{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){za.forEach(this.handlers,(function(e){null!==e&&t(e)}))}}const sl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ol={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Qa,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},rl="undefined"!=typeof window&&"undefined"!=typeof document,al="object"==typeof navigator&&navigator||void 0,ll=rl&&(!al||["ReactNative","NativeScript","NS"].indexOf(al.product)<0),ul="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,cl=rl&&window.location.href||"http://localhost",hl={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:rl,hasStandardBrowserEnv:ll,hasStandardBrowserWebWorkerEnv:ul,navigator:al,origin:cl},Symbol.toStringTag,{value:"Module"})),...ol};function dl(t){function e(t,n,i,s){let o=t[s++];if("__proto__"===o)return!0;const r=Number.isFinite(+o),a=s>=t.length;if(o=!o&&za.isArray(i)?i.length:o,a)return za.hasOwnProp(i,o)?i[o]=[i[o],n]:i[o]=n,!r;i[o]&&za.isObject(i[o])||(i[o]=[]);return e(t,n,i[o],s)&&za.isArray(i[o])&&(i[o]=function(t){const e={},n=Object.keys(t);let i;const s=n.length;let o;for(i=0;i<s;i++)o=n[i],e[o]=t[o];return e}(i[o])),!r}if(za.isFormData(t)&&za.isFunction(t.entries)){const n={};return za.forEachEntry(t,((t,i)=>{e(function(t){return za.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),i,n,0)})),n}return null}const pl={transitional:sl,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",i=n.indexOf("application/json")>-1,s=za.isObject(t);s&&za.isHTMLForm(t)&&(t=new FormData(t));if(za.isFormData(t))return i?JSON.stringify(dl(t)):t;if(za.isArrayBuffer(t)||za.isBuffer(t)||za.isStream(t)||za.isFile(t)||za.isBlob(t)||za.isReadableStream(t))return t;if(za.isArrayBufferView(t))return t.buffer;if(za.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return Ja(t,new hl.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,i){return hl.isNode&&za.isBuffer(t)?(this.append(e,t.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((o=za.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return Ja(o?{"files[]":t}:t,e&&new e,this.formSerializer)}}return s||i?(e.setContentType("application/json",!1),function(t,e,n){if(za.isString(t))try{return(e||JSON.parse)(t),za.trim(t)}catch(i){if("SyntaxError"!==i.name)throw i}return(n||JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||pl.transitional,n=e&&e.forcedJSONParsing,i="json"===this.responseType;if(za.isResponse(t)||za.isReadableStream(t))return t;if(t&&za.isString(t)&&(n&&!this.responseType||i)){const n=!(e&&e.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(s){if(n){if("SyntaxError"===s.name)throw $a.from(s,$a.ERR_BAD_RESPONSE,this,null,this.response);throw s}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:hl.classes.FormData,Blob:hl.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};za.forEach(["delete","get","head","post","put","patch"],(t=>{pl.headers[t]={}}));const ml=za.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),fl=Symbol("internals");function gl(t){return t&&String(t).trim().toLowerCase()}function yl(t){return!1===t||null==t?t:za.isArray(t)?t.map(yl):String(t)}function vl(t,e,n,i,s){return za.isFunction(i)?i.call(this,e,n):(s&&(e=n),za.isString(e)?za.isString(i)?-1!==e.indexOf(i):za.isRegExp(i)?i.test(e):void 0:void 0)}let xl=class{constructor(t){t&&this.set(t)}set(t,e,n){const i=this;function s(t,e,n){const s=gl(e);if(!s)throw new Error("header name must be a non-empty string");const o=za.findKey(i,s);(!o||void 0===i[o]||!0===n||void 0===n&&!1!==i[o])&&(i[o||e]=yl(t))}const o=(t,e)=>za.forEach(t,((t,n)=>s(t,n,e)));if(za.isPlainObject(t)||t instanceof this.constructor)o(t,e);else if(za.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))o((t=>{const e={};let n,i,s;return t&&t.split("\n").forEach((function(t){s=t.indexOf(":"),n=t.substring(0,s).trim().toLowerCase(),i=t.substring(s+1).trim(),!n||e[n]&&ml[n]||("set-cookie"===n?e[n]?e[n].push(i):e[n]=[i]:e[n]=e[n]?e[n]+", "+i:i)})),e})(t),e);else if(za.isObject(t)&&za.isIterable(t)){let n,i,s={};for(const e of t){if(!za.isArray(e))throw TypeError("Object iterator must return a key-value pair");s[i=e[0]]=(n=s[i])?za.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}o(s,e)}else null!=t&&s(e,t,n);return this}get(t,e){if(t=gl(t)){const n=za.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let i;for(;i=n.exec(t);)e[i[1]]=i[2];return e}(t);if(za.isFunction(e))return e.call(this,t,n);if(za.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=gl(t)){const n=za.findKey(this,t);return!(!n||void 0===this[n]||e&&!vl(0,this[n],n,e))}return!1}delete(t,e){const n=this;let i=!1;function s(t){if(t=gl(t)){const s=za.findKey(n,t);!s||e&&!vl(0,n[s],s,e)||(delete n[s],i=!0)}}return za.isArray(t)?t.forEach(s):s(t),i}clear(t){const e=Object.keys(this);let n=e.length,i=!1;for(;n--;){const s=e[n];t&&!vl(0,this[s],s,t,!0)||(delete this[s],i=!0)}return i}normalize(t){const e=this,n={};return za.forEach(this,((i,s)=>{const o=za.findKey(n,s);if(o)return e[o]=yl(i),void delete e[s];const r=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,n)=>e.toUpperCase()+n))}(s):String(s).trim();r!==s&&delete e[s],e[r]=yl(i),n[r]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return za.forEach(this,((n,i)=>{null!=n&&!1!==n&&(e[i]=t&&za.isArray(n)?n.join(", "):n)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach((t=>n.set(t))),n}static accessor(t){const e=(this[fl]=this[fl]={accessors:{}}).accessors,n=this.prototype;function i(t){const i=gl(t);e[i]||(!function(t,e){const n=za.toCamelCase(" "+e);["get","set","has"].forEach((i=>{Object.defineProperty(t,i+n,{value:function(t,n,s){return this[i].call(this,e,t,n,s)},configurable:!0})}))}(n,t),e[i]=!0)}return za.isArray(t)?t.forEach(i):i(t),this}};function wl(t,e){const n=this||pl,i=e||n,s=xl.from(i.headers);let o=i.data;return za.forEach(t,(function(t){o=t.call(n,o,s.normalize(),e?e.status:void 0)})),s.normalize(),o}function bl(t){return!(!t||!t.__CANCEL__)}function Tl(t,e,n){$a.call(this,null==t?"canceled":t,$a.ERR_CANCELED,e,n),this.name="CanceledError"}function Sl(t,e,n){const i=n.config.validateStatus;n.status&&i&&!i(n.status)?e(new $a("Request failed with status code "+n.status,[$a.ERR_BAD_REQUEST,$a.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}xl.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),za.reduceDescriptors(xl.prototype,(({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}})),za.freezeMethods(xl),za.inherits(Tl,$a,{__CANCEL__:!0});const Pl=(t,e,n=3)=>{let i=0;const s=function(t,e){t=t||10;const n=new Array(t),i=new Array(t);let s,o=0,r=0;return e=void 0!==e?e:1e3,function(a){const l=Date.now(),u=i[r];s||(s=l),n[o]=a,i[o]=l;let c=r,h=0;for(;c!==o;)h+=n[c++],c%=t;if(o=(o+1)%t,o===r&&(r=(r+1)%t),l-s<e)return;const d=u&&l-u;return d?Math.round(1e3*h/d):void 0}}(50,250);return function(t,e){let n,i,s=0,o=1e3/e;const r=(e,o=Date.now())=>{s=o,n=null,i&&(clearTimeout(i),i=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),a=e-s;a>=o?r(t,e):(n=t,i||(i=setTimeout((()=>{i=null,r(n)}),o-a)))},()=>n&&r(n)]}((n=>{const o=n.loaded,r=n.lengthComputable?n.total:void 0,a=o-i,l=s(a);i=o;t({loaded:o,total:r,progress:r?o/r:void 0,bytes:a,rate:l||void 0,estimated:l&&r&&o<=r?(r-o)/l:void 0,event:n,lengthComputable:null!=r,[e?"download":"upload"]:!0})}),n)},El=(t,e)=>{const n=null!=t;return[i=>e[0]({lengthComputable:n,total:t,loaded:i}),e[1]]},Al=t=>(...e)=>za.asap((()=>t(...e))),Rl=hl.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,hl.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(hl.origin),hl.navigator&&/(msie|trident)/i.test(hl.navigator.userAgent)):()=>!0,Cl=hl.hasStandardBrowserEnv?{write(t,e,n,i,s,o){const r=[t+"="+encodeURIComponent(e)];za.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),za.isString(i)&&r.push("path="+i),za.isString(s)&&r.push("domain="+s),!0===o&&r.push("secure"),document.cookie=r.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Dl(t,e,n){let i=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(i||0==n)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const Ol=t=>t instanceof xl?{...t}:t;function Ml(t,e){e=e||{};const n={};function i(t,e,n,i){return za.isPlainObject(t)&&za.isPlainObject(e)?za.merge.call({caseless:i},t,e):za.isPlainObject(e)?za.merge({},e):za.isArray(e)?e.slice():e}function s(t,e,n,s){return za.isUndefined(e)?za.isUndefined(t)?void 0:i(void 0,t,0,s):i(t,e,0,s)}function o(t,e){if(!za.isUndefined(e))return i(void 0,e)}function r(t,e){return za.isUndefined(e)?za.isUndefined(t)?void 0:i(void 0,t):i(void 0,e)}function a(n,s,o){return o in e?i(n,s):o in t?i(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:a,headers:(t,e,n)=>s(Ol(t),Ol(e),0,!0)};return za.forEach(Object.keys(Object.assign({},t,e)),(function(i){const o=l[i]||s,r=o(t[i],e[i],i);za.isUndefined(r)&&o!==a||(n[i]=r)})),n}const jl=t=>{const e=Ml({},t);let n,{data:i,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:r,headers:a,auth:l}=e;if(e.headers=a=xl.from(a),e.url=nl(Dl(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),za.isFormData(i))if(hl.hasStandardBrowserEnv||hl.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[t,...e]=n?n.split(";").map((t=>t.trim())).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...e].join("; "))}if(hl.hasStandardBrowserEnv&&(s&&za.isFunction(s)&&(s=s(e)),s||!1!==s&&Rl(e.url))){const t=o&&r&&Cl.read(r);t&&a.set(o,t)}return e},Vl="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,n){const i=jl(t);let s=i.data;const o=xl.from(i.headers).normalize();let r,a,l,u,c,{responseType:h,onUploadProgress:d,onDownloadProgress:p}=i;function m(){u&&u(),c&&c(),i.cancelToken&&i.cancelToken.unsubscribe(r),i.signal&&i.signal.removeEventListener("abort",r)}let f=new XMLHttpRequest;function g(){if(!f)return;const i=xl.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders());Sl((function(t){e(t),m()}),(function(t){n(t),m()}),{data:h&&"text"!==h&&"json"!==h?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:i,config:t,request:f}),f=null}f.open(i.method.toUpperCase(),i.url,!0),f.timeout=i.timeout,"onloadend"in f?f.onloadend=g:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(g)},f.onabort=function(){f&&(n(new $a("Request aborted",$a.ECONNABORTED,t,f)),f=null)},f.onerror=function(){n(new $a("Network Error",$a.ERR_NETWORK,t,f)),f=null},f.ontimeout=function(){let e=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const s=i.transitional||sl;i.timeoutErrorMessage&&(e=i.timeoutErrorMessage),n(new $a(e,s.clarifyTimeoutError?$a.ETIMEDOUT:$a.ECONNABORTED,t,f)),f=null},void 0===s&&o.setContentType(null),"setRequestHeader"in f&&za.forEach(o.toJSON(),(function(t,e){f.setRequestHeader(e,t)})),za.isUndefined(i.withCredentials)||(f.withCredentials=!!i.withCredentials),h&&"json"!==h&&(f.responseType=i.responseType),p&&([l,c]=Pl(p,!0),f.addEventListener("progress",l)),d&&f.upload&&([a,u]=Pl(d),f.upload.addEventListener("progress",a),f.upload.addEventListener("loadend",u)),(i.cancelToken||i.signal)&&(r=e=>{f&&(n(!e||e.type?new Tl(null,t,f):e),f.abort(),f=null)},i.cancelToken&&i.cancelToken.subscribe(r),i.signal&&(i.signal.aborted?r():i.signal.addEventListener("abort",r)));const y=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(i.url);y&&-1===hl.protocols.indexOf(y)?n(new $a("Unsupported protocol "+y+":",$a.ERR_BAD_REQUEST,t)):f.send(s||null)}))},kl=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,i=new AbortController;const s=function(t){if(!n){n=!0,r();const e=t instanceof Error?t:this.reason;i.abort(e instanceof $a?e:new Tl(e instanceof Error?e.message:e))}};let o=e&&setTimeout((()=>{o=null,s(new $a(`timeout ${e} of ms exceeded`,$a.ETIMEDOUT))}),e);const r=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach((t=>{t.unsubscribe?t.unsubscribe(s):t.removeEventListener("abort",s)})),t=null)};t.forEach((t=>t.addEventListener("abort",s)));const{signal:a}=i;return a.unsubscribe=()=>za.asap(r),a}},Ll=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let i,s=0;for(;s<n;)i=s+e,yield t.slice(s,i),s=i},Fl=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);const e=t.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},Bl=(t,e,n,i)=>{const s=async function*(t,e){for await(const n of Fl(t))yield*Ll(n,e)}(t,e);let o,r=0,a=t=>{o||(o=!0,i&&i(t))};return new ReadableStream({async pull(t){try{const{done:e,value:i}=await s.next();if(e)return a(),void t.close();let o=i.byteLength;if(n){let t=r+=o;n(t)}t.enqueue(new Uint8Array(i))}catch(e){throw a(e),e}},cancel:t=>(a(t),s.return())},{highWaterMark:2})},Ul="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Nl=Ul&&"function"==typeof ReadableStream,Il=Ul&&("function"==typeof TextEncoder?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),_l=(t,...e)=>{try{return!!t(...e)}catch(n){return!1}},Wl=Nl&&_l((()=>{let t=!1;const e=new Request(hl.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e})),zl=Nl&&_l((()=>za.isReadableStream(new Response("").body))),$l={stream:zl&&(t=>t.body)};var ql;Ul&&(ql=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((t=>{!$l[t]&&($l[t]=za.isFunction(ql[t])?e=>e[t]():(e,n)=>{throw new $a(`Response type '${t}' is not supported`,$a.ERR_NOT_SUPPORT,n)})})));const Hl=async(t,e)=>{const n=za.toFiniteNumber(t.getContentLength());return null==n?(async t=>{if(null==t)return 0;if(za.isBlob(t))return t.size;if(za.isSpecCompliantForm(t)){const e=new Request(hl.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return za.isArrayBufferView(t)||za.isArrayBuffer(t)?t.byteLength:(za.isURLSearchParams(t)&&(t+=""),za.isString(t)?(await Il(t)).byteLength:void 0)})(e):n},Kl={http:null,xhr:Vl,fetch:Ul&&(async t=>{let{url:e,method:n,data:i,signal:s,cancelToken:o,timeout:r,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:h="same-origin",fetchOptions:d}=jl(t);u=u?(u+"").toLowerCase():"text";let p,m=kl([s,o&&o.toAbortSignal()],r);const f=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let g;try{if(l&&Wl&&"get"!==n&&"head"!==n&&0!==(g=await Hl(c,i))){let t,n=new Request(e,{method:"POST",body:i,duplex:"half"});if(za.isFormData(i)&&(t=n.headers.get("content-type"))&&c.setContentType(t),n.body){const[t,e]=El(g,Pl(Al(l)));i=Bl(n.body,65536,t,e)}}za.isString(h)||(h=h?"include":"omit");const s="credentials"in Request.prototype;p=new Request(e,{...d,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:i,duplex:"half",credentials:s?h:void 0});let o=await fetch(p,d);const r=zl&&("stream"===u||"response"===u);if(zl&&(a||r&&f)){const t={};["status","statusText","headers"].forEach((e=>{t[e]=o[e]}));const e=za.toFiniteNumber(o.headers.get("content-length")),[n,i]=a&&El(e,Pl(Al(a),!0))||[];o=new Response(Bl(o.body,65536,n,(()=>{i&&i(),f&&f()})),t)}u=u||"text";let y=await $l[za.findKey($l,u)||"text"](o,t);return!r&&f&&f(),await new Promise(((e,n)=>{Sl(e,n,{data:y,headers:xl.from(o.headers),status:o.status,statusText:o.statusText,config:t,request:p})}))}catch(y){if(f&&f(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new $a("Network Error",$a.ERR_NETWORK,t,p),{cause:y.cause||y});throw $a.from(y,y&&y.code,t,p)}})};za.forEach(Kl,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(n){}Object.defineProperty(t,"adapterName",{value:e})}}));const Xl=t=>`- ${t}`,Yl=t=>za.isFunction(t)||null===t||!1===t,Gl=t=>{t=za.isArray(t)?t:[t];const{length:e}=t;let n,i;const s={};for(let o=0;o<e;o++){let e;if(n=t[o],i=n,!Yl(n)&&(i=Kl[(e=String(n)).toLowerCase()],void 0===i))throw new $a(`Unknown adapter '${e}'`);if(i)break;s[e||"#"+o]=i}if(!i){const t=Object.entries(s).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new $a("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(Xl).join("\n"):" "+Xl(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i};function Jl(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Tl(null,t)}function Zl(t){Jl(t),t.headers=xl.from(t.headers),t.data=wl.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return Gl(t.adapter||pl.adapter)(t).then((function(e){return Jl(t),e.data=wl.call(t,t.transformResponse,e),e.headers=xl.from(e.headers),e}),(function(e){return bl(e)||(Jl(t),e&&e.response&&(e.response.data=wl.call(t,t.transformResponse,e.response),e.response.headers=xl.from(e.response.headers))),Promise.reject(e)}))}const Ql="1.10.0",tu={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{tu[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}}));const eu={};tu.transitional=function(t,e,n){return(i,s,o)=>{if(!1===t)throw new $a(function(t,e){return"[Axios v"+Ql+"] Transitional option '"+t+"'"+e+(n?". "+n:"")}(s," has been removed"+(e?" in "+e:"")),$a.ERR_DEPRECATED);return e&&!eu[s]&&(eu[s]=!0),!t||t(i,s,o)}},tu.spelling=function(t){return(t,e)=>!0};const nu={assertOptions:function(t,e,n){if("object"!=typeof t)throw new $a("options must be an object",$a.ERR_BAD_OPTION_VALUE);const i=Object.keys(t);let s=i.length;for(;s-- >0;){const o=i[s],r=e[o];if(r){const e=t[o],n=void 0===e||r(e,o,t);if(!0!==n)throw new $a("option "+o+" must be "+n,$a.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new $a("Unknown option "+o,$a.ERR_BAD_OPTION)}},validators:tu},iu=nu.validators;let su=class{constructor(t){this.defaults=t||{},this.interceptors={request:new il,response:new il}}async request(t,e){try{return await this._request(t,e)}catch(n){if(n instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const e=t.stack?t.stack.replace(/^.+\n/,""):"";try{n.stack?e&&!String(n.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+e):n.stack=e}catch(i){}}throw n}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Ml(this.defaults,e);const{transitional:n,paramsSerializer:i,headers:s}=e;void 0!==n&&nu.assertOptions(n,{silentJSONParsing:iu.transitional(iu.boolean),forcedJSONParsing:iu.transitional(iu.boolean),clarifyTimeoutError:iu.transitional(iu.boolean)},!1),null!=i&&(za.isFunction(i)?e.paramsSerializer={serialize:i}:nu.assertOptions(i,{encode:iu.function,serialize:iu.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),nu.assertOptions(e,{baseUrl:iu.spelling("baseURL"),withXsrfToken:iu.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let o=s&&za.merge(s.common,s[e.method]);s&&za.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete s[t]})),e.headers=xl.concat(o,s);const r=[];let a=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(a=a&&t.synchronous,r.unshift(t.fulfilled,t.rejected))}));const l=[];let u;this.interceptors.response.forEach((function(t){l.push(t.fulfilled,t.rejected)}));let c,h=0;if(!a){const t=[Zl.bind(this),void 0];for(t.unshift.apply(t,r),t.push.apply(t,l),c=t.length,u=Promise.resolve(e);h<c;)u=u.then(t[h++],t[h++]);return u}c=r.length;let d=e;for(h=0;h<c;){const t=r[h++],e=r[h++];try{d=t(d)}catch(p){e.call(this,p);break}}try{u=Zl.call(this,d)}catch(p){return Promise.reject(p)}for(h=0,c=l.length;h<c;)u=u.then(l[h++],l[h++]);return u}getUri(t){return nl(Dl((t=Ml(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}};za.forEach(["delete","get","head","options"],(function(t){su.prototype[t]=function(e,n){return this.request(Ml(n||{},{method:t,url:e,data:(n||{}).data}))}})),za.forEach(["post","put","patch"],(function(t){function e(e){return function(n,i,s){return this.request(Ml(s||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}su.prototype[t]=e(),su.prototype[t+"Form"]=e(!0)}));const ou={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ou).forEach((([t,e])=>{ou[e]=t}));const ru=function t(e){const n=new su(e),i=na(su.prototype.request,n);return za.extend(i,su.prototype,n,{allOwnKeys:!0}),za.extend(i,n,null,{allOwnKeys:!0}),i.create=function(n){return t(Ml(e,n))},i}(pl);ru.Axios=su,ru.CanceledError=Tl,ru.CancelToken=class t{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const n=this;this.promise.then((t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null})),this.promise.then=t=>{let e;const i=new Promise((t=>{n.subscribe(t),e=t})).then(t);return i.cancel=function(){n.unsubscribe(e)},i},t((function(t,i,s){n.reason||(n.reason=new Tl(t,i,s),e(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let e;return{token:new t((function(t){e=t})),cancel:e}}},ru.isCancel=bl,ru.VERSION=Ql,ru.toFormData=Ja,ru.AxiosError=$a,ru.Cancel=ru.CanceledError,ru.all=function(t){return Promise.all(t)},ru.spread=function(t){return function(e){return t.apply(null,e)}},ru.isAxiosError=function(t){return za.isObject(t)&&!0===t.isAxiosError},ru.mergeConfig=Ml,ru.AxiosHeaders=xl,ru.formToJSON=t=>dl(za.isHTMLForm(t)?new FormData(t):t),ru.getAdapter=Gl,ru.HttpStatusCode=ou,ru.default=ru;const{Axios:au,AxiosError:lu,CanceledError:uu,isCancel:cu,CancelToken:hu,VERSION:du,all:pu,Cancel:mu,isAxiosError:fu,spread:gu,toFormData:yu,AxiosHeaders:vu,HttpStatusCode:xu,formToJSON:wu,getAdapter:bu,mergeConfig:Tu}=ru;export{y as A,ru as a,i as c,ea as m};
