var t,e,n,r,s,i,a,o,c,l,u,h,f,d,p,g,m,y,w,A,b,E,v,k,x,P,N,B,I,O,C,R,T,S,U,F,M,L,D,G,H,Q,j,z,W,V,J,K,q,Z,_,Y,X,$,tt,et,nt,rt,st,it,at,ot,ct,lt,ut,ht,ft,dt,pt,gt,mt,yt,wt,At,bt,Et,vt,kt,xt,Pt,Nt,Bt,It,Ot,Ct,Rt,Tt,St,Ut,Ft,Mt,Lt,Dt,Gt,Ht,Qt,jt,zt,Wt,Vt,Jt,Kt,qt,Zt,_t,Yt,Xt,$t,te,ee,ne,re,se,ie,ae,oe,ce,le,ue,he,fe,de,pe,ge,me,ye,we,Ae,be,Ee,ve,ke,xe,<PERSON>e,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>e,<PERSON>,<PERSON>,<PERSON>,<PERSON>,Ge,He,Qe,je,ze,<PERSON>,Ve,Je,<PERSON>,qe,Ze,_e,Ye,Xe,$e,tn,en,nn,rn,sn,an,on,cn,ln,un,hn,fn,dn,pn=Object.defineProperty,gn=t=>{throw TypeError(t)},mn=(t,e,n)=>((t,e,n)=>e in t?pn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n)(t,"symbol"!=typeof e?e+"":e,n),yn=(t,e,n)=>e.has(t)||gn("Cannot "+n),wn=(t,e,n)=>(yn(t,e,"read from private field"),n?n.call(t):e.get(t)),An=(t,e,n)=>e.has(t)?gn("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),bn=(t,e,n,r)=>(yn(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),En=(t,e,n)=>(yn(t,e,"access private method"),n),vn=(t,e,n,r)=>({set _(r){bn(t,e,r,n)},get _(){return wn(t,e,r)}});function kn(t,e,n){const r=e.split("|").map((t=>t.trim()));for(let i=0;i<r.length;i++)switch(e){case"any":return;case"bigint":case"boolean":case"number":case"string":if(typeof t===e)return}const s=new Error(`invalid value for type ${e}`);throw s.code="INVALID_ARGUMENT",s.argument=`value.${n}`,s.value=t,s}async function xn(t){const e=Object.keys(t);return(await Promise.all(e.map((e=>Promise.resolve(t[e]))))).reduce(((t,n,r)=>(t[e[r]]=n,t)),{})}function Pn(t,e,n){for(let r in e){let s=e[r];const i=n?n[r]:null;i&&kn(s,i,r),Object.defineProperty(t,r,{enumerable:!0,value:s,writable:!1})}}function Nn(t,e){if(null==t)return"null";if(null==e&&(e=new Set),"object"==typeof t){if(e.has(t))return"[Circular]";e.add(t)}if(Array.isArray(t))return"[ "+t.map((t=>Nn(t,e))).join(", ")+" ]";if(t instanceof Uint8Array){const e="0123456789abcdef";let n="0x";for(let r=0;r<t.length;r++)n+=e[t[r]>>4],n+=e[15&t[r]];return n}if("object"==typeof t&&"function"==typeof t.toJSON)return Nn(t.toJSON(),e);switch(typeof t){case"boolean":case"number":case"symbol":return t.toString();case"bigint":return BigInt(t).toString();case"string":return JSON.stringify(t);case"object":{const n=Object.keys(t);return n.sort(),"{ "+n.map((n=>`${Nn(n,e)}: ${Nn(t[n],e)}`)).join(", ")+" }"}}return"[ COULD NOT SERIALIZE ]"}function Bn(t,e){return t&&t.code===e}function In(t){return Bn(t,"CALL_EXCEPTION")}function On(t,e,n){let r,s=t;{const r=[];if(n){if("message"in n||"code"in n||"name"in n)throw new Error(`value will overwrite populated values: ${Nn(n)}`);for(const t in n){if("shortMessage"===t)continue;const e=n[t];r.push(t+"="+Nn(e))}}r.push(`code=${e}`),r.push("version=6.14.4"),r.length&&(t+=" ("+r.join(", ")+")")}switch(e){case"INVALID_ARGUMENT":r=new TypeError(t);break;case"NUMERIC_FAULT":case"BUFFER_OVERRUN":r=new RangeError(t);break;default:r=new Error(t)}return Pn(r,{code:e}),n&&Object.assign(r,n),null==r.shortMessage&&Pn(r,{shortMessage:s}),r}function Cn(t,e,n,r){if(!t)throw On(e,n,r)}function Rn(t,e,n,r){Cn(t,e,"INVALID_ARGUMENT",{argument:n,value:r})}function Tn(t,e,n){null==n&&(n=""),n&&(n=": "+n),Cn(t>=e,"missing argument"+n,"MISSING_ARGUMENT",{count:t,expectedCount:e}),Cn(t<=e,"too many arguments"+n,"UNEXPECTED_ARGUMENT",{count:t,expectedCount:e})}function Sn(t,e,n){if(null==n&&(n=""),t!==e){let t=n,e="new";n&&(t+=".",e+=" "+n),Cn(!1,`private constructor; use ${t}from* methods`,"UNSUPPORTED_OPERATION",{operation:e})}}function Un(t,e,n){if(t instanceof Uint8Array)return n?new Uint8Array(t):t;if("string"==typeof t&&t.match(/^0x(?:[0-9a-f][0-9a-f])*$/i)){const e=new Uint8Array((t.length-2)/2);let n=2;for(let r=0;r<e.length;r++)e[r]=parseInt(t.substring(n,n+2),16),n+=2;return e}Rn(!1,"invalid BytesLike value",e||"value",t)}function Fn(t,e){return Un(t,e,!1)}function Mn(t,e){return Un(t,e,!0)}function Ln(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&(("number"!=typeof e||t.length===2+2*e)&&(!0!==e||t.length%2==0))}function Dn(t){return Ln(t,!0)||t instanceof Uint8Array}["NFD","NFC","NFKD","NFKC"].reduce(((t,e)=>{try{if("test"!=="test".normalize(e))throw new Error("bad");if("NFD"===e){const t=String.fromCharCode(233).normalize("NFD");if(t!==String.fromCharCode(101,769))throw new Error("broken")}t.push(e)}catch(n){}return t}),[]);const Gn="0123456789abcdef";function Hn(t){const e=Fn(t);let n="0x";for(let r=0;r<e.length;r++){const t=e[r];n+=Gn[(240&t)>>4]+Gn[15&t]}return n}function Qn(t){return"0x"+t.map((t=>Hn(t).substring(2))).join("")}function jn(t){return Ln(t,!0)?(t.length-2)/2:Fn(t).length}function zn(t,e,n){const r=Fn(t);return null!=n&&n>r.length&&Cn(!1,"cannot slice beyond data bounds","BUFFER_OVERRUN",{buffer:r,length:r.length,offset:n}),Hn(r.slice(null==e?0:e,null==n?r.length:n))}function Wn(t,e,n){const r=Fn(t);Cn(e>=r.length,"padding exceeds data length","BUFFER_OVERRUN",{buffer:new Uint8Array(r),length:e,offset:e+1});const s=new Uint8Array(e);return s.fill(0),n?s.set(r,e-r.length):s.set(r,0),Hn(s)}function Vn(t,e){return Wn(t,e,!0)}const Jn=BigInt(0),Kn=BigInt(1),qn=9007199254740991;function Zn(t,e){const n=$n(t,"value"),r=BigInt(nr(e,"width"));if(Cn(n>>r===Jn,"overflow","NUMERIC_FAULT",{operation:"fromTwos",fault:"overflow",value:t}),n>>r-Kn){return-((~n&(Kn<<r)-Kn)+Kn)}return n}function _n(t,e){let n=Xn(t,"value");const r=BigInt(nr(e,"width")),s=Kn<<r-Kn;if(n<Jn){n=-n,Cn(n<=s,"too low","NUMERIC_FAULT",{operation:"toTwos",fault:"overflow",value:t});return(~n&(Kn<<r)-Kn)+Kn}return Cn(n<s,"too high","NUMERIC_FAULT",{operation:"toTwos",fault:"overflow",value:t}),n}function Yn(t,e){const n=$n(t,"value"),r=BigInt(nr(e,"bits"));return n&(Kn<<r)-Kn}function Xn(t,e){switch(typeof t){case"bigint":return t;case"number":return Rn(Number.isInteger(t),"underflow",e||"value",t),Rn(t>=-qn&&t<=qn,"overflow",e||"value",t),BigInt(t);case"string":try{if(""===t)throw new Error("empty string");return"-"===t[0]&&"-"!==t[1]?-BigInt(t.substring(1)):BigInt(t)}catch(n){Rn(!1,`invalid BigNumberish string: ${n.message}`,e||"value",t)}}Rn(!1,"invalid BigNumberish value",e||"value",t)}function $n(t,e){const n=Xn(t,e);return Cn(n>=Jn,"unsigned value cannot be negative","NUMERIC_FAULT",{fault:"overflow",operation:"getUint",value:t}),n}const tr="0123456789abcdef";function er(t){if(t instanceof Uint8Array){let e="0x0";for(const n of t)e+=tr[n>>4],e+=tr[15&n];return BigInt(e)}return Xn(t)}function nr(t,e){switch(typeof t){case"bigint":return Rn(t>=-qn&&t<=qn,"overflow",e||"value",t),Number(t);case"number":return Rn(Number.isInteger(t),"underflow",e||"value",t),Rn(t>=-qn&&t<=qn,"overflow",e||"value",t),t;case"string":try{if(""===t)throw new Error("empty string");return nr(BigInt(t),e)}catch(n){Rn(!1,`invalid numeric string: ${n.message}`,e||"value",t)}}Rn(!1,"invalid numeric value",e||"value",t)}function rr(t,e){let n=$n(t,"value").toString(16);if(null==e)n.length%2&&(n="0"+n);else{const r=nr(e,"width");for(Cn(2*r>=n.length,`value exceeds width (${r} bytes)`,"NUMERIC_FAULT",{operation:"toBeHex",fault:"overflow",value:t});n.length<2*r;)n="0"+n}return"0x"+n}function sr(t){const e=$n(t,"value");if(e===Jn)return new Uint8Array([]);let n=e.toString(16);n.length%2&&(n="0"+n);const r=new Uint8Array(n.length/2);for(let s=0;s<r.length;s++){const t=2*s;r[s]=parseInt(n.substring(t,t+2),16)}return r}function ir(t){let e=Hn(Dn(t)?t:sr(t)).substring(2);for(;e.startsWith("0");)e=e.substring(1);return""===e&&(e="0"),"0x"+e}const ar="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";BigInt(0);const or=BigInt(58);class cr{constructor(e,n,r){mn(this,"filter"),mn(this,"emitter"),An(this,t),bn(this,t,n),Pn(this,{emitter:e,filter:r})}async removeListener(){null!=wn(this,t)&&await this.emitter.off(this.filter,wn(this,t))}}function lr(t,e,n,r,s){if("BAD_PREFIX"===t||"UNEXPECTED_CONTINUE"===t){let t=0;for(let r=e+1;r<n.length&&n[r]>>6==2;r++)t++;return t}return"OVERRUN"===t?n.length-e-1:0}t=new WeakMap;const ur=Object.freeze({error:function(t,e,n,r,s){Rn(!1,`invalid codepoint at offset ${e}; ${t}`,"bytes",n)},ignore:lr,replace:function(t,e,n,r,s){return"OVERLONG"===t?(Rn("number"==typeof s,"invalid bad code point for replacement","badCodepoint",s),r.push(s),0):(r.push(65533),lr(t,e,n))}});function hr(t,e){Rn("string"==typeof t,"invalid string value","str",t);let n=[];for(let r=0;r<t.length;r++){const e=t.charCodeAt(r);if(e<128)n.push(e);else if(e<2048)n.push(e>>6|192),n.push(63&e|128);else if(55296==(64512&e)){r++;const s=t.charCodeAt(r);Rn(r<t.length&&56320==(64512&s),"invalid surrogate pair","str",t);const i=65536+((1023&e)<<10)+(1023&s);n.push(i>>18|240),n.push(i>>12&63|128),n.push(i>>6&63|128),n.push(63&i|128)}else n.push(e>>12|224),n.push(e>>6&63|128),n.push(63&e|128)}return new Uint8Array(n)}function fr(t,e){return n=function(t,e){null==e&&(e=ur.error);const n=Fn(t,"bytes"),r=[];let s=0;for(;s<n.length;){const t=n[s++];if(!(t>>7)){r.push(t);continue}let i=null,a=null;if(192==(224&t))i=1,a=127;else if(224==(240&t))i=2,a=2047;else{if(240!=(248&t)){s+=e(128==(192&t)?"UNEXPECTED_CONTINUE":"BAD_PREFIX",s-1,n,r);continue}i=3,a=65535}if(s-1+i>=n.length){s+=e("OVERRUN",s-1,n,r);continue}let o=t&(1<<8-i-1)-1;for(let c=0;c<i;c++){let t=n[s];if(128!=(192&t)){s+=e("MISSING_CONTINUE",s,n,r),o=null;break}o=o<<6|63&t,s++}null!==o&&(o>1114111?s+=e("OUT_OF_RANGE",s-1-i,n,r,o):o>=55296&&o<=57343?s+=e("UTF16_SURROGATE",s-1-i,n,r,o):o<=a?s+=e("OVERLONG",s-1-i,n,r,o):r.push(o))}return r}(t,e),n.map((t=>t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10&1023),56320+(1023&t))))).join("");var n}function dr(t){return async function(e,n){Cn(null==n||!n.cancelled,"request cancelled before sending","CANCELLED");const r=e.url.split(":")[0].toLowerCase();Cn("http"===r||"https"===r,`unsupported protocol ${r}`,"UNSUPPORTED_OPERATION",{info:{protocol:r},operation:"request"}),Cn("https"===r||!e.credentials||e.allowInsecureAuthentication,"insecure authorized connections unsupported","UNSUPPORTED_OPERATION",{operation:"request"});let s=null;const i=new AbortController,a=setTimeout((()=>{s=On("request timeout","TIMEOUT"),i.abort()}),e.timeout);n&&n.addListener((()=>{s=On("request cancelled","CANCELLED"),i.abort()}));const o=Object.assign({},t,{method:e.method,headers:new Headers(Array.from(e)),body:e.body||void 0,signal:i.signal});let c;try{c=await fetch(e.url,o)}catch(f){if(clearTimeout(a),s)throw s;throw f}clearTimeout(a);const l={};c.headers.forEach(((t,e)=>{l[e.toLowerCase()]=t}));const u=await c.arrayBuffer(),h=null==u?null:new Uint8Array(u);return{statusCode:c.status,statusMessage:c.statusText,headers:l,body:h}}}let pr=dr();const gr=new RegExp("^data:([^;:]*)?(;base64)?,(.*)$","i"),mr=new RegExp("^ipfs://(ipfs/)?(.*)$","i");let yr=!1;async function wr(t,e){try{const e=t.match(gr);if(!e)throw new Error("invalid data");return new Br(200,"OK",{"content-type":e[1]||"text/plain"},e[2]?function(t){t=atob(t);const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return Fn(e)}(e[3]):hr(e[3].replace(/%([0-9a-f][0-9a-f])/gi,((t,e)=>String.fromCharCode(parseInt(e,16))))))}catch(n){return new Br(599,"BAD REQUEST (invalid data: URI)",{},null,new Pr(t))}}function Ar(t){return async function(e,n){try{const n=e.match(mr);if(!n)throw new Error("invalid link");return new Pr(`${t}${n[2]}`)}catch(r){return new Br(599,"BAD REQUEST (invalid IPFS URI)",{},null,new Pr(e))}}}const br={data:wr,ipfs:Ar("https://gateway.ipfs.io/ipfs/")},Er=new WeakMap;class vr{constructor(t){An(this,e),An(this,n),bn(this,e,[]),bn(this,n,!1),Er.set(t,(()=>{if(!wn(this,n)){bn(this,n,!0);for(const t of wn(this,e))setTimeout((()=>{t()}),0);bn(this,e,[])}}))}addListener(t){Cn(!wn(this,n),"singal already cancelled","UNSUPPORTED_OPERATION",{operation:"fetchCancelSignal.addCancelListener"}),wn(this,e).push(t)}get cancelled(){return wn(this,n)}checkSignal(){Cn(!this.cancelled,"cancelled","CANCELLED",{})}}function kr(t){if(null==t)throw new Error("missing signal; should not happen");return t.checkSignal(),t}e=new WeakMap,n=new WeakMap;const xr=class t{constructor(t){An(this,w),An(this,r),An(this,s),An(this,i),An(this,a),An(this,o),An(this,c),An(this,l),An(this,u),An(this,h),An(this,f),An(this,d),An(this,p),An(this,g),An(this,m),An(this,y),bn(this,c,String(t)),bn(this,r,!1),bn(this,s,!0),bn(this,i,{}),bn(this,a,""),bn(this,o,3e5),bn(this,m,{slotInterval:250,maxAttempts:12}),bn(this,y,null)}get url(){return wn(this,c)}set url(t){bn(this,c,String(t))}get body(){return null==wn(this,l)?null:new Uint8Array(wn(this,l))}set body(t){if(null==t)bn(this,l,void 0),bn(this,u,void 0);else if("string"==typeof t)bn(this,l,hr(t)),bn(this,u,"text/plain");else if(t instanceof Uint8Array)bn(this,l,t),bn(this,u,"application/octet-stream");else{if("object"!=typeof t)throw new Error("invalid body");bn(this,l,hr(JSON.stringify(t))),bn(this,u,"application/json")}}hasBody(){return null!=wn(this,l)}get method(){return wn(this,a)?wn(this,a):this.hasBody()?"POST":"GET"}set method(t){null==t&&(t=""),bn(this,a,String(t).toUpperCase())}get headers(){const t=Object.assign({},wn(this,i));return wn(this,h)&&(t.authorization=`Basic ${function(t){const e=Fn(t);let n="";for(let r=0;r<e.length;r++)n+=String.fromCharCode(e[r]);return btoa(n)}(hr(wn(this,h)))}`),this.allowGzip&&(t["accept-encoding"]="gzip"),null==t["content-type"]&&wn(this,u)&&(t["content-type"]=wn(this,u)),this.body&&(t["content-length"]=String(this.body.length)),t}getHeader(t){return this.headers[t.toLowerCase()]}setHeader(t,e){wn(this,i)[String(t).toLowerCase()]=String(e)}clearHeaders(){bn(this,i,{})}[Symbol.iterator](){const t=this.headers,e=Object.keys(t);let n=0;return{next:()=>{if(n<e.length){const r=e[n++];return{value:[r,t[r]],done:!1}}return{value:void 0,done:!0}}}}get credentials(){return wn(this,h)||null}setCredentials(t,e){Rn(!t.match(/:/),"invalid basic authentication username","username","[REDACTED]"),bn(this,h,`${t}:${e}`)}get allowGzip(){return wn(this,s)}set allowGzip(t){bn(this,s,!!t)}get allowInsecureAuthentication(){return!!wn(this,r)}set allowInsecureAuthentication(t){bn(this,r,!!t)}get timeout(){return wn(this,o)}set timeout(t){Rn(t>=0,"timeout must be non-zero","timeout",t),bn(this,o,t)}get preflightFunc(){return wn(this,f)||null}set preflightFunc(t){bn(this,f,t)}get processFunc(){return wn(this,d)||null}set processFunc(t){bn(this,d,t)}get retryFunc(){return wn(this,p)||null}set retryFunc(t){bn(this,p,t)}get getUrlFunc(){return wn(this,y)||pr}set getUrlFunc(t){bn(this,y,t)}toString(){return`<FetchRequest method=${JSON.stringify(this.method)} url=${JSON.stringify(this.url)} headers=${JSON.stringify(this.headers)} body=${wn(this,l)?Hn(wn(this,l)):"null"}>`}setThrottleParams(t){null!=t.slotInterval&&(wn(this,m).slotInterval=t.slotInterval),null!=t.maxAttempts&&(wn(this,m).maxAttempts=t.maxAttempts)}send(){return Cn(null==wn(this,g),"request already sent","UNSUPPORTED_OPERATION",{operation:"fetchRequest.send"}),bn(this,g,new vr(this)),En(this,w,A).call(this,0,Ir()+this.timeout,0,this,new Br(0,"",{},null,this))}cancel(){Cn(null!=wn(this,g),"request has not been sent","UNSUPPORTED_OPERATION",{operation:"fetchRequest.cancel"});const t=Er.get(this);if(!t)throw new Error("missing signal; should not happen");t()}redirect(e){const n=this.url.split(":")[0].toLowerCase(),r=e.split(":")[0].toLowerCase();Cn("GET"===this.method&&("https"!==n||"http"!==r)&&e.match(/^https?:/),"unsupported redirect","UNSUPPORTED_OPERATION",{operation:`redirect(${this.method} ${JSON.stringify(this.url)} => ${JSON.stringify(e)})`});const s=new t(e);return s.method="GET",s.allowGzip=this.allowGzip,s.timeout=this.timeout,bn(s,i,Object.assign({},wn(this,i))),wn(this,l)&&bn(s,l,new Uint8Array(wn(this,l))),bn(s,u,wn(this,u)),s}clone(){const e=new t(this.url);return bn(e,a,wn(this,a)),wn(this,l)&&bn(e,l,wn(this,l)),bn(e,u,wn(this,u)),bn(e,i,Object.assign({},wn(this,i))),bn(e,h,wn(this,h)),this.allowGzip&&(e.allowGzip=!0),e.timeout=this.timeout,this.allowInsecureAuthentication&&(e.allowInsecureAuthentication=!0),bn(e,f,wn(this,f)),bn(e,d,wn(this,d)),bn(e,p,wn(this,p)),bn(e,m,Object.assign({},wn(this,m))),bn(e,y,wn(this,y)),e}static lockConfig(){yr=!0}static getGateway(t){return br[t.toLowerCase()]||null}static registerGateway(t,e){if("http"===(t=t.toLowerCase())||"https"===t)throw new Error(`cannot intercept ${t}; use registerGetUrl`);if(yr)throw new Error("gateways locked");br[t]=e}static registerGetUrl(t){if(yr)throw new Error("gateways locked");pr=t}static createGetUrlFunc(t){return dr(t)}static createDataGateway(){return wr}static createIpfsGatewayFunc(t){return Ar(t)}};r=new WeakMap,s=new WeakMap,i=new WeakMap,a=new WeakMap,o=new WeakMap,c=new WeakMap,l=new WeakMap,u=new WeakMap,h=new WeakMap,f=new WeakMap,d=new WeakMap,p=new WeakMap,g=new WeakMap,m=new WeakMap,y=new WeakMap,w=new WeakSet,A=async function(t,e,n,r,s){var i,a,o;if(t>=wn(this,m).maxAttempts)return s.makeServerError("exceeded maximum retry limit");Cn(Ir()<=e,"timeout","TIMEOUT",{operation:"request.send",reason:"timeout",request:r}),n>0&&await function(t){return new Promise((e=>setTimeout(e,t)))}(n);let c=this.clone();const l=(c.url.split(":")[0]||"").toLowerCase();if(l in br){const t=await br[l](c.url,kr(wn(r,g)));if(t instanceof Br){let e=t;if(this.processFunc){kr(wn(r,g));try{e=await this.processFunc(c,e)}catch(f){null!=f.throttle&&"number"==typeof f.stall||e.makeServerError("error in post-processing function",f).assertOk()}}return e}c=t}this.preflightFunc&&(c=await this.preflightFunc(c));const u=await this.getUrlFunc(c,kr(wn(r,g)));let h=new Br(u.statusCode,u.statusMessage,u.headers,u.body,r);if(301===h.statusCode||302===h.statusCode){try{const n=h.headers.location||"";return En(i=c.redirect(n),w,A).call(i,t+1,e,0,r,h)}catch(f){}return h}if(429===h.statusCode&&(null==this.retryFunc||await this.retryFunc(c,h,t))){const n=h.headers["retry-after"];let s=wn(this,m).slotInterval*Math.trunc(Math.random()*Math.pow(2,t));return"string"==typeof n&&n.match(/^[1-9][0-9]*$/)&&(s=parseInt(n)),En(a=c.clone(),w,A).call(a,t+1,e,s,r,h)}if(this.processFunc){kr(wn(r,g));try{h=await this.processFunc(c,h)}catch(f){null!=f.throttle&&"number"==typeof f.stall||h.makeServerError("error in post-processing function",f).assertOk();let n=wn(this,m).slotInterval*Math.trunc(Math.random()*Math.pow(2,t));return f.stall>=0&&(n=f.stall),En(o=c.clone(),w,A).call(o,t+1,e,n,r,h)}}return h};let Pr=xr;const Nr=class t{constructor(t,e,n,r,s){An(this,b),An(this,E),An(this,v),An(this,k),An(this,x),An(this,P),bn(this,b,t),bn(this,E,e),bn(this,v,Object.keys(n).reduce(((t,e)=>(t[e.toLowerCase()]=String(n[e]),t)),{})),bn(this,k,null==r?null:new Uint8Array(r)),bn(this,x,s||null),bn(this,P,{message:""})}toString(){return`<FetchResponse status=${this.statusCode} body=${wn(this,k)?Hn(wn(this,k)):"null"}>`}get statusCode(){return wn(this,b)}get statusMessage(){return wn(this,E)}get headers(){return Object.assign({},wn(this,v))}get body(){return null==wn(this,k)?null:new Uint8Array(wn(this,k))}get bodyText(){try{return null==wn(this,k)?"":fr(wn(this,k))}catch(t){Cn(!1,"response body is not valid UTF-8 data","UNSUPPORTED_OPERATION",{operation:"bodyText",info:{response:this}})}}get bodyJson(){try{return JSON.parse(this.bodyText)}catch(t){Cn(!1,"response body is not valid JSON","UNSUPPORTED_OPERATION",{operation:"bodyJson",info:{response:this}})}}[Symbol.iterator](){const t=this.headers,e=Object.keys(t);let n=0;return{next:()=>{if(n<e.length){const r=e[n++];return{value:[r,t[r]],done:!1}}return{value:void 0,done:!0}}}}makeServerError(e,n){let r;r=e?`CLIENT ESCALATED SERVER ERROR (${this.statusCode} ${this.statusMessage}; ${e})`:`CLIENT ESCALATED SERVER ERROR (${e=`${this.statusCode} ${this.statusMessage}`})`;const s=new t(599,r,this.headers,this.body,wn(this,x)||void 0);return bn(s,P,{message:e,error:n}),s}throwThrottleError(t,e){null==e?e=-1:Rn(Number.isInteger(e)&&e>=0,"invalid stall timeout","stall",e);const n=new Error(t||"throttling requests");throw Pn(n,{stall:e,throttle:!0}),n}getHeader(t){return this.headers[t.toLowerCase()]}hasBody(){return null!=wn(this,k)}get request(){return wn(this,x)}ok(){return""===wn(this,P).message&&this.statusCode>=200&&this.statusCode<300}assertOk(){if(this.ok())return;let{message:t,error:e}=wn(this,P);""===t&&(t=`server response ${this.statusCode} ${this.statusMessage}`);let n=null;this.request&&(n=this.request.url);let r=null;try{wn(this,k)&&(r=fr(wn(this,k)))}catch(s){}Cn(!1,t,"SERVER_ERROR",{request:this.request||"unknown request",response:this,error:e,info:{requestUrl:n,responseBody:r,responseStatus:`${this.statusCode} ${this.statusMessage}`}})}};b=new WeakMap,E=new WeakMap,v=new WeakMap,k=new WeakMap,x=new WeakMap,P=new WeakMap;let Br=Nr;function Ir(){return(new Date).getTime()}const Or=BigInt(-1),Cr=BigInt(0),Rr=BigInt(1),Tr=BigInt(5),Sr={};let Ur="0000";for(;Ur.length<80;)Ur+=Ur;function Fr(t){let e=Ur;for(;e.length<t;)e+=e;return BigInt("1"+e.substring(0,t))}function Mr(t,e,n){const r=BigInt(e.width);if(e.signed){const e=Rr<<r-Rr;Cn(null==n||t>=-e&&t<e,"overflow","NUMERIC_FAULT",{operation:n,fault:"overflow",value:t}),t=t>Cr?Zn(Yn(t,r),r):-Zn(Yn(-t,r),r)}else{const e=Rr<<r;Cn(null==n||t>=0&&t<e,"overflow","NUMERIC_FAULT",{operation:n,fault:"overflow",value:t}),t=(t%e+e)%e&e-Rr}return t}function Lr(t){"number"==typeof t&&(t=`fixed128x${t}`);let e=!0,n=128,r=18;if("string"==typeof t)if("fixed"===t);else if("ufixed"===t)e=!1;else{const s=t.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);Rn(s,"invalid fixed format","format",t),e="u"!==s[1],n=parseInt(s[2]),r=parseInt(s[3])}else if(t){const s=t,i=(t,e,n)=>null==s[t]?n:(Rn(typeof s[t]===e,"invalid fixed format ("+t+" not "+e+")","format."+t,s[t]),s[t]);e=i("signed","boolean",e),n=i("width","number",n),r=i("decimals","number",r)}Rn(n%8==0,"invalid FixedNumber width (not byte aligned)","format.width",n),Rn(r<=80,"invalid FixedNumber decimals (too large)","format.decimals",r);return{signed:e,width:n,decimals:r,name:(e?"":"u")+"fixed"+String(n)+"x"+String(r)}}const Dr=class t{constructor(t,e,n){An(this,O),mn(this,"format"),An(this,N),An(this,B),An(this,I),mn(this,"_value"),Sn(t,Sr,"FixedNumber"),bn(this,B,e),bn(this,N,n);const r=function(t,e){let n="";t<Cr&&(n="-",t*=Or);let r=t.toString();if(0===e)return n+r;for(;r.length<=e;)r=Ur+r;const s=r.length-e;for(r=r.substring(0,s)+"."+r.substring(s);"0"===r[0]&&"."!==r[1];)r=r.substring(1);for(;"0"===r[r.length-1]&&"."!==r[r.length-2];)r=r.substring(0,r.length-1);return n+r}(e,n.decimals);Pn(this,{format:n.name,_value:r}),bn(this,I,Fr(n.decimals))}get signed(){return wn(this,N).signed}get width(){return wn(this,N).width}get decimals(){return wn(this,N).decimals}get value(){return wn(this,B)}addUnsafe(t){return En(this,O,T).call(this,t)}add(t){return En(this,O,T).call(this,t,"add")}subUnsafe(t){return En(this,O,S).call(this,t)}sub(t){return En(this,O,S).call(this,t,"sub")}mulUnsafe(t){return En(this,O,U).call(this,t)}mul(t){return En(this,O,U).call(this,t,"mul")}mulSignal(t){En(this,O,C).call(this,t);const e=wn(this,B)*wn(t,B);return Cn(e%wn(this,I)===Cr,"precision lost during signalling mul","NUMERIC_FAULT",{operation:"mulSignal",fault:"underflow",value:this}),En(this,O,R).call(this,e/wn(this,I),"mulSignal")}divUnsafe(t){return En(this,O,F).call(this,t)}div(t){return En(this,O,F).call(this,t,"div")}divSignal(t){Cn(wn(t,B)!==Cr,"division by zero","NUMERIC_FAULT",{operation:"div",fault:"divide-by-zero",value:this}),En(this,O,C).call(this,t);const e=wn(this,B)*wn(this,I);return Cn(e%wn(t,B)===Cr,"precision lost during signalling div","NUMERIC_FAULT",{operation:"divSignal",fault:"underflow",value:this}),En(this,O,R).call(this,e/wn(t,B),"divSignal")}cmp(t){let e=this.value,n=t.value;const r=this.decimals-t.decimals;return r>0?n*=Fr(r):r<0&&(e*=Fr(-r)),e<n?-1:e>n?1:0}eq(t){return 0===this.cmp(t)}lt(t){return this.cmp(t)<0}lte(t){return this.cmp(t)<=0}gt(t){return this.cmp(t)>0}gte(t){return this.cmp(t)>=0}floor(){let t=wn(this,B);return wn(this,B)<Cr&&(t-=wn(this,I)-Rr),t=wn(this,B)/wn(this,I)*wn(this,I),En(this,O,R).call(this,t,"floor")}ceiling(){let t=wn(this,B);return wn(this,B)>Cr&&(t+=wn(this,I)-Rr),t=wn(this,B)/wn(this,I)*wn(this,I),En(this,O,R).call(this,t,"ceiling")}round(e){if(null==e&&(e=0),e>=this.decimals)return this;const n=this.decimals-e,r=Tr*Fr(n-1);let s=this.value+r;const i=Fr(n);return s=s/i*i,Mr(s,wn(this,N),"round"),new t(Sr,s,wn(this,N))}isZero(){return wn(this,B)===Cr}isNegative(){return wn(this,B)<Cr}toString(){return this._value}toUnsafeFloat(){return parseFloat(this.toString())}toFormat(e){return t.fromString(this.toString(),e)}static fromValue(e,n,r){const s=null==n?0:nr(n),i=Lr(r);let a=Xn(e,"value");const o=s-i.decimals;if(o>0){const t=Fr(o);Cn(a%t===Cr,"value loses precision for format","NUMERIC_FAULT",{operation:"fromValue",fault:"underflow",value:e}),a/=t}else o<0&&(a*=Fr(-o));return Mr(a,i,"fromValue"),new t(Sr,a,i)}static fromString(e,n){const r=e.match(/^(-?)([0-9]*)\.?([0-9]*)$/);Rn(r&&r[2].length+r[3].length>0,"invalid FixedNumber string value","value",e);const s=Lr(n);let i=r[2]||"0",a=r[3]||"";for(;a.length<s.decimals;)a+=Ur;Cn(a.substring(s.decimals).match(/^0*$/),"too many decimals for format","NUMERIC_FAULT",{operation:"fromString",fault:"underflow",value:e}),a=a.substring(0,s.decimals);const o=BigInt(r[1]+i+a);return Mr(o,s,"fromString"),new t(Sr,o,s)}static fromBytes(e,n){let r=er(Fn(e,"value"));const s=Lr(n);return s.signed&&(r=Zn(r,s.width)),Mr(r,s,"fromBytes"),new t(Sr,r,s)}};N=new WeakMap,B=new WeakMap,I=new WeakMap,O=new WeakSet,C=function(t){Rn(this.format===t.format,"incompatible format; use fixedNumber.toFormat","other",t)},R=function(t,e){return t=Mr(t,wn(this,N),e),new Dr(Sr,t,wn(this,N))},T=function(t,e){return En(this,O,C).call(this,t),En(this,O,R).call(this,wn(this,B)+wn(t,B),e)},S=function(t,e){return En(this,O,C).call(this,t),En(this,O,R).call(this,wn(this,B)-wn(t,B),e)},U=function(t,e){return En(this,O,C).call(this,t),En(this,O,R).call(this,wn(this,B)*wn(t,B)/wn(this,I),e)},F=function(t,e){return Cn(wn(t,B)!==Cr,"division by zero","NUMERIC_FAULT",{operation:"div",fault:"divide-by-zero",value:this}),En(this,O,C).call(this,t),En(this,O,R).call(this,wn(this,B)*wn(this,I)/wn(t,B),e)};let Gr=Dr;function Hr(t){let e=t.toString(16);for(;e.length<2;)e="0"+e;return"0x"+e}function Qr(t,e,n){let r=0;for(let s=0;s<n;s++)r=256*r+t[e+s];return r}function jr(t,e,n,r){const s=[];for(;n<e+1+r;){const i=zr(t,n);s.push(i.result),Cn((n+=i.consumed)<=e+1+r,"child data too short","BUFFER_OVERRUN",{buffer:t,length:r,offset:e})}return{consumed:1+r,result:s}}function zr(t,e){Cn(0!==t.length,"data too short","BUFFER_OVERRUN",{buffer:t,length:0,offset:1});const n=e=>{Cn(e<=t.length,"data short segment too short","BUFFER_OVERRUN",{buffer:t,length:t.length,offset:e})};if(t[e]>=248){const r=t[e]-247;n(e+1+r);const s=Qr(t,e+1,r);return n(e+1+r+s),jr(t,e,e+1+r,r+s)}if(t[e]>=192){const r=t[e]-192;return n(e+1+r),jr(t,e,e+1,r)}if(t[e]>=184){const r=t[e]-183;n(e+1+r);const s=Qr(t,e+1,r);n(e+1+r+s);return{consumed:1+r+s,result:Hn(t.slice(e+1+r,e+1+r+s))}}if(t[e]>=128){const r=t[e]-128;n(e+1+r);return{consumed:1+r,result:Hn(t.slice(e+1,e+1+r))}}return{consumed:1,result:Hr(t[e])}}function Wr(t){const e=Fn(t,"data"),n=zr(e,0);return Rn(n.consumed===e.length,"unexpected junk after rlp payload","data",t),n.result}function Vr(t){const e=[];for(;t;)e.unshift(255&t),t>>=8;return e}function Jr(t){if(Array.isArray(t)){let e=[];if(t.forEach((function(t){e=e.concat(Jr(t))})),e.length<=55)return e.unshift(192+e.length),e;const n=Vr(e.length);return n.unshift(247+n.length),n.concat(e)}const e=Array.prototype.slice.call(Fn(t,"object"));if(1===e.length&&e[0]<=127)return e;if(e.length<=55)return e.unshift(128+e.length),e;const n=Vr(e.length);return n.unshift(183+n.length),n.concat(e)}const Kr="0123456789abcdef";function qr(t){let e="0x";for(const n of Jr(t))e+=Kr[n>>4],e+=Kr[15&n];return e}const Zr=["wei","kwei","mwei","gwei","szabo","finney","ether"];function _r(t,e){let n=18;if("string"==typeof e){const t=Zr.indexOf(e);Rn(t>=0,"invalid unit","unit",e),n=3*t}else null!=e&&(n=nr(e,"unit"));return Gr.fromValue(t,n,{decimals:n,width:512}).toString()}function Yr(t){return _r(t,18)}const Xr=32,$r=new Uint8Array(Xr),ts=["then"],es={},ns=new WeakMap;function rs(t){return ns.get(t)}function ss(t,e){ns.set(t,e)}function is(t,e){const n=new Error(`deferred error during ABI decoding triggered accessing ${t}`);throw n.error=e,n}function as(t,e,n){return t.indexOf(null)>=0?e.map(((t,e)=>t instanceof os?as(rs(t),t,n):t)):t.reduce(((t,r,s)=>{let i=e.getValue(r);return r in t||(n&&i instanceof os&&(i=as(rs(i),i,n)),t[r]=i),t}),{})}M=new WeakMap;let os=class t extends Array{constructor(...t){const e=t[0];let n=t[1],r=(t[2]||[]).slice(),s=!0;e!==es&&(n=t,r=[],s=!1),super(n.length),An(this,M),n.forEach(((t,e)=>{this[e]=t}));const i=r.reduce(((t,e)=>("string"==typeof e&&t.set(e,(t.get(e)||0)+1),t)),new Map);if(ss(this,Object.freeze(n.map(((t,e)=>{const n=r[e];return null!=n&&1===i.get(n)?n:null})))),bn(this,M,[]),null==wn(this,M)&&wn(this,M),!s)return;Object.freeze(this);const a=new Proxy(this,{get:(t,e,n)=>{if("string"==typeof e){if(e.match(/^[0-9]+$/)){const n=nr(e,"%index");if(n<0||n>=this.length)throw new RangeError("out of result range");const r=t[n];return r instanceof Error&&is(`index ${n}`,r),r}if(ts.indexOf(e)>=0)return Reflect.get(t,e,n);const r=t[e];if(r instanceof Function)return function(...e){return r.apply(this===n?t:this,e)};if(!(e in t))return t.getValue.apply(this===n?t:this,[e])}return Reflect.get(t,e,n)}});return ss(a,rs(this)),a}toArray(e){const n=[];return this.forEach(((r,s)=>{r instanceof Error&&is(`index ${s}`,r),e&&r instanceof t&&(r=r.toArray(e)),n.push(r)})),n}toObject(t){const e=rs(this);return e.reduce(((n,r,s)=>(Cn(null!=r,`value at index ${s} unnamed`,"UNSUPPORTED_OPERATION",{operation:"toObject()"}),as(e,this,t))),{})}slice(e,n){null==e&&(e=0),e<0&&(e+=this.length)<0&&(e=0),null==n&&(n=this.length),n<0&&(n+=this.length)<0&&(n=0),n>this.length&&(n=this.length);const r=rs(this),s=[],i=[];for(let t=e;t<n;t++)s.push(this[t]),i.push(r[t]);return new t(es,s,i)}filter(e,n){const r=rs(this),s=[],i=[];for(let t=0;t<this.length;t++){const a=this[t];a instanceof Error&&is(`index ${t}`,a),e.call(n,a,t,this)&&(s.push(a),i.push(r[t]))}return new t(es,s,i)}map(t,e){const n=[];for(let r=0;r<this.length;r++){const s=this[r];s instanceof Error&&is(`index ${r}`,s),n.push(t.call(e,s,r,this))}return n}getValue(t){const e=rs(this).indexOf(t);if(-1===e)return;const n=this[e];return n instanceof Error&&is(`property ${JSON.stringify(t)}`,n.error),n}static fromItems(e,n){return new t(es,e,n)}};function cs(t){let e=sr(t);return Cn(e.length<=Xr,"value out-of-bounds","BUFFER_OVERRUN",{buffer:e,length:Xr,offset:e.length}),e.length!==Xr&&(e=Mn(Qn([$r.slice(e.length%Xr),e]))),e}class ls{constructor(t,e,n,r){mn(this,"name"),mn(this,"type"),mn(this,"localName"),mn(this,"dynamic"),Pn(this,{name:t,type:e,localName:n,dynamic:r},{name:"string",type:"string",localName:"string",dynamic:"boolean"})}_throwError(t,e){Rn(!1,t,this.localName,e)}}class us{constructor(){An(this,G),An(this,L),An(this,D),bn(this,L,[]),bn(this,D,0)}get data(){return Qn(wn(this,L))}get length(){return wn(this,D)}appendWriter(t){return En(this,G,H).call(this,Mn(t.data))}writeBytes(t){let e=Mn(t);const n=e.length%Xr;return n&&(e=Mn(Qn([e,$r.slice(n)]))),En(this,G,H).call(this,e)}writeValue(t){return En(this,G,H).call(this,cs(t))}writeUpdatableValue(){const t=wn(this,L).length;return wn(this,L).push($r),bn(this,D,wn(this,D)+Xr),e=>{wn(this,L)[t]=cs(e)}}}L=new WeakMap,D=new WeakMap,G=new WeakSet,H=function(t){return wn(this,L).push(t),bn(this,D,wn(this,D)+t.length),t.length};Q=new WeakMap,j=new WeakMap,z=new WeakMap,W=new WeakMap,V=new WeakMap,J=new WeakSet,K=function(t){var e;if(wn(this,W))return En(e=wn(this,W),J,K).call(e,t);bn(this,z,wn(this,z)+t),Cn(wn(this,V)<1||wn(this,z)<=wn(this,V)*this.dataLength,`compressed ABI data exceeds inflation ratio of ${wn(this,V)} ( see: https://github.com/ethers-io/ethers.js/issues/4537 )`,"BUFFER_OVERRUN",{buffer:Mn(wn(this,Q)),offset:wn(this,j),length:t,info:{bytesRead:wn(this,z),dataLength:this.dataLength}})},q=function(t,e,n){let r=Math.ceil(e/Xr)*Xr;return wn(this,j)+r>wn(this,Q).length&&(this.allowLoose&&n&&wn(this,j)+e<=wn(this,Q).length?r=e:Cn(!1,"data out-of-bounds","BUFFER_OVERRUN",{buffer:Mn(wn(this,Q)),length:wn(this,Q).length,offset:wn(this,j)+r})),wn(this,Q).slice(wn(this,j),wn(this,j)+r)};let hs=class t{constructor(t,e,n){An(this,J),mn(this,"allowLoose"),An(this,Q),An(this,j),An(this,z),An(this,W),An(this,V),Pn(this,{allowLoose:!!e}),bn(this,Q,Mn(t)),bn(this,z,0),bn(this,W,null),bn(this,V,null!=n?n:1024),bn(this,j,0)}get data(){return Hn(wn(this,Q))}get dataLength(){return wn(this,Q).length}get consumed(){return wn(this,j)}get bytes(){return new Uint8Array(wn(this,Q))}subReader(e){const n=new t(wn(this,Q).slice(wn(this,j)+e),this.allowLoose,wn(this,V));return bn(n,W,this),n}readBytes(t,e){let n=En(this,J,q).call(this,0,t,!!e);return En(this,J,K).call(this,t),bn(this,j,wn(this,j)+n.length),n.slice(0,t)}readValue(){return er(this.readBytes(Xr))}readIndex(){return nr(er(this.readBytes(Xr)))}};function fs(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function ds(t,...e){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}function ps(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function gs(t,e){ds(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}const ms="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,ys=t=>t instanceof Uint8Array,ws=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),As=(t,e)=>t<<32-e|t>>>e;
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function bs(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!ys(t))throw new Error("expected Uint8Array, got "+typeof t);return t}class Es{clone(){return this._cloneInto()}}function vs(t){const e=e=>t().update(bs(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}function ks(t=32){if(ms&&"function"==typeof ms.getRandomValues)return ms.getRandomValues(new Uint8Array(t));throw new Error("crypto.getRandomValues must be defined")}class xs extends Es{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");fs(t.outputLen),fs(t.blockLen)}(t);const n=bs(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const r=this.blockLen,s=new Uint8Array(r);s.set(n.length>r?t.create().update(n).digest():n);for(let i=0;i<s.length;i++)s[i]^=54;this.iHash.update(s),this.oHash=t.create();for(let i=0;i<s.length;i++)s[i]^=106;this.oHash.update(s),s.fill(0)}update(t){return ps(this),this.iHash.update(t),this}digestInto(t){ps(this),ds(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:n,finished:r,destroyed:s,blockLen:i,outputLen:a}=this;return t.finished=r,t.destroyed=s,t.blockLen=i,t.outputLen=a,t.oHash=e._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Ps=(t,e,n)=>new xs(t,e).update(n).digest();Ps.create=(t,e)=>new xs(t,e);class Ns extends Es{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=ws(this.buffer)}update(t){ps(this);const{view:e,buffer:n,blockLen:r}=this,s=(t=bs(t)).length;for(let i=0;i<s;){const a=Math.min(r-this.pos,s-i);if(a!==r)n.set(t.subarray(i,i+a),this.pos),this.pos+=a,i+=a,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=ws(t);for(;r<=s-i;i+=r)this.process(e,i)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){ps(this),gs(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:s}=this;let{pos:i}=this;e[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>r-i&&(this.process(n,0),i=0);for(let u=i;u<r;u++)e[u]=0;!function(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),a=Number(n>>s&i),o=Number(n&i),c=r?4:0,l=r?0:4;t.setUint32(e+c,a,r),t.setUint32(e+l,o,r)}(n,r-8,BigInt(8*this.length),s),this.process(n,0);const a=ws(t),o=this.outputLen;if(o%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const c=o/4,l=this.get();if(c>l.length)throw new Error("_sha2: outputLen bigger than state");for(let u=0;u<c;u++)a.setUint32(4*u,l[u],s)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:a}=this;return t.length=r,t.pos=a,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}}const Bs=(t,e,n)=>t&e^t&n^e&n,Is=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Os=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Cs=new Uint32Array(64);class Rs extends Ns{constructor(){super(64,32,8,!1),this.A=0|Os[0],this.B=0|Os[1],this.C=0|Os[2],this.D=0|Os[3],this.E=0|Os[4],this.F=0|Os[5],this.G=0|Os[6],this.H=0|Os[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:a,H:o}=this;return[t,e,n,r,s,i,a,o]}set(t,e,n,r,s,i,a,o){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|a,this.H=0|o}process(t,e){for(let h=0;h<16;h++,e+=4)Cs[h]=t.getUint32(e,!1);for(let h=16;h<64;h++){const t=Cs[h-15],e=Cs[h-2],n=As(t,7)^As(t,18)^t>>>3,r=As(e,17)^As(e,19)^e>>>10;Cs[h]=r+Cs[h-7]+n+Cs[h-16]|0}let{A:n,B:r,C:s,D:i,E:a,F:o,G:c,H:l}=this;for(let h=0;h<64;h++){const t=l+(As(a,6)^As(a,11)^As(a,25))+((u=a)&o^~u&c)+Is[h]+Cs[h]|0,e=(As(n,2)^As(n,13)^As(n,22))+Bs(n,r,s)|0;l=c,c=o,o=a,a=i+t|0,i=s,s=r,r=n,n=t+e|0}var u;n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,a=a+this.E|0,o=o+this.F|0,c=c+this.G|0,l=l+this.H|0,this.set(n,r,s,i,a,o,c,l)}roundClean(){Cs.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Ts=vs((()=>new Rs)),Ss=BigInt(2**32-1),Us=BigInt(32);function Fs(t,e=!1){return e?{h:Number(t&Ss),l:Number(t>>Us&Ss)}:{h:0|Number(t>>Us&Ss),l:0|Number(t&Ss)}}function Ms(t,e=!1){let n=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:i,l:a}=Fs(t[s],e);[n[s],r[s]]=[i,a]}return[n,r]}const Ls=(t,e,n)=>t<<n|e>>>32-n,Ds=(t,e,n)=>e<<n|t>>>32-n,Gs=(t,e,n)=>e<<n-32|t>>>64-n,Hs=(t,e,n)=>t<<n-32|e>>>64-n;const Qs={fromBig:Fs,split:Ms,toBig:(t,e)=>BigInt(t>>>0)<<Us|BigInt(e>>>0),shrSH:(t,e,n)=>t>>>n,shrSL:(t,e,n)=>t<<32-n|e>>>n,rotrSH:(t,e,n)=>t>>>n|e<<32-n,rotrSL:(t,e,n)=>t<<32-n|e>>>n,rotrBH:(t,e,n)=>t<<64-n|e>>>n-32,rotrBL:(t,e,n)=>t>>>n-32|e<<64-n,rotr32H:(t,e)=>e,rotr32L:(t,e)=>t,rotlSH:Ls,rotlSL:Ds,rotlBH:Gs,rotlBL:Hs,add:function(t,e,n,r){const s=(e>>>0)+(r>>>0);return{h:t+n+(s/2**32|0)|0,l:0|s}},add3L:(t,e,n)=>(t>>>0)+(e>>>0)+(n>>>0),add3H:(t,e,n,r)=>e+n+r+(t/2**32|0)|0,add4L:(t,e,n,r)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0),add4H:(t,e,n,r,s)=>e+n+r+s+(t/2**32|0)|0,add5H:(t,e,n,r,s,i)=>e+n+r+s+i+(t/2**32|0)|0,add5L:(t,e,n,r,s)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0)+(s>>>0)},[js,zs]=(()=>Qs.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map((t=>BigInt(t)))))(),Ws=new Uint32Array(80),Vs=new Uint32Array(80);class Js extends Ns{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:t,Al:e,Bh:n,Bl:r,Ch:s,Cl:i,Dh:a,Dl:o,Eh:c,El:l,Fh:u,Fl:h,Gh:f,Gl:d,Hh:p,Hl:g}=this;return[t,e,n,r,s,i,a,o,c,l,u,h,f,d,p,g]}set(t,e,n,r,s,i,a,o,c,l,u,h,f,d,p,g){this.Ah=0|t,this.Al=0|e,this.Bh=0|n,this.Bl=0|r,this.Ch=0|s,this.Cl=0|i,this.Dh=0|a,this.Dl=0|o,this.Eh=0|c,this.El=0|l,this.Fh=0|u,this.Fl=0|h,this.Gh=0|f,this.Gl=0|d,this.Hh=0|p,this.Hl=0|g}process(t,e){for(let w=0;w<16;w++,e+=4)Ws[w]=t.getUint32(e),Vs[w]=t.getUint32(e+=4);for(let w=16;w<80;w++){const t=0|Ws[w-15],e=0|Vs[w-15],n=Qs.rotrSH(t,e,1)^Qs.rotrSH(t,e,8)^Qs.shrSH(t,e,7),r=Qs.rotrSL(t,e,1)^Qs.rotrSL(t,e,8)^Qs.shrSL(t,e,7),s=0|Ws[w-2],i=0|Vs[w-2],a=Qs.rotrSH(s,i,19)^Qs.rotrBH(s,i,61)^Qs.shrSH(s,i,6),o=Qs.rotrSL(s,i,19)^Qs.rotrBL(s,i,61)^Qs.shrSL(s,i,6),c=Qs.add4L(r,o,Vs[w-7],Vs[w-16]),l=Qs.add4H(c,n,a,Ws[w-7],Ws[w-16]);Ws[w]=0|l,Vs[w]=0|c}let{Ah:n,Al:r,Bh:s,Bl:i,Ch:a,Cl:o,Dh:c,Dl:l,Eh:u,El:h,Fh:f,Fl:d,Gh:p,Gl:g,Hh:m,Hl:y}=this;for(let w=0;w<80;w++){const t=Qs.rotrSH(u,h,14)^Qs.rotrSH(u,h,18)^Qs.rotrBH(u,h,41),e=Qs.rotrSL(u,h,14)^Qs.rotrSL(u,h,18)^Qs.rotrBL(u,h,41),A=u&f^~u&p,b=h&d^~h&g,E=Qs.add5L(y,e,b,zs[w],Vs[w]),v=Qs.add5H(E,m,t,A,js[w],Ws[w]),k=0|E,x=Qs.rotrSH(n,r,28)^Qs.rotrBH(n,r,34)^Qs.rotrBH(n,r,39),P=Qs.rotrSL(n,r,28)^Qs.rotrBL(n,r,34)^Qs.rotrBL(n,r,39),N=n&s^n&a^s&a,B=r&i^r&o^i&o;m=0|p,y=0|g,p=0|f,g=0|d,f=0|u,d=0|h,({h:u,l:h}=Qs.add(0|c,0|l,0|v,0|k)),c=0|a,l=0|o,a=0|s,o=0|i,s=0|n,i=0|r;const I=Qs.add3L(k,P,B);n=Qs.add3H(I,v,x,N),r=0|I}({h:n,l:r}=Qs.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:s,l:i}=Qs.add(0|this.Bh,0|this.Bl,0|s,0|i)),({h:a,l:o}=Qs.add(0|this.Ch,0|this.Cl,0|a,0|o)),({h:c,l:l}=Qs.add(0|this.Dh,0|this.Dl,0|c,0|l)),({h:u,l:h}=Qs.add(0|this.Eh,0|this.El,0|u,0|h)),({h:f,l:d}=Qs.add(0|this.Fh,0|this.Fl,0|f,0|d)),({h:p,l:g}=Qs.add(0|this.Gh,0|this.Gl,0|p,0|g)),({h:m,l:y}=Qs.add(0|this.Hh,0|this.Hl,0|m,0|y)),this.set(n,r,s,i,a,o,c,l,u,h,f,d,p,g,m,y)}roundClean(){Ws.fill(0),Vs.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const Ks=vs((()=>new Js));const qs=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();qs.crypto||qs.msCrypto;const[Zs,_s,Ys]=[[],[],[]],Xs=BigInt(0),$s=BigInt(1),ti=BigInt(2),ei=BigInt(7),ni=BigInt(256),ri=BigInt(113);for(let uf=0,hf=$s,ff=1,df=0;uf<24;uf++){[ff,df]=[df,(2*ff+3*df)%5],Zs.push(2*(5*df+ff)),_s.push((uf+1)*(uf+2)/2%64);let t=Xs;for(let e=0;e<7;e++)hf=(hf<<$s^(hf>>ei)*ri)%ni,hf&ti&&(t^=$s<<($s<<BigInt(e))-$s);Ys.push(t)}const[si,ii]=Ms(Ys,!0),ai=(t,e,n)=>n>32?Gs(t,e,n):Ls(t,e,n),oi=(t,e,n)=>n>32?Hs(t,e,n):Ds(t,e,n);class ci extends Es{constructor(t,e,n,r=!1,s=24){if(super(),this.blockLen=t,this.suffix=e,this.outputLen=n,this.enableXOF=r,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,fs(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");var i;this.state=new Uint8Array(200),this.state32=(i=this.state,new Uint32Array(i.buffer,i.byteOffset,Math.floor(i.byteLength/4)))}keccak(){!function(t,e=24){const n=new Uint32Array(10);for(let r=24-e;r<24;r++){for(let r=0;r<10;r++)n[r]=t[r]^t[r+10]^t[r+20]^t[r+30]^t[r+40];for(let r=0;r<10;r+=2){const e=(r+8)%10,s=(r+2)%10,i=n[s],a=n[s+1],o=ai(i,a,1)^n[e],c=oi(i,a,1)^n[e+1];for(let n=0;n<50;n+=10)t[r+n]^=o,t[r+n+1]^=c}let e=t[2],s=t[3];for(let n=0;n<24;n++){const r=_s[n],i=ai(e,s,r),a=oi(e,s,r),o=Zs[n];e=t[o],s=t[o+1],t[o]=i,t[o+1]=a}for(let r=0;r<50;r+=10){for(let e=0;e<10;e++)n[e]=t[r+e];for(let e=0;e<10;e++)t[r+e]^=~n[(e+2)%10]&n[(e+4)%10]}t[0]^=si[r],t[1]^=ii[r]}n.fill(0)}(this.state32,this.rounds),this.posOut=0,this.pos=0}update(t){ps(this);const{blockLen:e,state:n}=this,r=(t=bs(t)).length;for(let s=0;s<r;){const i=Math.min(e-this.pos,r-s);for(let e=0;e<i;e++)n[this.pos++]^=t[s++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:e,pos:n,blockLen:r}=this;t[n]^=e,128&e&&n===r-1&&this.keccak(),t[r-1]^=128,this.keccak()}writeInto(t){ps(this,!1),ds(t),this.finish();const e=this.state,{blockLen:n}=this;for(let r=0,s=t.length;r<s;){this.posOut>=n&&this.keccak();const i=Math.min(n-this.posOut,s-r);t.set(e.subarray(this.posOut,this.posOut+i),r),this.posOut+=i,r+=i}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return fs(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(gs(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){const{blockLen:e,suffix:n,outputLen:r,rounds:s,enableXOF:i}=this;return t||(t=new ci(e,n,r,i,s)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=s,t.suffix=n,t.outputLen=r,t.enableXOF=i,t.destroyed=this.destroyed,t}}const li=((t,e,n)=>vs((()=>new ci(e,t,n))))(1,136,32);let ui=!1;const hi=function(t){return li(t)};let fi=hi;function di(t){const e=Fn(t,"data");return Hn(fi(e))}di._=hi,di.lock=function(){ui=!0},di.register=function(t){if(ui)throw new TypeError("keccak256 is locked");fi=t},Object.freeze(di);const pi=function(t){return function(t){switch(t){case"sha256":return Ts.create();case"sha512":return Ks.create()}Rn(!1,"invalid hashing algorithm name","algorithm",t)}("sha256").update(t).digest()};let gi=pi,mi=!1;function yi(t){const e=Fn(t,"data");return Hn(gi(e))}yi._=pi,yi.lock=function(){mi=!0},yi.register=function(t){if(mi)throw new Error("sha256 is locked");gi=t},Object.freeze(yi),Object.freeze(yi);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const wi=BigInt(0),Ai=BigInt(1),bi=BigInt(2),Ei=t=>t instanceof Uint8Array,vi=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function ki(t){if(!Ei(t))throw new Error("Uint8Array expected");let e="";for(let n=0;n<t.length;n++)e+=vi[t[n]];return e}function xi(t){const e=t.toString(16);return 1&e.length?`0${e}`:e}function Pi(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);return BigInt(""===t?"0":`0x${t}`)}function Ni(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);const e=t.length;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(e/2);for(let r=0;r<n.length;r++){const e=2*r,s=t.slice(e,e+2),i=Number.parseInt(s,16);if(Number.isNaN(i)||i<0)throw new Error("Invalid byte sequence");n[r]=i}return n}function Bi(t){return Pi(ki(t))}function Ii(t){if(!Ei(t))throw new Error("Uint8Array expected");return Pi(ki(Uint8Array.from(t).reverse()))}function Oi(t,e){return Ni(t.toString(16).padStart(2*e,"0"))}function Ci(t,e){return Oi(t,e).reverse()}function Ri(t,e,n){let r;if("string"==typeof e)try{r=Ni(e)}catch(i){throw new Error(`${t} must be valid hex string, got "${e}". Cause: ${i}`)}else{if(!Ei(e))throw new Error(`${t} must be hex string or Uint8Array`);r=Uint8Array.from(e)}const s=r.length;if("number"==typeof n&&s!==n)throw new Error(`${t} expected ${n} bytes, got ${s}`);return r}function Ti(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let n=0;return t.forEach((t=>{if(!Ei(t))throw new Error("Uint8Array expected");e.set(t,n),n+=t.length})),e}const Si=t=>(bi<<BigInt(t-1))-Ai,Ui=t=>new Uint8Array(t),Fi=t=>Uint8Array.from(t);function Mi(t,e,n){if("number"!=typeof t||t<2)throw new Error("hashLen must be a number");if("number"!=typeof e||e<2)throw new Error("qByteLen must be a number");if("function"!=typeof n)throw new Error("hmacFn must be a function");let r=Ui(t),s=Ui(t),i=0;const a=()=>{r.fill(1),s.fill(0),i=0},o=(...t)=>n(s,r,...t),c=(t=Ui())=>{s=o(Fi([0]),t),r=o(),0!==t.length&&(s=o(Fi([1]),t),r=o())},l=()=>{if(i++>=1e3)throw new Error("drbg: tried 1000 values");let t=0;const n=[];for(;t<e;){r=o();const e=r.slice();n.push(e),t+=r.length}return Ti(...n)};return(t,e)=>{let n;for(a(),c(t);!(n=e(l()));)c();return a(),n}}const Li={bigint:t=>"bigint"==typeof t,function:t=>"function"==typeof t,boolean:t=>"boolean"==typeof t,string:t=>"string"==typeof t,stringOrUint8Array:t=>"string"==typeof t||t instanceof Uint8Array,isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>"function"==typeof t&&Number.isSafeInteger(t.outputLen)};function Di(t,e,n={}){const r=(e,n,r)=>{const s=Li[n];if("function"!=typeof s)throw new Error(`Invalid validator "${n}", expected function`);const i=t[e];if(!(r&&void 0===i||s(i,t)))throw new Error(`Invalid param ${String(e)}=${i} (${typeof i}), expected ${n}`)};for(const[s,i]of Object.entries(e))r(s,i,!1);for(const[s,i]of Object.entries(n))r(s,i,!0);return t}const Gi=Object.freeze(Object.defineProperty({__proto__:null,bitGet:function(t,e){return t>>BigInt(e)&Ai},bitLen:function(t){let e;for(e=0;t>wi;t>>=Ai,e+=1);return e},bitMask:Si,bitSet:(t,e,n)=>t|(n?Ai:wi)<<BigInt(e),bytesToHex:ki,bytesToNumberBE:Bi,bytesToNumberLE:Ii,concatBytes:Ti,createHmacDrbg:Mi,ensureBytes:Ri,equalBytes:function(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0},hexToBytes:Ni,hexToNumber:Pi,numberToBytesBE:Oi,numberToBytesLE:Ci,numberToHexUnpadded:xi,numberToVarBytesBE:function(t){return Ni(xi(t))},utf8ToBytes:function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))},validateObject:Di},Symbol.toStringTag,{value:"Module"})),Hi=BigInt(0),Qi=BigInt(1),ji=BigInt(2),zi=BigInt(3),Wi=BigInt(4),Vi=BigInt(5),Ji=BigInt(8);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Ki(t,e){const n=t%e;return n>=Hi?n:e+n}function qi(t,e,n){if(n<=Hi||e<Hi)throw new Error("Expected power/modulo > 0");if(n===Qi)return Hi;let r=Qi;for(;e>Hi;)e&Qi&&(r=r*t%n),t=t*t%n,e>>=Qi;return r}function Zi(t,e,n){let r=t;for(;e-- >Hi;)r*=r,r%=n;return r}function _i(t,e){if(t===Hi||e<=Hi)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let n=Ki(t,e),r=e,s=Hi,i=Qi;for(;n!==Hi;){const t=r%n,e=s-i*(r/n);r=n,n=t,s=i,i=e}if(r!==Qi)throw new Error("invert: does not exist");return Ki(s,e)}function Yi(t){if(t%Wi===zi){const e=(t+Qi)/Wi;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error("Cannot find square root");return r}}if(t%Ji===Vi){const e=(t-Vi)/Ji;return function(t,n){const r=t.mul(n,ji),s=t.pow(r,e),i=t.mul(n,s),a=t.mul(t.mul(i,ji),s),o=t.mul(i,t.sub(a,t.ONE));if(!t.eql(t.sqr(o),n))throw new Error("Cannot find square root");return o}}return function(t){const e=(t-Qi)/ji;let n,r,s;for(n=t-Qi,r=0;n%ji===Hi;n/=ji,r++);for(s=ji;s<t&&qi(s,e,t)!==t-Qi;s++);if(1===r){const e=(t+Qi)/Wi;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error("Cannot find square root");return r}}const i=(n+Qi)/ji;return function(t,a){if(t.pow(a,e)===t.neg(t.ONE))throw new Error("Cannot find square root");let o=r,c=t.pow(t.mul(t.ONE,s),n),l=t.pow(a,i),u=t.pow(a,n);for(;!t.eql(u,t.ONE);){if(t.eql(u,t.ZERO))return t.ZERO;let e=1;for(let r=t.sqr(u);e<o&&!t.eql(r,t.ONE);e++)r=t.sqr(r);const n=t.pow(c,Qi<<BigInt(o-e-1));c=t.sqr(n),l=t.mul(l,n),u=t.mul(u,c),o=e}return l}}(t)}BigInt(9),BigInt(16);const Xi=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function $i(t,e){const n=void 0!==e?e:t.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function ta(t){if("bigint"!=typeof t)throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function ea(t){const e=ta(t);return e+Math.ceil(e/2)}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const na=BigInt(0),ra=BigInt(1);function sa(t){return Di(t.Fp,Xi.reduce(((t,e)=>(t[e]="function",t)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"})),Di(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...$i(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const{bytesToNumberBE:ia,hexToBytes:aa}=Gi,oa={Err:class extends Error{constructor(t=""){super(t)}},_parseInt(t){const{Err:e}=oa;if(t.length<2||2!==t[0])throw new e("Invalid signature integer tag");const n=t[1],r=t.subarray(2,n+2);if(!n||r.length!==n)throw new e("Invalid signature integer: wrong length");if(128&r[0])throw new e("Invalid signature integer: negative");if(0===r[0]&&!(128&r[1]))throw new e("Invalid signature integer: unnecessary leading zero");return{d:ia(r),l:t.subarray(n+2)}},toSig(t){const{Err:e}=oa,n="string"==typeof t?aa(t):t;if(!(n instanceof Uint8Array))throw new Error("ui8a expected");let r=n.length;if(r<2||48!=n[0])throw new e("Invalid signature tag");if(n[1]!==r-2)throw new e("Invalid signature: incorrect length");const{d:s,l:i}=oa._parseInt(n.subarray(2)),{d:a,l:o}=oa._parseInt(i);if(o.length)throw new e("Invalid signature: left bytes after parsing");return{r:s,s:a}},hexFromSig(t){const e=t=>8&Number.parseInt(t[0],16)?"00"+t:t,n=t=>{const e=t.toString(16);return 1&e.length?`0${e}`:e},r=e(n(t.s)),s=e(n(t.r)),i=r.length/2,a=s.length/2,o=n(i),c=n(a);return`30${n(a+i+4)}02${c}${s}02${o}${r}`}},ca=BigInt(0),la=BigInt(1);BigInt(2);const ua=BigInt(3);function ha(t){const e=function(t){const e=sa(t);Di(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:n,Fp:r,a:s}=e;if(n){if(!r.eql(s,r.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof n||"bigint"!=typeof n.beta||"function"!=typeof n.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}(t),{Fp:n}=e,r=e.toBytes||((t,e,r)=>{const s=e.toAffine();return Ti(Uint8Array.from([4]),n.toBytes(s.x),n.toBytes(s.y))}),s=e.fromBytes||(t=>{const e=t.subarray(1);return{x:n.fromBytes(e.subarray(0,n.BYTES)),y:n.fromBytes(e.subarray(n.BYTES,2*n.BYTES))}});function i(t){const{a:r,b:s}=e,i=n.sqr(t),a=n.mul(i,t);return n.add(n.add(a,n.mul(t,r)),s)}if(!n.eql(n.sqr(e.Gy),i(e.Gx)))throw new Error("bad generator point: equation left != right");function a(t){return"bigint"==typeof t&&ca<t&&t<e.n}function o(t){if(!a(t))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function c(t){const{allowedPrivateKeyLengths:n,nByteLength:r,wrapPrivateKey:s,n:i}=e;if(n&&"bigint"!=typeof t){if(t instanceof Uint8Array&&(t=ki(t)),"string"!=typeof t||!n.includes(t.length))throw new Error("Invalid key");t=t.padStart(2*r,"0")}let a;try{a="bigint"==typeof t?t:Bi(Ri("private key",t,r))}catch(c){throw new Error(`private key must be ${r} bytes, hex or bigint, not ${typeof t}`)}return s&&(a=Ki(a,i)),o(a),a}const l=new Map;function u(t){if(!(t instanceof h))throw new Error("ProjectivePoint expected")}class h{constructor(t,e,r){if(this.px=t,this.py=e,this.pz=r,null==t||!n.isValid(t))throw new Error("x required");if(null==e||!n.isValid(e))throw new Error("y required");if(null==r||!n.isValid(r))throw new Error("z required")}static fromAffine(t){const{x:e,y:r}=t||{};if(!t||!n.isValid(e)||!n.isValid(r))throw new Error("invalid affine point");if(t instanceof h)throw new Error("projective point not allowed");const s=t=>n.eql(t,n.ZERO);return s(e)&&s(r)?h.ZERO:new h(e,r,n.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}static fromHex(t){const e=h.fromAffine(s(Ri("pointHex",t)));return e.assertValidity(),e}static fromPrivateKey(t){return h.BASE.multiply(c(t))}_setWindowSize(t){this._WINDOW_SIZE=t,l.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint&&!n.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:t,y:r}=this.toAffine();if(!n.isValid(t)||!n.isValid(r))throw new Error("bad point: x or y not FE");const s=n.sqr(r),a=i(t);if(!n.eql(s,a))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:t}=this.toAffine();if(n.isOdd)return!n.isOdd(t);throw new Error("Field doesn't support isOdd")}equals(t){u(t);const{px:e,py:r,pz:s}=this,{px:i,py:a,pz:o}=t,c=n.eql(n.mul(e,o),n.mul(i,s)),l=n.eql(n.mul(r,o),n.mul(a,s));return c&&l}negate(){return new h(this.px,n.neg(this.py),this.pz)}double(){const{a:t,b:r}=e,s=n.mul(r,ua),{px:i,py:a,pz:o}=this;let c=n.ZERO,l=n.ZERO,u=n.ZERO,f=n.mul(i,i),d=n.mul(a,a),p=n.mul(o,o),g=n.mul(i,a);return g=n.add(g,g),u=n.mul(i,o),u=n.add(u,u),c=n.mul(t,u),l=n.mul(s,p),l=n.add(c,l),c=n.sub(d,l),l=n.add(d,l),l=n.mul(c,l),c=n.mul(g,c),u=n.mul(s,u),p=n.mul(t,p),g=n.sub(f,p),g=n.mul(t,g),g=n.add(g,u),u=n.add(f,f),f=n.add(u,f),f=n.add(f,p),f=n.mul(f,g),l=n.add(l,f),p=n.mul(a,o),p=n.add(p,p),f=n.mul(p,g),c=n.sub(c,f),u=n.mul(p,d),u=n.add(u,u),u=n.add(u,u),new h(c,l,u)}add(t){u(t);const{px:r,py:s,pz:i}=this,{px:a,py:o,pz:c}=t;let l=n.ZERO,f=n.ZERO,d=n.ZERO;const p=e.a,g=n.mul(e.b,ua);let m=n.mul(r,a),y=n.mul(s,o),w=n.mul(i,c),A=n.add(r,s),b=n.add(a,o);A=n.mul(A,b),b=n.add(m,y),A=n.sub(A,b),b=n.add(r,i);let E=n.add(a,c);return b=n.mul(b,E),E=n.add(m,w),b=n.sub(b,E),E=n.add(s,i),l=n.add(o,c),E=n.mul(E,l),l=n.add(y,w),E=n.sub(E,l),d=n.mul(p,b),l=n.mul(g,w),d=n.add(l,d),l=n.sub(y,d),d=n.add(y,d),f=n.mul(l,d),y=n.add(m,m),y=n.add(y,m),w=n.mul(p,w),b=n.mul(g,b),y=n.add(y,w),w=n.sub(m,w),w=n.mul(p,w),b=n.add(b,w),m=n.mul(y,b),f=n.add(f,m),m=n.mul(E,b),l=n.mul(A,l),l=n.sub(l,m),m=n.mul(A,y),d=n.mul(E,d),d=n.add(d,m),new h(l,f,d)}subtract(t){return this.add(t.negate())}is0(){return this.equals(h.ZERO)}wNAF(t){return d.wNAFCached(this,l,t,(t=>{const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}))}multiplyUnsafe(t){const r=h.ZERO;if(t===ca)return r;if(o(t),t===la)return this;const{endo:s}=e;if(!s)return d.unsafeLadder(this,t);let{k1neg:i,k1:a,k2neg:c,k2:l}=s.splitScalar(t),u=r,f=r,p=this;for(;a>ca||l>ca;)a&la&&(u=u.add(p)),l&la&&(f=f.add(p)),p=p.double(),a>>=la,l>>=la;return i&&(u=u.negate()),c&&(f=f.negate()),f=new h(n.mul(f.px,s.beta),f.py,f.pz),u.add(f)}multiply(t){o(t);let r,s,i=t;const{endo:a}=e;if(a){const{k1neg:t,k1:e,k2neg:o,k2:c}=a.splitScalar(i);let{p:l,f:u}=this.wNAF(e),{p:f,f:p}=this.wNAF(c);l=d.constTimeNegate(t,l),f=d.constTimeNegate(o,f),f=new h(n.mul(f.px,a.beta),f.py,f.pz),r=l.add(f),s=u.add(p)}else{const{p:t,f:e}=this.wNAF(i);r=t,s=e}return h.normalizeZ([r,s])[0]}multiplyAndAddUnsafe(t,e,n){const r=h.BASE,s=(t,e)=>e!==ca&&e!==la&&t.equals(r)?t.multiply(e):t.multiplyUnsafe(e),i=s(this,e).add(s(t,n));return i.is0()?void 0:i}toAffine(t){const{px:e,py:r,pz:s}=this,i=this.is0();null==t&&(t=i?n.ONE:n.inv(s));const a=n.mul(e,t),o=n.mul(r,t),c=n.mul(s,t);if(i)return{x:n.ZERO,y:n.ZERO};if(!n.eql(c,n.ONE))throw new Error("invZ was invalid");return{x:a,y:o}}isTorsionFree(){const{h:t,isTorsionFree:n}=e;if(t===la)return!0;if(n)return n(h,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:t,clearCofactor:n}=e;return t===la?this:n?n(h,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return this.assertValidity(),r(h,this,t)}toHex(t=!0){return ki(this.toRawBytes(t))}}h.BASE=new h(e.Gx,e.Gy,n.ONE),h.ZERO=new h(n.ZERO,n.ONE,n.ZERO);const f=e.nBitLength,d=function(t,e){const n=(t,e)=>{const n=e.negate();return t?n:e},r=t=>({windows:Math.ceil(e/t)+1,windowSize:2**(t-1)});return{constTimeNegate:n,unsafeLadder(e,n){let r=t.ZERO,s=e;for(;n>na;)n&ra&&(r=r.add(s)),s=s.double(),n>>=ra;return r},precomputeWindow(t,e){const{windows:n,windowSize:s}=r(e),i=[];let a=t,o=a;for(let r=0;r<n;r++){o=a,i.push(o);for(let t=1;t<s;t++)o=o.add(a),i.push(o);a=o.double()}return i},wNAF(e,s,i){const{windows:a,windowSize:o}=r(e);let c=t.ZERO,l=t.BASE;const u=BigInt(2**e-1),h=2**e,f=BigInt(e);for(let t=0;t<a;t++){const e=t*o;let r=Number(i&u);i>>=f,r>o&&(r-=h,i+=ra);const a=e,d=e+Math.abs(r)-1,p=t%2!=0,g=r<0;0===r?l=l.add(n(p,s[a])):c=c.add(n(g,s[d]))}return{p:c,f:l}},wNAFCached(t,e,n,r){const s=t._WINDOW_SIZE||1;let i=e.get(t);return i||(i=this.precomputeWindow(t,s),1!==s&&e.set(t,r(i))),this.wNAF(s,i,n)}}}(h,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:h,normPrivateKeyToScalar:c,weierstrassEquation:i,isWithinCurveOrder:a}}function fa(t){const e=function(t){const e=sa(t);return Di(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}(t),{Fp:n,n:r}=e,s=n.BYTES+1,i=2*n.BYTES+1;function a(t){return Ki(t,r)}function o(t){return _i(t,r)}const{ProjectivePoint:c,normPrivateKeyToScalar:l,weierstrassEquation:u,isWithinCurveOrder:h}=ha({...e,toBytes(t,e,r){const s=e.toAffine(),i=n.toBytes(s.x),a=Ti;return r?a(Uint8Array.from([e.hasEvenY()?2:3]),i):a(Uint8Array.from([4]),i,n.toBytes(s.y))},fromBytes(t){const e=t.length,r=t[0],a=t.subarray(1);if(e!==s||2!==r&&3!==r){if(e===i&&4===r){return{x:n.fromBytes(a.subarray(0,n.BYTES)),y:n.fromBytes(a.subarray(n.BYTES,2*n.BYTES))}}throw new Error(`Point of length ${e} was invalid. Expected ${s} compressed bytes or ${i} uncompressed bytes`)}{const t=Bi(a);if(!(ca<(o=t)&&o<n.ORDER))throw new Error("Point is not on curve");const e=u(t);let s=n.sqrt(e);return!(1&~r)!==((s&la)===la)&&(s=n.neg(s)),{x:t,y:s}}var o}}),f=t=>ki(Oi(t,e.nByteLength));function d(t){return t>r>>la}const p=(t,e,n)=>Bi(t.slice(e,n));class g{constructor(t,e,n){this.r=t,this.s=e,this.recovery=n,this.assertValidity()}static fromCompact(t){const n=e.nByteLength;return t=Ri("compactSignature",t,2*n),new g(p(t,0,n),p(t,n,2*n))}static fromDER(t){const{r:e,s:n}=oa.toSig(Ri("DER",t));return new g(e,n)}assertValidity(){if(!h(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!h(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(t){return new g(this.r,this.s,t)}recoverPublicKey(t){const{r:r,s:s,recovery:i}=this,l=A(Ri("msgHash",t));if(null==i||![0,1,2,3].includes(i))throw new Error("recovery id invalid");const u=2===i||3===i?r+e.n:r;if(u>=n.ORDER)throw new Error("recovery id 2 or 3 invalid");const h=1&i?"03":"02",d=c.fromHex(h+f(u)),p=o(u),g=a(-l*p),m=a(s*p),y=c.BASE.multiplyAndAddUnsafe(d,g,m);if(!y)throw new Error("point at infinify");return y.assertValidity(),y}hasHighS(){return d(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,a(-this.s),this.recovery):this}toDERRawBytes(){return Ni(this.toDERHex())}toDERHex(){return oa.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Ni(this.toCompactHex())}toCompactHex(){return f(this.r)+f(this.s)}}const m={isValidPrivateKey(t){try{return l(t),!0}catch(e){return!1}},normPrivateKeyToScalar:l,randomPrivateKey:()=>{const t=ea(e.n);return function(t,e,n=!1){const r=t.length,s=ta(e),i=ea(e);if(r<16||r<i||r>1024)throw new Error(`expected ${i}-1024 bytes of input, got ${r}`);const a=Ki(n?Bi(t):Ii(t),e-Qi)+Qi;return n?Ci(a,s):Oi(a,s)}(e.randomBytes(t),e.n)},precompute:(t=8,e=c.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)};function y(t){const e=t instanceof Uint8Array,n="string"==typeof t,r=(e||n)&&t.length;return e?r===s||r===i:n?r===2*s||r===2*i:t instanceof c}const w=e.bits2int||function(t){const n=Bi(t),r=8*t.length-e.nBitLength;return r>0?n>>BigInt(r):n},A=e.bits2int_modN||function(t){return a(w(t))},b=Si(e.nBitLength);function E(t){if("bigint"!=typeof t)throw new Error("bigint expected");if(!(ca<=t&&t<b))throw new Error(`bigint expected < 2^${e.nBitLength}`);return Oi(t,e.nByteLength)}function v(t,r,s=k){if(["recovered","canonical"].some((t=>t in s)))throw new Error("sign() legacy options not supported");const{hash:i,randomBytes:u}=e;let{lowS:f,prehash:p,extraEntropy:m}=s;null==f&&(f=!0),t=Ri("msgHash",t),p&&(t=Ri("prehashed msgHash",i(t)));const y=A(t),b=l(r),v=[E(b),E(y)];if(null!=m){const t=!0===m?u(n.BYTES):m;v.push(Ri("extraEntropy",t))}const x=Ti(...v),P=y;return{seed:x,k2sig:function(t){const e=w(t);if(!h(e))return;const n=o(e),r=c.BASE.multiply(e).toAffine(),s=a(r.x);if(s===ca)return;const i=a(n*a(P+s*b));if(i===ca)return;let l=(r.x===s?0:2)|Number(r.y&la),u=i;return f&&d(i)&&(u=function(t){return d(t)?a(-t):t}(i),l^=1),new g(s,u,l)}}}const k={lowS:e.lowS,prehash:!1},x={lowS:e.lowS,prehash:!1};return c.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return c.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,n=!0){if(y(t))throw new Error("first arg must be private key");if(!y(e))throw new Error("second arg must be public key");return c.fromHex(e).multiply(l(t)).toRawBytes(n)},sign:function(t,n,r=k){const{seed:s,k2sig:i}=v(t,n,r),a=e;return Mi(a.hash.outputLen,a.nByteLength,a.hmac)(s,i)},verify:function(t,n,r,s=x){const i=t;if(n=Ri("msgHash",n),r=Ri("publicKey",r),"strict"in s)throw new Error("options.strict was renamed to lowS");const{lowS:l,prehash:u}=s;let h,f;try{if("string"==typeof i||i instanceof Uint8Array)try{h=g.fromDER(i)}catch(v){if(!(v instanceof oa.Err))throw v;h=g.fromCompact(i)}else{if("object"!=typeof i||"bigint"!=typeof i.r||"bigint"!=typeof i.s)throw new Error("PARSE");{const{r:t,s:e}=i;h=new g(t,e)}}f=c.fromHex(r)}catch(k){if("PARSE"===k.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(l&&h.hasHighS())return!1;u&&(n=e.hash(n));const{r:d,s:p}=h,m=A(n),y=o(p),w=a(m*y),b=a(d*y),E=c.BASE.multiplyAndAddUnsafe(f,w,b)?.toAffine();return!!E&&a(E.x)===d},ProjectivePoint:c,Signature:g,utils:m}}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function da(t){return{hash:t,hmac:(e,...n)=>Ps(t,e,function(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let n=0;return t.forEach((t=>{if(!ys(t))throw new Error("Uint8Array expected");e.set(t,n),n+=t.length})),e}(...n)),randomBytes:ks}}BigInt(4);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const pa=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),ga=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),ma=BigInt(1),ya=BigInt(2),wa=(t,e)=>(t+e/ya)/e;const Aa=function(t,e,n=!1,r={}){if(t<=Hi)throw new Error(`Expected Field ORDER > 0, got ${t}`);const{nBitLength:s,nByteLength:i}=$i(t,e);if(i>2048)throw new Error("Field lengths over 2048 bytes are not supported");const a=Yi(t),o=Object.freeze({ORDER:t,BITS:s,BYTES:i,MASK:Si(s),ZERO:Hi,ONE:Qi,create:e=>Ki(e,t),isValid:e=>{if("bigint"!=typeof e)throw new Error("Invalid field element: expected bigint, got "+typeof e);return Hi<=e&&e<t},is0:t=>t===Hi,isOdd:t=>(t&Qi)===Qi,neg:e=>Ki(-e,t),eql:(t,e)=>t===e,sqr:e=>Ki(e*e,t),add:(e,n)=>Ki(e+n,t),sub:(e,n)=>Ki(e-n,t),mul:(e,n)=>Ki(e*n,t),pow:(t,e)=>function(t,e,n){if(n<Hi)throw new Error("Expected power > 0");if(n===Hi)return t.ONE;if(n===Qi)return e;let r=t.ONE,s=e;for(;n>Hi;)n&Qi&&(r=t.mul(r,s)),s=t.sqr(s),n>>=Qi;return r}(o,t,e),div:(e,n)=>Ki(e*_i(n,t),t),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:e=>_i(e,t),sqrt:r.sqrt||(t=>a(o,t)),invertBatch:t=>function(t,e){const n=new Array(e.length),r=e.reduce(((e,r,s)=>t.is0(r)?e:(n[s]=e,t.mul(e,r))),t.ONE),s=t.inv(r);return e.reduceRight(((e,r,s)=>t.is0(r)?e:(n[s]=t.mul(e,n[s]),t.mul(e,r))),s),n}(o,t),cmov:(t,e,n)=>n?e:t,toBytes:t=>n?Ci(t,i):Oi(t,i),fromBytes:t=>{if(t.length!==i)throw new Error(`Fp.fromBytes: expected ${i}, got ${t.length}`);return n?Ii(t):Bi(t)}});return Object.freeze(o)}(pa,void 0,void 0,{sqrt:function(t){const e=pa,n=BigInt(3),r=BigInt(6),s=BigInt(11),i=BigInt(22),a=BigInt(23),o=BigInt(44),c=BigInt(88),l=t*t*t%e,u=l*l*t%e,h=Zi(u,n,e)*u%e,f=Zi(h,n,e)*u%e,d=Zi(f,ya,e)*l%e,p=Zi(d,s,e)*d%e,g=Zi(p,i,e)*p%e,m=Zi(g,o,e)*g%e,y=Zi(m,c,e)*m%e,w=Zi(y,o,e)*g%e,A=Zi(w,n,e)*u%e,b=Zi(A,a,e)*p%e,E=Zi(b,r,e)*l%e,v=Zi(E,ya,e);if(!Aa.eql(Aa.sqr(v),t))throw new Error("Cannot find square root");return v}}),ba=function(t,e){const n=e=>fa({...t,...da(e)});return Object.freeze({...n(e),create:n})}({a:BigInt(0),b:BigInt(7),Fp:Aa,n:ga,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=ga,n=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-ma*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),i=n,a=BigInt("0x100000000000000000000000000000000"),o=wa(i*t,e),c=wa(-r*t,e);let l=Ki(t-o*n-c*s,e),u=Ki(-o*r-c*i,e);const h=l>a,f=u>a;if(h&&(l=e-l),f&&(u=e-u),l>a||u>a)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:h,k1:l,k2neg:f,k2:u}}}},Ts);BigInt(0),ba.ProjectivePoint;const Ea="0x0000000000000000000000000000000000000000",va="0x0000000000000000000000000000000000000000000000000000000000000000",ka=BigInt(0),xa=BigInt(1),Pa=BigInt(2),Na=BigInt(27),Ba=BigInt(28),Ia=BigInt(35),Oa={};function Ca(t){return Vn(sr(t),32)}const Ra=class t{constructor(t,e,n,r){An(this,Z),An(this,_),An(this,Y),An(this,X),Sn(t,Oa,"Signature"),bn(this,Z,e),bn(this,_,n),bn(this,Y,r),bn(this,X,null)}get r(){return wn(this,Z)}set r(t){Rn(32===jn(t),"invalid r","value",t),bn(this,Z,Hn(t))}get s(){return wn(this,_)}set s(t){Rn(32===jn(t),"invalid s","value",t);const e=Hn(t);Rn(parseInt(e.substring(0,3))<8,"non-canonical s","value",e),bn(this,_,e)}get v(){return wn(this,Y)}set v(t){const e=nr(t,"value");Rn(27===e||28===e,"invalid v","v",t),bn(this,Y,e)}get networkV(){return wn(this,X)}get legacyChainId(){const e=this.networkV;return null==e?null:t.getChainId(e)}get yParity(){return 27===this.v?0:1}get yParityAndS(){const t=Fn(this.s);return this.yParity&&(t[0]|=128),Hn(t)}get compactSerialized(){return Qn([this.r,this.yParityAndS])}get serialized(){return Qn([this.r,this.s,this.yParity?"0x1c":"0x1b"])}[Symbol.for("nodejs.util.inspect.custom")](){return`Signature { r: "${this.r}", s: "${this.s}", yParity: ${this.yParity}, networkV: ${this.networkV} }`}clone(){const e=new t(Oa,this.r,this.s,this.v);return this.networkV&&bn(e,X,this.networkV),e}toJSON(){const t=this.networkV;return{_type:"signature",networkV:null!=t?t.toString():null,r:this.r,s:this.s,v:this.v}}static getChainId(t){const e=Xn(t,"v");return e==Na||e==Ba?ka:(Rn(e>=Ia,"invalid EIP-155 v","v",t),(e-Ia)/Pa)}static getChainIdV(t,e){return Xn(t)*Pa+BigInt(35+e-27)}static getNormalizedV(t){const e=Xn(t);return e===ka||e===Na?27:e===xa||e===Ba?28:(Rn(e>=Ia,"invalid v","v",t),e&xa?27:28)}static from(e){function n(t,n){Rn(t,n,"signature",e)}if(null==e)return new t(Oa,va,va,27);if("string"==typeof e){const r=Fn(e,"signature");if(64===r.length){const e=Hn(r.slice(0,32)),n=r.slice(32,64),s=128&n[0]?28:27;return n[0]&=127,new t(Oa,e,Hn(n),s)}if(65===r.length){const e=Hn(r.slice(0,32)),s=r.slice(32,64);n(!(128&s[0]),"non-canonical s");const i=t.getNormalizedV(r[64]);return new t(Oa,e,Hn(s),i)}n(!1,"invalid raw signature length")}if(e instanceof t)return e.clone();const r=e.r;n(null!=r,"missing r");const s=Ca(r),i=function(t,e){if(null!=t)return Ca(t);if(null!=e){n(Ln(e,32),"invalid yParityAndS");const t=Fn(e);return t[0]&=127,Hn(t)}n(!1,"missing s")}(e.s,e.yParityAndS);n(!(128&Fn(i)[0]),"non-canonical s");const{networkV:a,v:o}=function(e,r,s){if(null!=e){const n=Xn(e);return{networkV:n>=Ia?n:void 0,v:t.getNormalizedV(n)}}if(null!=r)return n(Ln(r,32),"invalid yParityAndS"),{v:128&Fn(r)[0]?28:27};if(null!=s){switch(nr(s,"sig.yParity")){case 0:return{v:27};case 1:return{v:28}}n(!1,"invalid yParity")}n(!1,"missing v")}(e.v,e.yParityAndS,e.yParity),c=new t(Oa,s,i,o);return a&&bn(c,X,a),n(null==e.yParity||nr(e.yParity,"sig.yParity")===c.yParity,"yParity mismatch"),n(null==e.yParityAndS||e.yParityAndS===c.yParityAndS,"yParityAndS mismatch"),c}};Z=new WeakMap,_=new WeakMap,Y=new WeakMap,X=new WeakMap;let Ta=Ra;$=new WeakMap;let Sa=class t{constructor(t){An(this,$),Rn(32===jn(t),"invalid private key","privateKey","[REDACTED]"),bn(this,$,Hn(t))}get privateKey(){return wn(this,$)}get publicKey(){return t.computePublicKey(wn(this,$))}get compressedPublicKey(){return t.computePublicKey(wn(this,$),!0)}sign(t){Rn(32===jn(t),"invalid digest length","digest",t);const e=ba.sign(Mn(t),Mn(wn(this,$)),{lowS:!0});return Ta.from({r:rr(e.r,32),s:rr(e.s,32),v:e.recovery?28:27})}computeSharedSecret(e){const n=t.computePublicKey(e);return Hn(ba.getSharedSecret(Mn(wn(this,$)),Fn(n),!1))}static computePublicKey(t,e){let n=Fn(t,"key");if(32===n.length){return Hn(ba.getPublicKey(n,!!e))}if(64===n.length){const t=new Uint8Array(65);t[0]=4,t.set(n,1),n=t}return Hn(ba.ProjectivePoint.fromHex(n).toRawBytes(e))}static recoverPublicKey(t,e){Rn(32===jn(t),"invalid digest length","digest",t);const n=Ta.from(e);let r=ba.Signature.fromCompact(Mn(Qn([n.r,n.s])));r=r.addRecoveryBit(n.yParity);const s=r.recoverPublicKey(Mn(t));return Rn(null!=s,"invalid signature for digest","signature",e),"0x"+s.toHex(!1)}static addPoints(e,n,r){const s=ba.ProjectivePoint.fromHex(t.computePublicKey(e).substring(2)),i=ba.ProjectivePoint.fromHex(t.computePublicKey(n).substring(2));return"0x"+s.add(i).toHex(!!r)}};const Ua=BigInt(0),Fa=BigInt(36);function Ma(t){const e=(t=t.toLowerCase()).substring(2).split(""),n=new Uint8Array(40);for(let s=0;s<40;s++)n[s]=e[s].charCodeAt(0);const r=Fn(di(n));for(let s=0;s<40;s+=2)r[s>>1]>>4>=8&&(e[s]=e[s].toUpperCase()),(15&r[s>>1])>=8&&(e[s+1]=e[s+1].toUpperCase());return"0x"+e.join("")}const La={};for(let uf=0;uf<10;uf++)La[String(uf)]=String(uf);for(let uf=0;uf<26;uf++)La[String.fromCharCode(65+uf)]=String(10+uf);const Da=function(){const t={};for(let e=0;e<36;e++){t["0123456789abcdefghijklmnopqrstuvwxyz"[e]]=BigInt(e)}return t}();function Ga(t){if(Rn("string"==typeof t,"invalid address","address",t),t.match(/^(0x)?[0-9a-fA-F]{40}$/)){t.startsWith("0x")||(t="0x"+t);const e=Ma(t);return Rn(!t.match(/([A-F].*[a-f])|([a-f].*[A-F])/)||e===t,"bad address checksum","address",t),e}if(t.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)){Rn(t.substring(2,4)===function(t){let e=(t=(t=t.toUpperCase()).substring(4)+t.substring(0,2)+"00").split("").map((t=>La[t])).join("");for(;e.length>=15;){let t=e.substring(0,15);e=parseInt(t,10)%97+e.substring(t.length)}let n=String(98-parseInt(e,10)%97);for(;n.length<2;)n="0"+n;return n}(t),"bad icap checksum","address",t);let e=function(t){t=t.toLowerCase();let e=Ua;for(let n=0;n<t.length;n++)e=e*Fa+Da[t[n]];return e}(t.substring(4)).toString(16);for(;e.length<40;)e="0"+e;return Ma("0x"+e)}Rn(!1,"invalid address","address",t)}function Ha(t){return t&&"function"==typeof t.getAddress}async function Qa(t,e){const n=await e;return null!=n&&"0x0000000000000000000000000000000000000000"!==n||(Cn("string"!=typeof t,"unconfigured name","UNCONFIGURED_NAME",{value:t}),Rn(!1,"invalid AddressLike value; did not resolve to a value address","target",t)),Ga(n)}function ja(t,e){return"string"==typeof t?t.match(/^0x[0-9a-f]{40}$/i)?Ga(t):(Cn(null!=e,"ENS resolution requires a provider","UNSUPPORTED_OPERATION",{operation:"resolveName"}),Qa(t,e.resolveName(t))):Ha(t)?Qa(t,t.getAddress()):t&&"function"==typeof t.then?Qa(t,t):void Rn(!1,"unsupported addressable value","target",t)}const za={};function Wa(t,e){let n=!1;return e<0&&(n=!0,e*=-1),new Ka(za,`${n?"":"u"}int${e}`,t,{signed:n,width:e})}function Va(t,e){return new Ka(za,`bytes${e||""}`,t,{size:e})}const Ja=Symbol.for("_ethers_typed");tt=new WeakMap;let Ka=class t{constructor(t,e,n,r){mn(this,"type"),mn(this,"value"),An(this,tt),mn(this,"_typedSymbol"),null==r&&(r=null),Sn(za,t,"Typed"),Pn(this,{_typedSymbol:Ja,type:e,value:n}),bn(this,tt,r),this.format()}format(){if("array"===this.type)throw new Error("");if("dynamicArray"===this.type)throw new Error("");return"tuple"===this.type?`tuple(${this.value.map((t=>t.format())).join(",")})`:this.type}defaultValue(){return 0}minValue(){return 0}maxValue(){return 0}isBigInt(){return!!this.type.match(/^u?int[0-9]+$/)}isData(){return this.type.startsWith("bytes")}isString(){return"string"===this.type}get tupleName(){if("tuple"!==this.type)throw TypeError("not a tuple");return wn(this,tt)}get arrayLength(){if("array"!==this.type)throw TypeError("not an array");return!0===wn(this,tt)?-1:!1===wn(this,tt)?this.value.length:null}static from(e,n){return new t(za,e,n)}static uint8(t){return Wa(t,8)}static uint16(t){return Wa(t,16)}static uint24(t){return Wa(t,24)}static uint32(t){return Wa(t,32)}static uint40(t){return Wa(t,40)}static uint48(t){return Wa(t,48)}static uint56(t){return Wa(t,56)}static uint64(t){return Wa(t,64)}static uint72(t){return Wa(t,72)}static uint80(t){return Wa(t,80)}static uint88(t){return Wa(t,88)}static uint96(t){return Wa(t,96)}static uint104(t){return Wa(t,104)}static uint112(t){return Wa(t,112)}static uint120(t){return Wa(t,120)}static uint128(t){return Wa(t,128)}static uint136(t){return Wa(t,136)}static uint144(t){return Wa(t,144)}static uint152(t){return Wa(t,152)}static uint160(t){return Wa(t,160)}static uint168(t){return Wa(t,168)}static uint176(t){return Wa(t,176)}static uint184(t){return Wa(t,184)}static uint192(t){return Wa(t,192)}static uint200(t){return Wa(t,200)}static uint208(t){return Wa(t,208)}static uint216(t){return Wa(t,216)}static uint224(t){return Wa(t,224)}static uint232(t){return Wa(t,232)}static uint240(t){return Wa(t,240)}static uint248(t){return Wa(t,248)}static uint256(t){return Wa(t,256)}static uint(t){return Wa(t,256)}static int8(t){return Wa(t,-8)}static int16(t){return Wa(t,-16)}static int24(t){return Wa(t,-24)}static int32(t){return Wa(t,-32)}static int40(t){return Wa(t,-40)}static int48(t){return Wa(t,-48)}static int56(t){return Wa(t,-56)}static int64(t){return Wa(t,-64)}static int72(t){return Wa(t,-72)}static int80(t){return Wa(t,-80)}static int88(t){return Wa(t,-88)}static int96(t){return Wa(t,-96)}static int104(t){return Wa(t,-104)}static int112(t){return Wa(t,-112)}static int120(t){return Wa(t,-120)}static int128(t){return Wa(t,-128)}static int136(t){return Wa(t,-136)}static int144(t){return Wa(t,-144)}static int152(t){return Wa(t,-152)}static int160(t){return Wa(t,-160)}static int168(t){return Wa(t,-168)}static int176(t){return Wa(t,-176)}static int184(t){return Wa(t,-184)}static int192(t){return Wa(t,-192)}static int200(t){return Wa(t,-200)}static int208(t){return Wa(t,-208)}static int216(t){return Wa(t,-216)}static int224(t){return Wa(t,-224)}static int232(t){return Wa(t,-232)}static int240(t){return Wa(t,-240)}static int248(t){return Wa(t,-248)}static int256(t){return Wa(t,-256)}static int(t){return Wa(t,-256)}static bytes1(t){return Va(t,1)}static bytes2(t){return Va(t,2)}static bytes3(t){return Va(t,3)}static bytes4(t){return Va(t,4)}static bytes5(t){return Va(t,5)}static bytes6(t){return Va(t,6)}static bytes7(t){return Va(t,7)}static bytes8(t){return Va(t,8)}static bytes9(t){return Va(t,9)}static bytes10(t){return Va(t,10)}static bytes11(t){return Va(t,11)}static bytes12(t){return Va(t,12)}static bytes13(t){return Va(t,13)}static bytes14(t){return Va(t,14)}static bytes15(t){return Va(t,15)}static bytes16(t){return Va(t,16)}static bytes17(t){return Va(t,17)}static bytes18(t){return Va(t,18)}static bytes19(t){return Va(t,19)}static bytes20(t){return Va(t,20)}static bytes21(t){return Va(t,21)}static bytes22(t){return Va(t,22)}static bytes23(t){return Va(t,23)}static bytes24(t){return Va(t,24)}static bytes25(t){return Va(t,25)}static bytes26(t){return Va(t,26)}static bytes27(t){return Va(t,27)}static bytes28(t){return Va(t,28)}static bytes29(t){return Va(t,29)}static bytes30(t){return Va(t,30)}static bytes31(t){return Va(t,31)}static bytes32(t){return Va(t,32)}static address(e){return new t(za,"address",e)}static bool(e){return new t(za,"bool",!!e)}static bytes(e){return new t(za,"bytes",e)}static string(e){return new t(za,"string",e)}static array(t,e){throw new Error("not implemented yet")}static tuple(t,e){throw new Error("not implemented yet")}static overrides(e){return new t(za,"overrides",Object.assign({},e))}static isTyped(t){return t&&"object"==typeof t&&"_typedSymbol"in t&&t._typedSymbol===Ja}static dereference(e,n){if(t.isTyped(e)){if(e.type!==n)throw new Error(`invalid type: expecetd ${n}, got ${e.type}`);return e.value}return e}};class qa extends ls{constructor(t){super("address","address",t,!1)}defaultValue(){return"0x0000000000000000000000000000000000000000"}encode(t,e){let n=Ka.dereference(e,"string");try{n=Ga(n)}catch(r){return this._throwError(r.message,e)}return t.writeValue(n)}decode(t){return Ga(rr(t.readValue(),20))}}class Za extends ls{constructor(t){super(t.name,t.type,"_",t.dynamic),mn(this,"coder"),this.coder=t}defaultValue(){return this.coder.defaultValue()}encode(t,e){return this.coder.encode(t,e)}decode(t){return this.coder.decode(t)}}function _a(t,e,n){let r=[];if(Array.isArray(n))r=n;else if(n&&"object"==typeof n){let t={};r=e.map((e=>{const r=e.localName;return Cn(r,"cannot encode object for signature with missing names","INVALID_ARGUMENT",{argument:"values",info:{coder:e},value:n}),Cn(!t[r],"cannot encode object for signature with duplicate names","INVALID_ARGUMENT",{argument:"values",info:{coder:e},value:n}),t[r]=!0,n[r]}))}else Rn(!1,"invalid tuple value","tuple",n);Rn(e.length===r.length,"types/value length mismatch","tuple",n);let s=new us,i=new us,a=[];e.forEach(((t,e)=>{let n=r[e];if(t.dynamic){let e=i.length;t.encode(i,n);let r=s.writeUpdatableValue();a.push((t=>{r(t+e)}))}else t.encode(s,n)})),a.forEach((t=>{t(s.length)}));let o=t.appendWriter(s);return o+=t.appendWriter(i),o}function Ya(t,e){let n=[],r=[],s=t.subReader(0);return e.forEach((e=>{let i=null;if(e.dynamic){let n=t.readIndex(),r=s.subReader(n);try{i=e.decode(r)}catch(a){if(Bn(a,"BUFFER_OVERRUN"))throw a;i=a,i.baseType=e.name,i.name=e.localName,i.type=e.type}}else try{i=e.decode(t)}catch(a){if(Bn(a,"BUFFER_OVERRUN"))throw a;i=a,i.baseType=e.name,i.name=e.localName,i.type=e.type}if(null==i)throw new Error("investigate");n.push(i),r.push(e.localName||null)})),os.fromItems(n,r)}class Xa extends ls{constructor(t,e,n){super("array",t.type+"["+(e>=0?e:"")+"]",n,-1===e||t.dynamic),mn(this,"coder"),mn(this,"length"),Pn(this,{coder:t,length:e})}defaultValue(){const t=this.coder.defaultValue(),e=[];for(let n=0;n<this.length;n++)e.push(t);return e}encode(t,e){const n=Ka.dereference(e,"array");Array.isArray(n)||this._throwError("expected array value",n);let r=this.length;-1===r&&(r=n.length,t.writeValue(n.length)),Tn(n.length,r,"coder array"+(this.localName?" "+this.localName:""));let s=[];for(let i=0;i<n.length;i++)s.push(this.coder);return _a(t,s,n)}decode(t){let e=this.length;-1===e&&(e=t.readIndex(),Cn(e*Xr<=t.dataLength,"insufficient data length","BUFFER_OVERRUN",{buffer:t.bytes,offset:e*Xr,length:t.dataLength}));let n=[];for(let r=0;r<e;r++)n.push(new Za(this.coder));return Ya(t,n)}}class $a extends ls{constructor(t){super("bool","bool",t,!1)}defaultValue(){return!1}encode(t,e){const n=Ka.dereference(e,"bool");return t.writeValue(n?1:0)}decode(t){return!!t.readValue()}}class to extends ls{constructor(t,e){super(t,t,e,!0)}defaultValue(){return"0x"}encode(t,e){e=Mn(e);let n=t.writeValue(e.length);return n+=t.writeBytes(e),n}decode(t){return t.readBytes(t.readIndex(),!0)}}class eo extends to{constructor(t){super("bytes",t)}decode(t){return Hn(super.decode(t))}}class no extends ls{constructor(t,e){let n="bytes"+String(t);super(n,n,e,!1),mn(this,"size"),Pn(this,{size:t},{size:"number"})}defaultValue(){return"0x0000000000000000000000000000000000000000000000000000000000000000".substring(0,2+2*this.size)}encode(t,e){let n=Mn(Ka.dereference(e,this.type));return n.length!==this.size&&this._throwError("incorrect data length",e),t.writeBytes(n)}decode(t){return Hn(t.readBytes(this.size))}}const ro=new Uint8Array([]);class so extends ls{constructor(t){super("null","",t,!1)}defaultValue(){return null}encode(t,e){return null!=e&&this._throwError("not null",e),t.writeBytes(ro)}decode(t){return t.readBytes(0),null}}const io=BigInt(0),ao=BigInt(1),oo=BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");class co extends ls{constructor(t,e,n){const r=(e?"int":"uint")+8*t;super(r,r,n,!1),mn(this,"size"),mn(this,"signed"),Pn(this,{size:t,signed:e},{size:"number",signed:"boolean"})}defaultValue(){return 0}encode(t,e){let n=Xn(Ka.dereference(e,this.type)),r=Yn(oo,256);if(this.signed){let t=Yn(r,8*this.size-1);(n>t||n<-(t+ao))&&this._throwError("value out-of-bounds",e),n=_n(n,256)}else(n<io||n>Yn(r,8*this.size))&&this._throwError("value out-of-bounds",e);return t.writeValue(n)}decode(t){let e=Yn(t.readValue(),8*this.size);return this.signed&&(e=Zn(e,8*this.size)),e}}class lo extends to{constructor(t){super("string",t)}defaultValue(){return""}encode(t,e){return super.encode(t,hr(Ka.dereference(e,"string")))}decode(t){return fr(super.decode(t))}}class uo extends ls{constructor(t,e){let n=!1;const r=[];t.forEach((t=>{t.dynamic&&(n=!0),r.push(t.type)}));super("tuple","tuple("+r.join(",")+")",e,n),mn(this,"coders"),Pn(this,{coders:Object.freeze(t.slice())})}defaultValue(){const t=[];this.coders.forEach((e=>{t.push(e.defaultValue())}));const e=this.coders.reduce(((t,e)=>{const n=e.localName;return n&&(t[n]||(t[n]=0),t[n]++),t}),{});return this.coders.forEach(((n,r)=>{let s=n.localName;s&&1===e[s]&&("length"===s&&(s="_length"),null==t[s]&&(t[s]=t[r]))})),Object.freeze(t)}encode(t,e){const n=Ka.dereference(e,"tuple");return _a(t,this.coders,n)}decode(t){return Ya(t,this.coders)}}function ho(t,e){return{address:Ga(t),storageKeys:e.map(((t,e)=>(Rn(Ln(t,32),"invalid slot",`storageKeys[${e}]`,t),t.toLowerCase())))}}function fo(t){if(Array.isArray(t))return t.map(((e,n)=>Array.isArray(e)?(Rn(2===e.length,"invalid slot set",`value[${n}]`,e),ho(e[0],e[1])):(Rn(null!=e&&"object"==typeof e,"invalid address-slot set","value",t),ho(e.address,e.storageKeys))));Rn(null!=t&&"object"==typeof t,"invalid access list","value",t);const e=Object.keys(t).map((e=>{const n=t[e].reduce(((t,e)=>(t[e]=!0,t)),{});return ho(e,Object.keys(n).sort())}));return e.sort(((t,e)=>t.address.localeCompare(e.address))),e}function po(t){return{address:Ga(t.address),nonce:Xn(null!=t.nonce?t.nonce:0),chainId:Xn(null!=t.chainId?t.chainId:0),signature:Ta.from(t.signature)}}function go(t,e){return function(t){let e;return e="string"==typeof t?Sa.computePublicKey(t,!1):t.publicKey,Ga(di("0x"+e.substring(4)).substring(26))}(Sa.recoverPublicKey(t,e))}const mo=BigInt(0),yo=BigInt(2),wo=BigInt(27),Ao=BigInt(28),bo=BigInt(35),Eo=BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),vo=131072;function ko(t,e){let n=t.toString(16);for(;n.length<2;)n="0"+n;return n+=yi(e).substring(4),"0x"+n}function xo(t){return"0x"===t?null:Ga(t)}function Po(t,e){try{return fo(t)}catch(n){Rn(!1,n.message,e,t)}}function No(t,e){try{if(!Array.isArray(t))throw new Error("authorizationList: invalid array");const e=[];for(let n=0;n<t.length;n++){const r=t[n];if(!Array.isArray(r))throw new Error(`authorization[${n}]: invalid array`);if(6!==r.length)throw new Error(`authorization[${n}]: wrong length`);if(!r[1])throw new Error(`authorization[${n}]: null address`);e.push({address:xo(r[1]),nonce:Io(r[2],"nonce"),chainId:Io(r[0],"chainId"),signature:Ta.from({yParity:Bo(r[3],"yParity"),r:Vn(r[4],32),s:Vn(r[5],32)})})}return e}catch(n){Rn(!1,n.message,e,t)}}function Bo(t,e){return"0x"===t?0:nr(t,e)}function Io(t,e){if("0x"===t)return mo;const n=Xn(t,e);return Rn(n<=Eo,"value exceeds uint size",e,n),n}function Oo(t,e){const n=Xn(t,"value"),r=sr(n);return Rn(r.length<=32,"value too large",`tx.${e}`,n),r}function Co(t){return fo(t).map((t=>[t.address,t.storageKeys]))}function Ro(t,e){Rn(Array.isArray(t),`invalid ${e}`,"value",t);for(let n=0;n<t.length;n++)Rn(Ln(t[n],32),"invalid ${ param } hash",`value[${n}]`,t[n]);return t}function To(t,e){let n;try{if(n=Bo(e[0],"yParity"),0!==n&&1!==n)throw new Error("bad yParity")}catch(a){Rn(!1,"invalid yParity","yParity",e[0])}const r=Vn(e[1],32),s=Vn(e[2],32),i=Ta.from({r:r,s:s,yParity:n});t.signature=i}et=new WeakMap,nt=new WeakMap,rt=new WeakMap,st=new WeakMap,it=new WeakMap,at=new WeakMap,ot=new WeakMap,ct=new WeakMap,lt=new WeakMap,ut=new WeakMap,ht=new WeakMap,ft=new WeakMap,dt=new WeakMap,pt=new WeakMap,gt=new WeakMap,mt=new WeakMap,yt=new WeakMap,wt=new WeakSet,At=function(t,e){Cn(!t||null!=this.signature,"cannot serialize unsigned transaction; maybe you meant .unsignedSerialized","UNSUPPORTED_OPERATION",{operation:".serialized"});const n=t?this.signature:null;switch(this.inferType()){case 0:return function(t,e){const n=[Oo(t.nonce,"nonce"),Oo(t.gasPrice||0,"gasPrice"),Oo(t.gasLimit,"gasLimit"),t.to||"0x",Oo(t.value,"value"),t.data];let r=mo;if(t.chainId!=mo)r=Xn(t.chainId,"tx.chainId"),Rn(!e||null==e.networkV||e.legacyChainId===r,"tx.chainId/sig.v mismatch","sig",e);else if(t.signature){const e=t.signature.legacyChainId;null!=e&&(r=e)}if(!e)return r!==mo&&(n.push(sr(r)),n.push("0x"),n.push("0x")),qr(n);let s=BigInt(27+e.yParity);return r!==mo?s=Ta.getChainIdV(r,e.v):BigInt(e.v)!==s&&Rn(!1,"tx.chainId/sig.v mismatch","sig",e),n.push(sr(s)),n.push(sr(e.r)),n.push(sr(e.s)),qr(n)}(this,n);case 1:return function(t,e){const n=[Oo(t.chainId,"chainId"),Oo(t.nonce,"nonce"),Oo(t.gasPrice||0,"gasPrice"),Oo(t.gasLimit,"gasLimit"),t.to||"0x",Oo(t.value,"value"),t.data,Co(t.accessList||[])];return e&&(n.push(Oo(e.yParity,"recoveryParam")),n.push(sr(e.r)),n.push(sr(e.s))),Qn(["0x01",qr(n)])}(this,n);case 2:return function(t,e){const n=[Oo(t.chainId,"chainId"),Oo(t.nonce,"nonce"),Oo(t.maxPriorityFeePerGas||0,"maxPriorityFeePerGas"),Oo(t.maxFeePerGas||0,"maxFeePerGas"),Oo(t.gasLimit,"gasLimit"),t.to||"0x",Oo(t.value,"value"),t.data,Co(t.accessList||[])];return e&&(n.push(Oo(e.yParity,"yParity")),n.push(sr(e.r)),n.push(sr(e.s))),Qn(["0x02",qr(n)])}(this,n);case 3:return function(t,e,n){const r=[Oo(t.chainId,"chainId"),Oo(t.nonce,"nonce"),Oo(t.maxPriorityFeePerGas||0,"maxPriorityFeePerGas"),Oo(t.maxFeePerGas||0,"maxFeePerGas"),Oo(t.gasLimit,"gasLimit"),t.to||Ea,Oo(t.value,"value"),t.data,Co(t.accessList||[]),Oo(t.maxFeePerBlobGas||0,"maxFeePerBlobGas"),Ro(t.blobVersionedHashes||[],"blobVersionedHashes")];return e&&(r.push(Oo(e.yParity,"yParity")),r.push(sr(e.r)),r.push(sr(e.s)),n)?Qn(["0x03",qr([r,n.map((t=>t.data)),n.map((t=>t.commitment)),n.map((t=>t.proof))])]):Qn(["0x03",qr(r)])}(this,n,e?this.blobs:null);case 4:return function(t,e){const n=[Oo(t.chainId,"chainId"),Oo(t.nonce,"nonce"),Oo(t.maxPriorityFeePerGas||0,"maxPriorityFeePerGas"),Oo(t.maxFeePerGas||0,"maxFeePerGas"),Oo(t.gasLimit,"gasLimit"),t.to||"0x",Oo(t.value,"value"),t.data,Co(t.accessList||[]),(r=t.authorizationList||[],r.map((t=>[Oo(t.chainId,"chainId"),t.address,Oo(t.nonce,"nonce"),Oo(t.signature.yParity,"yParity"),sr(t.signature.r),sr(t.signature.s)])))];var r;return e&&(n.push(Oo(e.yParity,"yParity")),n.push(sr(e.r)),n.push(sr(e.s))),Qn(["0x04",qr(n)])}(this,n)}Cn(!1,"unsupported transaction type","UNSUPPORTED_OPERATION",{operation:".serialized"})};let So=class t{constructor(){An(this,wt),An(this,et),An(this,nt),An(this,rt),An(this,st),An(this,it),An(this,at),An(this,ot),An(this,ct),An(this,lt),An(this,ut),An(this,ht),An(this,ft),An(this,dt),An(this,pt),An(this,gt),An(this,mt),An(this,yt),bn(this,et,null),bn(this,nt,null),bn(this,st,0),bn(this,it,mo),bn(this,at,null),bn(this,ot,null),bn(this,ct,null),bn(this,rt,"0x"),bn(this,lt,mo),bn(this,ut,mo),bn(this,ht,null),bn(this,ft,null),bn(this,dt,null),bn(this,pt,null),bn(this,gt,null),bn(this,mt,null),bn(this,yt,null)}get type(){return wn(this,et)}set type(t){switch(t){case null:bn(this,et,null);break;case 0:case"legacy":bn(this,et,0);break;case 1:case"berlin":case"eip-2930":bn(this,et,1);break;case 2:case"london":case"eip-1559":bn(this,et,2);break;case 3:case"cancun":case"eip-4844":bn(this,et,3);break;case 4:case"pectra":case"eip-7702":bn(this,et,4);break;default:Rn(!1,"unsupported transaction type","type",t)}}get typeName(){switch(this.type){case 0:return"legacy";case 1:return"eip-2930";case 2:return"eip-1559";case 3:return"eip-4844";case 4:return"eip-7702"}return null}get to(){const t=wn(this,nt);return null==t&&3===this.type?Ea:t}set to(t){bn(this,nt,null==t?null:Ga(t))}get nonce(){return wn(this,st)}set nonce(t){bn(this,st,nr(t,"value"))}get gasLimit(){return wn(this,it)}set gasLimit(t){bn(this,it,Xn(t))}get gasPrice(){const t=wn(this,at);return null!=t||0!==this.type&&1!==this.type?t:mo}set gasPrice(t){bn(this,at,null==t?null:Xn(t,"gasPrice"))}get maxPriorityFeePerGas(){const t=wn(this,ot);return null==t?2===this.type||3===this.type?mo:null:t}set maxPriorityFeePerGas(t){bn(this,ot,null==t?null:Xn(t,"maxPriorityFeePerGas"))}get maxFeePerGas(){const t=wn(this,ct);return null==t?2===this.type||3===this.type?mo:null:t}set maxFeePerGas(t){bn(this,ct,null==t?null:Xn(t,"maxFeePerGas"))}get data(){return wn(this,rt)}set data(t){bn(this,rt,Hn(t))}get value(){return wn(this,lt)}set value(t){bn(this,lt,Xn(t,"value"))}get chainId(){return wn(this,ut)}set chainId(t){bn(this,ut,Xn(t))}get signature(){return wn(this,ht)||null}set signature(t){bn(this,ht,null==t?null:Ta.from(t))}get accessList(){const t=wn(this,ft)||null;return null==t?1===this.type||2===this.type||3===this.type?[]:null:t}set accessList(t){bn(this,ft,null==t?null:fo(t))}get authorizationList(){const t=wn(this,yt)||null;return null==t&&4===this.type?[]:t}set authorizationList(t){bn(this,yt,null==t?null:t.map((t=>po(t))))}get maxFeePerBlobGas(){const t=wn(this,dt);return null==t&&3===this.type?mo:t}set maxFeePerBlobGas(t){bn(this,dt,null==t?null:Xn(t,"maxFeePerBlobGas"))}get blobVersionedHashes(){let t=wn(this,pt);return null==t&&3===this.type?[]:t}set blobVersionedHashes(t){if(null!=t){Rn(Array.isArray(t),"blobVersionedHashes must be an Array","value",t),t=t.slice();for(let e=0;e<t.length;e++)Rn(Ln(t[e],32),"invalid blobVersionedHash",`value[${e}]`,t[e])}bn(this,pt,t)}get blobs(){return null==wn(this,mt)?null:wn(this,mt).map((t=>Object.assign({},t)))}set blobs(t){if(null==t)return void bn(this,mt,null);const e=[],n=[];for(let r=0;r<t.length;r++){const s=t[r];if(Dn(s)){Cn(wn(this,gt),"adding a raw blob requires a KZG library","UNSUPPORTED_OPERATION",{operation:"set blobs()"});let t=Fn(s);if(Rn(t.length<=vo,"blob is too large",`blobs[${r}]`,s),t.length!==vo){const e=new Uint8Array(vo);e.set(t),t=e}const i=wn(this,gt).blobToKzgCommitment(t),a=Hn(wn(this,gt).computeBlobKzgProof(t,i));e.push({data:Hn(t),commitment:Hn(i),proof:a}),n.push(ko(1,i))}else{const t=Hn(s.commitment);e.push({data:Hn(s.data),commitment:t,proof:Hn(s.proof)}),n.push(ko(1,t))}}bn(this,mt,e),bn(this,pt,n)}get kzg(){return wn(this,gt)}set kzg(t){bn(this,gt,null==t?null:function(t){return{blobToKzgCommitment:e=>{if("computeBlobProof"in t){if("blobToKzgCommitment"in t&&"function"==typeof t.blobToKzgCommitment)return Fn(t.blobToKzgCommitment(Hn(e)))}else if("blobToKzgCommitment"in t&&"function"==typeof t.blobToKzgCommitment)return Fn(t.blobToKzgCommitment(e));if("blobToKZGCommitment"in t&&"function"==typeof t.blobToKZGCommitment)return Fn(t.blobToKZGCommitment(Hn(e)));Rn(!1,"unsupported KZG library","kzg",t)},computeBlobKzgProof:(e,n)=>"computeBlobProof"in t&&"function"==typeof t.computeBlobProof?Fn(t.computeBlobProof(Hn(e),Hn(n))):"computeBlobKzgProof"in t&&"function"==typeof t.computeBlobKzgProof?t.computeBlobKzgProof(e,n):"computeBlobKZGProof"in t&&"function"==typeof t.computeBlobKZGProof?Fn(t.computeBlobKZGProof(Hn(e),Hn(n))):void Rn(!1,"unsupported KZG library","kzg",t)}}(t))}get hash(){return null==this.signature?null:di(En(this,wt,At).call(this,!0,!1))}get unsignedHash(){return di(this.unsignedSerialized)}get from(){return null==this.signature?null:go(this.unsignedHash,this.signature)}get fromPublicKey(){return null==this.signature?null:Sa.recoverPublicKey(this.unsignedHash,this.signature)}isSigned(){return null!=this.signature}get serialized(){return En(this,wt,At).call(this,!0,!0)}get unsignedSerialized(){return En(this,wt,At).call(this,!1,!1)}inferType(){const t=this.inferTypes();return t.indexOf(2)>=0?2:t.pop()}inferTypes(){const t=null!=this.gasPrice,e=null!=this.maxFeePerGas||null!=this.maxPriorityFeePerGas,n=null!=this.accessList,r=null!=wn(this,dt)||wn(this,pt);null!=this.maxFeePerGas&&null!=this.maxPriorityFeePerGas&&Cn(this.maxFeePerGas>=this.maxPriorityFeePerGas,"priorityFee cannot be more than maxFee","BAD_DATA",{value:this}),Cn(!e||0!==this.type&&1!==this.type,"transaction type cannot have maxFeePerGas or maxPriorityFeePerGas","BAD_DATA",{value:this}),Cn(0!==this.type||!n,"legacy transaction cannot have accessList","BAD_DATA",{value:this});const s=[];return null!=this.type?s.push(this.type):this.authorizationList&&this.authorizationList.length?s.push(4):e?s.push(2):t?(s.push(1),n||s.push(0)):n?(s.push(1),s.push(2)):(r&&this.to||(s.push(0),s.push(1),s.push(2)),s.push(3)),s.sort(),s}isLegacy(){return 0===this.type}isBerlin(){return 1===this.type}isLondon(){return 2===this.type}isCancun(){return 3===this.type}clone(){return t.from(this)}toJSON(){const t=t=>null==t?null:t.toString();return{type:this.type,to:this.to,data:this.data,nonce:this.nonce,gasLimit:t(this.gasLimit),gasPrice:t(this.gasPrice),maxPriorityFeePerGas:t(this.maxPriorityFeePerGas),maxFeePerGas:t(this.maxFeePerGas),value:t(this.value),chainId:t(this.chainId),sig:this.signature?this.signature.toJSON():null,accessList:this.accessList}}static from(e){if(null==e)return new t;if("string"==typeof e){const n=Fn(e);if(n[0]>=127)return t.from(function(t){const e=Wr(t);Rn(Array.isArray(e)&&(9===e.length||6===e.length),"invalid field count for legacy transaction","data",t);const n={type:0,nonce:Bo(e[0],"nonce"),gasPrice:Io(e[1],"gasPrice"),gasLimit:Io(e[2],"gasLimit"),to:xo(e[3]),value:Io(e[4],"value"),data:Hn(e[5]),chainId:mo};if(6===e.length)return n;const r=Io(e[6],"v"),s=Io(e[7],"r"),i=Io(e[8],"s");if(s===mo&&i===mo)n.chainId=r;else{let t=(r-bo)/yo;t<mo&&(t=mo),n.chainId=t,Rn(t!==mo||r===wo||r===Ao,"non-canonical legacy v","v",e[6]),n.signature=Ta.from({r:Vn(e[7],32),s:Vn(e[8],32),v:r})}return n}(n));switch(n[0]){case 1:return t.from(function(t){const e=Wr(Fn(t).slice(1));Rn(Array.isArray(e)&&(8===e.length||11===e.length),"invalid field count for transaction type: 1","data",Hn(t));const n={type:1,chainId:Io(e[0],"chainId"),nonce:Bo(e[1],"nonce"),gasPrice:Io(e[2],"gasPrice"),gasLimit:Io(e[3],"gasLimit"),to:xo(e[4]),value:Io(e[5],"value"),data:Hn(e[6]),accessList:Po(e[7],"accessList")};return 8===e.length||To(n,e.slice(8)),n}(n));case 2:return t.from(function(t){const e=Wr(Fn(t).slice(1));Rn(Array.isArray(e)&&(9===e.length||12===e.length),"invalid field count for transaction type: 2","data",Hn(t));const n={type:2,chainId:Io(e[0],"chainId"),nonce:Bo(e[1],"nonce"),maxPriorityFeePerGas:Io(e[2],"maxPriorityFeePerGas"),maxFeePerGas:Io(e[3],"maxFeePerGas"),gasPrice:null,gasLimit:Io(e[4],"gasLimit"),to:xo(e[5]),value:Io(e[6],"value"),data:Hn(e[7]),accessList:Po(e[8],"accessList")};return 9===e.length||To(n,e.slice(9)),n}(n));case 3:return t.from(function(t){let e=Wr(Fn(t).slice(1)),n="3",r=null;if(4===e.length&&Array.isArray(e[0])){n="3 (network format)";const t=e[1],s=e[2],i=e[3];Rn(Array.isArray(t),"invalid network format: blobs not an array","fields[1]",t),Rn(Array.isArray(s),"invalid network format: commitments not an array","fields[2]",s),Rn(Array.isArray(i),"invalid network format: proofs not an array","fields[3]",i),Rn(t.length===s.length,"invalid network format: blobs/commitments length mismatch","fields",e),Rn(t.length===i.length,"invalid network format: blobs/proofs length mismatch","fields",e),r=[];for(let n=0;n<e[1].length;n++)r.push({data:t[n],commitment:s[n],proof:i[n]});e=e[0]}Rn(Array.isArray(e)&&(11===e.length||14===e.length),`invalid field count for transaction type: ${n}`,"data",Hn(t));const s={type:3,chainId:Io(e[0],"chainId"),nonce:Bo(e[1],"nonce"),maxPriorityFeePerGas:Io(e[2],"maxPriorityFeePerGas"),maxFeePerGas:Io(e[3],"maxFeePerGas"),gasPrice:null,gasLimit:Io(e[4],"gasLimit"),to:xo(e[5]),value:Io(e[6],"value"),data:Hn(e[7]),accessList:Po(e[8],"accessList"),maxFeePerBlobGas:Io(e[9],"maxFeePerBlobGas"),blobVersionedHashes:e[10]};r&&(s.blobs=r),Rn(null!=s.to,`invalid address for transaction type: ${n}`,"data",t),Rn(Array.isArray(s.blobVersionedHashes),"invalid blobVersionedHashes: must be an array","data",t);for(let i=0;i<s.blobVersionedHashes.length;i++)Rn(Ln(s.blobVersionedHashes[i],32),`invalid blobVersionedHash at index ${i}: must be length 32`,"data",t);return 11===e.length||To(s,e.slice(11)),s}(n));case 4:return t.from(function(t){const e=Wr(Fn(t).slice(1));Rn(Array.isArray(e)&&(10===e.length||13===e.length),"invalid field count for transaction type: 4","data",Hn(t));const n={type:4,chainId:Io(e[0],"chainId"),nonce:Bo(e[1],"nonce"),maxPriorityFeePerGas:Io(e[2],"maxPriorityFeePerGas"),maxFeePerGas:Io(e[3],"maxFeePerGas"),gasPrice:null,gasLimit:Io(e[4],"gasLimit"),to:xo(e[5]),value:Io(e[6],"value"),data:Hn(e[7]),accessList:Po(e[8],"accessList"),authorizationList:No(e[9],"authorizationList")};return 10===e.length||To(n,e.slice(10)),n}(n))}Cn(!1,"unsupported transaction type","UNSUPPORTED_OPERATION",{operation:"from"})}const n=new t;return null!=e.type&&(n.type=e.type),null!=e.to&&(n.to=e.to),null!=e.nonce&&(n.nonce=e.nonce),null!=e.gasLimit&&(n.gasLimit=e.gasLimit),null!=e.gasPrice&&(n.gasPrice=e.gasPrice),null!=e.maxPriorityFeePerGas&&(n.maxPriorityFeePerGas=e.maxPriorityFeePerGas),null!=e.maxFeePerGas&&(n.maxFeePerGas=e.maxFeePerGas),null!=e.maxFeePerBlobGas&&(n.maxFeePerBlobGas=e.maxFeePerBlobGas),null!=e.data&&(n.data=e.data),null!=e.value&&(n.value=e.value),null!=e.chainId&&(n.chainId=e.chainId),null!=e.signature&&(n.signature=Ta.from(e.signature)),null!=e.accessList&&(n.accessList=e.accessList),null!=e.authorizationList&&(n.authorizationList=e.authorizationList),null!=e.blobVersionedHashes&&(n.blobVersionedHashes=e.blobVersionedHashes),null!=e.kzg&&(n.kzg=e.kzg),null!=e.blobs&&(n.blobs=e.blobs),null!=e.hash&&(Rn(n.isSigned(),"unsigned transaction cannot define '.hash'","tx",e),Rn(n.hash===e.hash,"hash mismatch","tx",e)),null!=e.from&&(Rn(n.isSigned(),"unsigned transaction cannot define '.from'","tx",e),Rn(n.from.toLowerCase()===(e.from||"").toLowerCase(),"from mismatch","tx",e)),n}};function Uo(t){return di(hr(t))}const Fo=new Map([[8217,"apostrophe"],[8260,"fraction slash"],[12539,"middle dot"]]);function Mo(t){return function(t){let e=0;return()=>t[e++]}(function(t){let e=0;function n(){return t[e++]<<8|t[e++]}let r=n(),s=1,i=[0,1];for(let b=1;b<r;b++)i.push(s+=n());let a=n(),o=e;e+=a;let c=0,l=0;function u(){return 0==c&&(l=l<<8|t[e++],c=8),l>>--c&1}const h=2**31,f=h>>>1,d=f>>1,p=h-1;let g=0;for(let b=0;b<31;b++)g=g<<1|u();let m=[],y=0,w=h;for(;;){let t=Math.floor(((g-y+1)*s-1)/w),e=0,n=r;for(;n-e>1;){let r=e+n>>>1;t<i[r]?n=r:e=r}if(0==e)break;m.push(e);let a=y+Math.floor(w*i[e]/s),o=y+Math.floor(w*i[e+1]/s)-1;for(;0==((a^o)&f);)g=g<<1&p|u(),a=a<<1&p,o=o<<1&p|1;for(;a&~o&d;)g=g&f|g<<1&p>>>1|u(),a=a<<1^f,o=(o^f)<<1|f|1;y=a,w=1+o-a}let A=r-4;return m.map((e=>{switch(e-A){case 3:return A+65792+(t[o++]<<16|t[o++]<<8|t[o++]);case 2:return A+256+(t[o++]<<8|t[o++]);case 1:return A+t[o++];default:return e-1}}))}(function(t){let e=[];[..."ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"].forEach(((t,n)=>e[t.charCodeAt(0)]=n));let n=t.length,r=new Uint8Array(6*n>>3);for(let s=0,i=0,a=0,o=0;s<n;s++)o=o<<6|e[t.charCodeAt(s)],a+=6,a>=8&&(r[i++]=o>>(a-=8));return r}(t)))}function Lo(t){return 1&t?~t>>1:t>>1}function Do(t,e){let n=Array(t);for(let r=0,s=0;r<t;r++)n[r]=s+=Lo(e());return n}function Go(t,e=0){let n=[];for(;;){let r=t(),s=t();if(!s)break;e+=r;for(let t=0;t<s;t++)n.push(e+t);e+=s+1}return n}function Ho(t){return jo((()=>{let e=Go(t);if(e.length)return e}))}function Qo(t){let e=[];for(;;){let n=t();if(0==n)break;e.push(Wo(n,t))}for(;;){let n=t()-1;if(n<0)break;e.push(Vo(n,t))}return e.flat()}function jo(t){let e=[];for(;;){let n=t(e.length);if(!n)break;e.push(n)}return e}function zo(t,e,n){let r=Array(t).fill().map((()=>[]));for(let s=0;s<e;s++)Do(t,n).forEach(((t,e)=>r[e].push(t)));return r}function Wo(t,e){let n=1+e(),r=e(),s=jo(e);return zo(s.length,1+t,e).flatMap(((t,e)=>{let[i,...a]=t;return Array(s[e]).fill().map(((t,e)=>{let s=e*r;return[i+e*n,a.map((t=>t+s))]}))}))}function Vo(t,e){return zo(1+e(),1+t,e).map((t=>[t[0],t.slice(1)]))}function Jo(t){return`{${function(t){return t.toString(16).toUpperCase().padStart(2,"0")}(t)}}`}function Ko(t){let e=t.length;if(e<4096)return String.fromCodePoint(...t);let n=[];for(let r=0;r<e;)n.push(String.fromCodePoint(...t.slice(r,r+=4096)));return n.join("")}function qo(t,e){let n=t.length,r=n-e.length;for(let s=0;0==r&&s<n;s++)r=t[s]-e[s];return r}const Zo=44032,_o=4352,Yo=4449,Xo=4519,$o=28,tc=588;function ec(t){return t>>24&255}function nc(t){return 16777215&t}let rc,sc,ic,ac;function oc(t){return t>=Zo&&t<55204}function cc(t,e){if(t>=_o&&t<4371&&e>=Yo&&e<4470)return Zo+(t-_o)*tc+(e-Yo)*$o;if(oc(t)&&e>Xo&&e<4547&&(t-Zo)%$o==0)return t+(e-Xo);{let n=ac.get(t);return n&&(n=n.get(e),n)?n:-1}}function lc(t){rc||function(){let t=Mo("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");rc=new Map(Ho(t).flatMap(((t,e)=>t.map((t=>[t,e+1<<24]))))),sc=new Set(Go(t)),ic=new Map,ac=new Map;for(let[e,n]of Qo(t)){if(!sc.has(e)&&2==n.length){let[t,r]=n,s=ac.get(t);s||(s=new Map,ac.set(t,s)),s.set(r,e)}ic.set(e,n.reverse())}}();let e=[],n=[],r=!1;function s(t){let n=rc.get(t);n&&(r=!0,t|=n),e.push(t)}for(let i of t)for(;;){if(i<128)e.push(i);else if(oc(i)){let t=i-Zo,e=t%tc/$o|0,n=t%$o;s(_o+(t/tc|0)),s(Yo+e),n>0&&s(Xo+n)}else{let t=ic.get(i);t?n.push(...t):s(i)}if(!n.length)break;i=n.pop()}if(r&&e.length>1){let t=ec(e[0]);for(let n=1;n<e.length;n++){let r=ec(e[n]);if(0==r||t<=r){t=r;continue}let s=n-1;for(;;){let n=e[s+1];if(e[s+1]=e[s],e[s]=n,!s)break;if(t=ec(e[--s]),t<=r)break}t=ec(e[n])}}return e}function uc(t){return lc(t).map(nc)}function hc(t){return function(t){let e=[],n=[],r=-1,s=0;for(let i of t){let t=ec(i),a=nc(i);if(-1==r)0==t?r=a:e.push(a);else if(s>0&&s>=t)0==t?(e.push(r,...n),n.length=0,r=a):n.push(a),s=t;else{let i=cc(r,a);i>=0?r=i:0==s&&0==t?(e.push(r),r=a):(n.push(a),s=t)}}return r>=0&&e.push(r,...n),e}(lc(t))}const fc=".",dc=t=>Array.from(t);function pc(t,e){return t.P.has(e)||t.Q.has(e)}class gc extends Array{get is_emoji(){return!0}}let mc,yc,wc,Ac,bc,Ec,vc,kc,xc,Pc,Nc;function Bc(){if(mc)return;let t=Mo("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");const e=()=>Go(t),n=()=>new Set(e()),r=(t,e)=>e.forEach((e=>t.add(e)));mc=new Map(Qo(t)),yc=n(),wc=e(),Ac=new Set(e().map((t=>wc[t]))),wc=new Set(wc),bc=n(),n();let s=Ho(t),i=t();const a=()=>{let t=new Set;return e().forEach((e=>r(t,s[e]))),r(t,e()),t};Ec=jo((e=>{let n=jo(t).map((t=>t+96));if(n.length){let r=e>=i;return n[0]-=32,n=Ko(n),r&&(n=`Restricted[${n}]`),{N:n,P:a(),Q:a(),M:!t(),R:r}}})),vc=n(),kc=new Map;let o=e().concat(dc(vc)).sort(((t,e)=>t-e));o.forEach(((e,n)=>{let r=t(),s=o[n]=r?o[n-r]:{V:[],M:new Map};s.V.push(e),vc.has(e)||kc.set(e,s)}));for(let{V:u,M:h}of new Set(kc.values())){let t=[];for(let n of u){let e=Ec.filter((t=>pc(t,n))),s=t.find((({G:t})=>e.some((e=>t.has(e)))));s||(s={G:new Set,V:[]},t.push(s)),s.V.push(n),r(s.G,e)}let e=t.flatMap((t=>dc(t.G)));for(let{G:n,V:r}of t){let t=new Set(e.filter((t=>!n.has(t))));for(let e of r)h.set(e,t)}}xc=new Set;let c=new Set;const l=t=>xc.has(t)?c.add(t):xc.add(t);for(let u of Ec){for(let t of u.P)l(t);for(let t of u.Q)l(t)}for(let u of xc)kc.has(u)||c.has(u)||kc.set(u,1);r(xc,uc(xc)),Pc=function(t){let e=[],n=Go(t);return function t({S:n,B:r},s,i){if(!(4&n&&i===s[s.length-1])){2&n&&(i=s[s.length-1]),1&n&&e.push(s);for(let e of r)for(let n of e.Q)t(e,[...s,n],i)}}(function e(r){return{S:t(),B:jo((()=>{let r=Go(t).map((t=>n[t]));if(r.length)return e(r)})),Q:r}}([]),[]),e}(t).map((t=>gc.from(t))).sort(qo),Nc=new Map;for(let u of Pc){let t=[Nc];for(let e of u){let n=t.map((t=>{let n=t.get(e);return n||(n=new Map,t.set(e,n)),n}));65039===e?t.push(...n):t=n}for(let e of t)e.V=u}}function Ic(t){return(Rc(t)?"":`${Oc(Cc([t]))} `)+Jo(t)}function Oc(t){return`"${t}"‎`}function Cc(t,e=1/0,n=Jo){let r=[];var s;s=t[0],Bc(),wc.has(s)&&r.push("◌"),t.length>e&&(e>>=1,t=[...t.slice(0,e),8230,...t.slice(-e)]);let i=0,a=t.length;for(let o=0;o<a;o++){let e=t[o];Rc(e)&&(r.push(Ko(t.slice(i,o))),r.push(n(e)),i=o+1)}return r.push(Ko(t.slice(i,a))),r.join("")}function Rc(t){return Bc(),bc.has(t)}function Tc(t){return e=function(t,e,n){if(!t)return[];Bc();let r=0;return t.split(fc).map((t=>{let s=function(t){let e=[];for(let n=0,r=t.length;n<r;){let r=t.codePointAt(n);n+=r<65536?1:2,e.push(r)}return e}(t),i={input:s,offset:r};r+=s.length+1;try{let t,r=i.tokens=function(t,e,n){let r=[],s=[];for(t=t.slice().reverse();t.length;){let i=Lc(t);if(i)s.length&&(r.push(e(s)),s=[]),r.push(n(i));else{let e=t.pop();if(xc.has(e))s.push(e);else{let t=mc.get(e);if(t)s.push(...t);else if(!yc.has(e))throw Sc(e)}}}return s.length&&r.push(e(s)),r}(s,e,n),a=r.length;if(!a)throw new Error("empty label");let o=i.output=r.flat();if(function(t){for(let e=t.lastIndexOf(95);e>0;)if(95!==t[--e])throw new Error("underscore allowed only at start")}(o),!(i.emoji=a>1||r[0].is_emoji)&&o.every((t=>t<128)))!function(t){if(t.length>=4&&45==t[2]&&45==t[3])throw new Error(`invalid label extension: "${Ko(t.slice(0,4))}"`)}(o),t="ASCII";else{let e=r.flatMap((t=>t.is_emoji?[]:t));if(e.length){if(wc.has(o[0]))throw Fc("leading combining mark");for(let t=1;t<a;t++){let e=r[t];if(!e.is_emoji&&wc.has(e[0]))throw Fc(`emoji + combining mark: "${Ko(r[t-1])} + ${Cc([e[0]])}"`)}!function(t){let e=t[0],n=Fo.get(e);if(n)throw Fc(`leading ${n}`);let r=t.length,s=-1;for(let i=1;i<r;i++){e=t[i];let r=Fo.get(e);if(r){if(s==i)throw Fc(`${n} + ${r}`);s=i+1,n=r}}if(s==r)throw Fc(`trailing ${n}`)}(o);let n=dc(new Set(e)),[s]=function(t){let e=Ec;for(let n of t){let t=e.filter((t=>pc(t,n)));if(!t.length)throw Ec.some((t=>pc(t,n)))?Uc(e[0],n):Sc(n);if(e=t,1==t.length)break}return e}(n);!function(t,e){for(let n of e)if(!pc(t,n))throw Uc(t,n);if(t.M){let t=uc(e);for(let e=1,n=t.length;e<n;e++)if(Ac.has(t[e])){let r=e+1;for(let s;r<n&&Ac.has(s=t[r]);r++)for(let n=e;n<r;n++)if(t[n]==s)throw new Error(`duplicate non-spacing marks: ${Ic(s)}`);if(r-e>4)throw new Error(`excessive non-spacing marks: ${Oc(Cc(t.slice(e-1,r)))} (${r-e}/4)`);e=r}}}(s,e),function(t,e){let n,r=[];for(let s of e){let t=kc.get(s);if(1===t)return;if(t){let e=t.M.get(s);if(n=n?n.filter((t=>e.has(t))):dc(e),!n.length)return}else r.push(s)}if(n)for(let s of n)if(r.every((t=>pc(s,t))))throw new Error(`whole-script confusable: ${t.N}/${s.N}`)}(s,n),t=s.N}else t="Emoji"}i.type=t}catch(a){i.error=a}return i}))}(t,hc,Mc),e.map((({input:t,error:n,output:r})=>{if(n){let r=n.message;throw new Error(1==e.length?r:`Invalid label ${Oc(Cc(t,63))}: ${r}`)}return Ko(r)})).join(fc);var e}function Sc(t){return new Error(`disallowed character: ${Ic(t)}`)}function Uc(t,e){let n=Ic(e),r=Ec.find((t=>t.P.has(e)));return r&&(n=`${r.N} ${n}`),new Error(`illegal mixture: ${t.N} + ${n}`)}function Fc(t){return new Error(`illegal placement: ${t}`)}function Mc(t){return t.filter((t=>65039!=t))}function Lc(t,e){let n,r=Nc,s=t.length;for(;s&&(r=r.get(t[--s]),r);){let{V:e}=r;e&&(n=e,t.length=s)}return n}const Dc=new Uint8Array(32);function Gc(t){return Rn(0!==t.length,"invalid ENS name; empty component","comp",t),t}function Hc(t){const e=hr(function(t){try{if(0===t.length)throw new Error("empty label");return Tc(t)}catch(e){Rn(!1,`invalid ENS name (${e.message})`,"name",t)}}(t)),n=[];if(0===t.length)return n;let r=0;for(let s=0;s<e.length;s++){46===e[s]&&(n.push(Gc(e.slice(r,s))),r=s+1)}return Rn(r<e.length,"invalid ENS name; empty component","name",t),n.push(Gc(e.slice(r))),n}function Qc(t){Rn("string"==typeof t,"invalid ENS name; not a string","name",t),Rn(t.length,"invalid ENS name (empty label)","name",t);let e=Dc;const n=Hc(t);for(;n.length;)e=di(Qn([e,di(n.pop())]));return Hn(e)}function jc(t,e){const n=e;return Rn(n<=255,"DNS encoded label cannot exceed 255","length",n),Hn(Qn(Hc(t).map((e=>{Rn(e.length<=n,`label ${JSON.stringify(t)} exceeds ${n} bytes`,"name",t);const r=new Uint8Array(e.length+1);return r.set(e,1),r[0]=r.length-1,r}))))+"00"}Dc.fill(0);const zc=new Uint8Array(32);zc.fill(0);const Wc=BigInt(-1),Vc=BigInt(0),Jc=BigInt(1),Kc=BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");const qc=rr(Jc,32),Zc=rr(Vc,32),_c={name:"string",version:"string",chainId:"uint256",verifyingContract:"address",salt:"bytes32"},Yc=["name","version","chainId","verifyingContract","salt"];function Xc(t){return function(e){return Rn("string"==typeof e,`invalid domain value for ${JSON.stringify(t)}`,`domain.${t}`,e),e}}const $c={name:Xc("name"),version:Xc("version"),chainId:function(t){const e=Xn(t,"domain.chainId");return Rn(e>=0,"invalid chain ID","domain.chainId",t),Number.isSafeInteger(e)?Number(e):ir(e)},verifyingContract:function(t){try{return Ga(t).toLowerCase()}catch(e){}Rn(!1,'invalid domain value "verifyingContract"',"domain.verifyingContract",t)},salt:function(t){const e=Fn(t,"domain.salt");return Rn(32===e.length,'invalid domain value "salt"',"domain.salt",t),Hn(e)}};function tl(t){{const e=t.match(/^(u?)int(\d+)$/);if(e){const n=""===e[1],r=parseInt(e[2]);Rn(r%8==0&&0!==r&&r<=256&&e[2]===String(r),"invalid numeric width","type",t);const s=Yn(Kc,n?r-1:r),i=n?(s+Jc)*Wc:Vc;return function(e){const r=Xn(e,"value");return Rn(r>=i&&r<=s,`value out-of-bounds for ${t}`,"value",r),rr(n?_n(r,256):r,32)}}}{const e=t.match(/^bytes(\d+)$/);if(e){const n=parseInt(e[1]);return Rn(0!==n&&n<=32&&e[1]===String(n),"invalid bytes width","type",t),function(e){return Rn(Fn(e).length===n,`invalid length for ${t}`,"value",e),function(t){const e=Fn(t),n=e.length%32;return n?Qn([e,zc.slice(n)]):Hn(e)}(e)}}}switch(t){case"address":return function(t){return Vn(Ga(t),32)};case"bool":return function(t){return t?qc:Zc};case"bytes":return function(t){return di(t)};case"string":return function(t){return Uo(t)}}return null}function el(t,e){return`${t}(${e.map((({name:t,type:e})=>e+" "+t)).join(",")})`}function nl(t){const e=t.match(/^([^\x5b]*)((\x5b\d*\x5d)*)(\x5b(\d*)\x5d)$/);return e?{base:e[1],index:e[2]+e[4],array:{base:e[1],prefix:e[1]+e[2],count:e[5]?parseInt(e[5]):-1}}:{base:t}}bt=new WeakMap,Et=new WeakMap,vt=new WeakMap,kt=new WeakSet,xt=function(t){{const e=tl(t);if(e)return e}const e=nl(t).array;if(e){const t=e.prefix,n=this.getEncoder(t);return r=>{Rn(-1===e.count||e.count===r.length,`array length mismatch; expected length ${e.count}`,"value",r);let s=r.map(n);return wn(this,Et).has(t)&&(s=s.map(di)),di(Qn(s))}}const n=this.types[t];if(n){const e=Uo(wn(this,Et).get(t));return t=>{const r=n.map((({name:e,type:n})=>{const r=this.getEncoder(n)(t[e]);return wn(this,Et).has(n)?di(r):r}));return r.unshift(e),Qn(r)}}Rn(!1,`unknown type: ${t}`,"type",t)};let rl=class t{constructor(t){An(this,kt),mn(this,"primaryType"),An(this,bt),An(this,Et),An(this,vt),bn(this,Et,new Map),bn(this,vt,new Map);const e=new Map,n=new Map,r=new Map,s={};Object.keys(t).forEach((i=>{s[i]=t[i].map((({name:e,type:n})=>{let{base:r,index:s}=nl(n);return"int"!==r||t.int||(r="int256"),"uint"!==r||t.uint||(r="uint256"),{name:e,type:r+(s||"")}})),e.set(i,new Set),n.set(i,[]),r.set(i,new Set)})),bn(this,bt,JSON.stringify(s));for(const a in s){const r=new Set;for(const i of s[a]){Rn(!r.has(i.name),`duplicate variable name ${JSON.stringify(i.name)} in ${JSON.stringify(a)}`,"types",t),r.add(i.name);const s=nl(i.type).base;Rn(s!==a,`circular type reference to ${JSON.stringify(s)}`,"types",t);tl(s)||(Rn(n.has(s),`unknown type ${JSON.stringify(s)}`,"types",t),n.get(s).push(a),e.get(a).add(s))}}const i=Array.from(n.keys()).filter((t=>0===n.get(t).length));Rn(0!==i.length,"missing primary type","types",t),Rn(1===i.length,`ambiguous primary types or unused types: ${i.map((t=>JSON.stringify(t))).join(", ")}`,"types",t),Pn(this,{primaryType:i[0]}),function s(i,a){Rn(!a.has(i),`circular type reference to ${JSON.stringify(i)}`,"types",t),a.add(i);for(const t of e.get(i))if(n.has(t)){s(t,a);for(const e of a)r.get(e).add(t)}a.delete(i)}(this.primaryType,new Set);for(const[a,o]of r){const t=Array.from(o);t.sort(),wn(this,Et).set(a,el(a,s[a])+t.map((t=>el(t,s[t]))).join(""))}}get types(){return JSON.parse(wn(this,bt))}getEncoder(t){let e=wn(this,vt).get(t);return e||(e=En(this,kt,xt).call(this,t),wn(this,vt).set(t,e)),e}encodeType(t){const e=wn(this,Et).get(t);return Rn(e,`unknown type: ${JSON.stringify(t)}`,"name",t),e}encodeData(t,e){return this.getEncoder(t)(e)}hashStruct(t,e){return di(this.encodeData(t,e))}encode(t){return this.encodeData(this.primaryType,t)}hash(t){return this.hashStruct(this.primaryType,t)}_visit(t,e,n){if(tl(t))return n(t,e);const r=nl(t).array;if(r)return Rn(-1===r.count||r.count===e.length,`array length mismatch; expected length ${r.count}`,"value",e),e.map((t=>this._visit(r.prefix,t,n)));const s=this.types[t];if(s)return s.reduce(((t,{name:r,type:s})=>(t[r]=this._visit(s,e[r],n),t)),{});Rn(!1,`unknown type: ${t}`,"type",t)}visit(t,e){return this._visit(this.primaryType,t,e)}static from(e){return new t(e)}static getPrimaryType(e){return t.from(e).primaryType}static hashStruct(e,n,r){return t.from(n).hashStruct(e,r)}static hashDomain(e){const n=[];for(const t in e){if(null==e[t])continue;const r=_c[t];Rn(r,`invalid typed-data domain key: ${JSON.stringify(t)}`,"domain",e),n.push({name:t,type:r})}return n.sort(((t,e)=>Yc.indexOf(t.name)-Yc.indexOf(e.name))),t.hashStruct("EIP712Domain",{EIP712Domain:n},e)}static encode(e,n,r){return Qn(["0x1901",t.hashDomain(e),t.from(n).hash(r)])}static hash(e,n,r){return di(t.encode(e,n,r))}static async resolveNames(e,n,r,s){e=Object.assign({},e);for(const t in e)null==e[t]&&delete e[t];const i={};e.verifyingContract&&!Ln(e.verifyingContract,20)&&(i[e.verifyingContract]="0x");const a=t.from(n);a.visit(r,((t,e)=>("address"!==t||Ln(e,20)||(i[e]="0x"),e)));for(const t in i)i[t]=await s(t);return e.verifyingContract&&i[e.verifyingContract]&&(e.verifyingContract=i[e.verifyingContract]),{domain:e,value:r=a.visit(r,((t,e)=>"address"===t&&i[e]?i[e]:e))}}static getPayload(e,n,r){t.hashDomain(e);const s={},i=[];Yc.forEach((t=>{const n=e[t];null!=n&&(s[t]=$c[t](n),i.push({name:t,type:_c[t]}))}));const a=t.from(n);n=a.types;const o=Object.assign({},n);return Rn(null==o.EIP712Domain,"types must not contain EIP712Domain type","types.EIP712Domain",n),o.EIP712Domain=i,a.encode(r),{types:o,domain:s,primaryType:a.primaryType,message:a.visit(r,((t,e)=>{if(t.match(/^bytes(\d*)/))return Hn(Fn(e));if(t.match(/^u?int/))return Xn(e).toString();switch(t){case"address":return e.toLowerCase();case"bool":return!!e;case"string":return Rn("string"==typeof e,"invalid string","value",e),e}Rn(!1,"unsupported type","type",t)}))}}};function sl(t){const e=new Set;return t.forEach((t=>e.add(t))),Object.freeze(e)}const il=sl("external public payable override".split(" ")),al="constant external internal payable private public pure view override",ol=sl(al.split(" ")),cl="constructor error event fallback function receive struct",ll=sl(cl.split(" ")),ul="calldata memory storage payable indexed",hl=sl(ul.split(" ")),fl=sl([cl,ul,"tuple returns",al].join(" ").split(" ")),dl={"(":"OPEN_PAREN",")":"CLOSE_PAREN","[":"OPEN_BRACKET","]":"CLOSE_BRACKET",",":"COMMA","@":"AT"},pl=new RegExp("^(\\s*)"),gl=new RegExp("^([0-9]+)"),ml=new RegExp("^([a-zA-Z$_][a-zA-Z0-9$_]*)"),yl=new RegExp("^([a-zA-Z$_][a-zA-Z0-9$_]*)$"),wl=new RegExp("^(address|bool|bytes([0-9]*)|string|u?int([0-9]*))$"),Al=class t{constructor(t){An(this,Bt),An(this,Pt),An(this,Nt),bn(this,Pt,0),bn(this,Nt,t.slice())}get offset(){return wn(this,Pt)}get length(){return wn(this,Nt).length-wn(this,Pt)}clone(){return new t(wn(this,Nt))}reset(){bn(this,Pt,0)}popKeyword(t){const e=this.peek();if("KEYWORD"!==e.type||!t.has(e.text))throw new Error(`expected keyword ${e.text}`);return this.pop().text}popType(t){if(this.peek().type!==t){const e=this.peek();throw new Error(`expected ${t}; got ${e.type} ${JSON.stringify(e.text)}`)}return this.pop().text}popParen(){const t=this.peek();if("OPEN_PAREN"!==t.type)throw new Error("bad start");const e=En(this,Bt,It).call(this,wn(this,Pt)+1,t.match+1);return bn(this,Pt,t.match+1),e}popParams(){const t=this.peek();if("OPEN_PAREN"!==t.type)throw new Error("bad start");const e=[];for(;wn(this,Pt)<t.match-1;){const t=this.peek().linkNext;e.push(En(this,Bt,It).call(this,wn(this,Pt)+1,t)),bn(this,Pt,t)}return bn(this,Pt,t.match+1),e}peek(){if(wn(this,Pt)>=wn(this,Nt).length)throw new Error("out-of-bounds");return wn(this,Nt)[wn(this,Pt)]}peekKeyword(t){const e=this.peekType("KEYWORD");return null!=e&&t.has(e)?e:null}peekType(t){if(0===this.length)return null;const e=this.peek();return e.type===t?e.text:null}pop(){const t=this.peek();return vn(this,Pt)._++,t}toString(){const t=[];for(let e=wn(this,Pt);e<wn(this,Nt).length;e++){const n=wn(this,Nt)[e];t.push(`${n.type}:${n.text}`)}return`<TokenString ${t.join(" ")}>`}};Pt=new WeakMap,Nt=new WeakMap,Bt=new WeakSet,It=function(t=0,e=0){return new Al(wn(this,Nt).slice(t,e).map((e=>Object.freeze(Object.assign({},e,{match:e.match-t,linkBack:e.linkBack-t,linkNext:e.linkNext-t})))))};let bl=Al;function El(t){const e=[],n=e=>{const n=i<t.length?JSON.stringify(t[i]):"$EOI";throw new Error(`invalid token ${n} at ${i}: ${e}`)};let r=[],s=[],i=0;for(;i<t.length;){let a=t.substring(i),o=a.match(pl);o&&(i+=o[1].length,a=t.substring(i));const c={depth:r.length,linkBack:-1,linkNext:-1,match:-1,type:"",text:"",offset:i,value:-1};e.push(c);let l=dl[a[0]]||"";if(l){if(c.type=l,c.text=a[0],i++,"OPEN_PAREN"===l)r.push(e.length-1),s.push(e.length-1);else if("CLOSE_PAREN"==l)0===r.length&&n("no matching open bracket"),c.match=r.pop(),e[c.match].match=e.length-1,c.depth--,c.linkBack=s.pop(),e[c.linkBack].linkNext=e.length-1;else if("COMMA"===l)c.linkBack=s.pop(),e[c.linkBack].linkNext=e.length-1,s.push(e.length-1);else if("OPEN_BRACKET"===l)c.type="BRACKET";else if("CLOSE_BRACKET"===l){let t=e.pop().text;if(e.length>0&&"NUMBER"===e[e.length-1].type){const n=e.pop().text;t=n+t,e[e.length-1].value=nr(n)}if(0===e.length||"BRACKET"!==e[e.length-1].type)throw new Error("missing opening bracket");e[e.length-1].text+=t}}else if(o=a.match(ml),o){if(c.text=o[1],i+=c.text.length,fl.has(c.text)){c.type="KEYWORD";continue}if(c.text.match(wl)){c.type="TYPE";continue}c.type="ID"}else{if(o=a.match(gl),!o)throw new Error(`unexpected token ${JSON.stringify(a[0])} at position ${i}`);c.text=o[1],c.type="NUMBER",i+=c.text.length}}return new bl(e.map((t=>Object.freeze(t))))}function vl(t,e){let n=[];for(const r in e.keys())t.has(r)&&n.push(r);if(n.length>1)throw new Error(`conflicting types: ${n.join(", ")}`)}function kl(t,e){if(e.peekKeyword(ll)){const n=e.pop().text;if(n!==t)throw new Error(`expected ${t}, got ${n}`)}return e.popType("ID")}function xl(t,e){const n=new Set;for(;;){const r=t.peekType("KEYWORD");if(null==r||e&&!e.has(r))break;if(t.pop(),n.has(r))throw new Error(`duplicate keywords: ${JSON.stringify(r)}`);n.add(r)}return Object.freeze(n)}function Pl(t){let e=xl(t,ol);return vl(e,sl("constant payable nonpayable".split(" "))),vl(e,sl("pure view payable nonpayable".split(" "))),e.has("view")?"view":e.has("pure")?"pure":e.has("payable")?"payable":e.has("nonpayable")?"nonpayable":e.has("constant")?"view":"nonpayable"}function Nl(t,e){return t.popParams().map((t=>Hl.from(t,e)))}function Bl(t){if(t.peekType("AT")){if(t.pop(),t.peekType("NUMBER"))return Xn(t.pop().text);throw new Error("invalid gas")}return null}function Il(t){if(t.length)throw new Error(`unexpected tokens at offset ${t.offset}: ${t.toString()}`)}const Ol=new RegExp(/^(.*)\[([0-9]*)\]$/);function Cl(t){const e=t.match(wl);if(Rn(e,"invalid type","type",t),"uint"===t)return"uint256";if("int"===t)return"int256";if(e[2]){const n=parseInt(e[2]);Rn(0!==n&&n<=32,"invalid bytes length","type",t)}else if(e[3]){const n=parseInt(e[3]);Rn(0!==n&&n<=256&&n%8==0,"invalid numeric width","type",t)}return t}const Rl={},Tl=Symbol.for("_ethers_internal"),Sl="_ParamTypeInternal",Ul="_ErrorInternal",Fl="_EventInternal",Ml="_ConstructorInternal",Ll="_FallbackInternal",Dl="_FunctionInternal",Gl="_StructInternal";Ot=new WeakSet,Ct=function(t,e,n,r){if(this.isArray()){if(!Array.isArray(e))throw new Error("invalid array value");if(-1!==this.arrayLength&&e.length!==this.arrayLength)throw new Error("array is wrong length");const s=this.arrayChildren,i=e.slice();return i.forEach(((e,r)=>{var a;En(a=s,Ot,Ct).call(a,t,e,n,(t=>{i[r]=t}))})),void r(i)}if(this.isTuple()){const s=this.components;let i;if(Array.isArray(e))i=e.slice();else{if(null==e||"object"!=typeof e)throw new Error("invalid tuple value");i=s.map((t=>{if(!t.name)throw new Error("cannot use object value with unnamed components");if(!(t.name in e))throw new Error(`missing value for component ${t.name}`);return e[t.name]}))}if(i.length!==this.components.length)throw new Error("array is wrong length");return i.forEach(((e,r)=>{var a;En(a=s[r],Ot,Ct).call(a,t,e,n,(t=>{i[r]=t}))})),void r(i)}const s=n(this.type,e);s.then?t.push(async function(){r(await s)}()):r(s)};let Hl=class t{constructor(t,e,n,r,s,i,a,o){if(An(this,Ot),mn(this,"name"),mn(this,"type"),mn(this,"baseType"),mn(this,"indexed"),mn(this,"components"),mn(this,"arrayLength"),mn(this,"arrayChildren"),Sn(t,Rl,"ParamType"),Object.defineProperty(this,Tl,{value:Sl}),i&&(i=Object.freeze(i.slice())),"array"===r){if(null==a||null==o)throw new Error("")}else if(null!=a||null!=o)throw new Error("");if("tuple"===r){if(null==i)throw new Error("")}else if(null!=i)throw new Error("");Pn(this,{name:e,type:n,baseType:r,indexed:s,components:i,arrayLength:a,arrayChildren:o})}format(t){if(null==t&&(t="sighash"),"json"===t){const e=this.name||"";if(this.isArray()){const t=JSON.parse(this.arrayChildren.format("json"));return t.name=e,t.type+=`[${this.arrayLength<0?"":String(this.arrayLength)}]`,JSON.stringify(t)}const n={type:"tuple"===this.baseType?"tuple":this.type,name:e};return"boolean"==typeof this.indexed&&(n.indexed=this.indexed),this.isTuple()&&(n.components=this.components.map((e=>JSON.parse(e.format(t))))),JSON.stringify(n)}let e="";return this.isArray()?(e+=this.arrayChildren.format(t),e+=`[${this.arrayLength<0?"":String(this.arrayLength)}]`):this.isTuple()?e+="("+this.components.map((e=>e.format(t))).join("full"===t?", ":",")+")":e+=this.type,"sighash"!==t&&(!0===this.indexed&&(e+=" indexed"),"full"===t&&this.name&&(e+=" "+this.name)),e}isArray(){return"array"===this.baseType}isTuple(){return"tuple"===this.baseType}isIndexable(){return null!=this.indexed}walk(t,e){if(this.isArray()){if(!Array.isArray(t))throw new Error("invalid array value");if(-1!==this.arrayLength&&t.length!==this.arrayLength)throw new Error("array is wrong length");const n=this;return t.map((t=>n.arrayChildren.walk(t,e)))}if(this.isTuple()){if(!Array.isArray(t))throw new Error("invalid tuple value");if(t.length!==this.components.length)throw new Error("array is wrong length");const n=this;return t.map(((t,r)=>n.components[r].walk(t,e)))}return e(this.type,t)}async walkAsync(t,e){const n=[],r=[t];return En(this,Ot,Ct).call(this,n,t,e,(t=>{r[0]=t})),n.length&&await Promise.all(n),r[0]}static from(e,n){if(t.isParamType(e))return e;if("string"==typeof e)try{return t.from(El(e),n)}catch(o){Rn(!1,"invalid param type","obj",e)}else if(e instanceof bl){let r="",s="",i=null;xl(e,sl(["tuple"])).has("tuple")||e.peekType("OPEN_PAREN")?(s="tuple",i=e.popParams().map((e=>t.from(e))),r=`tuple(${i.map((t=>t.format())).join(",")})`):(r=Cl(e.popType("TYPE")),s=r);let a=null,o=null;for(;e.length&&e.peekType("BRACKET");){const n=e.pop();a=new t(Rl,"",r,s,null,i,o,a),o=n.value,r+=n.text,s="array",i=null}let c=null;if(xl(e,hl).has("indexed")){if(!n)throw new Error("");c=!0}const l=e.peekType("ID")?e.pop().text:"";if(e.length)throw new Error("leftover tokens");return new t(Rl,l,r,s,c,i,o,a)}const r=e.name;Rn(!r||"string"==typeof r&&r.match(yl),"invalid name","obj.name",r);let s=e.indexed;null!=s&&(Rn(n,"parameter cannot be indexed","obj.indexed",e.indexed),s=!!s);let i=e.type,a=i.match(Ol);if(a){const n=parseInt(a[2]||"-1"),o=t.from({type:a[1],components:e.components});return new t(Rl,r||"",i,"array",s,null,n,o)}if("tuple"===i||i.startsWith("tuple(")||i.startsWith("(")){const n=null!=e.components?e.components.map((e=>t.from(e))):null;return new t(Rl,r||"",i,"tuple",s,n,null,null)}return i=Cl(e.type),new t(Rl,r||"",i,i,s,null,null,null)}static isParamType(t){return t&&t[Tl]===Sl}};class Ql{constructor(t,e,n){mn(this,"type"),mn(this,"inputs"),Sn(t,Rl,"Fragment"),Pn(this,{type:e,inputs:n=Object.freeze(n.slice())})}static from(t){if("string"==typeof t){try{Ql.from(JSON.parse(t))}catch(e){}return Ql.from(El(t))}if(t instanceof bl){switch(t.peekKeyword(ll)){case"constructor":return Jl.from(t);case"error":return Wl.from(t);case"event":return Vl.from(t);case"fallback":case"receive":return Kl.from(t);case"function":return ql.from(t);case"struct":return Zl.from(t)}}else if("object"==typeof t){switch(t.type){case"constructor":return Jl.from(t);case"error":return Wl.from(t);case"event":return Vl.from(t);case"fallback":case"receive":return Kl.from(t);case"function":return ql.from(t);case"struct":return Zl.from(t)}Cn(!1,`unsupported type: ${t.type}`,"UNSUPPORTED_OPERATION",{operation:"Fragment.from"})}Rn(!1,"unsupported frgament object","obj",t)}static isConstructor(t){return Jl.isFragment(t)}static isError(t){return Wl.isFragment(t)}static isEvent(t){return Vl.isFragment(t)}static isFunction(t){return ql.isFragment(t)}static isStruct(t){return Zl.isFragment(t)}}class jl extends Ql{constructor(t,e,n,r){super(t,e,r),mn(this,"name"),Rn("string"==typeof n&&n.match(yl),"invalid identifier","name",n),r=Object.freeze(r.slice()),Pn(this,{name:n})}}function zl(t,e){return"("+e.map((e=>e.format(t))).join("full"===t?", ":",")+")"}class Wl extends jl{constructor(t,e,n){super(t,"error",e,n),Object.defineProperty(this,Tl,{value:Ul})}get selector(){return Uo(this.format("sighash")).substring(0,10)}format(t){if(null==t&&(t="sighash"),"json"===t)return JSON.stringify({type:"error",name:this.name,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[];return"sighash"!==t&&e.push("error"),e.push(this.name+zl(t,this.inputs)),e.join(" ")}static from(t){if(Wl.isFragment(t))return t;if("string"==typeof t)return Wl.from(El(t));if(t instanceof bl){const e=kl("error",t),n=Nl(t);return Il(t),new Wl(Rl,e,n)}return new Wl(Rl,t.name,t.inputs?t.inputs.map(Hl.from):[])}static isFragment(t){return t&&t[Tl]===Ul}}class Vl extends jl{constructor(t,e,n,r){super(t,"event",e,n),mn(this,"anonymous"),Object.defineProperty(this,Tl,{value:Fl}),Pn(this,{anonymous:r})}get topicHash(){return Uo(this.format("sighash"))}format(t){if(null==t&&(t="sighash"),"json"===t)return JSON.stringify({type:"event",anonymous:this.anonymous,name:this.name,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[];return"sighash"!==t&&e.push("event"),e.push(this.name+zl(t,this.inputs)),"sighash"!==t&&this.anonymous&&e.push("anonymous"),e.join(" ")}static getTopicHash(t,e){e=(e||[]).map((t=>Hl.from(t)));return new Vl(Rl,t,e,!1).topicHash}static from(t){if(Vl.isFragment(t))return t;if("string"==typeof t)try{return Vl.from(El(t))}catch(e){Rn(!1,"invalid event fragment","obj",t)}else if(t instanceof bl){const e=kl("event",t),n=Nl(t,!0),r=!!xl(t,sl(["anonymous"])).has("anonymous");return Il(t),new Vl(Rl,e,n,r)}return new Vl(Rl,t.name,t.inputs?t.inputs.map((t=>Hl.from(t,!0))):[],!!t.anonymous)}static isFragment(t){return t&&t[Tl]===Fl}}class Jl extends Ql{constructor(t,e,n,r,s){super(t,e,n),mn(this,"payable"),mn(this,"gas"),Object.defineProperty(this,Tl,{value:Ml}),Pn(this,{payable:r,gas:s})}format(t){if(Cn(null!=t&&"sighash"!==t,"cannot format a constructor for sighash","UNSUPPORTED_OPERATION",{operation:"format(sighash)"}),"json"===t)return JSON.stringify({type:"constructor",stateMutability:this.payable?"payable":"undefined",payable:this.payable,gas:null!=this.gas?this.gas:void 0,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[`constructor${zl(t,this.inputs)}`];return this.payable&&e.push("payable"),null!=this.gas&&e.push(`@${this.gas.toString()}`),e.join(" ")}static from(t){if(Jl.isFragment(t))return t;if("string"==typeof t)try{return Jl.from(El(t))}catch(e){Rn(!1,"invalid constuctor fragment","obj",t)}else if(t instanceof bl){xl(t,sl(["constructor"]));const e=Nl(t),n=!!xl(t,il).has("payable"),r=Bl(t);return Il(t),new Jl(Rl,"constructor",e,n,r)}return new Jl(Rl,"constructor",t.inputs?t.inputs.map(Hl.from):[],!!t.payable,null!=t.gas?t.gas:null)}static isFragment(t){return t&&t[Tl]===Ml}}class Kl extends Ql{constructor(t,e,n){super(t,"fallback",e),mn(this,"payable"),Object.defineProperty(this,Tl,{value:Ll}),Pn(this,{payable:n})}format(t){const e=0===this.inputs.length?"receive":"fallback";if("json"===t){const t=this.payable?"payable":"nonpayable";return JSON.stringify({type:e,stateMutability:t})}return`${e}()${this.payable?" payable":""}`}static from(t){if(Kl.isFragment(t))return t;if("string"==typeof t)try{return Kl.from(El(t))}catch(e){Rn(!1,"invalid fallback fragment","obj",t)}else if(t instanceof bl){const e=t.toString();Rn(t.peekKeyword(sl(["fallback","receive"])),"type must be fallback or receive","obj",e);if("receive"===t.popKeyword(sl(["fallback","receive"]))){const e=Nl(t);return Rn(0===e.length,"receive cannot have arguments","obj.inputs",e),xl(t,sl(["payable"])),Il(t),new Kl(Rl,[],!0)}let n=Nl(t);n.length?Rn(1===n.length&&"bytes"===n[0].type,"invalid fallback inputs","obj.inputs",n.map((t=>t.format("minimal"))).join(", ")):n=[Hl.from("bytes")];const r=Pl(t);if(Rn("nonpayable"===r||"payable"===r,"fallback cannot be constants","obj.stateMutability",r),xl(t,sl(["returns"])).has("returns")){const e=Nl(t);Rn(1===e.length&&"bytes"===e[0].type,"invalid fallback outputs","obj.outputs",e.map((t=>t.format("minimal"))).join(", "))}return Il(t),new Kl(Rl,n,"payable"===r)}if("receive"===t.type)return new Kl(Rl,[],!0);if("fallback"===t.type){const e=[Hl.from("bytes")],n="payable"===t.stateMutability;return new Kl(Rl,e,n)}Rn(!1,"invalid fallback description","obj",t)}static isFragment(t){return t&&t[Tl]===Ll}}class ql extends jl{constructor(t,e,n,r,s,i){super(t,"function",e,r),mn(this,"constant"),mn(this,"outputs"),mn(this,"stateMutability"),mn(this,"payable"),mn(this,"gas"),Object.defineProperty(this,Tl,{value:Dl});Pn(this,{constant:"view"===n||"pure"===n,gas:i,outputs:s=Object.freeze(s.slice()),payable:"payable"===n,stateMutability:n})}get selector(){return Uo(this.format("sighash")).substring(0,10)}format(t){if(null==t&&(t="sighash"),"json"===t)return JSON.stringify({type:"function",name:this.name,constant:this.constant,stateMutability:"nonpayable"!==this.stateMutability?this.stateMutability:void 0,payable:this.payable,gas:null!=this.gas?this.gas:void 0,inputs:this.inputs.map((e=>JSON.parse(e.format(t)))),outputs:this.outputs.map((e=>JSON.parse(e.format(t))))});const e=[];return"sighash"!==t&&e.push("function"),e.push(this.name+zl(t,this.inputs)),"sighash"!==t&&("nonpayable"!==this.stateMutability&&e.push(this.stateMutability),this.outputs&&this.outputs.length&&(e.push("returns"),e.push(zl(t,this.outputs))),null!=this.gas&&e.push(`@${this.gas.toString()}`)),e.join(" ")}static getSelector(t,e){e=(e||[]).map((t=>Hl.from(t)));return new ql(Rl,t,"view",e,[],null).selector}static from(t){if(ql.isFragment(t))return t;if("string"==typeof t)try{return ql.from(El(t))}catch(n){Rn(!1,"invalid function fragment","obj",t)}else if(t instanceof bl){const e=kl("function",t),n=Nl(t),r=Pl(t);let s=[];xl(t,sl(["returns"])).has("returns")&&(s=Nl(t));const i=Bl(t);return Il(t),new ql(Rl,e,r,n,s,i)}let e=t.stateMutability;return null==e&&(e="payable","boolean"==typeof t.constant?(e="view",t.constant||(e="payable","boolean"!=typeof t.payable||t.payable||(e="nonpayable"))):"boolean"!=typeof t.payable||t.payable||(e="nonpayable")),new ql(Rl,t.name,e,t.inputs?t.inputs.map(Hl.from):[],t.outputs?t.outputs.map(Hl.from):[],null!=t.gas?t.gas:null)}static isFragment(t){return t&&t[Tl]===Dl}}class Zl extends jl{constructor(t,e,n){super(t,"struct",e,n),Object.defineProperty(this,Tl,{value:Gl})}format(){throw new Error("@TODO")}static from(t){if("string"==typeof t)try{return Zl.from(El(t))}catch(e){Rn(!1,"invalid struct fragment","obj",t)}else if(t instanceof bl){const e=kl("struct",t),n=Nl(t);return Il(t),new Zl(Rl,e,n)}return new Zl(Rl,t.name,t.inputs?t.inputs.map(Hl.from):[])}static isFragment(t){return t&&t[Tl]===Gl}}const _l=new Map;_l.set(0,"GENERIC_PANIC"),_l.set(1,"ASSERT_FALSE"),_l.set(17,"OVERFLOW"),_l.set(18,"DIVIDE_BY_ZERO"),_l.set(33,"ENUM_RANGE_ERROR"),_l.set(34,"BAD_STORAGE_DATA"),_l.set(49,"STACK_UNDERFLOW"),_l.set(50,"ARRAY_RANGE_ERROR"),_l.set(65,"OUT_OF_MEMORY"),_l.set(81,"UNINITIALIZED_FUNCTION_CALL");const Yl=new RegExp(/^bytes([0-9]*)$/),Xl=new RegExp(/^(u?int)([0-9]*)$/);let $l=null,tu=1024;Rt=new WeakSet,Tt=function(t){if(t.isArray())return new Xa(En(this,Rt,Tt).call(this,t.arrayChildren),t.arrayLength,t.name);if(t.isTuple())return new uo(t.components.map((t=>En(this,Rt,Tt).call(this,t))),t.name);switch(t.baseType){case"address":return new qa(t.name);case"bool":return new $a(t.name);case"string":return new lo(t.name);case"bytes":return new eo(t.name);case"":return new so(t.name)}let e=t.type.match(Xl);if(e){let n=parseInt(e[2]||"256");return Rn(0!==n&&n<=256&&n%8==0,"invalid "+e[1]+" bit length","param",t),new co(n/8,"int"===e[1],t.name)}if(e=t.type.match(Yl),e){let n=parseInt(e[1]);return Rn(0!==n&&n<=32,"invalid bytes length","param",t),new no(n,t.name)}Rn(!1,"invalid type","type",t.type)};let eu=class t{constructor(){An(this,Rt)}getDefaultValue(t){const e=t.map((t=>En(this,Rt,Tt).call(this,Hl.from(t))));return new uo(e,"_").defaultValue()}encode(t,e){Tn(e.length,t.length,"types/values length mismatch");const n=t.map((t=>En(this,Rt,Tt).call(this,Hl.from(t)))),r=new uo(n,"_"),s=new us;return r.encode(s,e),s.data}decode(t,e,n){const r=t.map((t=>En(this,Rt,Tt).call(this,Hl.from(t))));return new uo(r,"_").decode(new hs(e,n,tu))}static _setDefaultMaxInflation(t){Rn("number"==typeof t&&Number.isInteger(t),"invalid defaultMaxInflation factor","value",t),tu=t}static defaultAbiCoder(){return null==$l&&($l=new t),$l}static getBuiltinCallException(e,n,r){return function(t,e,n,r){let s="missing revert data",i=null,a=null;if(n){s="execution reverted";const t=Fn(n);if(n=Hn(n),0===t.length)s+=" (no data present; likely require(false) occurred",i="require(false)";else if(t.length%32!=4)s+=" (could not decode reason; invalid data length)";else if("0x08c379a0"===Hn(t.slice(0,4)))try{i=r.decode(["string"],t.slice(4))[0],a={signature:"Error(string)",name:"Error",args:[i]},s+=`: ${JSON.stringify(i)}`}catch(c){s+=" (could not decode reason; invalid string data)"}else if("0x4e487b71"===Hn(t.slice(0,4)))try{const e=Number(r.decode(["uint256"],t.slice(4))[0]);a={signature:"Panic(uint256)",name:"Panic",args:[e]},i=`Panic due to ${_l.get(e)||"UNKNOWN"}(${e})`,s+=`: ${i}`}catch(c){s+=" (could not decode panic code)"}else s+=" (unknown custom error)"}const o={to:e.to?Ga(e.to):null,data:e.data||"0x"};return e.from&&(o.from=Ga(e.from)),On(s,"CALL_EXCEPTION",{action:t,data:n,reason:i,transaction:o,invocation:null,revert:a})}(e,n,r,t.defaultAbiCoder())}};class nu{constructor(t,e,n){mn(this,"fragment"),mn(this,"name"),mn(this,"signature"),mn(this,"topic"),mn(this,"args");const r=t.name,s=t.format();Pn(this,{fragment:t,name:r,signature:s,topic:e,args:n})}}class ru{constructor(t,e,n,r){mn(this,"fragment"),mn(this,"name"),mn(this,"args"),mn(this,"signature"),mn(this,"selector"),mn(this,"value");const s=t.name,i=t.format();Pn(this,{fragment:t,name:s,args:n,signature:i,selector:e,value:r})}}class su{constructor(t,e,n){mn(this,"fragment"),mn(this,"name"),mn(this,"args"),mn(this,"signature"),mn(this,"selector");const r=t.name,s=t.format();Pn(this,{fragment:t,name:r,args:n,signature:s,selector:e})}}class iu{constructor(t){mn(this,"hash"),mn(this,"_isIndexed"),Pn(this,{hash:t,_isIndexed:!0})}static isIndexed(t){return!(!t||!t._isIndexed)}}const au={0:"generic panic",1:"assert(false)",17:"arithmetic overflow",18:"division or modulo by zero",33:"enum overflow",34:"invalid encoded storage byte array accessed",49:"out-of-bounds array access; popping on an empty array",50:"out-of-bounds access of an array or bytesN",65:"out of memory",81:"uninitialized function"},ou={"0x08c379a0":{signature:"Error(string)",name:"Error",inputs:["string"],reason:t=>`reverted with reason string ${JSON.stringify(t)}`},"0x4e487b71":{signature:"Panic(uint256)",name:"Panic",inputs:["uint256"],reason:t=>{let e="unknown panic code";return t>=0&&t<=255&&au[t.toString()]&&(e=au[t.toString()]),`reverted with panic code 0x${t.toString(16)} (${e})`}}};St=new WeakMap,Ut=new WeakMap,Ft=new WeakMap,Mt=new WeakMap,Lt=new WeakSet,Dt=function(t,e,n){if(Ln(t)){const e=t.toLowerCase();for(const t of wn(this,Ft).values())if(e===t.selector)return t;return null}if(-1===t.indexOf("(")){const r=[];for(const[e,n]of wn(this,Ft))e.split("(")[0]===t&&r.push(n);if(e){const t=e.length>0?e[e.length-1]:null;let n=e.length,s=!0;Ka.isTyped(t)&&"overrides"===t.type&&(s=!1,n--);for(let e=r.length-1;e>=0;e--){const t=r[e].inputs.length;t===n||s&&t===n-1||r.splice(e,1)}for(let i=r.length-1;i>=0;i--){const t=r[i].inputs;for(let n=0;n<e.length;n++)if(Ka.isTyped(e[n])){if(n>=t.length){if("overrides"===e[n].type)continue;r.splice(i,1);break}if(e[n].type!==t[n].baseType){r.splice(i,1);break}}}}if(1===r.length&&e&&e.length!==r[0].inputs.length){const t=e[e.length-1];(null==t||Array.isArray(t)||"object"!=typeof t)&&r.splice(0,1)}if(0===r.length)return null;if(r.length>1&&n){Rn(!1,`ambiguous function description (i.e. matches ${r.map((t=>JSON.stringify(t.format()))).join(", ")})`,"key",t)}return r[0]}const r=wn(this,Ft).get(ql.from(t).format());return r||null},Gt=function(t,e,n){if(Ln(t)){const e=t.toLowerCase();for(const t of wn(this,Ut).values())if(e===t.topicHash)return t;return null}if(-1===t.indexOf("(")){const r=[];for(const[e,n]of wn(this,Ut))e.split("(")[0]===t&&r.push(n);if(e){for(let t=r.length-1;t>=0;t--)r[t].inputs.length<e.length&&r.splice(t,1);for(let t=r.length-1;t>=0;t--){const n=r[t].inputs;for(let s=0;s<e.length;s++)if(Ka.isTyped(e[s])&&e[s].type!==n[s].baseType){r.splice(t,1);break}}}if(0===r.length)return null;if(r.length>1&&n){Rn(!1,`ambiguous event description (i.e. matches ${r.map((t=>JSON.stringify(t.format()))).join(", ")})`,"key",t)}return r[0]}const r=wn(this,Ut).get(Vl.from(t).format());return r||null};let cu=class t{constructor(t){An(this,Lt),mn(this,"fragments"),mn(this,"deploy"),mn(this,"fallback"),mn(this,"receive"),An(this,St),An(this,Ut),An(this,Ft),An(this,Mt);let e=[];e="string"==typeof t?JSON.parse(t):t,bn(this,Ft,new Map),bn(this,St,new Map),bn(this,Ut,new Map);const n=[];for(const a of e)try{n.push(Ql.from(a))}catch(i){}Pn(this,{fragments:Object.freeze(n)});let r=null,s=!1;bn(this,Mt,this.getAbiCoder()),this.fragments.forEach(((t,e)=>{let n;switch(t.type){case"constructor":if(this.deploy)return;return void Pn(this,{deploy:t});case"fallback":return void(0===t.inputs.length?s=!0:(Rn(!r||t.payable!==r.payable,"conflicting fallback fragments",`fragments[${e}]`,t),r=t,s=r.payable));case"function":n=wn(this,Ft);break;case"event":n=wn(this,Ut);break;case"error":n=wn(this,St);break;default:return}const i=t.format();n.has(i)||n.set(i,t)})),this.deploy||Pn(this,{deploy:Jl.from("constructor()")}),Pn(this,{fallback:r,receive:s})}format(t){const e=t?"minimal":"full";return this.fragments.map((t=>t.format(e)))}formatJson(){const t=this.fragments.map((t=>t.format("json")));return JSON.stringify(t.map((t=>JSON.parse(t))))}getAbiCoder(){return eu.defaultAbiCoder()}getFunctionName(t){const e=En(this,Lt,Dt).call(this,t,null,!1);return Rn(e,"no matching function","key",t),e.name}hasFunction(t){return!!En(this,Lt,Dt).call(this,t,null,!1)}getFunction(t,e){return En(this,Lt,Dt).call(this,t,e||null,!0)}forEachFunction(t){const e=Array.from(wn(this,Ft).keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(wn(this,Ft).get(r),n)}}getEventName(t){const e=En(this,Lt,Gt).call(this,t,null,!1);return Rn(e,"no matching event","key",t),e.name}hasEvent(t){return!!En(this,Lt,Gt).call(this,t,null,!1)}getEvent(t,e){return En(this,Lt,Gt).call(this,t,e||null,!0)}forEachEvent(t){const e=Array.from(wn(this,Ut).keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(wn(this,Ut).get(r),n)}}getError(t,e){if(Ln(t)){const e=t.toLowerCase();if(ou[e])return Wl.from(ou[e].signature);for(const t of wn(this,St).values())if(e===t.selector)return t;return null}if(-1===t.indexOf("(")){const e=[];for(const[n,r]of wn(this,St))n.split("(")[0]===t&&e.push(r);if(0===e.length)return"Error"===t?Wl.from("error Error(string)"):"Panic"===t?Wl.from("error Panic(uint256)"):null;if(e.length>1){Rn(!1,`ambiguous error description (i.e. ${e.map((t=>JSON.stringify(t.format()))).join(", ")})`,"name",t)}return e[0]}if("Error(string)"===(t=Wl.from(t).format()))return Wl.from("error Error(string)");if("Panic(uint256)"===t)return Wl.from("error Panic(uint256)");const n=wn(this,St).get(t);return n||null}forEachError(t){const e=Array.from(wn(this,St).keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(wn(this,St).get(r),n)}}_decodeParams(t,e){return wn(this,Mt).decode(t,e)}_encodeParams(t,e){return wn(this,Mt).encode(t,e)}encodeDeploy(t){return this._encodeParams(this.deploy.inputs,t||[])}decodeErrorResult(t,e){if("string"==typeof t){const e=this.getError(t);Rn(e,"unknown error","fragment",t),t=e}return Rn(zn(e,0,4)===t.selector,`data signature does not match error ${t.name}.`,"data",e),this._decodeParams(t.inputs,zn(e,4))}encodeErrorResult(t,e){if("string"==typeof t){const e=this.getError(t);Rn(e,"unknown error","fragment",t),t=e}return Qn([t.selector,this._encodeParams(t.inputs,e||[])])}decodeFunctionData(t,e){if("string"==typeof t){const e=this.getFunction(t);Rn(e,"unknown function","fragment",t),t=e}return Rn(zn(e,0,4)===t.selector,`data signature does not match function ${t.name}.`,"data",e),this._decodeParams(t.inputs,zn(e,4))}encodeFunctionData(t,e){if("string"==typeof t){const e=this.getFunction(t);Rn(e,"unknown function","fragment",t),t=e}return Qn([t.selector,this._encodeParams(t.inputs,e||[])])}decodeFunctionResult(t,e){if("string"==typeof t){const e=this.getFunction(t);Rn(e,"unknown function","fragment",t),t=e}let n="invalid length for result data";const r=Mn(e);if(r.length%32==0)try{return wn(this,Mt).decode(t.outputs,r)}catch(s){n="could not decode result data"}Cn(!1,n,"BAD_DATA",{value:Hn(r),info:{method:t.name,signature:t.format()}})}makeError(t,e){const n=Fn(t,"data"),r=eu.getBuiltinCallException("call",e,n);if(r.message.startsWith("execution reverted (unknown custom error)")){const t=Hn(n.slice(0,4)),e=this.getError(t);if(e)try{const t=wn(this,Mt).decode(e.inputs,n.slice(4));r.revert={name:e.name,signature:e.format(),args:t},r.reason=r.revert.signature,r.message=`execution reverted: ${r.reason}`}catch(i){r.message="execution reverted (coult not decode custom error)"}}const s=this.parseTransaction(e);return s&&(r.invocation={method:s.name,signature:s.signature,args:s.args}),r}encodeFunctionResult(t,e){if("string"==typeof t){const e=this.getFunction(t);Rn(e,"unknown function","fragment",t),t=e}return Hn(wn(this,Mt).encode(t.outputs,e||[]))}encodeFilterTopics(t,e){if("string"==typeof t){const e=this.getEvent(t);Rn(e,"unknown event","eventFragment",t),t=e}Cn(e.length<=t.inputs.length,`too many arguments for ${t.format()}`,"UNEXPECTED_ARGUMENT",{count:e.length,expectedCount:t.inputs.length});const n=[];t.anonymous||n.push(t.topicHash);const r=(t,e)=>"string"===t.type?Uo(e):"bytes"===t.type?di(Hn(e)):("bool"===t.type&&"boolean"==typeof e?e=e?"0x01":"0x00":t.type.match(/^u?int/)?e=rr(e):t.type.match(/^bytes/)?e=Wn(e,32,!1):"address"===t.type&&wn(this,Mt).encode(["address"],[e]),Vn(Hn(e),32));for(e.forEach(((e,s)=>{const i=t.inputs[s];i.indexed?null==e?n.push(null):"array"===i.baseType||"tuple"===i.baseType?Rn(!1,"filtering with tuples or arrays not supported","contract."+i.name,e):Array.isArray(e)?n.push(e.map((t=>r(i,t)))):n.push(r(i,e)):Rn(null==e,"cannot filter non-indexed parameters; must be null","contract."+i.name,e)}));n.length&&null===n[n.length-1];)n.pop();return n}encodeEventLog(t,e){if("string"==typeof t){const e=this.getEvent(t);Rn(e,"unknown event","eventFragment",t),t=e}const n=[],r=[],s=[];return t.anonymous||n.push(t.topicHash),Rn(e.length===t.inputs.length,"event arguments/values mismatch","values",e),t.inputs.forEach(((t,i)=>{const a=e[i];if(t.indexed)if("string"===t.type)n.push(Uo(a));else if("bytes"===t.type)n.push(di(a));else{if("tuple"===t.baseType||"array"===t.baseType)throw new Error("not implemented");n.push(wn(this,Mt).encode([t.type],[a]))}else r.push(t),s.push(a)})),{data:wn(this,Mt).encode(r,s),topics:n}}decodeEventLog(t,e,n){if("string"==typeof t){const e=this.getEvent(t);Rn(e,"unknown event","eventFragment",t),t=e}if(null!=n&&!t.anonymous){const e=t.topicHash;Rn(Ln(n[0],32)&&n[0].toLowerCase()===e,"fragment/topic mismatch","topics[0]",n[0]),n=n.slice(1)}const r=[],s=[],i=[];t.inputs.forEach(((t,e)=>{t.indexed?"string"===t.type||"bytes"===t.type||"tuple"===t.baseType||"array"===t.baseType?(r.push(Hl.from({type:"bytes32",name:t.name})),i.push(!0)):(r.push(t),i.push(!1)):(s.push(t),i.push(!1))}));const a=null!=n?wn(this,Mt).decode(r,Qn(n)):null,o=wn(this,Mt).decode(s,e,!0),c=[],l=[];let u=0,h=0;return t.inputs.forEach(((t,e)=>{let n=null;if(t.indexed)if(null==a)n=new iu(null);else if(i[e])n=new iu(a[h++]);else try{n=a[h++]}catch(r){n=r}else try{n=o[u++]}catch(r){n=r}c.push(n),l.push(t.name||null)})),os.fromItems(c,l)}parseTransaction(t){const e=Fn(t.data,"tx.data"),n=Xn(null!=t.value?t.value:0,"tx.value"),r=this.getFunction(Hn(e.slice(0,4)));if(!r)return null;const s=wn(this,Mt).decode(r.inputs,e.slice(4));return new ru(r,r.selector,s,n)}parseCallResult(t){throw new Error("@TODO")}parseLog(t){const e=this.getEvent(t.topics[0]);return!e||e.anonymous?null:new nu(e,e.topicHash,this.decodeEventLog(e,t.data,t.topics))}parseError(t){const e=Hn(t),n=this.getError(zn(e,0,4));if(!n)return null;const r=wn(this,Mt).decode(n.inputs,zn(e,4));return new su(n,n.selector,r)}static from(e){return e instanceof t?e:"string"==typeof e?new t(JSON.parse(e)):"function"==typeof e.formatJson?new t(e.formatJson()):"function"==typeof e.format?new t(e.format("json")):new t(e)}};const lu=BigInt(0);function uu(t){return null==t?null:t}function hu(t){return null==t?null:t.toString()}class fu{constructor(t,e,n){mn(this,"gasPrice"),mn(this,"maxFeePerGas"),mn(this,"maxPriorityFeePerGas"),Pn(this,{gasPrice:uu(t),maxFeePerGas:uu(e),maxPriorityFeePerGas:uu(n)})}toJSON(){const{gasPrice:t,maxFeePerGas:e,maxPriorityFeePerGas:n}=this;return{_type:"FeeData",gasPrice:hu(t),maxFeePerGas:hu(e),maxPriorityFeePerGas:hu(n)}}}function du(t){const e={};t.to&&(e.to=t.to),t.from&&(e.from=t.from),t.data&&(e.data=Hn(t.data));const n="chainId,gasLimit,gasPrice,maxFeePerBlobGas,maxFeePerGas,maxPriorityFeePerGas,value".split(/,/);for(const s of n)s in t&&null!=t[s]&&(e[s]=Xn(t[s],`request.${s}`));const r="type,nonce".split(/,/);for(const s of r)s in t&&null!=t[s]&&(e[s]=nr(t[s],`request.${s}`));return t.accessList&&(e.accessList=fo(t.accessList)),t.authorizationList&&(e.authorizationList=t.authorizationList.slice()),"blockTag"in t&&(e.blockTag=t.blockTag),"enableCcipRead"in t&&(e.enableCcipRead=!!t.enableCcipRead),"customData"in t&&(e.customData=t.customData),"blobVersionedHashes"in t&&t.blobVersionedHashes&&(e.blobVersionedHashes=t.blobVersionedHashes.slice()),"kzg"in t&&(e.kzg=t.kzg),"blobs"in t&&t.blobs&&(e.blobs=t.blobs.map((t=>Dn(t)?Hn(t):Object.assign({},t)))),e}class pu{constructor(t,e){mn(this,"provider"),mn(this,"number"),mn(this,"hash"),mn(this,"timestamp"),mn(this,"parentHash"),mn(this,"parentBeaconBlockRoot"),mn(this,"nonce"),mn(this,"difficulty"),mn(this,"gasLimit"),mn(this,"gasUsed"),mn(this,"stateRoot"),mn(this,"receiptsRoot"),mn(this,"blobGasUsed"),mn(this,"excessBlobGas"),mn(this,"miner"),mn(this,"prevRandao"),mn(this,"extraData"),mn(this,"baseFeePerGas"),An(this,Ht),bn(this,Ht,t.transactions.map((t=>"string"!=typeof t?new yu(t,e):t))),Pn(this,{provider:e,hash:uu(t.hash),number:t.number,timestamp:t.timestamp,parentHash:t.parentHash,parentBeaconBlockRoot:t.parentBeaconBlockRoot,nonce:t.nonce,difficulty:t.difficulty,gasLimit:t.gasLimit,gasUsed:t.gasUsed,blobGasUsed:t.blobGasUsed,excessBlobGas:t.excessBlobGas,miner:t.miner,prevRandao:uu(t.prevRandao),extraData:t.extraData,baseFeePerGas:uu(t.baseFeePerGas),stateRoot:t.stateRoot,receiptsRoot:t.receiptsRoot})}get transactions(){return wn(this,Ht).map((t=>"string"==typeof t?t:t.hash))}get prefetchedTransactions(){const t=wn(this,Ht).slice();return 0===t.length?[]:(Cn("object"==typeof t[0],"transactions were not prefetched with block request","UNSUPPORTED_OPERATION",{operation:"transactionResponses()"}),t)}toJSON(){const{baseFeePerGas:t,difficulty:e,extraData:n,gasLimit:r,gasUsed:s,hash:i,miner:a,prevRandao:o,nonce:c,number:l,parentHash:u,parentBeaconBlockRoot:h,stateRoot:f,receiptsRoot:d,timestamp:p,transactions:g}=this;return{_type:"Block",baseFeePerGas:hu(t),difficulty:hu(e),extraData:n,gasLimit:hu(r),gasUsed:hu(s),blobGasUsed:hu(this.blobGasUsed),excessBlobGas:hu(this.excessBlobGas),hash:i,miner:a,prevRandao:o,nonce:c,number:l,parentHash:u,timestamp:p,parentBeaconBlockRoot:h,stateRoot:f,receiptsRoot:d,transactions:g}}[Symbol.iterator](){let t=0;const e=this.transactions;return{next:()=>t<this.length?{value:e[t++],done:!1}:{value:void 0,done:!0}}}get length(){return wn(this,Ht).length}get date(){return null==this.timestamp?null:new Date(1e3*this.timestamp)}async getTransaction(t){let e;if("number"==typeof t)e=wn(this,Ht)[t];else{const n=t.toLowerCase();for(const t of wn(this,Ht)){if("string"==typeof t){if(t!==n)continue;e=t;break}if(t.hash===n){e=t;break}}}if(null==e)throw new Error("no such tx");return"string"==typeof e?await this.provider.getTransaction(e):e}getPrefetchedTransaction(t){const e=this.prefetchedTransactions;if("number"==typeof t)return e[t];t=t.toLowerCase();for(const n of e)if(n.hash===t)return n;Rn(!1,"no matching transaction","indexOrHash",t)}isMined(){return!!this.hash}isLondon(){return!!this.baseFeePerGas}orphanedEvent(){if(!this.isMined())throw new Error("");return{orphan:"drop-block",hash:(t=this).hash,number:t.number};var t}}Ht=new WeakMap;class gu{constructor(t,e){mn(this,"provider"),mn(this,"transactionHash"),mn(this,"blockHash"),mn(this,"blockNumber"),mn(this,"removed"),mn(this,"address"),mn(this,"data"),mn(this,"topics"),mn(this,"index"),mn(this,"transactionIndex"),this.provider=e;const n=Object.freeze(t.topics.slice());Pn(this,{transactionHash:t.transactionHash,blockHash:t.blockHash,blockNumber:t.blockNumber,removed:t.removed,address:t.address,data:t.data,topics:n,index:t.index,transactionIndex:t.transactionIndex})}toJSON(){const{address:t,blockHash:e,blockNumber:n,data:r,index:s,removed:i,topics:a,transactionHash:o,transactionIndex:c}=this;return{_type:"log",address:t,blockHash:e,blockNumber:n,data:r,index:s,removed:i,topics:a,transactionHash:o,transactionIndex:c}}async getBlock(){const t=await this.provider.getBlock(this.blockHash);return Cn(!!t,"failed to find transaction","UNKNOWN_ERROR",{}),t}async getTransaction(){const t=await this.provider.getTransaction(this.transactionHash);return Cn(!!t,"failed to find transaction","UNKNOWN_ERROR",{}),t}async getTransactionReceipt(){const t=await this.provider.getTransactionReceipt(this.transactionHash);return Cn(!!t,"failed to find transaction receipt","UNKNOWN_ERROR",{}),t}removedEvent(){return{orphan:"drop-log",log:{transactionHash:(t=this).transactionHash,blockHash:t.blockHash,blockNumber:t.blockNumber,address:t.address,data:t.data,topics:Object.freeze(t.topics.slice()),index:t.index}};var t}}class mu{constructor(t,e){mn(this,"provider"),mn(this,"to"),mn(this,"from"),mn(this,"contractAddress"),mn(this,"hash"),mn(this,"index"),mn(this,"blockHash"),mn(this,"blockNumber"),mn(this,"logsBloom"),mn(this,"gasUsed"),mn(this,"blobGasUsed"),mn(this,"cumulativeGasUsed"),mn(this,"gasPrice"),mn(this,"blobGasPrice"),mn(this,"type"),mn(this,"status"),mn(this,"root"),An(this,Qt),bn(this,Qt,Object.freeze(t.logs.map((t=>new gu(t,e)))));let n=lu;null!=t.effectiveGasPrice?n=t.effectiveGasPrice:null!=t.gasPrice&&(n=t.gasPrice),Pn(this,{provider:e,to:t.to,from:t.from,contractAddress:t.contractAddress,hash:t.hash,index:t.index,blockHash:t.blockHash,blockNumber:t.blockNumber,logsBloom:t.logsBloom,gasUsed:t.gasUsed,cumulativeGasUsed:t.cumulativeGasUsed,blobGasUsed:t.blobGasUsed,gasPrice:n,blobGasPrice:t.blobGasPrice,type:t.type,status:t.status,root:t.root})}get logs(){return wn(this,Qt)}toJSON(){const{to:t,from:e,contractAddress:n,hash:r,index:s,blockHash:i,blockNumber:a,logsBloom:o,logs:c,status:l,root:u}=this;return{_type:"TransactionReceipt",blockHash:i,blockNumber:a,contractAddress:n,cumulativeGasUsed:hu(this.cumulativeGasUsed),from:e,gasPrice:hu(this.gasPrice),blobGasUsed:hu(this.blobGasUsed),blobGasPrice:hu(this.blobGasPrice),gasUsed:hu(this.gasUsed),hash:r,index:s,logs:c,logsBloom:o,root:u,status:l,to:t}}get length(){return this.logs.length}[Symbol.iterator](){let t=0;return{next:()=>t<this.length?{value:this.logs[t++],done:!1}:{value:void 0,done:!0}}}get fee(){return this.gasUsed*this.gasPrice}async getBlock(){const t=await this.provider.getBlock(this.blockHash);if(null==t)throw new Error("TODO");return t}async getTransaction(){const t=await this.provider.getTransaction(this.hash);if(null==t)throw new Error("TODO");return t}async getResult(){return await this.provider.getTransactionResult(this.hash)}async confirmations(){return await this.provider.getBlockNumber()-this.blockNumber+1}removedEvent(){return Au(this)}reorderedEvent(t){return Cn(!t||t.isMined(),"unmined 'other' transction cannot be orphaned","UNSUPPORTED_OPERATION",{operation:"reorderedEvent(other)"}),wu(this,t)}}Qt=new WeakMap;jt=new WeakMap;let yu=class t{constructor(t,e){mn(this,"provider"),mn(this,"blockNumber"),mn(this,"blockHash"),mn(this,"index"),mn(this,"hash"),mn(this,"type"),mn(this,"to"),mn(this,"from"),mn(this,"nonce"),mn(this,"gasLimit"),mn(this,"gasPrice"),mn(this,"maxPriorityFeePerGas"),mn(this,"maxFeePerGas"),mn(this,"maxFeePerBlobGas"),mn(this,"data"),mn(this,"value"),mn(this,"chainId"),mn(this,"signature"),mn(this,"accessList"),mn(this,"blobVersionedHashes"),mn(this,"authorizationList"),An(this,jt),this.provider=e,this.blockNumber=null!=t.blockNumber?t.blockNumber:null,this.blockHash=null!=t.blockHash?t.blockHash:null,this.hash=t.hash,this.index=t.index,this.type=t.type,this.from=t.from,this.to=t.to||null,this.gasLimit=t.gasLimit,this.nonce=t.nonce,this.data=t.data,this.value=t.value,this.gasPrice=t.gasPrice,this.maxPriorityFeePerGas=null!=t.maxPriorityFeePerGas?t.maxPriorityFeePerGas:null,this.maxFeePerGas=null!=t.maxFeePerGas?t.maxFeePerGas:null,this.maxFeePerBlobGas=null!=t.maxFeePerBlobGas?t.maxFeePerBlobGas:null,this.chainId=t.chainId,this.signature=t.signature,this.accessList=null!=t.accessList?t.accessList:null,this.blobVersionedHashes=null!=t.blobVersionedHashes?t.blobVersionedHashes:null,this.authorizationList=null!=t.authorizationList?t.authorizationList:null,bn(this,jt,-1)}toJSON(){const{blockNumber:t,blockHash:e,index:n,hash:r,type:s,to:i,from:a,nonce:o,data:c,signature:l,accessList:u,blobVersionedHashes:h}=this;return{_type:"TransactionResponse",accessList:u,blockNumber:t,blockHash:e,blobVersionedHashes:h,chainId:hu(this.chainId),data:c,from:a,gasLimit:hu(this.gasLimit),gasPrice:hu(this.gasPrice),hash:r,maxFeePerGas:hu(this.maxFeePerGas),maxPriorityFeePerGas:hu(this.maxPriorityFeePerGas),maxFeePerBlobGas:hu(this.maxFeePerBlobGas),nonce:o,signature:l,to:i,index:n,type:s,value:hu(this.value)}}async getBlock(){let t=this.blockNumber;if(null==t){const e=await this.getTransaction();e&&(t=e.blockNumber)}if(null==t)return null;const e=this.provider.getBlock(t);if(null==e)throw new Error("TODO");return e}async getTransaction(){return this.provider.getTransaction(this.hash)}async confirmations(){if(null==this.blockNumber){const{tx:t,blockNumber:e}=await xn({tx:this.getTransaction(),blockNumber:this.provider.getBlockNumber()});return null==t||null==t.blockNumber?0:e-t.blockNumber+1}return await this.provider.getBlockNumber()-this.blockNumber+1}async wait(t,e){const n=null==t?1:t,r=null==e?0:e;let s=wn(this,jt),i=-1,a=-1===s;const o=async()=>{if(a)return null;const{blockNumber:t,nonce:e}=await xn({blockNumber:this.provider.getBlockNumber(),nonce:this.provider.getTransactionCount(this.from)});if(e<this.nonce)return void(s=t);if(a)return null;const r=await this.getTransaction();if(!r||null==r.blockNumber)for(-1===i&&(i=s-3,i<wn(this,jt)&&(i=wn(this,jt)));i<=t;){if(a)return null;const e=await this.provider.getBlock(i,!0);if(null==e)return;for(const t of e)if(t===this.hash)return;for(let r=0;r<e.length;r++){const i=await e.getTransaction(r);if(i.from===this.from&&i.nonce===this.nonce){if(a)return null;const e=await this.provider.getTransactionReceipt(i.hash);if(null==e)return;if(t-e.blockNumber+1<n)return;let r="replaced";i.data===this.data&&i.to===this.to&&i.value===this.value?r="repriced":"0x"===i.data&&i.from===i.to&&i.value===lu&&(r="cancelled"),Cn(!1,"transaction was replaced","TRANSACTION_REPLACED",{cancelled:"replaced"===r||"cancelled"===r,reason:r,replacement:i.replaceableTransaction(s),hash:i.hash,receipt:e})}}i++}},c=t=>{if(null==t||0!==t.status)return t;Cn(!1,"transaction execution reverted","CALL_EXCEPTION",{action:"sendTransaction",data:null,reason:null,invocation:null,revert:null,transaction:{to:t.to,from:t.from,data:""},receipt:t})},l=await this.provider.getTransactionReceipt(this.hash);if(0===n)return c(l);if(l){if(1===n||await l.confirmations()>=n)return c(l)}else if(await o(),0===n)return null;const u=new Promise(((t,e)=>{const i=[],l=()=>{i.forEach((t=>t()))};if(i.push((()=>{a=!0})),r>0){const t=setTimeout((()=>{l(),e(On("wait for transaction timeout","TIMEOUT"))}),r);i.push((()=>{clearTimeout(t)}))}const u=async r=>{if(await r.confirmations()>=n){l();try{t(c(r))}catch(s){e(s)}}};if(i.push((()=>{this.provider.off(this.hash,u)})),this.provider.on(this.hash,u),s>=0){const t=async()=>{try{await o()}catch(n){if(Bn(n,"TRANSACTION_REPLACED"))return l(),void e(n)}a||this.provider.once("block",t)};i.push((()=>{this.provider.off("block",t)})),this.provider.once("block",t)}}));return await u}isMined(){return null!=this.blockHash}isLegacy(){return 0===this.type}isBerlin(){return 1===this.type}isLondon(){return 2===this.type}isCancun(){return 3===this.type}removedEvent(){return Cn(this.isMined(),"unmined transaction canot be orphaned","UNSUPPORTED_OPERATION",{operation:"removeEvent()"}),Au(this)}reorderedEvent(t){return Cn(this.isMined(),"unmined transaction canot be orphaned","UNSUPPORTED_OPERATION",{operation:"removeEvent()"}),Cn(!t||t.isMined(),"unmined 'other' transaction canot be orphaned","UNSUPPORTED_OPERATION",{operation:"removeEvent()"}),wu(this,t)}replaceableTransaction(e){Rn(Number.isInteger(e)&&e>=0,"invalid startBlock","startBlock",e);const n=new t(this,this.provider);return bn(n,jt,e),n}};function wu(t,e){return{orphan:"reorder-transaction",tx:t,other:e}}function Au(t){return{orphan:"drop-transaction",tx:t}}class bu extends gu{constructor(t,e,n){super(t,t.provider),mn(this,"interface"),mn(this,"fragment"),mn(this,"args");Pn(this,{args:e.decodeEventLog(n,t.data,t.topics),fragment:n,interface:e})}get eventName(){return this.fragment.name}get eventSignature(){return this.fragment.format()}}class Eu extends gu{constructor(t,e){super(t,t.provider),mn(this,"error"),Pn(this,{error:e})}}class vu extends mu{constructor(t,e,n){super(n,e),An(this,zt),bn(this,zt,t)}get logs(){return super.logs.map((t=>{const e=t.topics.length?wn(this,zt).getEvent(t.topics[0]):null;if(e)try{return new bu(t,wn(this,zt),e)}catch(n){return new Eu(t,n)}return t}))}}zt=new WeakMap;class ku extends yu{constructor(t,e,n){super(n,e),An(this,Wt),bn(this,Wt,t)}async wait(t,e){const n=await super.wait(t,e);return null==n?null:new vu(wn(this,Wt),this.provider,n)}}Wt=new WeakMap;class xu extends cr{constructor(t,e,n,r){super(t,e,n),mn(this,"log"),Pn(this,{log:r})}async getBlock(){return await this.log.getBlock()}async getTransaction(){return await this.log.getTransaction()}async getTransactionReceipt(){return await this.log.getTransactionReceipt()}}class Pu extends xu{constructor(t,e,n,r,s){super(t,e,n,new bu(s,t.interface,r));Pn(this,{args:t.interface.decodeEventLog(r,this.log.data,this.log.topics),fragment:r})}get eventName(){return this.fragment.name}get eventSignature(){return this.fragment.format()}}const Nu=BigInt(0);function Bu(t){return t&&"function"==typeof t.call}function Iu(t){return t&&"function"==typeof t.estimateGas}function Ou(t){return t&&"function"==typeof t.resolveName}function Cu(t){return t&&"function"==typeof t.sendTransaction}function Ru(t){if(null!=t){if(Ou(t))return t;if(t.provider)return t.provider}}class Tu{constructor(t,e,n){if(An(this,Vt),mn(this,"fragment"),Pn(this,{fragment:e}),e.inputs.length<n.length)throw new Error("too many arguments");const r=Su(t.runner,"resolveName"),s=Ou(r)?r:null;bn(this,Vt,async function(){const r=await Promise.all(e.inputs.map(((t,e)=>null==n[e]?null:t.walkAsync(n[e],((t,e)=>"address"===t?Array.isArray(e)?Promise.all(e.map((t=>ja(t,s)))):ja(e,s):e)))));return t.interface.encodeFilterTopics(e,r)}())}getTopicFilter(){return wn(this,Vt)}}function Su(t,e){return null==t?null:"function"==typeof t[e]?t:t.provider&&"function"==typeof t.provider[e]?t.provider:null}function Uu(t){return null==t?null:t.provider||null}async function Fu(t,e){const n=Ka.dereference(t,"overrides");Rn("object"==typeof n,"invalid overrides parameter","overrides",t);const r=du(n);return Rn(null==r.to||(e||[]).indexOf("to")>=0,"cannot override to","overrides.to",r.to),Rn(null==r.data||(e||[]).indexOf("data")>=0,"cannot override data","overrides.data",r.data),r.from&&(r.from=r.from),r}function Mu(t){const e=async function(e){const n=await Fu(e,["data"]);n.to=await t.getAddress(),n.from&&(n.from=await ja(n.from,Ru(t.runner)));const r=t.interface,s=Xn(n.value||Nu,"overrides.value")===Nu,i="0x"===(n.data||"0x");!r.fallback||r.fallback.payable||!r.receive||i||s||Rn(!1,"cannot send data to receive or send value to non-payable fallback","overrides",e),Rn(r.fallback||i,"cannot send data to receive-only contract","overrides.data",n.data);return Rn(r.receive||r.fallback&&r.fallback.payable||s,"cannot send value to non-payable fallback","overrides.value",n.value),Rn(r.fallback||i,"cannot send data to receive-only contract","overrides.data",n.data),n},n=async function(n){const r=t.runner;Cn(Cu(r),"contract runner does not support sending transactions","UNSUPPORTED_OPERATION",{operation:"sendTransaction"});const s=await r.sendTransaction(await e(n)),i=Uu(t.runner);return new ku(t.interface,i,s)},r=async t=>await n(t);return Pn(r,{_contract:t,estimateGas:async function(n){const r=Su(t.runner,"estimateGas");return Cn(Iu(r),"contract runner does not support gas estimation","UNSUPPORTED_OPERATION",{operation:"estimateGas"}),await r.estimateGas(await e(n))},populateTransaction:e,send:n,staticCall:async function(n){const r=Su(t.runner,"call");Cn(Bu(r),"contract runner does not support calling","UNSUPPORTED_OPERATION",{operation:"call"});const s=await e(n);try{return await r.call(s)}catch(i){if(In(i)&&i.data)throw t.interface.makeError(i.data,s);throw i}}}),r}function Lu(t,e){const n=function(...n){const r=t.interface.getFunction(e,n);return Cn(r,"no matching fragment","UNSUPPORTED_OPERATION",{operation:"fragment",info:{key:e,args:n}}),r},r=async function(...e){const r=n(...e);let s={};if(r.inputs.length+1===e.length&&(s=await Fu(e.pop()),s.from&&(s.from=await ja(s.from,Ru(t.runner)))),r.inputs.length!==e.length)throw new Error("internal error: fragment inputs doesn't match arguments; should not happen");const i=await async function(t,e,n){const r=Su(t,"resolveName"),s=Ou(r)?r:null;return await Promise.all(e.map(((t,e)=>t.walkAsync(n[e],((t,e)=>(e=Ka.dereference(e,t),"address"===t?ja(e,s):e))))))}(t.runner,r.inputs,e);return Object.assign({},s,await xn({to:t.getAddress(),data:t.interface.encodeFunctionData(r,i)}))},s=async function(...t){const e=await a(...t);return 1===e.length?e[0]:e},i=async function(...e){const n=t.runner;Cn(Cu(n),"contract runner does not support sending transactions","UNSUPPORTED_OPERATION",{operation:"sendTransaction"});const s=await n.sendTransaction(await r(...e)),i=Uu(t.runner);return new ku(t.interface,i,s)},a=async function(...e){const s=Su(t.runner,"call");Cn(Bu(s),"contract runner does not support calling","UNSUPPORTED_OPERATION",{operation:"call"});const i=await r(...e);let a="0x";try{a=await s.call(i)}catch(c){if(In(c)&&c.data)throw t.interface.makeError(c.data,i);throw c}const o=n(...e);return t.interface.decodeFunctionResult(o,a)},o=async(...t)=>n(...t).constant?await s(...t):await i(...t);return Pn(o,{name:t.interface.getFunctionName(e),_contract:t,_key:e,getFragment:n,estimateGas:async function(...e){const n=Su(t.runner,"estimateGas");return Cn(Iu(n),"contract runner does not support gas estimation","UNSUPPORTED_OPERATION",{operation:"estimateGas"}),await n.estimateGas(await r(...e))},populateTransaction:r,send:i,staticCall:s,staticCallResult:a}),Object.defineProperty(o,"fragment",{configurable:!1,enumerable:!0,get:()=>{const n=t.interface.getFunction(e);return Cn(n,"no matching fragment","UNSUPPORTED_OPERATION",{operation:"fragment",info:{key:e}}),n}}),o}Vt=new WeakMap;const Du=Symbol.for("_ethersInternal_contract"),Gu=new WeakMap;function Hu(t){return Gu.get(t[Du])}async function Qu(t,e){let n,r=null;if(Array.isArray(e)){const r=function(e){if(Ln(e,32))return e;const n=t.interface.getEvent(e);return Rn(n,"unknown fragment","name",e),n.topicHash};n=e.map((t=>null==t?null:Array.isArray(t)?t.map(r):r(t)))}else"*"===e?n=[null]:"string"==typeof e?Ln(e,32)?n=[e]:(r=t.interface.getEvent(e),Rn(r,"unknown fragment","event",e),n=[r.topicHash]):(s=e)&&"object"==typeof s&&"getTopicFilter"in s&&"function"==typeof s.getTopicFilter&&s.fragment?n=await e.getTopicFilter():"fragment"in e?(r=e.fragment,n=[r.topicHash]):Rn(!1,"unknown event name","event",e);var s;n=n.map((t=>{if(null==t)return null;if(Array.isArray(t)){const e=Array.from(new Set(t.map((t=>t.toLowerCase()))).values());return 1===e.length?e[0]:(e.sort(),e)}return t.toLowerCase()}));return{fragment:r,tag:n.map((t=>null==t?"null":Array.isArray(t)?t.join("|"):t)).join("&"),topics:n}}async function ju(t,e){const{subs:n}=Hu(t);return n.get((await Qu(t,e)).tag)||null}async function zu(t,e,n){const r=Uu(t.runner);Cn(r,"contract runner does not support subscribing","UNSUPPORTED_OPERATION",{operation:e});const{fragment:s,tag:i,topics:a}=await Qu(t,n),{addr:o,subs:c}=Hu(t);let l=c.get(i);if(!l){const e={address:o||t,topics:a},u=e=>{let r=s;if(null==r)try{r=t.interface.getEvent(e.topics[0])}catch(i){}if(r){const i=r,a=s?t.interface.decodeEventLog(s,e.data,e.topics):[];Vu(t,n,a,(r=>new Pu(t,r,n,i,e)))}else Vu(t,n,[],(r=>new xu(t,r,n,e)))};let h=[];l={tag:i,listeners:[],start:()=>{h.length||h.push(r.on(e,u))},stop:async()=>{if(0==h.length)return;let t=h;h=[],await Promise.all(t),r.off(e,u)}},c.set(i,l)}return l}let Wu=Promise.resolve();async function Vu(t,e,n,r){try{await Wu}catch(i){}const s=async function(t,e,n,r){await Wu;const s=await ju(t,e);if(!s)return!1;const a=s.listeners.length;return s.listeners=s.listeners.filter((({listener:e,once:s})=>{const a=Array.from(n);r&&a.push(r(s?null:e));try{e.call(t,...a)}catch(i){}return!s})),0===s.listeners.length&&(s.stop(),Hu(t).subs.delete(s.tag)),a>0}(t,e,n,r);return Wu=s,await s}const Ju=["then"];Jt=Du;let Ku=class t{constructor(t,e,n,r){mn(this,"target"),mn(this,"interface"),mn(this,"runner"),mn(this,"filters"),mn(this,Jt),mn(this,"fallback"),Rn("string"==typeof t||Ha(t),"invalid value for Contract target","target",t),null==n&&(n=null);const s=cu.from(e);let i;Pn(this,{target:t,runner:n,interface:s}),Object.defineProperty(this,Du,{value:{}});let a=null,o=null;if(r){const t=Uu(n);o=new ku(this.interface,t,r)}let c=new Map;if("string"==typeof t)if(Ln(t))a=t,i=Promise.resolve(t);else{const e=Su(n,"resolveName");if(!Ou(e))throw On("contract runner does not support name resolution","UNSUPPORTED_OPERATION",{operation:"resolveName"});i=e.resolveName(t).then((e=>{if(null==e)throw On("an ENS name used for a contract target must be correctly configured","UNCONFIGURED_NAME",{value:t});return Hu(this).addr=e,e}))}else i=t.getAddress().then((t=>{if(null==t)throw new Error("TODO");return Hu(this).addr=t,t}));var l,u;l=this,u={addrPromise:i,addr:a,deployTx:o,subs:c},Gu.set(l[Du],u);return Pn(this,{filters:new Proxy({},{get:(t,e,n)=>{if("symbol"==typeof e||Ju.indexOf(e)>=0)return Reflect.get(t,e,n);try{return this.getEvent(e)}catch(r){if(!Bn(r,"INVALID_ARGUMENT")||"key"!==r.argument)throw r}},has:(t,e)=>Ju.indexOf(e)>=0?Reflect.has(t,e):Reflect.has(t,e)||this.interface.hasEvent(String(e))})}),Pn(this,{fallback:s.receive||s.fallback?Mu(this):null}),new Proxy(this,{get:(t,e,n)=>{if("symbol"==typeof e||e in t||Ju.indexOf(e)>=0)return Reflect.get(t,e,n);try{return t.getFunction(e)}catch(r){if(!Bn(r,"INVALID_ARGUMENT")||"key"!==r.argument)throw r}},has:(t,e)=>"symbol"==typeof e||e in t||Ju.indexOf(e)>=0?Reflect.has(t,e):t.interface.hasFunction(e)})}connect(e){return new t(this.target,this.interface,e)}attach(e){return new t(e,this.interface,this.runner)}async getAddress(){return await Hu(this).addrPromise}async getDeployedCode(){const t=Uu(this.runner);Cn(t,"runner does not support .provider","UNSUPPORTED_OPERATION",{operation:"getDeployedCode"});const e=await t.getCode(await this.getAddress());return"0x"===e?null:e}async waitForDeployment(){const t=this.deploymentTransaction();if(t)return await t.wait(),this;if(null!=await this.getDeployedCode())return this;const e=Uu(this.runner);return Cn(null!=e,"contract runner does not support .provider","UNSUPPORTED_OPERATION",{operation:"waitForDeployment"}),new Promise(((t,n)=>{const r=async()=>{try{if(null!=await this.getDeployedCode())return t(this);e.once("block",r)}catch(s){n(s)}};r()}))}deploymentTransaction(){return Hu(this).deployTx}getFunction(t){"string"!=typeof t&&(t=t.format());return Lu(this,t)}getEvent(t){return"string"!=typeof t&&(t=t.format()),function(t,e){const n=function(...n){const r=t.interface.getEvent(e,n);return Cn(r,"no matching fragment","UNSUPPORTED_OPERATION",{operation:"fragment",info:{key:e,args:n}}),r},r=function(...e){return new Tu(t,n(...e),e)};return Pn(r,{name:t.interface.getEventName(e),_contract:t,_key:e,getFragment:n}),Object.defineProperty(r,"fragment",{configurable:!1,enumerable:!0,get:()=>{const n=t.interface.getEvent(e);return Cn(n,"no matching fragment","UNSUPPORTED_OPERATION",{operation:"fragment",info:{key:e}}),n}}),r}(this,t)}async queryTransaction(t){throw new Error("@TODO")}async queryFilter(t,e,n){null==e&&(e=0),null==n&&(n="latest");const{addr:r,addrPromise:s}=Hu(this),i=r||await s,{fragment:a,topics:o}=await Qu(this,t),c={address:i,topics:o,fromBlock:e,toBlock:n},l=Uu(this.runner);return Cn(l,"contract runner does not have a provider","UNSUPPORTED_OPERATION",{operation:"queryFilter"}),(await l.getLogs(c)).map((t=>{let e=a;if(null==e)try{e=this.interface.getEvent(t.topics[0])}catch(n){}if(e)try{return new bu(t,this.interface,e)}catch(n){return new Eu(t,n)}return new gu(t,l)}))}async on(t,e){const n=await zu(this,"on",t);return n.listeners.push({listener:e,once:!1}),n.start(),this}async once(t,e){const n=await zu(this,"once",t);return n.listeners.push({listener:e,once:!0}),n.start(),this}async emit(t,...e){return await Vu(this,t,e,null)}async listenerCount(t){if(t){const e=await ju(this,t);return e?e.listeners.length:0}const{subs:e}=Hu(this);let n=0;for(const{listeners:r}of e.values())n+=r.length;return n}async listeners(t){if(t){const e=await ju(this,t);return e?e.listeners.map((({listener:t})=>t)):[]}const{subs:e}=Hu(this);let n=[];for(const{listeners:r}of e.values())n=n.concat(r.map((({listener:t})=>t)));return n}async off(t,e){const n=await ju(this,t);if(!n)return this;if(e){const t=n.listeners.map((({listener:t})=>t)).indexOf(e);t>=0&&n.listeners.splice(t,1)}return null!=e&&0!==n.listeners.length||(n.stop(),Hu(this).subs.delete(n.tag)),this}async removeAllListeners(t){if(t){const e=await ju(this,t);if(!e)return this;e.stop(),Hu(this).subs.delete(e.tag)}else{const{subs:t}=Hu(this);for(const{tag:e,stop:n}of t.values())n(),t.delete(e)}return this}async addListener(t,e){return await this.on(t,e)}async removeListener(t,e){return await this.off(t,e)}static buildClass(e){return class extends t{constructor(t,n=null){super(t,e,n)}}}static from(t,e,n){null==n&&(n=null);return new this(t,e,n)}};class qu extends(function(){return Ku}()){}function Zu(t){return t.match(/^ipfs:\/\/ipfs\//i)?t=t.substring(12):t.match(/^ipfs:\/\//i)?t=t.substring(7):Rn(!1,"unsupported IPFS format","link",t),`https://gateway.ipfs.io/ipfs/${t}`}class _u{constructor(t){mn(this,"name"),Pn(this,{name:t})}connect(t){return this}supportsCoinType(t){return!1}async encodeAddress(t,e){throw new Error("unsupported coin")}async decodeAddress(t,e){throw new Error("unsupported coin")}}const Yu=new RegExp("^(ipfs)://(.*)$","i"),Xu=[new RegExp("^(https)://(.*)$","i"),new RegExp("^(data):(.*)$","i"),Yu,new RegExp("^eip155:[0-9]+/(erc[0-9]+):(.*)$","i")],$u=class t{constructor(t,e,n){An(this,Zt),mn(this,"provider"),mn(this,"address"),mn(this,"name"),An(this,Kt),An(this,qt),Pn(this,{provider:t,address:e,name:n}),bn(this,Kt,null),bn(this,qt,new qu(e,["function supportsInterface(bytes4) view returns (bool)","function resolve(bytes, bytes) view returns (bytes)","function addr(bytes32) view returns (address)","function addr(bytes32, uint) view returns (bytes)","function text(bytes32, string) view returns (string)","function contenthash(bytes32) view returns (bytes)"],t))}async supportsWildcard(){return null==wn(this,Kt)&&bn(this,Kt,(async()=>{try{return await wn(this,qt).supportsInterface("0x9061b923")}catch(t){if(Bn(t,"CALL_EXCEPTION"))return!1;throw bn(this,Kt,null),t}})()),await wn(this,Kt)}async getAddress(t){if(null==t&&(t=60),60===t)try{const t=await En(this,Zt,_t).call(this,"addr(bytes32)");return null==t||t===Ea?null:t}catch(s){if(Bn(s,"CALL_EXCEPTION"))return null;throw s}if(t>=0&&t<**********){let e=t+**********;const n=await En(this,Zt,_t).call(this,"addr(bytes32,uint)",[e]);if(Ln(n,20))return Ga(n)}let e=null;for(const i of this.provider.plugins)if(i instanceof _u&&i.supportsCoinType(t)){e=i;break}if(null==e)return null;const n=await En(this,Zt,_t).call(this,"addr(bytes32,uint)",[t]);if(null==n||"0x"===n)return null;const r=await e.decodeAddress(t,n);if(null!=r)return r;Cn(!1,"invalid coin data","UNSUPPORTED_OPERATION",{operation:`getAddress(${t})`,info:{coinType:t,data:n}})}async getText(t){const e=await En(this,Zt,_t).call(this,"text(bytes32,string)",[t]);return null==e||"0x"===e?null:e}async getContentHash(){const t=await En(this,Zt,_t).call(this,"contenthash(bytes32)");if(null==t||"0x"===t)return null;const e=t.match(/^0x(e3010170|e5010172)(([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])([0-9a-f]*))$/);if(e){const t="e3010170"===e[1]?"ipfs":"ipns",n=parseInt(e[4],16);if(e[5].length===2*n)return`${t}://${function(t){const e=Fn(t);let n=er(e),r="";for(;n;)r=ar[Number(n%or)]+r,n/=or;for(let s=0;s<e.length&&!e[s];s++)r=ar[0]+r;return r}("0x"+e[2])}`}const n=t.match(/^0xe40101fa011b20([0-9a-f]*)$/);if(n&&64===n[1].length)return`bzz://${n[1]}`;Cn(!1,"invalid or unsupported content hash data","UNSUPPORTED_OPERATION",{operation:"getContentHash()",info:{data:t}})}async getAvatar(){return(await this._getAvatar()).url}async _getAvatar(){const t=[{type:"name",value:this.name}];try{const r=await this.getText("avatar");if(null==r)return t.push({type:"!avatar",value:""}),{url:null,linkage:t};t.push({type:"avatar",value:r});for(let s=0;s<Xu.length;s++){const i=r.match(Xu[s]);if(null==i)continue;const a=i[1].toLowerCase();switch(a){case"https":case"data":return t.push({type:"url",value:r}),{linkage:t,url:r};case"ipfs":{const e=Zu(r);return t.push({type:"ipfs",value:r}),t.push({type:"url",value:e}),{linkage:t,url:e}}case"erc721":case"erc1155":{const s="erc721"===a?"tokenURI(uint256)":"uri(uint256)";t.push({type:a,value:r});const o=await this.getAddress();if(null==o)return t.push({type:"!owner",value:""}),{url:null,linkage:t};const c=(i[2]||"").split("/");if(2!==c.length)return t.push({type:`!${a}caip`,value:i[2]||""}),{url:null,linkage:t};const l=c[1],u=new qu(c[0],["function tokenURI(uint) view returns (string)","function ownerOf(uint) view returns (address)","function uri(uint) view returns (string)","function balanceOf(address, uint256) view returns (uint)"],this.provider);if("erc721"===a){const e=await u.ownerOf(l);if(o!==e)return t.push({type:"!owner",value:e}),{url:null,linkage:t};t.push({type:"owner",value:e})}else if("erc1155"===a){const e=await u.balanceOf(o,l);if(!e)return t.push({type:"!balance",value:"0"}),{url:null,linkage:t};t.push({type:"balance",value:e.toString()})}let h=await u[s](l);if(null==h||"0x"===h)return t.push({type:"!metadata-url",value:""}),{url:null,linkage:t};t.push({type:"metadata-url-base",value:h}),"erc1155"===a&&(h=h.replace("{id}",rr(l,32).substring(2)),t.push({type:"metadata-url-expanded",value:h})),h.match(/^ipfs:/i)&&(h=Zu(h)),t.push({type:"metadata-url",value:h});let f={};const d=await new Pr(h).send();d.assertOk();try{f=d.bodyJson}catch(e){try{t.push({type:"!metadata",value:d.bodyText})}catch(n){const e=d.body;return e&&t.push({type:"!metadata",value:Hn(e)}),{url:null,linkage:t}}return{url:null,linkage:t}}if(!f)return t.push({type:"!metadata",value:""}),{url:null,linkage:t};t.push({type:"metadata",value:JSON.stringify(f)});let p=f.image;if("string"!=typeof p)return t.push({type:"!imageUrl",value:""}),{url:null,linkage:t};if(p.match(/^(https:\/\/|data:)/i));else{if(null==p.match(Yu))return t.push({type:"!imageUrl-ipfs",value:p}),{url:null,linkage:t};t.push({type:"imageUrl-ipfs",value:p}),p=Zu(p)}return t.push({type:"url",value:p}),{linkage:t,url:p}}}}}catch(e){}return{linkage:t,url:null}}static async getEnsAddress(t){const e=await t.getNetwork(),n=e.getPlugin("org.ethers.plugins.network.Ens");return Cn(n,"network does not support ENS","UNSUPPORTED_OPERATION",{operation:"getEnsAddress",info:{network:e}}),n.address}static async fromName(e,n){var r;let s=n;for(;;){if(""===s||"."===s)return null;if("eth"!==n&&"eth"===s)return null;const i=await En(r=t,Yt,Xt).call(r,e,s);if(null!=i){const r=new t(e,i,n);return s===n||await r.supportsWildcard()?r:null}s=s.split(".").slice(1).join(".")}}};Kt=new WeakMap,qt=new WeakMap,Zt=new WeakSet,_t=async function(t,e){e=(e||[]).slice();const n=wn(this,qt).interface;e.unshift(Qc(this.name));let r=null;await this.supportsWildcard()&&(r=n.getFunction(t),Cn(r,"missing fragment","UNKNOWN_ERROR",{info:{funcName:t}}),e=[jc(this.name,255),n.encodeFunctionData(r,e)],t="resolve(bytes,bytes)"),e.push({enableCcipRead:!0});try{const s=await wn(this,qt)[t](...e);return r?n.decodeFunctionResult(r,s)[0]:s}catch(s){if(!Bn(s,"CALL_EXCEPTION"))throw s}return null},Yt=new WeakSet,Xt=async function(t,e){const n=await $u.getEnsAddress(t);try{const r=new qu(n,["function resolver(bytes32) view returns (address)"],t),s=await r.resolver(Qc(e),{enableCcipRead:!0});return s===Ea?null:s}catch(r){throw r}return null},An($u,Yt);let th=$u;const eh=BigInt(0);function nh(t,e){return function(n){return null==n?e:t(n)}}function rh(t,e){return n=>{if(e&&null==n)return null;if(!Array.isArray(n))throw new Error("not an array");return n.map((e=>t(e)))}}function sh(t,e){return n=>{const r={};for(const i in t){let a=i;if(e&&i in e&&!(a in n))for(const t of e[i])if(t in n){a=t;break}try{const e=t[i](n[a]);void 0!==e&&(r[i]=e)}catch(s){Cn(!1,`invalid value for value.${i} (${s instanceof Error?s.message:"not-an-error"})`,"BAD_DATA",{value:n})}}return r}}function ih(t){return Rn(Ln(t,!0),"invalid data","value",t),t}function ah(t){return Rn(Ln(t,32),"invalid hash","value",t),t}const oh=sh({address:Ga,blockHash:ah,blockNumber:nr,data:ih,index:nr,removed:nh((function(t){switch(t){case!0:case"true":return!0;case!1:case"false":return!1}Rn(!1,`invalid boolean; ${JSON.stringify(t)}`,"value",t)}),!1),topics:rh(ah),transactionHash:ah,transactionIndex:nr},{index:["logIndex"]});const ch=sh({hash:nh(ah),parentHash:ah,parentBeaconBlockRoot:nh(ah,null),number:nr,timestamp:nr,nonce:nh(ih),difficulty:Xn,gasLimit:Xn,gasUsed:Xn,stateRoot:nh(ah,null),receiptsRoot:nh(ah,null),blobGasUsed:nh(Xn,null),excessBlobGas:nh(Xn,null),miner:nh(Ga),prevRandao:nh(ah,null),extraData:ih,baseFeePerGas:nh(Xn)},{prevRandao:["mixHash"]});const lh=sh({transactionIndex:nr,blockNumber:nr,transactionHash:ah,address:Ga,topics:rh(ah),data:ih,index:nr,blockHash:ah},{index:["logIndex"]});const uh=sh({to:nh(Ga,null),from:nh(Ga,null),contractAddress:nh(Ga,null),index:nr,root:nh(Hn),gasUsed:Xn,blobGasUsed:nh(Xn,null),logsBloom:nh(ih),blockHash:ah,hash:ah,logs:rh((function(t){return lh(t)})),blockNumber:nr,cumulativeGasUsed:Xn,effectiveGasPrice:nh(Xn),blobGasPrice:nh(Xn,null),status:nh(nr),type:nh(nr,0)},{effectiveGasPrice:["gasPrice"],hash:["transactionHash"],index:["transactionIndex"]});function hh(t){t.to&&Xn(t.to)===eh&&(t.to="0x0000000000000000000000000000000000000000");const e=sh({hash:ah,index:nh(nr,void 0),type:t=>"0x"===t||null==t?0:nr(t),accessList:nh(fo,null),blobVersionedHashes:nh(rh(ah,!0),null),authorizationList:nh(rh((t=>{let e;if(t.signature)e=t.signature;else{let n=t.yParity;"0x1b"===n?n=0:"0x1c"===n&&(n=1),e=Object.assign({},t,{yParity:n})}return{address:Ga(t.address),chainId:Xn(t.chainId),nonce:Xn(t.nonce),signature:Ta.from(e)}}),!1),null),blockHash:nh(ah,null),blockNumber:nh(nr,null),transactionIndex:nh(nr,null),from:Ga,gasPrice:nh(Xn),maxPriorityFeePerGas:nh(Xn),maxFeePerGas:nh(Xn),maxFeePerBlobGas:nh(Xn,null),gasLimit:Xn,to:nh(Ga,null),value:Xn,nonce:nr,data:ih,creates:nh(Ga,null),chainId:nh(Xn,null)},{data:["input"],gasLimit:["gas"],index:["transactionIndex"]})(t);if(null==e.to&&null==e.creates&&(e.creates=function(t){const e=Ga(t.from);let n=Xn(t.nonce,"tx.nonce").toString(16);return n="0"===n?"0x":n.length%2?"0x0"+n:"0x"+n,Ga(zn(di(qr([e,n])),12))}(e)),1!==t.type&&2!==t.type||null!=t.accessList||(e.accessList=[]),t.signature?e.signature=Ta.from(t.signature):e.signature=Ta.from(t),null==e.chainId){const t=e.signature.legacyChainId;null!=t&&(e.chainId=t)}return e.blockHash&&Xn(e.blockHash)===eh&&(e.blockHash=null),e}class fh{constructor(t){mn(this,"name"),Pn(this,{name:t})}clone(){return new fh(this.name)}}class dh extends fh{constructor(t,e){null==t&&(t=0),super(`org.ethers.network.plugins.GasCost#${t||0}`),mn(this,"effectiveBlock"),mn(this,"txBase"),mn(this,"txCreate"),mn(this,"txDataZero"),mn(this,"txDataNonzero"),mn(this,"txAccessListStorageKey"),mn(this,"txAccessListAddress");const n={effectiveBlock:t};function r(t,r){let s=(e||{})[t];null==s&&(s=r),Rn("number"==typeof s,`invalud value for ${t}`,"costs",e),n[t]=s}r("txBase",21e3),r("txCreate",32e3),r("txDataZero",4),r("txDataNonzero",16),r("txAccessListStorageKey",1900),r("txAccessListAddress",2400),Pn(this,n)}clone(){return new dh(this.effectiveBlock,this)}}class ph extends fh{constructor(t,e){super("org.ethers.plugins.network.Ens"),mn(this,"address"),mn(this,"targetNetwork"),Pn(this,{address:t||"******************************************",targetNetwork:null==e?1:e})}clone(){return new ph(this.address,this.targetNetwork)}}class gh extends fh{constructor(t,e){super("org.ethers.plugins.network.FetchUrlFeeDataPlugin"),An(this,$t),An(this,te),bn(this,$t,t),bn(this,te,e)}get url(){return wn(this,$t)}get processFunc(){return wn(this,te)}clone(){return this}}$t=new WeakMap,te=new WeakMap;const mh=new Map;ee=new WeakMap,ne=new WeakMap,re=new WeakMap;let yh=class t{constructor(t,e){An(this,ee),An(this,ne),An(this,re),bn(this,ee,t),bn(this,ne,Xn(e)),bn(this,re,new Map)}toJSON(){return{name:this.name,chainId:String(this.chainId)}}get name(){return wn(this,ee)}set name(t){bn(this,ee,t)}get chainId(){return wn(this,ne)}set chainId(t){bn(this,ne,Xn(t,"chainId"))}matches(t){if(null==t)return!1;if("string"==typeof t){try{return this.chainId===Xn(t)}catch(e){}return this.name===t}if("number"==typeof t||"bigint"==typeof t){try{return this.chainId===Xn(t)}catch(e){}return!1}if("object"==typeof t){if(null!=t.chainId){try{return this.chainId===Xn(t.chainId)}catch(e){}return!1}return null!=t.name&&this.name===t.name}return!1}get plugins(){return Array.from(wn(this,re).values())}attachPlugin(t){if(wn(this,re).get(t.name))throw new Error(`cannot replace existing plugin: ${t.name} `);return wn(this,re).set(t.name,t.clone()),this}getPlugin(t){return wn(this,re).get(t)||null}getPlugins(t){return this.plugins.filter((e=>e.name.split("#")[0]===t))}clone(){const e=new t(this.name,this.chainId);return this.plugins.forEach((t=>{e.attachPlugin(t.clone())})),e}computeIntrinsicGas(t){const e=this.getPlugin("org.ethers.plugins.network.GasCost")||new dh;let n=e.txBase;if(null==t.to&&(n+=e.txCreate),t.data)for(let r=2;r<t.data.length;r+=2)"00"===t.data.substring(r,r+2)?n+=e.txDataZero:n+=e.txDataNonzero;if(t.accessList){const r=fo(t.accessList);for(const t in r)n+=e.txAccessListAddress+e.txAccessListStorageKey*r[t].storageKeys.length}return n}static from(e){if(function(){if(bh)return;function t(t,e,n){const r=function(){const r=new yh(t,e);return null!=n.ensNetwork&&r.attachPlugin(new ph(null,n.ensNetwork)),r.attachPlugin(new dh),(n.plugins||[]).forEach((t=>{r.attachPlugin(t)})),r};yh.register(t,r),yh.register(e,r),n.altNames&&n.altNames.forEach((t=>{yh.register(t,r)}))}bh=!0,t("mainnet",1,{ensNetwork:1,altNames:["homestead"]}),t("ropsten",3,{ensNetwork:3}),t("rinkeby",4,{ensNetwork:4}),t("goerli",5,{ensNetwork:5}),t("kovan",42,{ensNetwork:42}),t("sepolia",11155111,{ensNetwork:11155111}),t("holesky",17e3,{ensNetwork:17e3}),t("classic",61,{}),t("classicKotti",6,{}),t("arbitrum",42161,{ensNetwork:1}),t("arbitrum-goerli",421613,{}),t("arbitrum-sepolia",421614,{}),t("base",8453,{ensNetwork:1}),t("base-goerli",84531,{}),t("base-sepolia",84532,{}),t("bnb",56,{ensNetwork:1}),t("bnbt",97,{}),t("linea",59144,{ensNetwork:1}),t("linea-goerli",59140,{}),t("linea-sepolia",59141,{}),t("matic",137,{ensNetwork:1,plugins:[Ah("https://gasstation.polygon.technology/v2")]}),t("matic-amoy",80002,{}),t("matic-mumbai",80001,{altNames:["maticMumbai","maticmum"],plugins:[Ah("https://gasstation-testnet.polygon.technology/v2")]}),t("optimism",10,{ensNetwork:1,plugins:[]}),t("optimism-goerli",420,{}),t("optimism-sepolia",11155420,{}),t("xdai",100,{ensNetwork:1})}(),null==e)return t.from("mainnet");if("number"==typeof e&&(e=BigInt(e)),"string"==typeof e||"bigint"==typeof e){const n=mh.get(e);if(n)return n();if("bigint"==typeof e)return new t("unknown",e);Rn(!1,"unknown network","network",e)}if("function"==typeof e.clone){return e.clone()}if("object"==typeof e){Rn("string"==typeof e.name&&"number"==typeof e.chainId,"invalid network object name or chainId","network",e);const n=new t(e.name,e.chainId);return(e.ensAddress||null!=e.ensNetwork)&&n.attachPlugin(new ph(e.ensAddress,e.ensNetwork)),n}Rn(!1,"invalid network","network",e)}static register(t,e){"number"==typeof t&&(t=BigInt(t));const n=mh.get(t);n&&Rn(!1,`conflicting network for ${JSON.stringify(n.name)}`,"nameOrChainId",t),mh.set(t,e)}};function wh(t,e){const n=String(t);if(!n.match(/^[0-9.]+$/))throw new Error(`invalid gwei value: ${t}`);const r=n.split(".");if(1===r.length&&r.push(""),2!==r.length)throw new Error(`invalid gwei value: ${t}`);for(;r[1].length<e;)r[1]+="0";if(r[1].length>9){let t=BigInt(r[1].substring(0,9));r[1].substring(9).match(/^0+$/)||t++,r[1]=t.toString()}return BigInt(r[0]+r[1])}function Ah(t){return new gh(t,(async(t,e,n)=>{let r;n.setHeader("User-Agent","ethers");try{const[e,s]=await Promise.all([n.send(),t()]);r=e;const i=r.bodyJson.standard;return{gasPrice:s.gasPrice,maxFeePerGas:wh(i.maxFee,9),maxPriorityFeePerGas:wh(i.maxPriorityFee,9)}}catch(s){Cn(!1,`error encountered with polygon gas station (${JSON.stringify(n.url)})`,"SERVER_ERROR",{request:n,response:r,error:s})}}))}let bh=!1;function Eh(t){return JSON.parse(JSON.stringify(t))}class vh{constructor(t){An(this,ce),An(this,se),An(this,ie),An(this,ae),An(this,oe),bn(this,se,t),bn(this,ie,null),bn(this,ae,4e3),bn(this,oe,-2)}get pollingInterval(){return wn(this,ae)}set pollingInterval(t){bn(this,ae,t)}start(){wn(this,ie)||(bn(this,ie,wn(this,se)._setTimeout(En(this,ce,le).bind(this),wn(this,ae))),En(this,ce,le).call(this))}stop(){wn(this,ie)&&(wn(this,se)._clearTimeout(wn(this,ie)),bn(this,ie,null))}pause(t){this.stop(),t&&bn(this,oe,-2)}resume(){this.start()}}se=new WeakMap,ie=new WeakMap,ae=new WeakMap,oe=new WeakMap,ce=new WeakSet,le=async function(){try{const t=await wn(this,se).getBlockNumber();if(-2===wn(this,oe))return void bn(this,oe,t);if(t!==wn(this,oe)){for(let e=wn(this,oe)+1;e<=t;e++){if(null==wn(this,ie))return;await wn(this,se).emit("block",e)}bn(this,oe,t)}}catch(t){}null!=wn(this,ie)&&bn(this,ie,wn(this,se)._setTimeout(En(this,ce,le).bind(this),wn(this,ae)))};class kh{constructor(t){An(this,ue),An(this,he),An(this,fe),bn(this,ue,t),bn(this,fe,!1),bn(this,he,(t=>{this._poll(t,wn(this,ue))}))}async _poll(t,e){throw new Error("sub-classes must override this")}start(){wn(this,fe)||(bn(this,fe,!0),wn(this,he).call(this,-2),wn(this,ue).on("block",wn(this,he)))}stop(){wn(this,fe)&&(bn(this,fe,!1),wn(this,ue).off("block",wn(this,he)))}pause(t){this.stop()}resume(){this.start()}}ue=new WeakMap,he=new WeakMap,fe=new WeakMap;class xh extends kh{constructor(t,e){super(t),An(this,de),An(this,pe),bn(this,de,e),bn(this,pe,-2)}pause(t){t&&bn(this,pe,-2),super.pause(t)}async _poll(t,e){const n=await e.getBlock(wn(this,de));null!=n&&(-2===wn(this,pe)?bn(this,pe,n.number):n.number>wn(this,pe)&&(e.emit(wn(this,de),n.number),bn(this,pe,n.number)))}}de=new WeakMap,pe=new WeakMap;class Ph extends kh{constructor(t,e){super(t),An(this,ge),bn(this,ge,Eh(e))}async _poll(t,e){throw new Error("@TODO")}}ge=new WeakMap;class Nh extends kh{constructor(t,e){super(t),An(this,me),bn(this,me,e)}async _poll(t,e){const n=await e.getTransactionReceipt(wn(this,me));n&&e.emit(wn(this,me),n)}}me=new WeakMap;class Bh{constructor(t,e){An(this,ve),An(this,ye),An(this,we),An(this,Ae),An(this,be),An(this,Ee),bn(this,ye,t),bn(this,we,Eh(e)),bn(this,Ae,En(this,ve,ke).bind(this)),bn(this,be,!1),bn(this,Ee,-2)}start(){wn(this,be)||(bn(this,be,!0),-2===wn(this,Ee)&&wn(this,ye).getBlockNumber().then((t=>{bn(this,Ee,t)})),wn(this,ye).on("block",wn(this,Ae)))}stop(){wn(this,be)&&(bn(this,be,!1),wn(this,ye).off("block",wn(this,Ae)))}pause(t){this.stop(),t&&bn(this,Ee,-2)}resume(){this.start()}}ye=new WeakMap,we=new WeakMap,Ae=new WeakMap,be=new WeakMap,Ee=new WeakMap,ve=new WeakSet,ke=async function(t){if(-2===wn(this,Ee))return;const e=Eh(wn(this,we));e.fromBlock=wn(this,Ee)+1,e.toBlock=t;const n=await wn(this,ye).getLogs(e);if(0!==n.length)for(const r of n)wn(this,ye).emit(wn(this,we),r),bn(this,Ee,r.blockNumber);else wn(this,Ee)<t-60&&bn(this,Ee,t-60)};const Ih=BigInt(2);function Oh(t){return t&&"function"==typeof t.then}function Ch(t,e){return t+":"+JSON.stringify(e,((t,e)=>{if(null==e)return"null";if("bigint"==typeof e)return`bigint:${e.toString()}`;if("string"==typeof e)return e.toLowerCase();if("object"==typeof e&&!Array.isArray(e)){const t=Object.keys(e);return t.sort(),t.reduce(((t,n)=>(t[n]=e[n],t)),{})}return e}))}class Rh{constructor(t){mn(this,"name"),Pn(this,{name:t})}start(){}stop(){}pause(t){}resume(){}}function Th(t){return(t=Array.from(new Set(t).values())).sort(),t}async function Sh(t,e){if(null==t)throw new Error("invalid event");if(Array.isArray(t)&&(t={topics:t}),"string"==typeof t)switch(t){case"block":case"debug":case"error":case"finalized":case"network":case"pending":case"safe":return{type:t,tag:t}}if(Ln(t,32)){const e=t.toLowerCase();return{type:"transaction",tag:Ch("tx",{hash:e}),hash:e}}if(t.orphan){const e=t;return{type:"orphan",tag:Ch("orphan",e),filter:(n=e,JSON.parse(JSON.stringify(n)))}}var n;if(t.address||t.topics){const n=t,r={topics:(n.topics||[]).map((t=>null==t?null:Array.isArray(t)?Th(t.map((t=>t.toLowerCase()))):t.toLowerCase()))};if(n.address){const t=[],s=[],i=n=>{Ln(n)?t.push(n):s.push((async()=>{t.push(await ja(n,e))})())};Array.isArray(n.address)?n.address.forEach(i):i(n.address),s.length&&await Promise.all(s),r.address=Th(t.map((t=>t.toLowerCase())))}return{filter:r,tag:Ch("event",r),type:"event"}}Rn(!1,"unknown ProviderEvent","event",t)}function Uh(){return(new Date).getTime()}const Fh={cacheTimeout:250,pollingInterval:4e3};class Mh{constructor(t,e){if(An(this,Me),An(this,xe),An(this,Pe),An(this,Ne),An(this,Be),An(this,Ie),An(this,Oe),An(this,Ce),An(this,Re),An(this,Te),An(this,Se),An(this,Ue),An(this,Fe),bn(this,Fe,Object.assign({},Fh,e||{})),"any"===t)bn(this,Oe,!0),bn(this,Ie,null);else if(t){const e=yh.from(t);bn(this,Oe,!1),bn(this,Ie,Promise.resolve(e)),setTimeout((()=>{this.emit("network",e,null)}),0)}else bn(this,Oe,!1),bn(this,Ie,null);bn(this,Re,-1),bn(this,Ce,new Map),bn(this,xe,new Map),bn(this,Pe,new Map),bn(this,Ne,null),bn(this,Be,!1),bn(this,Te,1),bn(this,Se,new Map),bn(this,Ue,!1)}get pollingInterval(){return wn(this,Fe).pollingInterval}get provider(){return this}get plugins(){return Array.from(wn(this,Pe).values())}attachPlugin(t){if(wn(this,Pe).get(t.name))throw new Error(`cannot replace existing plugin: ${t.name} `);return wn(this,Pe).set(t.name,t.connect(this)),this}getPlugin(t){return wn(this,Pe).get(t)||null}get disableCcipRead(){return wn(this,Ue)}set disableCcipRead(t){bn(this,Ue,!!t)}async ccipReadFetch(t,e,n){if(this.disableCcipRead||0===n.length||null==t.to)return null;const r=t.to.toLowerCase(),s=e.toLowerCase(),i=[];for(let o=0;o<n.length;o++){const e=n[o],c=e.replace("{sender}",r).replace("{data}",s),l=new Pr(c);-1===e.indexOf("{data}")&&(l.body={data:s,sender:r}),this.emit("debug",{action:"sendCcipReadFetchRequest",request:l,index:o,urls:n});let u,h="unknown error";try{u=await l.send()}catch(a){i.push(a.message),this.emit("debug",{action:"receiveCcipReadFetchError",request:l,result:{error:a}});continue}try{const t=u.bodyJson;if(t.data)return this.emit("debug",{action:"receiveCcipReadFetchResult",request:l,result:t}),t.data;t.message&&(h=t.message),this.emit("debug",{action:"receiveCcipReadFetchError",request:l,result:t})}catch(a){}Cn(u.statusCode<400||u.statusCode>=500,`response not found during CCIP fetch: ${h}`,"OFFCHAIN_FAULT",{reason:"404_MISSING_RESOURCE",transaction:t,info:{url:e,errorMessage:h}}),i.push(h)}Cn(!1,`error encountered during CCIP fetch: ${i.map((t=>JSON.stringify(t))).join(", ")}`,"OFFCHAIN_FAULT",{reason:"500_SERVER_ERROR",transaction:t,info:{urls:n,errorMessages:i}})}_wrapBlock(t,e){return new pu(function(t){const e=ch(t);return e.transactions=t.transactions.map((t=>"string"==typeof t?t:hh(t))),e}(t),this)}_wrapLog(t,e){return new gu(function(t){return oh(t)}(t),this)}_wrapTransactionReceipt(t,e){return new mu(function(t){return uh(t)}(t),this)}_wrapTransactionResponse(t,e){return new yu(hh(t),this)}_detectNetwork(){Cn(!1,"sub-classes must implement this","UNSUPPORTED_OPERATION",{operation:"_detectNetwork"})}async _perform(t){Cn(!1,`unsupported method: ${t.method}`,"UNSUPPORTED_OPERATION",{operation:t.method,info:t})}async getBlockNumber(){const t=nr(await En(this,Me,Le).call(this,{method:"getBlockNumber"}),"%response");return wn(this,Re)>=0&&bn(this,Re,t),t}_getAddress(t){return ja(t,this)}_getBlockTag(t){if(null==t)return"latest";switch(t){case"earliest":return"0x0";case"finalized":case"latest":case"pending":case"safe":return t}return Ln(t)?Ln(t,32)?t:ir(t):("bigint"==typeof t&&(t=nr(t,"blockTag")),"number"==typeof t?t>=0?ir(t):wn(this,Re)>=0?ir(wn(this,Re)+t):this.getBlockNumber().then((e=>ir(e+t))):void Rn(!1,"invalid blockTag","blockTag",t))}_getFilter(t){const e=(t.topics||[]).map((t=>null==t?null:Array.isArray(t)?Th(t.map((t=>t.toLowerCase()))):t.toLowerCase())),n="blockHash"in t?t.blockHash:void 0,r=(t,r,s)=>{let i;switch(t.length){case 0:break;case 1:i=t[0];break;default:t.sort(),i=t}if(n&&(null!=r||null!=s))throw new Error("invalid filter");const a={};return i&&(a.address=i),e.length&&(a.topics=e),r&&(a.fromBlock=r),s&&(a.toBlock=s),n&&(a.blockHash=n),a};let s,i,a=[];if(t.address)if(Array.isArray(t.address))for(const o of t.address)a.push(this._getAddress(o));else a.push(this._getAddress(t.address));return"fromBlock"in t&&(s=this._getBlockTag(t.fromBlock)),"toBlock"in t&&(i=this._getBlockTag(t.toBlock)),a.filter((t=>"string"!=typeof t)).length||null!=s&&"string"!=typeof s||null!=i&&"string"!=typeof i?Promise.all([Promise.all(a),s,i]).then((t=>r(t[0],t[1],t[2]))):r(a,s,i)}_getTransactionRequest(t){const e=du(t),n=[];if(["to","from"].forEach((t=>{if(null==e[t])return;const r=ja(e[t],this);Oh(r)?n.push(async function(){e[t]=await r}()):e[t]=r})),null!=e.blockTag){const t=this._getBlockTag(e.blockTag);Oh(t)?n.push(async function(){e.blockTag=await t}()):e.blockTag=t}return n.length?async function(){return await Promise.all(n),e}():e}async getNetwork(){if(null==wn(this,Ie)){const t=(async()=>{try{const t=await this._detectNetwork();return this.emit("network",t,null),t}catch(e){throw wn(this,Ie)===t&&bn(this,Ie,null),e}})();return bn(this,Ie,t),(await t).clone()}const t=wn(this,Ie),[e,n]=await Promise.all([t,this._detectNetwork()]);return e.chainId!==n.chainId&&(wn(this,Oe)?(this.emit("network",n,e),wn(this,Ie)===t&&bn(this,Ie,Promise.resolve(n))):Cn(!1,`network changed: ${e.chainId} => ${n.chainId} `,"NETWORK_ERROR",{event:"changed"})),e.clone()}async getFeeData(){const t=await this.getNetwork(),e=async()=>{const{_block:e,gasPrice:n,priorityFee:r}=await xn({_block:En(this,Me,Qe).call(this,"latest",!1),gasPrice:(async()=>{try{return Xn(await En(this,Me,Le).call(this,{method:"getGasPrice"}),"%response")}catch(t){}return null})(),priorityFee:(async()=>{try{return Xn(await En(this,Me,Le).call(this,{method:"getPriorityFee"}),"%response")}catch(t){}return null})()});let s=null,i=null;const a=this._wrapBlock(e,t);return a&&a.baseFeePerGas&&(i=null!=r?r:BigInt("1000000000"),s=a.baseFeePerGas*Ih+i),new fu(n,s,i)},n=t.getPlugin("org.ethers.plugins.network.FetchUrlFeeDataPlugin");if(n){const t=new Pr(n.url),r=await n.processFunc(e,this,t);return new fu(r.gasPrice,r.maxFeePerGas,r.maxPriorityFeePerGas)}return await e()}async estimateGas(t){let e=this._getTransactionRequest(t);return Oh(e)&&(e=await e),Xn(await En(this,Me,Le).call(this,{method:"estimateGas",transaction:e}),"%response")}async call(t){const{tx:e,blockTag:n}=await xn({tx:this._getTransactionRequest(t),blockTag:this._getBlockTag(t.blockTag)});return await En(this,Me,Ge).call(this,En(this,Me,De).call(this,e,n,t.enableCcipRead?0:-1))}async getBalance(t,e){return Xn(await En(this,Me,He).call(this,{method:"getBalance"},t,e),"%response")}async getTransactionCount(t,e){return nr(await En(this,Me,He).call(this,{method:"getTransactionCount"},t,e),"%response")}async getCode(t,e){return Hn(await En(this,Me,He).call(this,{method:"getCode"},t,e))}async getStorage(t,e,n){const r=Xn(e,"position");return Hn(await En(this,Me,He).call(this,{method:"getStorage",position:r},t,n))}async broadcastTransaction(t){const{blockNumber:e,hash:n,network:r}=await xn({blockNumber:this.getBlockNumber(),hash:this._perform({method:"broadcastTransaction",signedTransaction:t}),network:this.getNetwork()}),s=So.from(t);if(s.hash!==n)throw new Error("@TODO: the returned hash did not match");return this._wrapTransactionResponse(s,r).replaceableTransaction(e)}async getBlock(t,e){const{network:n,params:r}=await xn({network:this.getNetwork(),params:En(this,Me,Qe).call(this,t,!!e)});return null==r?null:this._wrapBlock(r,n)}async getTransaction(t){const{network:e,params:n}=await xn({network:this.getNetwork(),params:En(this,Me,Le).call(this,{method:"getTransaction",hash:t})});return null==n?null:this._wrapTransactionResponse(n,e)}async getTransactionReceipt(t){const{network:e,params:n}=await xn({network:this.getNetwork(),params:En(this,Me,Le).call(this,{method:"getTransactionReceipt",hash:t})});if(null==n)return null;if(null==n.gasPrice&&null==n.effectiveGasPrice){const e=await En(this,Me,Le).call(this,{method:"getTransaction",hash:t});if(null==e)throw new Error("report this; could not find tx or effectiveGasPrice");n.effectiveGasPrice=e.gasPrice}return this._wrapTransactionReceipt(n,e)}async getTransactionResult(t){const{result:e}=await xn({network:this.getNetwork(),result:En(this,Me,Le).call(this,{method:"getTransactionResult",hash:t})});return null==e?null:Hn(e)}async getLogs(t){let e=this._getFilter(t);Oh(e)&&(e=await e);const{network:n,params:r}=await xn({network:this.getNetwork(),params:En(this,Me,Le).call(this,{method:"getLogs",filter:e})});return r.map((t=>this._wrapLog(t,n)))}_getProvider(t){Cn(!1,"provider cannot connect to target network","UNSUPPORTED_OPERATION",{operation:"_getProvider()"})}async getResolver(t){return await th.fromName(this,t)}async getAvatar(t){const e=await this.getResolver(t);return e?await e.getAvatar():null}async resolveName(t){const e=await this.getResolver(t);return e?await e.getAddress():null}async lookupAddress(t){const e=Qc((t=Ga(t)).substring(2).toLowerCase()+".addr.reverse");try{const n=await th.getEnsAddress(this),r=new qu(n,["function resolver(bytes32) view returns (address)"],this),s=await r.resolver(e);if(null==s||s===Ea)return null;const i=new qu(s,["function name(bytes32) view returns (string)"],this),a=await i.name(e);return await this.resolveName(a)!==t?null:a}catch(n){if(Bn(n,"BAD_DATA")&&"0x"===n.value)return null;if(Bn(n,"CALL_EXCEPTION"))return null;throw n}return null}async waitForTransaction(t,e,n){const r=null!=e?e:1;return 0===r?this.getTransactionReceipt(t):new Promise((async(e,s)=>{let i=null;const a=async n=>{try{const s=await this.getTransactionReceipt(t);if(null!=s&&n-s.blockNumber+1>=r)return e(s),void(i&&(clearTimeout(i),i=null))}catch(s){}this.once("block",a)};null!=n&&(i=setTimeout((()=>{null!=i&&(i=null,this.off("block",a),s(On("timeout","TIMEOUT",{reason:"timeout"})))}),n)),a(await this.getBlockNumber())}))}async waitForBlock(t){Cn(!1,"not implemented yet","NOT_IMPLEMENTED",{operation:"waitForBlock"})}_clearTimeout(t){const e=wn(this,Se).get(t);e&&(e.timer&&clearTimeout(e.timer),wn(this,Se).delete(t))}_setTimeout(t,e){null==e&&(e=0);const n=vn(this,Te)._++,r=()=>{wn(this,Se).delete(n),t()};if(this.paused)wn(this,Se).set(n,{timer:null,func:r,time:e});else{const t=setTimeout(r,e);wn(this,Se).set(n,{timer:t,func:r,time:Uh()})}return n}_forEachSubscriber(t){for(const e of wn(this,xe).values())t(e.subscriber)}_getSubscriber(t){switch(t.type){case"debug":case"error":case"network":return new Rh(t.type);case"block":{const t=new vh(this);return t.pollingInterval=this.pollingInterval,t}case"safe":case"finalized":return new xh(this,t.type);case"event":return new Bh(this,t.filter);case"transaction":return new Nh(this,t.hash);case"orphan":return new Ph(this,t.filter)}throw new Error(`unsupported event: ${t.type}`)}_recoverSubscriber(t,e){for(const n of wn(this,xe).values())if(n.subscriber===t){n.started&&n.subscriber.stop(),n.subscriber=e,n.started&&e.start(),null!=wn(this,Ne)&&e.pause(wn(this,Ne));break}}async on(t,e){const n=await En(this,Me,ze).call(this,t);return n.listeners.push({listener:e,once:!1}),n.started||(n.subscriber.start(),n.started=!0,null!=wn(this,Ne)&&n.subscriber.pause(wn(this,Ne))),this}async once(t,e){const n=await En(this,Me,ze).call(this,t);return n.listeners.push({listener:e,once:!0}),n.started||(n.subscriber.start(),n.started=!0,null!=wn(this,Ne)&&n.subscriber.pause(wn(this,Ne))),this}async emit(t,...e){const n=await En(this,Me,je).call(this,t,e);if(!n||0===n.listeners.length)return!1;const r=n.listeners.length;return n.listeners=n.listeners.filter((({listener:n,once:r})=>{const s=new cr(this,r?null:n,t);try{n.call(this,...e,s)}catch(i){}return!r})),0===n.listeners.length&&(n.started&&n.subscriber.stop(),wn(this,xe).delete(n.tag)),r>0}async listenerCount(t){if(t){const e=await En(this,Me,je).call(this,t);return e?e.listeners.length:0}let e=0;for(const{listeners:n}of wn(this,xe).values())e+=n.length;return e}async listeners(t){if(t){const e=await En(this,Me,je).call(this,t);return e?e.listeners.map((({listener:t})=>t)):[]}let e=[];for(const{listeners:n}of wn(this,xe).values())e=e.concat(n.map((({listener:t})=>t)));return e}async off(t,e){const n=await En(this,Me,je).call(this,t);if(!n)return this;if(e){const t=n.listeners.map((({listener:t})=>t)).indexOf(e);t>=0&&n.listeners.splice(t,1)}return e&&0!==n.listeners.length||(n.started&&n.subscriber.stop(),wn(this,xe).delete(n.tag)),this}async removeAllListeners(t){if(t){const{tag:e,started:n,subscriber:r}=await En(this,Me,ze).call(this,t);n&&r.stop(),wn(this,xe).delete(e)}else for(const[e,{started:n,subscriber:r}]of wn(this,xe))n&&r.stop(),wn(this,xe).delete(e);return this}async addListener(t,e){return await this.on(t,e)}async removeListener(t,e){return this.off(t,e)}get destroyed(){return wn(this,Be)}destroy(){this.removeAllListeners();for(const t of wn(this,Se).keys())this._clearTimeout(t);bn(this,Be,!0)}get paused(){return null!=wn(this,Ne)}set paused(t){!!t!==this.paused&&(this.paused?this.resume():this.pause(!1))}pause(t){if(bn(this,Re,-1),null!=wn(this,Ne)){if(wn(this,Ne)==!!t)return;Cn(!1,"cannot change pause type; resume first","UNSUPPORTED_OPERATION",{operation:"pause"})}this._forEachSubscriber((e=>e.pause(t))),bn(this,Ne,!!t);for(const e of wn(this,Se).values())e.timer&&clearTimeout(e.timer),e.time=Uh()-e.time}resume(){if(null!=wn(this,Ne)){this._forEachSubscriber((t=>t.resume())),bn(this,Ne,null);for(const t of wn(this,Se).values()){let e=t.time;e<0&&(e=0),t.time=Uh(),setTimeout(t.func,e)}}}}function Lh(t,e){try{const n=Dh(t,e);if(n)return fr(n)}catch(n){}return null}function Dh(t,e){if("0x"===t)return null;try{const n=nr(zn(t,e,e+32)),r=nr(zn(t,n,n+32));return zn(t,n+32,n+32+r)}catch(n){}return null}function Gh(t){const e=sr(t);if(e.length>32)throw new Error("internal; should not happen");const n=new Uint8Array(32);return n.set(e,32-e.length),n}function Hh(t){if(t.length%32==0)return t;const e=new Uint8Array(32*Math.ceil(t.length/32));return e.set(t),e}xe=new WeakMap,Pe=new WeakMap,Ne=new WeakMap,Be=new WeakMap,Ie=new WeakMap,Oe=new WeakMap,Ce=new WeakMap,Re=new WeakMap,Te=new WeakMap,Se=new WeakMap,Ue=new WeakMap,Fe=new WeakMap,Me=new WeakSet,Le=async function(t){const e=wn(this,Fe).cacheTimeout;if(e<0)return await this._perform(t);const n=Ch(t.method,t);let r=wn(this,Ce).get(n);return r||(r=this._perform(t),wn(this,Ce).set(n,r),setTimeout((()=>{wn(this,Ce).get(n)===r&&wn(this,Ce).delete(n)}),e)),await r},De=async function(t,e,n){Cn(n<10,"CCIP read exceeded maximum redirections","OFFCHAIN_FAULT",{reason:"TOO_MANY_REDIRECTS",transaction:Object.assign({},t,{blockTag:e,enableCcipRead:!0})});const r=du(t);try{return Hn(await this._perform({method:"call",transaction:r,blockTag:e}))}catch(s){if(!this.disableCcipRead&&In(s)&&s.data&&n>=0&&"latest"===e&&null!=r.to&&"0x556f1830"===zn(s.data,0,4)){const t=s.data,a=await ja(r.to,this);let o;try{o=function(t){const e={sender:"",urls:[],calldata:"",selector:"",extraData:"",errorArgs:[]};Cn(jn(t)>=160,"insufficient OffchainLookup data","OFFCHAIN_FAULT",{reason:"insufficient OffchainLookup data"});const n=zn(t,0,32);Cn(zn(n,0,12)===zn(zh,0,12),"corrupt OffchainLookup sender","OFFCHAIN_FAULT",{reason:"corrupt OffchainLookup sender"}),e.sender=zn(n,12);try{const n=[],r=nr(zn(t,32,64)),s=nr(zn(t,r,r+32)),i=zn(t,r+32);for(let t=0;t<s;t++){const e=Lh(i,32*t);if(null==e)throw new Error("abort");n.push(e)}e.urls=n}catch(s){Cn(!1,"corrupt OffchainLookup urls","OFFCHAIN_FAULT",{reason:"corrupt OffchainLookup urls"})}try{const n=Dh(t,64);if(null==n)throw new Error("abort");e.calldata=n}catch(s){Cn(!1,"corrupt OffchainLookup calldata","OFFCHAIN_FAULT",{reason:"corrupt OffchainLookup calldata"})}Cn(zn(t,100,128)===zn(zh,0,28),"corrupt OffchainLookup callbaackSelector","OFFCHAIN_FAULT",{reason:"corrupt OffchainLookup callbaackSelector"}),e.selector=zn(t,96,100);try{const n=Dh(t,128);if(null==n)throw new Error("abort");e.extraData=n}catch(s){Cn(!1,"corrupt OffchainLookup extraData","OFFCHAIN_FAULT",{reason:"corrupt OffchainLookup extraData"})}return e.errorArgs="sender,urls,calldata,selector,extraData".split(/,/).map((t=>e[t])),e}(zn(s.data,4))}catch(i){Cn(!1,i.message,"OFFCHAIN_FAULT",{reason:"BAD_DATA",transaction:r,info:{data:t}})}Cn(o.sender.toLowerCase()===a.toLowerCase(),"CCIP Read sender mismatch","CALL_EXCEPTION",{action:"call",data:t,reason:"OffchainLookup",transaction:r,invocation:null,revert:{signature:"OffchainLookup(address,string[],bytes,bytes4,bytes)",name:"OffchainLookup",args:o.errorArgs}});const c=await this.ccipReadFetch(r,o.calldata,o.urls);Cn(null!=c,"CCIP Read failed to fetch data","OFFCHAIN_FAULT",{reason:"FETCH_FAILED",transaction:r,info:{data:s.data,errorArgs:o.errorArgs}});const l={to:a,data:Qn([o.selector,jh([c,o.extraData])])};this.emit("debug",{action:"sendCcipReadCall",transaction:l});try{const t=await En(this,Me,De).call(this,l,e,n+1);return this.emit("debug",{action:"receiveCcipReadCallResult",transaction:Object.assign({},l),result:t}),t}catch(i){throw this.emit("debug",{action:"receiveCcipReadCallError",transaction:Object.assign({},l),error:i}),i}}throw s}},Ge=async function(t){const{value:e}=await xn({network:this.getNetwork(),value:t});return e},He=async function(t,e,n){let r=this._getAddress(e),s=this._getBlockTag(n);return"string"==typeof r&&"string"==typeof s||([r,s]=await Promise.all([r,s])),await En(this,Me,Ge).call(this,En(this,Me,Le).call(this,Object.assign(t,{address:r,blockTag:s})))},Qe=async function(t,e){if(Ln(t,32))return await En(this,Me,Le).call(this,{method:"getBlock",blockHash:t,includeTransactions:e});let n=this._getBlockTag(t);return"string"!=typeof n&&(n=await n),await En(this,Me,Le).call(this,{method:"getBlock",blockTag:n,includeTransactions:e})},je=async function(t,e){let n=await Sh(t,this);return"event"===n.type&&e&&e.length>0&&!0===e[0].removed&&(n=await Sh({orphan:"drop-log",log:e[0]},this)),wn(this,xe).get(n.tag)||null},ze=async function(t){const e=await Sh(t,this),n=e.tag;let r=wn(this,xe).get(n);if(!r){r={subscriber:this._getSubscriber(e),tag:n,addressableMap:new WeakMap,nameMap:new Map,started:!1,listeners:[]},wn(this,xe).set(n,r)}return r};const Qh=new Uint8Array([]);function jh(t){const e=[];let n=0;for(let r=0;r<t.length;r++)e.push(Qh),n+=32;for(let r=0;r<t.length;r++){const s=Fn(t[r]);e[r]=Gh(n),e.push(Gh(s.length)),e.push(Hh(s)),n+=32+32*Math.ceil(s.length/32)}return Qn(e)}const zh="0x0000000000000000000000000000000000000000000000000000000000000000";function Wh(t,e){if(t.provider)return t.provider;Cn(!1,"missing provider","UNSUPPORTED_OPERATION",{operation:e})}async function Vh(t,e){let n=du(e);if(null!=n.to&&(n.to=ja(n.to,t)),null!=n.from){const e=n.from;n.from=Promise.all([t.getAddress(),ja(e,t)]).then((([t,e])=>(Rn(t.toLowerCase()===e.toLowerCase(),"transaction from mismatch","tx.from",e),t)))}else n.from=t.getAddress();return await xn(n)}class Jh{constructor(t){mn(this,"provider"),Pn(this,{provider:t||null})}async getNonce(t){return Wh(this,"getTransactionCount").getTransactionCount(await this.getAddress(),t)}async populateCall(t){return await Vh(this,t)}async populateTransaction(t){const e=Wh(this,"populateTransaction"),n=await Vh(this,t);null==n.nonce&&(n.nonce=await this.getNonce("pending")),null==n.gasLimit&&(n.gasLimit=await this.estimateGas(n));const r=await this.provider.getNetwork();if(null!=n.chainId){Rn(Xn(n.chainId)===r.chainId,"transaction chainId mismatch","tx.chainId",t.chainId)}else n.chainId=r.chainId;const s=null!=n.maxFeePerGas||null!=n.maxPriorityFeePerGas;if(null==n.gasPrice||2!==n.type&&!s?0!==n.type&&1!==n.type||!s||Rn(!1,"pre-eip-1559 transaction do not support maxFeePerGas/maxPriorityFeePerGas","tx",t):Rn(!1,"eip-1559 transaction do not support gasPrice","tx",t),2!==n.type&&null!=n.type||null==n.maxFeePerGas||null==n.maxPriorityFeePerGas)if(0===n.type||1===n.type){const t=await e.getFeeData();Cn(null!=t.gasPrice,"network does not support gasPrice","UNSUPPORTED_OPERATION",{operation:"getGasPrice"}),null==n.gasPrice&&(n.gasPrice=t.gasPrice)}else{const t=await e.getFeeData();if(null==n.type)if(null!=t.maxFeePerGas&&null!=t.maxPriorityFeePerGas)if(n.authorizationList&&n.authorizationList.length?n.type=4:n.type=2,null!=n.gasPrice){const t=n.gasPrice;delete n.gasPrice,n.maxFeePerGas=t,n.maxPriorityFeePerGas=t}else null==n.maxFeePerGas&&(n.maxFeePerGas=t.maxFeePerGas),null==n.maxPriorityFeePerGas&&(n.maxPriorityFeePerGas=t.maxPriorityFeePerGas);else null!=t.gasPrice?(Cn(!s,"network does not support EIP-1559","UNSUPPORTED_OPERATION",{operation:"populateTransaction"}),null==n.gasPrice&&(n.gasPrice=t.gasPrice),n.type=0):Cn(!1,"failed to get consistent fee data","UNSUPPORTED_OPERATION",{operation:"signer.getFeeData"});else 2!==n.type&&3!==n.type&&4!==n.type||(null==n.maxFeePerGas&&(n.maxFeePerGas=t.maxFeePerGas),null==n.maxPriorityFeePerGas&&(n.maxPriorityFeePerGas=t.maxPriorityFeePerGas))}else n.type=2;return await xn(n)}async populateAuthorization(t){const e=Object.assign({},t);return null==e.chainId&&(e.chainId=(await Wh(this,"getNetwork").getNetwork()).chainId),null==e.nonce&&(e.nonce=await this.getNonce()),e}async estimateGas(t){return Wh(this,"estimateGas").estimateGas(await this.populateCall(t))}async call(t){return Wh(this,"call").call(await this.populateCall(t))}async resolveName(t){const e=Wh(this,"resolveName");return await e.resolveName(t)}async sendTransaction(t){const e=Wh(this,"sendTransaction"),n=await this.populateTransaction(t);delete n.from;const r=So.from(n);return await e.broadcastTransaction(await this.signTransaction(r))}authorize(t){Cn(!1,"authorization not implemented for this signer","UNSUPPORTED_OPERATION",{operation:"authorize"})}}class Kh{constructor(t){An(this,_e),An(this,We),An(this,Ve),An(this,Je),An(this,Ke),An(this,qe),An(this,Ze),bn(this,We,t),bn(this,Ve,null),bn(this,Je,En(this,_e,Ye).bind(this)),bn(this,Ke,!1),bn(this,qe,null),bn(this,Ze,!1)}_subscribe(t){throw new Error("subclasses must override this")}_emitResults(t,e){throw new Error("subclasses must override this")}_recover(t){throw new Error("subclasses must override this")}start(){wn(this,Ke)||(bn(this,Ke,!0),En(this,_e,Ye).call(this,-2))}stop(){wn(this,Ke)&&(bn(this,Ke,!1),bn(this,Ze,!0),En(this,_e,Xe).call(this),wn(this,We).off("block",wn(this,Je)))}pause(t){t&&En(this,_e,Xe).call(this),wn(this,We).off("block",wn(this,Je))}resume(){this.start()}}We=new WeakMap,Ve=new WeakMap,Je=new WeakMap,Ke=new WeakMap,qe=new WeakMap,Ze=new WeakMap,_e=new WeakSet,Ye=async function(t){try{null==wn(this,Ve)&&bn(this,Ve,this._subscribe(wn(this,We)));let t=null;try{t=await wn(this,Ve)}catch(e){if(!Bn(e,"UNSUPPORTED_OPERATION")||"eth_newFilter"!==e.operation)throw e}if(null==t)return bn(this,Ve,null),void wn(this,We)._recoverSubscriber(this,this._recover(wn(this,We)));const n=await wn(this,We).getNetwork();if(wn(this,qe)||bn(this,qe,n),wn(this,qe).chainId!==n.chainId)throw new Error("chaid changed");if(wn(this,Ze))return;const r=await wn(this,We).send("eth_getFilterChanges",[t]);await this._emitResults(wn(this,We),r)}catch(e){}wn(this,We).once("block",wn(this,Je))},Xe=function(){const t=wn(this,Ve);t&&(bn(this,Ve,null),t.then((t=>{wn(this,We).destroyed||wn(this,We).send("eth_uninstallFilter",[t])})))};class qh extends Kh{constructor(t,e){var n;super(t),An(this,$e),bn(this,$e,(n=e,JSON.parse(JSON.stringify(n))))}_recover(t){return new Bh(t,wn(this,$e))}async _subscribe(t){return await t.send("eth_newFilter",[wn(this,$e)])}async _emitResults(t,e){for(const n of e)t.emit(wn(this,$e),t._wrapLog(n,t._network))}}$e=new WeakMap;class Zh extends Kh{async _subscribe(t){return await t.send("eth_newPendingTransactionFilter",[])}async _emitResults(t,e){for(const n of e)t.emit("pending",n)}}const _h="bigint,boolean,function,number,string,symbol".split(/,/g);function Yh(t){if(null==t||_h.indexOf(typeof t)>=0)return t;if("function"==typeof t.getAddress)return t;if(Array.isArray(t))return t.map(Yh);if("object"==typeof t)return Object.keys(t).reduce(((e,n)=>(e[n]=t[n],e)),{});throw new Error(`should not happen: ${t} (${typeof t})`)}function Xh(t){return new Promise((e=>{setTimeout(e,t)}))}function $h(t){return t?t.toLowerCase():t}function tf(t){return t&&"number"==typeof t.pollingInterval}const ef={polling:!1,staticNetwork:null,batchStallTime:10,batchMaxSize:1<<20,batchMaxCount:100,cacheTimeout:250,pollingInterval:4e3};class nf extends Jh{constructor(t,e){super(t),mn(this,"address"),Pn(this,{address:e=Ga(e)})}connect(t){Cn(!1,"cannot reconnect JsonRpcSigner","UNSUPPORTED_OPERATION",{operation:"signer.connect"})}async getAddress(){return this.address}async populateTransaction(t){return await this.populateCall(t)}async sendUncheckedTransaction(t){const e=Yh(t),n=[];if(e.from){const r=e.from;n.push((async()=>{const n=await ja(r,this.provider);Rn(null!=n&&n.toLowerCase()===this.address.toLowerCase(),"from address mismatch","transaction",t),e.from=n})())}else e.from=this.address;if(null==e.gasLimit&&n.push((async()=>{e.gasLimit=await this.provider.estimateGas({...e,from:this.address})})()),null!=e.to){const t=e.to;n.push((async()=>{e.to=await ja(t,this.provider)})())}n.length&&await Promise.all(n);const r=this.provider.getRpcTransaction(e);return this.provider.send("eth_sendTransaction",[r])}async sendTransaction(t){const e=await this.provider.getBlockNumber(),n=await this.sendUncheckedTransaction(t);return await new Promise(((t,r)=>{const s=[1e3,100];let i=0;const a=async()=>{try{const r=await this.provider.getTransaction(n);if(null!=r)return void t(r.replaceableTransaction(e))}catch(o){if(Bn(o,"CANCELLED")||Bn(o,"BAD_DATA")||Bn(o,"NETWORK_ERROR")||Bn(o,"UNSUPPORTED_OPERATION"))return null==o.info&&(o.info={}),o.info.sendTransactionHash=n,void r(o);if(Bn(o,"INVALID_ARGUMENT")&&(i++,null==o.info&&(o.info={}),o.info.sendTransactionHash=n,i>10))return void r(o);this.provider.emit("error",On("failed to fetch transation after sending (will try again)","UNKNOWN_ERROR",{error:o}))}this.provider._setTimeout((()=>{a()}),s.pop()||4e3)};a()}))}async signTransaction(t){const e=Yh(t);if(e.from){const n=await ja(e.from,this.provider);Rn(null!=n&&n.toLowerCase()===this.address.toLowerCase(),"from address mismatch","transaction",t),e.from=n}else e.from=this.address;const n=this.provider.getRpcTransaction(e);return await this.provider.send("eth_signTransaction",[n])}async signMessage(t){const e="string"==typeof t?hr(t):t;return await this.provider.send("personal_sign",[Hn(e),this.address.toLowerCase()])}async signTypedData(t,e,n){const r=Yh(n),s=await rl.resolveNames(t,e,r,(async t=>{const e=await ja(t);return Rn(null!=e,"TypedData does not support null address","value",t),e}));return await this.provider.send("eth_signTypedData_v4",[this.address.toLowerCase(),JSON.stringify(rl.getPayload(s.domain,e,s.value))])}async unlock(t){return this.provider.send("personal_unlockAccount",[this.address.toLowerCase(),t,null])}async _legacySignMessage(t){const e="string"==typeof t?hr(t):t;return await this.provider.send("eth_sign",[this.address.toLowerCase(),Hn(e)])}}class rf extends Mh{constructor(t,e){super(t,e),An(this,cn),An(this,tn),An(this,en),An(this,nn),An(this,rn),An(this,sn),An(this,an),An(this,on),bn(this,en,1),bn(this,tn,Object.assign({},ef,e||{})),bn(this,nn,[]),bn(this,rn,null),bn(this,an,null),bn(this,on,null);{let t=null;const e=new Promise((e=>{t=e}));bn(this,sn,{promise:e,resolve:t})}const n=this._getOption("staticNetwork");"boolean"==typeof n?(Rn(!n||"any"!==t,"staticNetwork cannot be used on special network 'any'","options",e),n&&null!=t&&bn(this,an,yh.from(t))):n&&(Rn(null==t||n.matches(t),"staticNetwork MUST match network object","options",e),bn(this,an,n))}_getOption(t){return wn(this,tn)[t]}get _network(){return Cn(wn(this,an),"network is not available yet","NETWORK_ERROR"),wn(this,an)}async _perform(t){if("call"===t.method||"estimateGas"===t.method){let e=t.transaction;if(e&&null!=e.type&&Xn(e.type)&&null==e.maxFeePerGas&&null==e.maxPriorityFeePerGas){const n=await this.getFeeData();null==n.maxFeePerGas&&null==n.maxPriorityFeePerGas&&(t=Object.assign({},t,{transaction:Object.assign({},e,{type:void 0})}))}}const e=this.getRpcRequest(t);return null!=e?await this.send(e.method,e.args):super._perform(t)}async _detectNetwork(){const t=this._getOption("staticNetwork");if(t){if(!0!==t)return t;if(wn(this,an))return wn(this,an)}return wn(this,on)?await wn(this,on):this.ready?(bn(this,on,(async()=>{try{const t=yh.from(Xn(await this.send("eth_chainId",[])));return bn(this,on,null),t}catch(t){throw bn(this,on,null),t}})()),await wn(this,on)):(bn(this,on,(async()=>{const t={id:vn(this,en)._++,method:"eth_chainId",params:[],jsonrpc:"2.0"};let e;this.emit("debug",{action:"sendRpcPayload",payload:t});try{e=(await this._send(t))[0],bn(this,on,null)}catch(n){throw bn(this,on,null),this.emit("debug",{action:"receiveRpcError",error:n}),n}if(this.emit("debug",{action:"receiveRpcResult",result:e}),"result"in e)return yh.from(Xn(e.result));throw this.getRpcError(t,e)})()),await wn(this,on))}_start(){null!=wn(this,sn)&&null!=wn(this,sn).resolve&&(wn(this,sn).resolve(),bn(this,sn,null),(async()=>{for(;null==wn(this,an)&&!this.destroyed;)try{bn(this,an,await this._detectNetwork())}catch(t){if(this.destroyed)break;this.emit("error",On("failed to bootstrap network detection","NETWORK_ERROR",{event:"initial-network-discovery",info:{error:t}})),await Xh(1e3)}En(this,cn,ln).call(this)})())}async _waitUntilReady(){if(null!=wn(this,sn))return await wn(this,sn).promise}_getSubscriber(t){return"pending"===t.type?new Zh(this):"event"===t.type?this._getOption("polling")?new Bh(this,t.filter):new qh(this,t.filter):"orphan"===t.type&&"drop-log"===t.filter.orphan?new Rh("orphan"):super._getSubscriber(t)}get ready(){return null==wn(this,sn)}getRpcTransaction(t){const e={};return["chainId","gasLimit","gasPrice","type","maxFeePerGas","maxPriorityFeePerGas","nonce","value"].forEach((n=>{if(null==t[n])return;let r=n;"gasLimit"===n&&(r="gas"),e[r]=ir(Xn(t[n],`tx.${n}`))})),["from","to","data"].forEach((n=>{null!=t[n]&&(e[n]=Hn(t[n]))})),t.accessList&&(e.accessList=fo(t.accessList)),t.blobVersionedHashes&&(e.blobVersionedHashes=t.blobVersionedHashes.map((t=>t.toLowerCase()))),t.authorizationList&&(e.authorizationList=t.authorizationList.map((t=>{const e=po(t);return{address:e.address,nonce:ir(e.nonce),chainId:ir(e.chainId),yParity:ir(e.signature.yParity),r:ir(e.signature.r),s:ir(e.signature.s)}}))),e}getRpcRequest(t){switch(t.method){case"chainId":return{method:"eth_chainId",args:[]};case"getBlockNumber":return{method:"eth_blockNumber",args:[]};case"getGasPrice":return{method:"eth_gasPrice",args:[]};case"getPriorityFee":return{method:"eth_maxPriorityFeePerGas",args:[]};case"getBalance":return{method:"eth_getBalance",args:[$h(t.address),t.blockTag]};case"getTransactionCount":return{method:"eth_getTransactionCount",args:[$h(t.address),t.blockTag]};case"getCode":return{method:"eth_getCode",args:[$h(t.address),t.blockTag]};case"getStorage":return{method:"eth_getStorageAt",args:[$h(t.address),"0x"+t.position.toString(16),t.blockTag]};case"broadcastTransaction":return{method:"eth_sendRawTransaction",args:[t.signedTransaction]};case"getBlock":if("blockTag"in t)return{method:"eth_getBlockByNumber",args:[t.blockTag,!!t.includeTransactions]};if("blockHash"in t)return{method:"eth_getBlockByHash",args:[t.blockHash,!!t.includeTransactions]};break;case"getTransaction":return{method:"eth_getTransactionByHash",args:[t.hash]};case"getTransactionReceipt":return{method:"eth_getTransactionReceipt",args:[t.hash]};case"call":return{method:"eth_call",args:[this.getRpcTransaction(t.transaction),t.blockTag]};case"estimateGas":return{method:"eth_estimateGas",args:[this.getRpcTransaction(t.transaction)]};case"getLogs":return t.filter&&null!=t.filter.address&&(Array.isArray(t.filter.address)?t.filter.address=t.filter.address.map($h):t.filter.address=$h(t.filter.address)),{method:"eth_getLogs",args:[t.filter]}}return null}getRpcError(t,e){const{method:n}=t,{error:r}=e;if("eth_estimateGas"===n&&r.message){const e=r.message;if(!e.match(/revert/i)&&e.match(/insufficient funds/i))return On("insufficient funds","INSUFFICIENT_FUNDS",{transaction:t.params[0],info:{payload:t,error:r}});if(e.match(/nonce/i)&&e.match(/too low/i))return On("nonce has already been used","NONCE_EXPIRED",{transaction:t.params[0],info:{payload:t,error:r}})}if("eth_call"===n||"eth_estimateGas"===n){const e=of(r),s=eu.getBuiltinCallException("eth_call"===n?"call":"estimateGas",t.params[0],e?e.data:null);return s.info={error:r,payload:t},s}const s=JSON.stringify(function(t){const e=[];return cf(t,e),e}(r));if("string"==typeof r.message&&r.message.match(/user denied|ethers-user-denied/i)){return On("user rejected action","ACTION_REJECTED",{action:{eth_sign:"signMessage",personal_sign:"signMessage",eth_signTypedData_v4:"signTypedData",eth_signTransaction:"signTransaction",eth_sendTransaction:"sendTransaction",eth_requestAccounts:"requestAccess",wallet_requestAccounts:"requestAccess"}[n]||"unknown",reason:"rejected",info:{payload:t,error:r}})}if("eth_sendRawTransaction"===n||"eth_sendTransaction"===n){const e=t.params[0];if(s.match(/insufficient funds|base fee exceeds gas limit/i))return On("insufficient funds for intrinsic transaction cost","INSUFFICIENT_FUNDS",{transaction:e,info:{error:r}});if(s.match(/nonce/i)&&s.match(/too low/i))return On("nonce has already been used","NONCE_EXPIRED",{transaction:e,info:{error:r}});if(s.match(/replacement transaction/i)&&s.match(/underpriced/i))return On("replacement fee too low","REPLACEMENT_UNDERPRICED",{transaction:e,info:{error:r}});if(s.match(/only replay-protected/i))return On("legacy pre-eip-155 transactions not supported","UNSUPPORTED_OPERATION",{operation:n,info:{transaction:e,info:{error:r}}})}let i=!!s.match(/the method .* does not exist/i);return i||r&&r.details&&r.details.startsWith("Unauthorized method:")&&(i=!0),i?On("unsupported operation","UNSUPPORTED_OPERATION",{operation:t.method,info:{error:r,payload:t}}):On("could not coalesce error","UNKNOWN_ERROR",{error:r,payload:t})}send(t,e){if(this.destroyed)return Promise.reject(On("provider destroyed; cancelled request","UNSUPPORTED_OPERATION",{operation:t}));const n=vn(this,en)._++,r=new Promise(((r,s)=>{wn(this,nn).push({resolve:r,reject:s,payload:{method:t,params:e,id:n,jsonrpc:"2.0"}})}));return En(this,cn,ln).call(this),r}async getSigner(t){null==t&&(t=0);const e=this.send("eth_accounts",[]);if("number"==typeof t){const n=await e;if(t>=n.length)throw new Error("no such account");return new nf(this,n[t])}const{accounts:n}=await xn({network:this.getNetwork(),accounts:e});t=Ga(t);for(const r of n)if(Ga(r)===t)return new nf(this,t);throw new Error("invalid account")}async listAccounts(){return(await this.send("eth_accounts",[])).map((t=>new nf(this,t)))}destroy(){wn(this,rn)&&(clearTimeout(wn(this,rn)),bn(this,rn,null));for(const{payload:t,reject:e}of wn(this,nn))e(On("provider destroyed; cancelled request","UNSUPPORTED_OPERATION",{operation:t.method}));bn(this,nn,[]),super.destroy()}}tn=new WeakMap,en=new WeakMap,nn=new WeakMap,rn=new WeakMap,sn=new WeakMap,an=new WeakMap,on=new WeakMap,cn=new WeakSet,ln=function(){if(wn(this,rn))return;const t=1===this._getOption("batchMaxCount")?0:this._getOption("batchStallTime");bn(this,rn,setTimeout((()=>{bn(this,rn,null);const t=wn(this,nn);for(bn(this,nn,[]);t.length;){const e=[t.shift()];for(;t.length&&e.length!==wn(this,tn).batchMaxCount;){e.push(t.shift());if(JSON.stringify(e.map((t=>t.payload))).length>wn(this,tn).batchMaxSize){t.unshift(e.pop());break}}(async()=>{const t=1===e.length?e[0].payload:e.map((t=>t.payload));this.emit("debug",{action:"sendRpcPayload",payload:t});try{const n=await this._send(t);this.emit("debug",{action:"receiveRpcResult",result:n});for(const{resolve:t,reject:r,payload:s}of e){if(this.destroyed){r(On("provider destroyed; cancelled request","UNSUPPORTED_OPERATION",{operation:s.method}));continue}const e=n.filter((t=>t.id===s.id))[0];if(null!=e)"error"in e?r(this.getRpcError(s,e)):t(e.result);else{const t=On("missing response for request","BAD_DATA",{value:n,info:{payload:s}});this.emit("error",t),r(t)}}}catch(n){this.emit("debug",{action:"receiveRpcError",error:n});for(const{reject:t}of e)t(n)}})()}}),t))};class sf extends rf{constructor(t,e){super(t,e),An(this,un);let n=this._getOption("pollingInterval");null==n&&(n=ef.pollingInterval),bn(this,un,n)}_getSubscriber(t){const e=super._getSubscriber(t);return tf(e)&&(e.pollingInterval=wn(this,un)),e}get pollingInterval(){return wn(this,un)}set pollingInterval(t){if(!Number.isInteger(t)||t<0)throw new Error("invalid interval");bn(this,un,t),this._forEachSubscriber((t=>{tf(t)&&(t.pollingInterval=wn(this,un))}))}}un=new WeakMap;class af extends sf{constructor(t,e,n){null==t&&(t="http://localhost:8545"),super(e,n),An(this,hn),bn(this,hn,"string"==typeof t?new Pr(t):t.clone())}_getConnection(){return wn(this,hn).clone()}async send(t,e){return await this._start(),await super.send(t,e)}async _send(t){const e=this._getConnection();e.body=JSON.stringify(t),e.setHeader("content-type","application/json");const n=await e.send();n.assertOk();let r=n.bodyJson;return Array.isArray(r)||(r=[r]),r}}function of(t){if(null==t)return null;if("string"==typeof t.message&&t.message.match(/revert/i)&&Ln(t.data))return{message:t.message,data:t.data};if("object"==typeof t){for(const e in t){const n=of(t[e]);if(n)return n}return null}if("string"==typeof t)try{return of(JSON.parse(t))}catch(e){}return null}function cf(t,e){if(null!=t){if("string"==typeof t.message&&e.push(t.message),"object"==typeof t)for(const n in t)cf(t[n],e);if("string"==typeof t)try{return cf(JSON.parse(t),e)}catch(n){}}}hn=new WeakMap;fn=new WeakMap,dn=new WeakMap;let lf=class t extends sf{constructor(t,e,n){const r=Object.assign({},null!=n?n:{},{batchMaxCount:1});Rn(t&&t.request,"invalid EIP-1193 provider","ethereum",t),super(e,r),An(this,fn),An(this,dn),bn(this,dn,null),n&&n.providerInfo&&bn(this,dn,n.providerInfo),bn(this,fn,(async(e,n)=>{const r={method:e,params:n};this.emit("debug",{action:"sendEip1193Request",payload:r});try{const e=await t.request(r);return this.emit("debug",{action:"receiveEip1193Result",result:e}),e}catch(s){const t=new Error(s.message);throw t.code=s.code,t.data=s.data,t.payload=r,this.emit("debug",{action:"receiveEip1193Error",error:t}),t}}))}get providerInfo(){return wn(this,dn)}async send(t,e){return await this._start(),await super.send(t,e)}async _send(t){Rn(!Array.isArray(t),"EIP-1193 does not support batch request","payload",t);try{const e=await wn(this,fn).call(this,t.method,t.params||[]);return[{id:t.id,result:e}]}catch(e){return[{id:t.id,error:{code:e.code,data:e.data,message:e.message}}]}}getRpcError(t,e){switch((e=JSON.parse(JSON.stringify(e))).error.code||-1){case 4001:e.error.message=`ethers-user-denied: ${e.error.message}`;break;case 4200:e.error.message=`ethers-unsupported: ${e.error.message}`}return super.getRpcError(t,e)}async hasSigner(t){null==t&&(t=0);const e=await this.send("eth_accounts",[]);return"number"==typeof t?e.length>t:(t=t.toLowerCase(),0!==e.filter((e=>e.toLowerCase()===t)).length)}async getSigner(t){if(null==t&&(t=0),!(await this.hasSigner(t)))try{await wn(this,fn).call(this,"eth_requestAccounts",[])}catch(e){const t=e.payload;throw this.getRpcError(t,{id:t.id,error:e})}return await super.getSigner(t)}static async discover(e){if(null==e&&(e={}),e.provider)return new t(e.provider);const n=e.window?e.window:"undefined"!=typeof window?window:null;if(null==n)return null;const r=e.anyProvider;if(r&&n.ethereum)return new t(n.ethereum);if(!("addEventListener"in n&&"dispatchEvent"in n&&"removeEventListener"in n))return null;const s=e.timeout?e.timeout:300;return 0===s?null:await new Promise(((i,a)=>{let o=[];const c=t=>{o.push(t.detail),r&&l()},l=()=>{if(clearTimeout(u),o.length)if(e&&e.filter){const n=e.filter(o.map((t=>Object.assign({},t.info))));if(null==n)i(null);else if(n instanceof t)i(n);else{let e=null;if(n.uuid){e=o.filter((t=>n.uuid===t.info.uuid))[0]}if(e){const{provider:n,info:r}=e;i(new t(n,void 0,{providerInfo:r}))}else a(On("filter returned unknown info","UNSUPPORTED_OPERATION",{value:n}))}}else{const{provider:e,info:n}=o[0];i(new t(e,void 0,{providerInfo:n}))}else i(null);n.removeEventListener("eip6963:announceProvider",c)},u=setTimeout((()=>{l()}),s);n.addEventListener("eip6963:announceProvider",c),n.dispatchEvent(new Event("eip6963:requestProvider"))}))}};export{lf as B,qu as C,af as J,Ea as Z,_r as a,Yr as f};
