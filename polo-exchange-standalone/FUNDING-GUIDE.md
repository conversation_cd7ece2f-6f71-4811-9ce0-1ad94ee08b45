# 💰 Deployment Wallet Funding Guide

## 🎯 Your Deployment Wallet
**Address:** `******************************************`

## 💸 Required Funds for Deployment

### 🟡 **BSC (Recommended - Cheapest)**
- **Required:** ~0.01 BNB ($3-5)
- **Gas Cost:** ~5 gwei
- **Deployment Cost:** ~$1-3
- **Why BSC:** Lowest cost, fast confirmation

### 🔵 **Ethereum**
- **Required:** ~0.01 ETH ($30-50)
- **Gas Cost:** ~20 gwei
- **Deployment Cost:** ~$15-45
- **Why Ethereum:** Most liquidity, highest adoption

### 🟣 **Polygon**
- **Required:** ~0.1 MATIC ($0.10-0.50)
- **Gas Cost:** ~30 gwei
- **Deployment Cost:** ~$0.05-0.25
- **Why Polygon:** Very cheap, fast

### 🔶 **Avalanche**
- **Required:** ~0.01 AVAX ($0.50-1)
- **Gas Cost:** ~25 gwei
- **Deployment Cost:** ~$0.25-1
- **Why Avalanche:** Fast, cheap

## 🚀 **How to Fund Your Wallet**

### **Option 1: Send from Your Main Wallet**
1. Open your main wallet (Trust Wallet, MetaMask, etc.)
2. Send the required amount to: `******************************************`
3. Wait for confirmation (1-3 minutes)
4. Run deployment command

### **Option 2: Buy Crypto and Send**
1. Buy crypto on exchange (Binance, Coinbase, etc.)
2. Withdraw to: `******************************************`
3. Wait for confirmation
4. Run deployment

### **Option 3: Use Faucets (Testnets Only)**
- **BSC Testnet:** https://testnet.binance.org/faucet-smart
- **Sepolia Testnet:** https://sepoliafaucet.com/
- **Polygon Mumbai:** https://faucet.polygon.technology/

## 📋 **Deployment Order (Recommended)**

### **1. Start with BSC (Cheapest)**
```bash
# Fund with 0.01 BNB (~$3)
npx hardhat run scripts/deploy.cjs --network bsc
```

### **2. Deploy to Polygon (Very Cheap)**
```bash
# Fund with 0.1 MATIC (~$0.10)
npx hardhat run scripts/deploy.cjs --network polygon
```

### **3. Deploy to Ethereum (Most Important)**
```bash
# Fund with 0.01 ETH (~$30)
npx hardhat run scripts/deploy.cjs --network ethereum
```

### **4. Deploy to Other Networks**
```bash
# Avalanche
npx hardhat run scripts/deploy.cjs --network avalanche

# Arbitrum
npx hardhat run scripts/deploy.cjs --network arbitrum

# Optimism
npx hardhat run scripts/deploy.cjs --network optimism
```

## 🔍 **Check Wallet Balance**

### **BSC:**
- **Explorer:** https://bscscan.com/address/******************************************
- **Required:** 0.01 BNB

### **Ethereum:**
- **Explorer:** https://etherscan.io/address/******************************************
- **Required:** 0.01 ETH

### **Polygon:**
- **Explorer:** https://polygonscan.com/address/******************************************
- **Required:** 0.1 MATIC

## 💡 **Pro Tips**

### **Start Small:**
1. Fund BSC first (cheapest at ~$3)
2. Test deployment and functionality
3. Then deploy to other networks

### **Gas Optimization:**
- Deploy during low network usage (weekends, early morning)
- Use appropriate gas prices for each network
- BSC: 5 gwei, Ethereum: 15-25 gwei, Polygon: 30 gwei

### **Verification:**
- Get API keys from block explorers for contract verification
- Etherscan, BSCScan, PolygonScan, etc.

## 🎯 **Next Steps After Funding**

1. **Check Balance:**
   ```bash
   # Check if wallet is funded
   npx hardhat run scripts/check-balance.cjs --network bsc
   ```

2. **Deploy Contract:**
   ```bash
   # Deploy to BSC (cheapest)
   npx hardhat run scripts/deploy.cjs --network bsc
   ```

3. **Verify Contract:**
   ```bash
   # Verify on BSCScan
   npx hardhat verify --network bsc CONTRACT_ADDRESS "LIFI_DIAMOND" "FEE_COLLECTOR"
   ```

4. **Test Contract:**
   ```bash
   # Run contract tests
   npx hardhat test
   ```

## 🚨 **Important Notes**

- **Keep Private Key Safe:** Never share your deployment private key
- **Use Different Wallet:** Don't use your main wallet for deployment
- **Start with Testnet:** Test on testnets first if possible
- **Monitor Gas:** Check gas prices before deployment
- **Backup Everything:** Save deployment addresses and transaction hashes

## 📞 **Need Help?**

If you need help funding your wallet or have questions:
1. Check the block explorer links above
2. Verify the wallet address: `******************************************`
3. Start with BSC for cheapest deployment (~$3)
4. Contact support if you encounter issues

---

**Ready to deploy once funded!** 🚀
