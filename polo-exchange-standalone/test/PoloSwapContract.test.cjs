const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("PoloSwapContract", function () {
  let poloSwap;
  let owner;
  let user1;
  let user2;
  let feeCollector;
  let mockToken;
  let mockLiFiDiamond;

  const FEE_RATE = 300; // 3%
  const BASIS_POINTS = 10000;

  beforeEach(async function () {
    [owner, user1, user2, feeCollector] = await ethers.getSigners();

    // Deploy mock ERC20 token
    const MockToken = await ethers.getContractFactory("MockERC20");
    mockToken = await MockToken.deploy("Test Token", "TEST", ethers.parseEther("1000000"));

    // Deploy mock LiFi Diamond
    const MockLiFiDiamond = await ethers.getContractFactory("MockLiFiDiamond");
    mockLiFiDiamond = await MockLiFiDiamond.deploy();

    // Deploy PoloSwapContract
    const PoloSwapContract = await ethers.getContractFactory("PoloSwapContract");
    poloSwap = await PoloSwapContract.deploy(
      await mockLiFiDiamond.getAddress(),
      feeCollector.address
    );

    // Add mock token as supported
    await poloSwap.addSupportedToken(await mockToken.getAddress());
    await poloSwap.addSupportedToken(ethers.ZeroAddress); // ETH

    // Give user1 some tokens
    await mockToken.transfer(user1.address, ethers.parseEther("1000"));
    await mockToken.connect(user1).approve(await poloSwap.getAddress(), ethers.parseEther("1000"));
  });

  describe("🔧 Contract Setup", function () {
    it("Should set correct fee rate (3%)", async function () {
      expect(await poloSwap.FEE_RATE()).to.equal(300);
    });

    it("Should set correct LiFi Diamond address", async function () {
      expect(await poloSwap.lifiDiamond()).to.equal(await mockLiFiDiamond.getAddress());
    });

    it("Should set correct fee collector", async function () {
      expect(await poloSwap.feeCollector()).to.equal(feeCollector.address);
    });

    it("Should support added tokens", async function () {
      expect(await poloSwap.isTokenSupported(await mockToken.getAddress())).to.be.true;
      expect(await poloSwap.isTokenSupported(ethers.ZeroAddress)).to.be.true;
    });
  });

  describe("💰 Fee Calculation", function () {
    it("Should calculate 3% fee correctly", async function () {
      const amount = ethers.parseEther("100");
      const expectedFee = (amount * BigInt(FEE_RATE)) / BigInt(BASIS_POINTS);
      const expectedSwapAmount = amount - expectedFee;

      console.log(`\n💰 Fee Calculation Test:`);
      console.log(`   Amount: ${ethers.formatEther(amount)} TEST`);
      console.log(`   Fee (3%): ${ethers.formatEther(expectedFee)} TEST`);
      console.log(`   Swap Amount: ${ethers.formatEther(expectedSwapAmount)} TEST`);

      expect(expectedFee).to.equal(ethers.parseEther("3")); // 3% of 100 = 3
      expect(expectedSwapAmount).to.equal(ethers.parseEther("97")); // 100 - 3 = 97
    });
  });

  describe("🔄 Swap Request", function () {
    it("Should create swap request with correct fee deduction", async function () {
      const amount = ethers.parseEther("100");
      const fromToken = await mockToken.getAddress();
      const toToken = ethers.ZeroAddress; // ETH
      const recipient = user2.address;

      console.log(`\n🔄 Testing Swap Request:`);
      console.log(`   From: ${amount} TEST tokens`);
      console.log(`   To: ETH`);
      console.log(`   Recipient: ${recipient}`);

      const tx = await poloSwap.connect(user1).requestSwap(
        fromToken,
        toToken,
        amount,
        recipient
      );

      const receipt = await tx.wait();
      
      // Check events
      const swapRequestedEvent = receipt.logs.find(log => {
        try {
          const parsed = poloSwap.interface.parseLog(log);
          return parsed.name === 'SwapRequested';
        } catch {
          return false;
        }
      });

      expect(swapRequestedEvent).to.not.be.undefined;

      // Check swap request storage
      const swapRequest = await poloSwap.getSwapRequest(1);
      expect(swapRequest.user).to.equal(user1.address);
      expect(swapRequest.fromToken).to.equal(fromToken);
      expect(swapRequest.toToken).to.equal(toToken);
      expect(swapRequest.amount).to.equal(ethers.parseEther("97")); // After 3% fee
      expect(swapRequest.feeAmount).to.equal(ethers.parseEther("3"));
      expect(swapRequest.completed).to.be.false;

      // Check fee collection
      const collectedFees = await poloSwap.getCollectedFees(fromToken);
      expect(collectedFees).to.equal(ethers.parseEther("3"));

      console.log(`   ✅ Swap ID: ${1}`);
      console.log(`   ✅ Fee Collected: ${ethers.formatEther(collectedFees)} TEST`);
      console.log(`   ✅ Swap Amount: ${ethers.formatEther(swapRequest.amount)} TEST`);
    });

    it("Should reject unsupported tokens", async function () {
      const unsupportedToken = user2.address; // Random address
      
      await expect(
        poloSwap.connect(user1).requestSwap(
          unsupportedToken,
          ethers.ZeroAddress,
          ethers.parseEther("100"),
          user2.address
        )
      ).to.be.revertedWith("From token not supported");
    });

    it("Should reject zero amount", async function () {
      await expect(
        poloSwap.connect(user1).requestSwap(
          await mockToken.getAddress(),
          ethers.ZeroAddress,
          0,
          user2.address
        )
      ).to.be.revertedWith("Amount must be greater than 0");
    });
  });

  describe("🚀 Swap Execution", function () {
    beforeEach(async function () {
      // Create a swap request first
      await poloSwap.connect(user1).requestSwap(
        await mockToken.getAddress(),
        ethers.ZeroAddress,
        ethers.parseEther("100"),
        user2.address
      );
    });

    it("Should execute swap via LiFi", async function () {
      const swapId = 1;
      const lifiCalldata = "0x"; // Empty calldata for mock

      console.log(`\n🚀 Testing Swap Execution:`);
      console.log(`   Swap ID: ${swapId}`);
      console.log(`   LiFi Calldata: ${lifiCalldata || 'Empty (mock)'}`);

      const tx = await poloSwap.executeSwap(swapId, lifiCalldata);
      await tx.wait();

      // Check swap completion
      const swapRequest = await poloSwap.getSwapRequest(swapId);
      expect(swapRequest.completed).to.be.true;

      console.log(`   ✅ Swap completed successfully`);
    });

    it("Should only allow owner to execute swaps", async function () {
      await expect(
        poloSwap.connect(user1).executeSwap(1, "0x")
      ).to.be.revertedWithCustomError(poloSwap, "OwnableUnauthorizedAccount");
    });

    it("Should reject already completed swaps", async function () {
      await poloSwap.executeSwap(1, "0x");
      
      await expect(
        poloSwap.executeSwap(1, "0x")
      ).to.be.revertedWith("Swap already completed");
    });
  });

  describe("💸 Fee Withdrawal", function () {
    beforeEach(async function () {
      // Create and execute a swap to generate fees
      await poloSwap.connect(user1).requestSwap(
        await mockToken.getAddress(),
        ethers.ZeroAddress,
        ethers.parseEther("100"),
        user2.address
      );
    });

    it("Should allow owner to withdraw fees", async function () {
      const tokenAddress = await mockToken.getAddress();
      const feeAmount = ethers.parseEther("3");

      console.log(`\n💸 Testing Fee Withdrawal:`);
      console.log(`   Token: TEST`);
      console.log(`   Fee Amount: ${ethers.formatEther(feeAmount)} TEST`);

      const initialBalance = await mockToken.balanceOf(feeCollector.address);
      
      await poloSwap.withdrawFees(tokenAddress, feeAmount);
      
      const finalBalance = await mockToken.balanceOf(feeCollector.address);
      expect(finalBalance - initialBalance).to.equal(feeAmount);

      console.log(`   ✅ Fees withdrawn to fee collector`);
    });

    it("Should reject withdrawal of more fees than collected", async function () {
      const tokenAddress = await mockToken.getAddress();
      const excessiveAmount = ethers.parseEther("10"); // More than 3% fee

      await expect(
        poloSwap.withdrawFees(tokenAddress, excessiveAmount)
      ).to.be.revertedWith("Insufficient fees collected");
    });
  });

  describe("🔧 Admin Functions", function () {
    it("Should allow owner to add/remove supported tokens", async function () {
      const newToken = user2.address; // Random address as token

      // Add token
      await poloSwap.addSupportedToken(newToken);
      expect(await poloSwap.isTokenSupported(newToken)).to.be.true;

      // Remove token
      await poloSwap.removeSupportedToken(newToken);
      expect(await poloSwap.isTokenSupported(newToken)).to.be.false;
    });

    it("Should allow owner to update LiFi Diamond", async function () {
      const newLiFiDiamond = user2.address;
      
      await poloSwap.updateLiFiDiamond(newLiFiDiamond);
      expect(await poloSwap.lifiDiamond()).to.equal(newLiFiDiamond);
    });

    it("Should allow owner to update fee collector", async function () {
      const newFeeCollector = user2.address;
      
      await poloSwap.updateFeeCollector(newFeeCollector);
      expect(await poloSwap.feeCollector()).to.equal(newFeeCollector);
    });
  });

  describe("📊 Multiple Swaps Simulation", function () {
    it("Should handle multiple swaps and track fees correctly", async function () {
      console.log(`\n📊 Testing Multiple Swaps:`);

      const swaps = [
        { amount: ethers.parseEther("100"), expectedFee: ethers.parseEther("3") },
        { amount: ethers.parseEther("200"), expectedFee: ethers.parseEther("6") },
        { amount: ethers.parseEther("50"), expectedFee: ethers.parseEther("1.5") }
      ];

      let totalFees = BigInt(0);

      for (let i = 0; i < swaps.length; i++) {
        const swap = swaps[i];
        
        // Give user more tokens if needed
        await mockToken.transfer(user1.address, swap.amount);
        await mockToken.connect(user1).approve(await poloSwap.getAddress(), swap.amount);

        // Create swap request
        await poloSwap.connect(user1).requestSwap(
          await mockToken.getAddress(),
          ethers.ZeroAddress,
          swap.amount,
          user2.address
        );

        totalFees += swap.expectedFee;

        console.log(`   Swap ${i + 1}: ${ethers.formatEther(swap.amount)} TEST → Fee: ${ethers.formatEther(swap.expectedFee)} TEST`);
      }

      // Check total fees collected
      const collectedFees = await poloSwap.getCollectedFees(await mockToken.getAddress());
      expect(collectedFees).to.equal(totalFees);

      console.log(`   ✅ Total Fees Collected: ${ethers.formatEther(collectedFees)} TEST`);
      console.log(`   ✅ Expected Total: ${ethers.formatEther(totalFees)} TEST`);
    });
  });
});
