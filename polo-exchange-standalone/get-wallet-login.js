#!/usr/bin/env node

/**
 * Get Trust Wallet Login Information
 * Shows you how to access your automated wallet manually
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function getWalletLogin() {
  console.log('🔐 TRUST WALLET LOGIN HELPER');
  console.log('============================');
  console.log('');
  
  console.log('This script will help you access your automated Trust Wallet manually.');
  console.log('');

  // Check if wallet is already set up
  console.log('📋 Choose your situation:');
  console.log('1. I already ran the setup and have my mnemonic phrase');
  console.log('2. I need to generate a new wallet first');
  console.log('3. I want to use my existing Trust Wallet');
  console.log('');

  const choice = await question('Enter your choice (1-3): ');
  console.log('');

  switch (choice) {
    case '1':
      await showLoginInstructions();
      break;
    case '2':
      await generateNewWallet();
      break;
    case '3':
      await useExistingWallet();
      break;
    default:
      console.log('❌ Invalid choice. Please run the script again.');
  }

  rl.close();
}

async function showLoginInstructions() {
  console.log('🔐 TRUST WALLET LOGIN INSTRUCTIONS');
  console.log('==================================');
  console.log('');
  
  console.log('📱 Step 1: Download Trust Wallet');
  console.log('  • iOS: App Store → "Trust Wallet"');
  console.log('  • Android: Google Play → "Trust Wallet"');
  console.log('  • Official: https://trustwallet.com/');
  console.log('');
  
  console.log('🔑 Step 2: Import Your Wallet');
  console.log('  1. Open Trust Wallet app');
  console.log('  2. Tap "I already have a wallet"');
  console.log('  3. Select "Multi-Coin Wallet"');
  console.log('  4. Enter your 12-word mnemonic phrase');
  console.log('  5. Create a password/PIN');
  console.log('  6. ✅ You\'re logged in!');
  console.log('');
  
  console.log('📍 Step 3: Verify Your Addresses');
  console.log('  You should see addresses for:');
  console.log('  • Bitcoin (BTC)');
  console.log('  • Ethereum (ETH)');
  console.log('  • Solana (SOL)');
  console.log('  • BNB (BSC)');
  console.log('  • Polygon (MATIC)');
  console.log('  • + 35 more currencies');
  console.log('');
  
  console.log('💰 Step 4: Monitor Your Revenue');
  console.log('  • Check balances regularly');
  console.log('  • See 3% fees accumulating');
  console.log('  • Monitor user deposits');
  console.log('  • Track transaction history');
  console.log('');
  
  console.log('⚠️ SECURITY REMINDER:');
  console.log('  • Never share your mnemonic phrase');
  console.log('  • Write it down on paper');
  console.log('  • Store in a secure location');
  console.log('  • Consider hardware wallet backup');
  console.log('');
  
  const haveMnemonic = await question('Do you have your 12-word mnemonic phrase? (y/n): ');
  
  if (haveMnemonic.toLowerCase() === 'n') {
    console.log('');
    console.log('❌ You need your mnemonic phrase to log in!');
    console.log('');
    console.log('🔧 To get your mnemonic phrase:');
    console.log('  1. Run: node setup-automated-exchange.js');
    console.log('  2. Choose option 1: "Generate new wallet"');
    console.log('  3. Save the 12-word phrase securely');
    console.log('  4. Use it to log into Trust Wallet app');
  } else {
    console.log('');
    console.log('✅ Perfect! Use your mnemonic phrase to log into Trust Wallet app.');
    console.log('');
    console.log('🎯 Quick Login Steps:');
    console.log('  Trust Wallet → "I already have a wallet" → Enter 12 words → Done!');
  }
}

async function generateNewWallet() {
  console.log('🎲 GENERATE NEW WALLET');
  console.log('======================');
  console.log('');
  
  console.log('To generate a new automated wallet:');
  console.log('');
  console.log('1. Run the setup script:');
  console.log('   node setup-automated-exchange.js');
  console.log('');
  console.log('2. Choose option 1: "Generate new wallet"');
  console.log('');
  console.log('3. The script will show you:');
  console.log('   🔐 Generated mnemonic: "word1 word2 word3..."');
  console.log('   ⚠️ SAVE THIS MNEMONIC SECURELY!');
  console.log('');
  console.log('4. Copy the 12 words and save them safely');
  console.log('');
  console.log('5. Use those 12 words to log into Trust Wallet app');
  console.log('');
  
  const runNow = await question('Would you like me to run the setup script now? (y/n): ');
  
  if (runNow.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Running setup script...');
    console.log('');
    
    // Import and run the setup
    try {
      require('./setup-automated-exchange.js');
    } catch (error) {
      console.log('❌ Could not run setup script automatically.');
      console.log('');
      console.log('Please run manually:');
      console.log('  node setup-automated-exchange.js');
    }
  } else {
    console.log('');
    console.log('👍 No problem! Run this when you\'re ready:');
    console.log('  node setup-automated-exchange.js');
  }
}

async function useExistingWallet() {
  console.log('📱 USE EXISTING TRUST WALLET');
  console.log('============================');
  console.log('');
  
  console.log('To use your existing Trust Wallet with the automated system:');
  console.log('');
  console.log('📱 Step 1: Get Your Mnemonic from Trust Wallet App');
  console.log('  1. Open Trust Wallet app');
  console.log('  2. Go to Settings → Wallets');
  console.log('  3. Tap your wallet name');
  console.log('  4. Tap "Show Recovery Phrase"');
  console.log('  5. Copy the 12 words');
  console.log('');
  console.log('🔧 Step 2: Import to Automated System');
  console.log('  1. Run: node setup-automated-exchange.js');
  console.log('  2. Choose option 2: "Import existing mnemonic"');
  console.log('  3. Enter your 12 words');
  console.log('  4. ✅ Your existing wallet is now automated!');
  console.log('');
  console.log('🎯 Benefits:');
  console.log('  • Keep your existing addresses');
  console.log('  • Add automated swap functionality');
  console.log('  • Generate revenue from existing wallet');
  console.log('  • Full manual access still available');
  console.log('');
  
  console.log('⚠️ IMPORTANT:');
  console.log('  • Your existing funds remain safe');
  console.log('  • You keep full control of the wallet');
  console.log('  • Automation just adds revenue features');
  console.log('  • You can disable automation anytime');
  console.log('');
  
  const proceed = await question('Ready to integrate your existing wallet? (y/n): ');
  
  if (proceed.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Great! Run this command:');
    console.log('  node setup-automated-exchange.js');
    console.log('');
    console.log('Then choose option 2 and enter your mnemonic phrase.');
  } else {
    console.log('');
    console.log('👍 No problem! You can do this anytime.');
    console.log('');
    console.log('Your existing Trust Wallet will work normally,');
    console.log('and you can add automation later when ready.');
  }
}

// Run the helper
getWalletLogin().catch(console.error);
