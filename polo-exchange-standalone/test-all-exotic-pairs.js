#!/usr/bin/env node

/**
 * Test All Exotic Pairs
 * Comprehensive testing of profitable token pairs for revenue optimization
 */

import dotenv from 'dotenv';
dotenv.config();

console.log('🚀 TESTING ALL EXOTIC & PROFITABLE PAIRS');
console.log('=' .repeat(70));

async function testTokenPair(fromToken, toToken, fromChain, toChain, amount = 1, description = '') {
  if (!process.env.LIFI_API_KEY) {
    return { success: false, error: 'No API key' };
  }

  try {
    const quoteParams = new URLSearchParams({
      fromChain: fromChain.toString(),
      toChain: toChain.toString(),
      fromToken: '0x0000000000000000000000000000000000000000', // Native tokens
      toToken: '0x0000000000000000000000000000000000000000',
      fromAmount: (amount * 1e18).toString(),
      fromAddress: '******************************************',
      toAddress: '******************************************'
    });

    const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY
      }
    });

    if (response.ok) {
      const quote = await response.json();
      
      if (quote.estimate && quote.estimate.toAmount) {
        const outputAmount = parseFloat(quote.estimate.toAmount) / 1e18;
        const rate = outputAmount / amount;
        const executionTime = Math.round((quote.estimate.executionDuration || 60000) / 1000);
        
        return {
          success: true,
          fromToken,
          toToken,
          fromAmount: amount,
          toAmount: outputAmount,
          rate,
          tool: quote.tool,
          executionTime,
          description: description || `${fromToken} → ${toToken}`,
          steps: quote.steps?.length || 1
        };
      }
    }
    
    return { success: false, error: `No route (${response.status})` };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testExoticPairs() {
  console.log('🔥 TESTING EXOTIC PAIRS (Premium 1.2% fees)\n');

  const exoticPairs = [
    // Hyperliquid pairs
    { from: 'BNB', to: 'HYPE', fromChain: 56, toChain: 999, desc: 'BNB → HYPE (Confirmed working)' },
    { from: 'ETH', to: 'HYPE', fromChain: 1, toChain: 999, desc: 'ETH → HYPE (High demand)' },
    { from: 'HYPE', to: 'BNB', fromChain: 999, toChain: 56, desc: 'HYPE → BNB (Reverse)' },
    { from: 'HYPE', to: 'ETH', fromChain: 999, toChain: 1, desc: 'HYPE → ETH (Reverse)' },
    
    // SUI Network pairs
    { from: 'SUI', to: 'ETH', fromChain: 101, toChain: 1, desc: 'SUI → ETH (Hot L1)' },
    { from: 'SUI', to: 'BNB', fromChain: 101, toChain: 56, desc: 'SUI → BNB (Cross-chain)' },
    { from: 'ETH', to: 'SUI', fromChain: 1, toChain: 101, desc: 'ETH → SUI (L1 exposure)' },
    { from: 'BNB', to: 'SUI', fromChain: 56, toChain: 101, desc: 'BNB → SUI (BSC to L1)' },
    
    // Ultimate exotic: SUI ↔ HYPE
    { from: 'SUI', to: 'HYPE', fromChain: 101, toChain: 999, desc: 'SUI → HYPE (Ultimate exotic!)' },
    { from: 'HYPE', to: 'SUI', fromChain: 999, toChain: 101, desc: 'HYPE → SUI (Ultimate exotic!)' },
    
    // Other hot L1s
    { from: 'AVAX', to: 'HYPE', fromChain: 43114, toChain: 999, desc: 'AVAX → HYPE (L1 to DEX)' },
    { from: 'FTM', to: 'HYPE', fromChain: 250, toChain: 999, desc: 'FTM → HYPE (Fantom to DEX)' },
    { from: 'MATIC', to: 'HYPE', fromChain: 137, toChain: 999, desc: 'MATIC → HYPE (Polygon to DEX)' }
  ];

  const workingExotic = [];
  
  for (const pair of exoticPairs) {
    process.stdout.write(`   Testing ${pair.desc}... `);
    
    const result = await testTokenPair(
      pair.from, 
      pair.to, 
      pair.fromChain, 
      pair.toChain, 
      1, 
      pair.desc
    );
    
    if (result.success) {
      console.log(`✅ ${result.rate.toFixed(4)} rate (${result.executionTime}s)`);
      workingExotic.push(result);
    } else {
      console.log(`❌ ${result.error}`);
    }
  }

  return workingExotic;
}

async function testCrossChainPairs() {
  console.log('\n🌉 TESTING CROSS-CHAIN PAIRS (Premium 0.8% fees)\n');

  const crossChainPairs = [
    // Major cross-chain routes
    { from: 'ETH', to: 'BNB', fromChain: 1, toChain: 56, desc: 'ETH → BNB (Most popular)' },
    { from: 'BNB', to: 'ETH', fromChain: 56, toChain: 1, desc: 'BNB → ETH (Reverse)' },
    { from: 'ETH', to: 'MATIC', fromChain: 1, toChain: 137, desc: 'ETH → MATIC (L2 scaling)' },
    { from: 'MATIC', to: 'ETH', fromChain: 137, toChain: 1, desc: 'MATIC → ETH (L2 to L1)' },
    { from: 'ETH', to: 'AVAX', fromChain: 1, toChain: 43114, desc: 'ETH → AVAX (L1 to L1)' },
    { from: 'AVAX', to: 'ETH', fromChain: 43114, toChain: 1, desc: 'AVAX → ETH (L1 to L1)' },
    { from: 'BNB', to: 'MATIC', fromChain: 56, toChain: 137, desc: 'BNB → MATIC (BSC to Polygon)' },
    { from: 'MATIC', to: 'BNB', fromChain: 137, toChain: 56, desc: 'MATIC → BNB (Polygon to BSC)' },
    { from: 'ETH', to: 'FTM', fromChain: 1, toChain: 250, desc: 'ETH → FTM (Ethereum to Fantom)' },
    { from: 'FTM', to: 'ETH', fromChain: 250, toChain: 1, desc: 'FTM → ETH (Fantom to Ethereum)' }
  ];

  const workingCrossChain = [];
  
  for (const pair of crossChainPairs) {
    process.stdout.write(`   Testing ${pair.desc}... `);
    
    const result = await testTokenPair(
      pair.from, 
      pair.to, 
      pair.fromChain, 
      pair.toChain, 
      1, 
      pair.desc
    );
    
    if (result.success) {
      console.log(`✅ ${result.rate.toFixed(4)} rate (${result.executionTime}s)`);
      workingCrossChain.push(result);
    } else {
      console.log(`❌ ${result.error}`);
    }
  }

  return workingCrossChain;
}

async function testStablecoinPairs() {
  console.log('\n💰 TESTING STABLECOIN PAIRS (Competitive 0.4% fees)\n');

  // Note: Using USDC contract addresses for stablecoin tests
  const usdcEth = '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505';
  const usdcBsc = '******************************************';
  const usdcPolygon = '******************************************';

  const stablecoinTests = [
    { desc: 'USDC ETH → USDC BSC', fromChain: 1, toChain: 56 },
    { desc: 'USDC BSC → USDC ETH', fromChain: 56, toChain: 1 },
    { desc: 'USDC ETH → USDC Polygon', fromChain: 1, toChain: 137 },
    { desc: 'USDC Polygon → USDC ETH', fromChain: 137, toChain: 1 }
  ];

  const workingStablecoins = [];

  for (const test of stablecoinTests) {
    process.stdout.write(`   Testing ${test.desc}... `);
    
    try {
      const quoteParams = new URLSearchParams({
        fromChain: test.fromChain.toString(),
        toChain: test.toChain.toString(),
        fromToken: test.fromChain === 1 ? usdcEth : test.fromChain === 56 ? usdcBsc : usdcPolygon,
        toToken: test.toChain === 1 ? usdcEth : test.toChain === 56 ? usdcBsc : usdcPolygon,
        fromAmount: '1000000', // 1 USDC (6 decimals)
        fromAddress: '******************************************',
        toAddress: '******************************************'
      });

      const response = await fetch(`https://li.quest/v1/quote?${quoteParams}`, {
        headers: {
          'x-lifi-api-key': process.env.LIFI_API_KEY
        }
      });

      if (response.ok) {
        const quote = await response.json();
        if (quote.estimate) {
          const outputAmount = parseFloat(quote.estimate.toAmount) / 1e6;
          console.log(`✅ 1 USDC → ${outputAmount.toFixed(4)} USDC`);
          workingStablecoins.push({
            description: test.desc,
            rate: outputAmount,
            tool: quote.tool
          });
        } else {
          console.log('❌ No route');
        }
      } else {
        console.log(`❌ Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ Error`);
    }
  }

  return workingStablecoins;
}

async function calculateRevenuePotential(workingPairs) {
  console.log('\n💰 REVENUE POTENTIAL ANALYSIS\n');

  const feeStructure = {
    exotic: 0.012,      // 1.2%
    crossChain: 0.008,  // 0.8%
    stablecoin: 0.004   // 0.4%
  };

  console.log('📊 Fee Structure:');
  console.log(`   🔥 Exotic pairs: ${(feeStructure.exotic * 100).toFixed(1)}%`);
  console.log(`   🌉 Cross-chain: ${(feeStructure.crossChain * 100).toFixed(1)}%`);
  console.log(`   💰 Stablecoins: ${(feeStructure.stablecoin * 100).toFixed(1)}%`);

  console.log('\n💎 Revenue Scenarios (Daily Volume):');
  
  const scenarios = [
    { volume: 50000, desc: '$50K daily' },
    { volume: 100000, desc: '$100K daily' },
    { volume: 500000, desc: '$500K daily' },
    { volume: 1000000, desc: '$1M daily' }
  ];

  scenarios.forEach(scenario => {
    const exoticRevenue = scenario.volume * 0.3 * feeStructure.exotic;      // 30% exotic
    const crossChainRevenue = scenario.volume * 0.5 * feeStructure.crossChain; // 50% cross-chain
    const stablecoinRevenue = scenario.volume * 0.2 * feeStructure.stablecoin; // 20% stablecoin
    const totalRevenue = exoticRevenue + crossChainRevenue + stablecoinRevenue;

    console.log(`\n   ${scenario.desc} volume:`);
    console.log(`     🔥 Exotic (30%): $${exoticRevenue.toFixed(0)} daily`);
    console.log(`     🌉 Cross-chain (50%): $${crossChainRevenue.toFixed(0)} daily`);
    console.log(`     💰 Stablecoin (20%): $${stablecoinRevenue.toFixed(0)} daily`);
    console.log(`     💎 Total daily: $${totalRevenue.toFixed(0)}`);
    console.log(`     📅 Monthly: $${(totalRevenue * 30).toFixed(0)}`);
    console.log(`     📆 Annual: $${(totalRevenue * 365).toFixed(0)}`);
  });
}

async function showSummary(exotic, crossChain, stablecoins) {
  console.log('\n' + '=' .repeat(70));
  console.log('🏆 COMPREHENSIVE PAIR TESTING RESULTS');
  console.log('=' .repeat(70));

  console.log(`\n🔥 EXOTIC PAIRS (1.2% fees): ${exotic.length} working`);
  exotic.forEach(pair => {
    console.log(`   ✅ ${pair.description}: ${pair.rate.toFixed(4)} rate`);
  });

  console.log(`\n🌉 CROSS-CHAIN PAIRS (0.8% fees): ${crossChain.length} working`);
  crossChain.forEach(pair => {
    console.log(`   ✅ ${pair.description}: ${pair.rate.toFixed(4)} rate`);
  });

  console.log(`\n💰 STABLECOIN PAIRS (0.4% fees): ${stablecoins.length} working`);
  stablecoins.forEach(pair => {
    console.log(`   ✅ ${pair.description}: ${pair.rate.toFixed(4)} rate`);
  });

  const totalPairs = exotic.length + crossChain.length + stablecoins.length;
  
  console.log(`\n📊 SUMMARY:`);
  console.log(`   🎯 Total working pairs: ${totalPairs}`);
  console.log(`   🔥 Exotic pairs: ${exotic.length} (highest revenue)`);
  console.log(`   🌉 Cross-chain pairs: ${crossChain.length} (medium revenue)`);
  console.log(`   💰 Stablecoin pairs: ${stablecoins.length} (high volume)`);

  if (totalPairs > 10) {
    console.log('\n🎉 EXCELLENT! Your exchange supports a wide range of profitable pairs!');
  } else if (totalPairs > 5) {
    console.log('\n✅ GOOD! Your exchange has solid pair coverage!');
  } else {
    console.log('\n⚠️ LIMITED: Consider adding more liquidity providers');
  }

  console.log('\n🚀 NEXT STEPS:');
  console.log('   1. Focus marketing on exotic pairs (highest fees)');
  console.log('   2. Promote cross-chain capabilities (unique value)');
  console.log('   3. Offer competitive stablecoin rates (volume driver)');
  console.log('   4. Deploy to production and start earning!');
}

// Run comprehensive testing
async function runAllTests() {
  console.log('🧪 Starting comprehensive pair testing...\n');

  const exotic = await testExoticPairs();
  const crossChain = await testCrossChainPairs();
  const stablecoins = await testStablecoinPairs();

  await calculateRevenuePotential({ exotic, crossChain, stablecoins });
  await showSummary(exotic, crossChain, stablecoins);

  console.log('\n🌐 Your exchange: http://localhost:5001');
  console.log('💰 Ready to start earning from these pairs!');
}

runAllTests().catch(console.error);
