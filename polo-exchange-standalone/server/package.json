{"name": "polo-exchange-lifi-backend", "version": "1.0.0", "description": "Backend server for Polo Exchange LiFi integration", "main": "lifi-backend.js", "scripts": {"start": "node lifi-backend.js", "dev": "nodemon lifi-backend.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.6.7", "dotenv": "^16.3.1", "ethers": "^6.8.0", "@solana/web3.js": "^1.87.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["lifi", "defi", "swap", "backend"], "author": "Polo Exchange", "license": "MIT"}