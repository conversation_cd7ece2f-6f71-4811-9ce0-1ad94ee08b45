#!/usr/bin/env node

/**
 * Trust Wallet + LiFi Setup Script for Polo Exchange
 * Helps configure Trust Wallet addresses and LiFi integration
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🔗 Trust Wallet + LiFi Setup for Polo Exchange');
  console.log('===============================================\n');

  console.log('This script will help you configure Trust Wallet addresses for your exchange.');
  console.log('You\'ll need to have Trust Wallet installed and set up with the blockchains you want to support.\n');

  const proceed = await question('Do you want to continue? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('Setup cancelled.');
    rl.close();
    return;
  }

  console.log('\n📱 Trust Wallet Setup Guide:');
  console.log('1. Open Trust Wallet app on your phone');
  console.log('2. Go to Settings > Wallets');
  console.log('3. Add the blockchains you want to support');
  console.log('4. For each blockchain, copy the receive address');
  console.log('5. Export private keys (keep them secure!)');
  console.log('6. Enter the addresses below\n');

  const addresses = {};
  const privateKeys = {};

  // Core blockchains that Trust Wallet supports
  const supportedChains = [
    { symbol: 'ETH', name: 'Ethereum', required: true },
    { symbol: 'BTC', name: 'Bitcoin', required: true },
    { symbol: 'BNB', name: 'Binance Smart Chain', required: true },
    { symbol: 'SOL', name: 'Solana', required: true },
    { symbol: 'MATIC', name: 'Polygon', required: false },
    { symbol: 'AVAX', name: 'Avalanche', required: false },
    { symbol: 'LTC', name: 'Litecoin', required: false },
    { symbol: 'DOGE', name: 'Dogecoin', required: false },
    { symbol: 'ADA', name: 'Cardano', required: false },
    { symbol: 'DOT', name: 'Polkadot', required: false },
    { symbol: 'ATOM', name: 'Cosmos', required: false },
    { symbol: 'TRX', name: 'Tron', required: false },
    { symbol: 'XRP', name: 'Ripple', required: false },
    { symbol: 'TON', name: 'Toncoin', required: false },
    { symbol: 'SUI', name: 'Sui', required: false },
    { symbol: 'APT', name: 'Aptos', required: false },
    { symbol: 'NEAR', name: 'NEAR Protocol', required: false },
    { symbol: 'FIL', name: 'Filecoin', required: false },
    { symbol: 'VET', name: 'VeChain', required: false },
    { symbol: 'ALGO', name: 'Algorand', required: false },
    { symbol: 'HBAR', name: 'Hedera', required: false },
    { symbol: 'ICP', name: 'Internet Computer', required: false },
    { symbol: 'TAO', name: 'Bittensor', required: false }
  ];

  console.log('💰 Configuring Trust Wallet addresses...\n');

  for (const chain of supportedChains) {
    const required = chain.required ? ' (REQUIRED)' : ' (OPTIONAL)';
    console.log(`\n🔗 ${chain.name} (${chain.symbol})${required}`);
    
    if (!chain.required) {
      const skip = await question(`Do you want to configure ${chain.name}? (y/n): `);
      if (skip.toLowerCase() !== 'y') {
        continue;
      }
    }

    const address = await question(`Enter your Trust Wallet ${chain.name} address: `);
    if (address && address.trim() !== '') {
      addresses[chain.symbol] = address.trim();
      
      const includePrivateKey = await question(`Include private key for ${chain.name}? (y/n): `);
      if (includePrivateKey.toLowerCase() === 'y') {
        const privateKey = await question(`Enter private key for ${chain.name} (will be encrypted): `);
        if (privateKey && privateKey.trim() !== '') {
          privateKeys[chain.symbol] = privateKey.trim();
        }
      }
    }
  }

  // LiFi Configuration
  console.log('\n🚀 LiFi Configuration');
  console.log('Your LiFi API key: e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e');
  
  const customLifiKey = await question('Do you want to use a different LiFi API key? (y/n): ');
  let lifiApiKey = 'e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e';
  
  if (customLifiKey.toLowerCase() === 'y') {
    const newKey = await question('Enter your LiFi API key: ');
    if (newKey && newKey.trim() !== '') {
      lifiApiKey = newKey.trim();
    }
  }

  // Fee Configuration
  console.log('\n💰 Fee Configuration');
  const feeRate = await question('Enter your exchange fee rate (default 0.03 for 3%): ') || '0.03';
  const minAmount = await question('Enter minimum swap amount USD (default 20): ') || '20';
  const maxAmount = await question('Enter maximum swap amount USD (default 49000): ') || '49000';

  // Generate environment file
  console.log('\n📝 Generating configuration files...');

  let envContent = `# Trust Wallet + LiFi Configuration for Polo Exchange
# Generated on ${new Date().toISOString()}

# ===== LIFI CONFIGURATION =====
LIFI_API_KEY=${lifiApiKey}
LIFI_INTEGRATOR=marko-polo-capital

# ===== POLO SWAP CONFIGURATION =====
POLO_SWAP_FEE_RATE=${feeRate}
POLO_SWAP_MIN_AMOUNT=${minAmount}
POLO_SWAP_MAX_AMOUNT=${maxAmount}

# ===== TRUST WALLET ADDRESSES =====
`;

  // Add addresses
  for (const [symbol, address] of Object.entries(addresses)) {
    envContent += `TRUST_WALLET_${symbol}=${address}\n`;
  }

  envContent += '\n# ===== TRUST WALLET PRIVATE KEYS (ENCRYPTED) =====\n';
  
  // Add private keys
  for (const [symbol, privateKey] of Object.entries(privateKeys)) {
    envContent += `TRUST_WALLET_${symbol}_PRIVATE_KEY=${privateKey}\n`;
  }

  envContent += `
# ===== SECURITY SETTINGS =====
MASTER_PASSWORD=change-this-secure-password

# ===== MONITORING SETTINGS =====
DEPOSIT_CONFIRMATION_BLOCKS=3
SWAP_TIMEOUT_MINUTES=30
AUTO_SWAP_ENABLED=true

# ===== NOTIFICATION SETTINGS =====
DISCORD_WEBHOOK_URL=your_discord_webhook_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
`;

  // Write to file
  const envPath = path.join(__dirname, '.env.trust-wallet-generated');
  fs.writeFileSync(envPath, envContent);

  // Generate summary
  console.log('\n✅ Configuration completed!');
  console.log('\n📊 Summary:');
  console.log(`- Configured ${Object.keys(addresses).length} blockchain addresses`);
  console.log(`- LiFi API Key: ${lifiApiKey.substring(0, 20)}...`);
  console.log(`- Exchange Fee: ${parseFloat(feeRate) * 100}%`);
  console.log(`- Swap Limits: $${minAmount} - $${maxAmount}`);

  console.log('\n📁 Files created:');
  console.log(`- ${envPath}`);

  console.log('\n🔧 Next steps:');
  console.log('1. Review the generated .env.trust-wallet-generated file');
  console.log('2. Copy it to your main .env file or load it separately');
  console.log('3. Update your MASTER_PASSWORD for security');
  console.log('4. Test with small amounts first');
  console.log('5. Start the Trust Wallet deposit monitor');
  console.log('6. Monitor your exchange dashboard for deposits and swaps');

  console.log('\n🚀 Trust Wallet + LiFi Integration Benefits:');
  console.log('- Support for 70+ blockchains via Trust Wallet');
  console.log('- Access to $2B+ liquidity via LiFi');
  console.log('- Automatic 3% fee deduction');
  console.log('- Real-time deposit monitoring');
  console.log('- Unlimited swap combinations');
  console.log('- Zero capital risk for your exchange');

  console.log('\n⚠️  Security Reminders:');
  console.log('- Keep private keys secure and encrypted');
  console.log('- Use strong master password');
  console.log('- Monitor deposits regularly');
  console.log('- Test with small amounts first');
  console.log('- Set up proper backup procedures');

  rl.close();
}

main().catch(console.error);
