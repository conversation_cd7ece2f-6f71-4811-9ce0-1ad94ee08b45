name = "polo-exchange"
compatibility_date = "2024-06-19"
pages_build_output_dir = "dist/public"

[env.production]
name = "polo-exchange-production"

[env.staging]
name = "polo-exchange-staging"

# Environment variables for production
[env.production.vars]
NODE_ENV = "production"
VITE_APP_NAME = "Marko Polo Capital"
VITE_API_URL = "https://api.polo-exchange.com"

# Environment variables for staging
[env.staging.vars]
NODE_ENV = "staging"
VITE_APP_NAME = "Marko Polo Capital (Staging)"
VITE_API_URL = "https://staging-api.polo-exchange.com"
