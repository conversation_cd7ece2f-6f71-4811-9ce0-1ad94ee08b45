#!/usr/bin/env node

/**
 * Quick Test Script for Polo Exchange
 * Tests all critical components quickly
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🧪 Quick Test - Polo Exchange\n');

// Test 1: Environment Variables
console.log('1️⃣ Testing Environment Configuration...');
const requiredEnvVars = [
  'LIFI_API_KEY',
  'ETHEREUM_PRIVATE_KEY',
  'ETHEREUM_ADDRESS',
  'SOLANA_PRIVATE_KEY',
  'SOLANA_ADDRESS'
];

let envTestPassed = true;
requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar];
  if (value && value.length > 0) {
    console.log(`   ✅ ${envVar}: Configured`);
  } else {
    console.log(`   ❌ ${envVar}: Missing`);
    envTestPassed = false;
  }
});

if (envTestPassed) {
  console.log('   🎉 Environment configuration: PASSED\n');
} else {
  console.log('   ❌ Environment configuration: FAILED\n');
}

// Test 2: Wallet Addresses
console.log('2️⃣ Testing Wallet Addresses...');
const wallets = [
  { name: 'Ethereum', addr: process.env.ETHEREUM_ADDRESS },
  { name: 'BSC', addr: process.env.BSC_ADDRESS },
  { name: 'Polygon', addr: process.env.POLYGON_ADDRESS },
  { name: 'Avalanche', addr: process.env.AVALANCHE_ADDRESS },
  { name: 'Arbitrum', addr: process.env.ARBITRUM_ADDRESS },
  { name: 'Optimism', addr: process.env.OPTIMISM_ADDRESS },
  { name: 'Solana', addr: process.env.SOLANA_ADDRESS }
];

wallets.forEach(wallet => {
  if (wallet.addr && wallet.addr.length > 0) {
    console.log(`   ✅ ${wallet.name}: ${wallet.addr}`);
  } else {
    console.log(`   ❌ ${wallet.name}: Not configured`);
  }
});

console.log('   🎉 Wallet addresses: CONFIGURED\n');

// Test 3: LiFi API
console.log('3️⃣ Testing LiFi API...');
async function testLiFiAPI() {
  try {
    const apiKey = process.env.LIFI_API_KEY;
    const response = await fetch('https://li.quest/v1/chains', {
      headers: {
        'x-lifi-api-key': apiKey,
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ LiFi API: Connected (${data.chains?.length || 0} chains available)`);
      return true;
    } else {
      console.log(`   ❌ LiFi API: Failed (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ LiFi API: Error - ${error.message}`);
    return false;
  }
}

// Test 4: Backend Server
console.log('4️⃣ Testing Backend Server...');
async function testBackendServer() {
  try {
    const response = await fetch('http://localhost:3001/api/health');
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Backend Server: Running (${data.status})`);
      return true;
    } else {
      console.log(`   ❌ Backend Server: Not responding (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Backend Server: Not running - Start with 'npm run lifi-backend'`);
    return false;
  }
}

// Test 5: Sample Quote
console.log('5️⃣ Testing Sample Quote...');
async function testSampleQuote() {
  try {
    const params = new URLSearchParams({
      fromChain: '1',
      toChain: '56',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000',
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.BSC_ADDRESS
    });

    const response = await fetch(`http://localhost:3001/api/lifi/quote?${params}`);
    if (response.ok) {
      const data = await response.json();
      if (data.estimate) {
        console.log(`   ✅ Sample Quote: Working (ETH → BNB available)`);
        return true;
      } else {
        console.log(`   ⚠️ Sample Quote: API responded but no estimate`);
        return false;
      }
    } else {
      console.log(`   ❌ Sample Quote: Failed (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Sample Quote: Error - ${error.message}`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running all tests...\n');

  const lifiTest = await testLiFiAPI();
  console.log();

  const backendTest = await testBackendServer();
  console.log();

  const quoteTest = await testSampleQuote();
  console.log();

  // Summary
  console.log('📊 Test Summary:');
  console.log('=' .repeat(40));
  console.log(`Environment Config: ${envTestPassed ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`LiFi API: ${lifiTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Backend Server: ${backendTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Sample Quote: ${quoteTest ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = envTestPassed && lifiTest && backendTest && quoteTest;

  if (allPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Your exchange is ready to go!');
    console.log('\n💡 Next Steps:');
    console.log('   1. Fund your wallets with operational crypto');
    console.log('   2. Start your frontend: npm run dev');
    console.log('   3. Test a small swap ($10-20)');
    console.log('   4. Monitor wallet balances');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the issues above.');
    console.log('\n🔧 Troubleshooting:');
    if (!envTestPassed) console.log('   - Check your .env file configuration');
    if (!lifiTest) console.log('   - Verify your LiFi API key');
    if (!backendTest) console.log('   - Start backend: npm run lifi-backend');
    if (!quoteTest) console.log('   - Check backend logs for errors');
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
