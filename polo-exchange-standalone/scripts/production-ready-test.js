#!/usr/bin/env node

/**
 * Production Ready Test Suite
 * Tests only confirmed working pairs for production deployment
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🚀 PRODUCTION READY TEST SUITE');
console.log('Testing confirmed working pairs for production deployment...\n');

const BACKEND_URL = 'http://localhost:3001';

// CONFIRMED WORKING PAIRS (from stress test results)
const PRODUCTION_PAIRS = [
  {
    name: 'ETH → USDC',
    fromChain: '1',
    toChain: '1',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'same-chain',
    priority: 'high'
  },
  {
    name: 'ETH → BNB',
    fromChain: '1',
    toChain: '56',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'cross-chain',
    priority: 'high'
  },
  {
    name: 'BNB → BUSD',
    fromChain: '56',
    toChain: '56',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'same-chain',
    priority: 'high'
  },
  {
    name: 'MATIC → AVAX',
    fromChain: '137',
    toChain: '43114',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'cross-chain',
    priority: 'medium'
  },
  {
    name: 'UNI → AAVE',
    fromChain: '1',
    toChain: '1',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'exotic',
    priority: 'low'
  },
  {
    name: 'LINK → CRV',
    fromChain: '1',
    toChain: '1',
    fromToken: '******************************************',
    toToken: '******************************************',
    category: 'exotic',
    priority: 'low'
  }
];

// Production test amounts (realistic user amounts)
const PRODUCTION_AMOUNTS = [
  { name: 'Small ($10)', amount: '10000000000000000' }, // 0.01 ETH
  { name: 'Medium ($100)', amount: '100000000000000000' }, // 0.1 ETH
  { name: 'Large ($1000)', amount: '1000000000000000000' } // 1 ETH
];

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  critical: 0,
  warnings: []
};

// Test 1: Production Pair Validation
async function testProductionPairs() {
  console.log('💎 Testing Production-Ready Pairs...');
  
  let criticalFailures = 0;
  let totalTests = 0;
  
  for (const pair of PRODUCTION_PAIRS) {
    console.log(`\n   Testing ${pair.name} (${pair.category})...`);
    
    for (const amount of PRODUCTION_AMOUNTS) {
      totalTests++;
      testResults.total++;
      
      try {
        const params = new URLSearchParams({
          fromChain: pair.fromChain,
          toChain: pair.toChain,
          fromToken: pair.fromToken,
          toToken: pair.toToken,
          fromAmount: amount.amount,
          fromAddress: process.env.ETHEREUM_ADDRESS,
          toAddress: process.env.ETHEREUM_ADDRESS
        });
        
        const response = await fetch(`${BACKEND_URL}/api/lifi/quote?${params}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.estimate || data.toAmount) {
            console.log(`      ✅ ${amount.name}: Quote available`);
            testResults.passed++;
          } else {
            console.log(`      ⚠️ ${amount.name}: No estimate returned`);
            testResults.failed++;
            if (pair.priority === 'high') {
              criticalFailures++;
              testResults.critical++;
            }
          }
        } else {
          console.log(`      ❌ ${amount.name}: Failed (${response.status})`);
          testResults.failed++;
          if (pair.priority === 'high') {
            criticalFailures++;
            testResults.critical++;
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.log(`      ❌ ${amount.name}: Error - ${error.message}`);
        testResults.failed++;
        if (pair.priority === 'high') {
          criticalFailures++;
          testResults.critical++;
        }
      }
    }
  }
  
  const successRate = Math.round((testResults.passed / testResults.total) * 100);
  console.log(`\n   📊 Production Pairs Success Rate: ${testResults.passed}/${testResults.total} (${successRate}%)`);
  
  if (criticalFailures === 0) {
    console.log('   🎉 All critical pairs working!');
    return true;
  } else {
    console.log(`   🚨 ${criticalFailures} critical failures detected!`);
    return false;
  }
}

// Test 2: Fee Calculation Test
async function testFeeCalculation() {
  console.log('\n💰 Testing Fee Calculation (3% fee)...');
  
  try {
    const params = new URLSearchParams({
      fromChain: '1',
      toChain: '56',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '1000000000000000000', // 1 ETH
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.BSC_ADDRESS
    });
    
    const response = await fetch(`${BACKEND_URL}/api/lifi/quote?${params}`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.estimate) {
        const inputAmount = 1; // 1 ETH
        const outputAmount = parseFloat(data.estimate.toAmount) / 1e18;
        const expectedAfterFee = outputAmount * 0.97; // 3% fee
        
        console.log(`   📊 Input: ${inputAmount} ETH`);
        console.log(`   📊 LiFi Output: ${outputAmount.toFixed(4)} BNB`);
        console.log(`   📊 After 3% fee: ${expectedAfterFee.toFixed(4)} BNB`);
        console.log(`   ✅ Fee calculation: Working`);
        return true;
      } else {
        console.log('   ❌ Fee calculation: No estimate available');
        return false;
      }
    } else {
      console.log(`   ❌ Fee calculation: API failed (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Fee calculation: Error - ${error.message}`);
    return false;
  }
}

// Test 3: Load Test (Simulated Users)
async function testSimulatedLoad() {
  console.log('\n⚡ Testing Simulated User Load...');
  
  const userRequests = [];
  const numUsers = 10;
  
  // Simulate 10 users making requests simultaneously
  for (let i = 0; i < numUsers; i++) {
    const randomPair = PRODUCTION_PAIRS[Math.floor(Math.random() * PRODUCTION_PAIRS.length)];
    const randomAmount = PRODUCTION_AMOUNTS[Math.floor(Math.random() * PRODUCTION_AMOUNTS.length)];
    
    userRequests.push(
      fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: randomPair.fromChain,
        toChain: randomPair.toChain,
        fromToken: randomPair.fromToken,
        toToken: randomPair.toToken,
        fromAmount: randomAmount.amount,
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      })).then(response => ({
        success: response.ok,
        status: response.status,
        user: i + 1
      })).catch(error => ({
        success: false,
        error: error.message,
        user: i + 1
      }))
    );
  }
  
  try {
    const startTime = Date.now();
    const results = await Promise.all(userRequests);
    const endTime = Date.now();
    
    const successCount = results.filter(r => r.success).length;
    const totalTime = endTime - startTime;
    
    console.log(`   📊 Simulated Users: ${numUsers}`);
    console.log(`   📊 Successful Requests: ${successCount}/${numUsers}`);
    console.log(`   📊 Total Time: ${totalTime}ms`);
    console.log(`   📊 Average Time per User: ${Math.round(totalTime / numUsers)}ms`);
    
    if (successCount >= numUsers * 0.9) {
      console.log('   ✅ Load test: PASSED');
      return true;
    } else {
      console.log('   ❌ Load test: FAILED');
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Load test error: ${error.message}`);
    return false;
  }
}

// Test 4: End-to-End Swap Simulation
async function testSwapSimulation() {
  console.log('\n🔄 Testing End-to-End Swap Simulation...');
  
  try {
    // Step 1: Get quote
    console.log('   Step 1: Getting quote...');
    const quoteParams = new URLSearchParams({
      fromChain: '1',
      toChain: '56',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '100000000000000000', // 0.1 ETH
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.BSC_ADDRESS
    });
    
    const quoteResponse = await fetch(`${BACKEND_URL}/api/lifi/quote?${quoteParams}`);
    
    if (!quoteResponse.ok) {
      console.log('   ❌ Step 1 failed: Quote request failed');
      return false;
    }
    
    const quote = await quoteResponse.json();
    if (!quote.estimate) {
      console.log('   ❌ Step 1 failed: No estimate in quote');
      return false;
    }
    
    console.log('   ✅ Step 1: Quote received');
    
    // Step 2: Simulate user deposit (we can't actually send crypto in test)
    console.log('   Step 2: Simulating user deposit...');
    console.log(`   💰 User would send 0.1 ETH to: ${process.env.ETHEREUM_ADDRESS}`);
    console.log('   ✅ Step 2: Deposit simulation complete');
    
    // Step 3: Simulate swap execution
    console.log('   Step 3: Simulating swap execution...');
    const executionData = {
      route: quote,
      adjustedAmount: 0.097, // After 3% fee
      fromCurrency: 'ETH',
      toCurrency: 'BNB',
      userReceiveAddress: process.env.BSC_ADDRESS,
      originalAmount: 0.1
    };
    
    // Note: We don't actually call the execution endpoint to avoid real transactions
    console.log('   💱 LiFi would execute swap with your wallets');
    console.log('   ✅ Step 3: Execution simulation complete');
    
    // Step 4: Simulate sending tokens to user
    console.log('   Step 4: Simulating token delivery...');
    const expectedOutput = parseFloat(quote.estimate.toAmount) / 1e18 * 0.97;
    console.log(`   📤 User would receive ~${expectedOutput.toFixed(4)} BNB`);
    console.log('   ✅ Step 4: Delivery simulation complete');
    
    console.log('   🎉 End-to-end simulation: SUCCESS');
    return true;
    
  } catch (error) {
    console.log(`   ❌ Swap simulation error: ${error.message}`);
    return false;
  }
}

// Test 5: Production Readiness Checklist
async function testProductionReadiness() {
  console.log('\n📋 Production Readiness Checklist...');
  
  const checks = [
    {
      name: 'Backend Health',
      test: async () => {
        const response = await fetch(`${BACKEND_URL}/api/health`);
        return response.ok;
      }
    },
    {
      name: 'LiFi API Key',
      test: async () => {
        return !!process.env.LIFI_API_KEY && process.env.LIFI_API_KEY.length > 20;
      }
    },
    {
      name: 'Wallet Configuration',
      test: async () => {
        return !!(process.env.ETHEREUM_PRIVATE_KEY && process.env.BSC_PRIVATE_KEY);
      }
    },
    {
      name: 'High Priority Pairs',
      test: async () => {
        // Test ETH → BNB (most important pair)
        const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
          fromChain: '1',
          toChain: '56',
          fromToken: '******************************************',
          toToken: '******************************************',
          fromAmount: '100000000000000000',
          fromAddress: process.env.ETHEREUM_ADDRESS,
          toAddress: process.env.BSC_ADDRESS
        }));
        return response.ok;
      }
    }
  ];
  
  let passedChecks = 0;
  
  for (const check of checks) {
    try {
      const result = await check.test();
      if (result) {
        console.log(`   ✅ ${check.name}: PASS`);
        passedChecks++;
      } else {
        console.log(`   ❌ ${check.name}: FAIL`);
      }
    } catch (error) {
      console.log(`   ❌ ${check.name}: ERROR - ${error.message}`);
    }
  }
  
  const readinessScore = Math.round((passedChecks / checks.length) * 100);
  console.log(`\n   📊 Production Readiness: ${passedChecks}/${checks.length} (${readinessScore}%)`);
  
  return readinessScore >= 100;
}

// Main test runner
async function runProductionTest() {
  console.log('🚀 Starting Production Ready Test Suite...\n');
  
  const tests = [
    { name: 'Production Pairs', fn: testProductionPairs },
    { name: 'Fee Calculation', fn: testFeeCalculation },
    { name: 'Simulated Load', fn: testSimulatedLoad },
    { name: 'Swap Simulation', fn: testSwapSimulation },
    { name: 'Production Readiness', fn: testProductionReadiness }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`❌ ${test.name} crashed: ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Final Assessment
  console.log('\n' + '='.repeat(60));
  console.log('🏁 PRODUCTION READY TEST COMPLETE');
  console.log('='.repeat(60));
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
  });
  
  const passedTests = results.filter(r => r.passed).length;
  const passRate = Math.round((passedTests / results.length) * 100);
  
  console.log(`\n📊 Overall Score: ${passedTests}/${results.length} (${passRate}%)`);
  console.log(`🚨 Critical Issues: ${testResults.critical}`);
  
  if (passRate >= 80 && testResults.critical === 0) {
    console.log('\n🎉 PRODUCTION READY! Your exchange can be deployed!');
    console.log('\n🚀 Deployment Checklist:');
    console.log('   ✅ Fund your operational wallets');
    console.log('   ✅ Start with small test transactions');
    console.log('   ✅ Monitor all swaps closely');
    console.log('   ✅ Set up automated alerts');
  } else {
    console.log('\n⚠️ NOT READY FOR PRODUCTION');
    console.log('🔧 Fix critical issues before deploying');
  }
}

// Run the production test
runProductionTest().catch(error => {
  console.error('💥 Production test crashed:', error);
  process.exit(1);
});
