#!/usr/bin/env node

/**
 * Deployment Wallet Setup Script
 * Creates a new wallet for smart contract deployment
 */

const { ethers } = require("ethers");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🔐 Polo Exchange Deployment Wallet Setup");
  console.log("========================================\n");

  console.log("This script will create a new wallet specifically for deploying your smart contracts.");
  console.log("⚠️  NEVER use your main Trust Wallet for deployment!\n");

  // Generate new wallet
  console.log("🎲 Generating new deployment wallet...");
  const wallet = ethers.Wallet.createRandom();
  
  console.log("✅ New deployment wallet created!\n");
  
  console.log("📋 WALLET DETAILS:");
  console.log("==================");
  console.log(`Address: ${wallet.address}`);
  console.log(`Private Key: ${wallet.privateKey}`);
  console.log(`Mnemonic: ${wallet.mnemonic.phrase}\n`);
  
  console.log("🔒 SECURITY INSTRUCTIONS:");
  console.log("=========================");
  console.log("1. Save the private key and mnemonic in a secure location");
  console.log("2. NEVER share these with anyone");
  console.log("3. This wallet is ONLY for deploying contracts");
  console.log("4. Fund it with just enough ETH/BNB/MATIC for gas fees");
  console.log("5. Don't store large amounts in this wallet\n");

  // Create .env file
  const envContent = `# Polo Exchange Deployment Configuration
# Generated on ${new Date().toISOString()}

# ===== DEPLOYMENT WALLET =====
DEPLOYER_PRIVATE_KEY=${wallet.privateKey}
DEPLOYER_ADDRESS=${wallet.address}

# ===== LIFI CONFIGURATION =====
LIFI_API_KEY=e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e
LIFI_INTEGRATOR=marko-polo-capital

# ===== FEE COLLECTOR =====
# Where 3% fees will be sent (use your main wallet address)
FEE_COLLECTOR_ADDRESS=${wallet.address}

# ===== RPC ENDPOINTS =====
# Add your RPC URLs here
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/
POLYGON_RPC_URL=https://polygon-rpc.com/
AVALANCHE_RPC_URL=https://api.avax.network/ext/bc/C/rpc
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
OPTIMISM_RPC_URL=https://mainnet.optimism.io

# ===== BLOCK EXPLORER API KEYS =====
# Get these from respective block explorers for contract verification
ETHERSCAN_API_KEY=
BSCSCAN_API_KEY=
POLYGONSCAN_API_KEY=
SNOWTRACE_API_KEY=
ARBISCAN_API_KEY=
OPTIMISTIC_ETHERSCAN_API_KEY=

# ===== SETTINGS =====
REPORT_GAS=true
VERIFY_CONTRACTS=true
`;

  const envPath = path.join(__dirname, "../.env");
  fs.writeFileSync(envPath, envContent);
  
  console.log(`💾 Environment file created: ${envPath}`);
  console.log("📝 Please update the RPC URLs and API keys in the .env file\n");

  // Funding instructions
  console.log("💰 FUNDING INSTRUCTIONS:");
  console.log("========================");
  console.log("You need to fund this wallet with native tokens for gas fees:\n");
  
  const networks = [
    { name: "Ethereum", symbol: "ETH", amount: "0.1", explorer: "https://etherscan.io" },
    { name: "BSC", symbol: "BNB", amount: "0.05", explorer: "https://bscscan.com" },
    { name: "Polygon", symbol: "MATIC", amount: "50", explorer: "https://polygonscan.com" },
    { name: "Avalanche", symbol: "AVAX", amount: "2", explorer: "https://snowtrace.io" },
    { name: "Arbitrum", symbol: "ETH", amount: "0.01", explorer: "https://arbiscan.io" },
    { name: "Optimism", symbol: "ETH", amount: "0.01", explorer: "https://optimistic.etherscan.io" }
  ];

  networks.forEach(network => {
    console.log(`${network.name}: Send ~${network.amount} ${network.symbol} to ${wallet.address}`);
  });

  console.log("\n🧪 TESTNET TESTING (RECOMMENDED):");
  console.log("=================================");
  console.log("Before mainnet deployment, test on testnets:");
  console.log("1. Get testnet tokens from faucets");
  console.log("2. Deploy to Sepolia (Ethereum testnet)");
  console.log("3. Deploy to BSC Testnet");
  console.log("4. Deploy to Polygon Mumbai");
  console.log("5. Test all functionality");
  console.log("6. Only then deploy to mainnet\n");

  console.log("🚀 DEPLOYMENT COMMANDS:");
  console.log("=======================");
  console.log("# Test compilation");
  console.log("npx hardhat compile\n");
  
  console.log("# Deploy to testnet first");
  console.log("npx hardhat run scripts/deploy.js --network sepolia");
  console.log("npx hardhat run scripts/deploy.js --network bscTestnet");
  console.log("npx hardhat run scripts/deploy.js --network polygonMumbai\n");
  
  console.log("# Deploy to mainnet (after testing)");
  console.log("npx hardhat run scripts/deploy.js --network ethereum");
  console.log("npx hardhat run scripts/deploy.js --network bsc");
  console.log("npx hardhat run scripts/deploy.js --network polygon");
  console.log("npx hardhat run scripts/deploy.js --network avalanche");
  console.log("npx hardhat run scripts/deploy.js --network arbitrum");
  console.log("npx hardhat run scripts/deploy.js --network optimism\n");

  console.log("🔍 CONTRACT VERIFICATION:");
  console.log("=========================");
  console.log("After deployment, verify contracts:");
  console.log("npx hardhat verify --network <network> <contract_address> <lifi_diamond> <fee_collector>\n");

  console.log("✅ Setup complete! Next steps:");
  console.log("1. Update .env file with RPC URLs and API keys");
  console.log("2. Fund the deployment wallet");
  console.log("3. Test on testnets first");
  console.log("4. Deploy to mainnet");
  console.log("5. Verify contracts");
  console.log("6. Update your frontend with contract addresses\n");

  console.log("🎉 Your automated Polo Exchange smart contracts are ready to deploy!");
}

main().catch(console.error);
