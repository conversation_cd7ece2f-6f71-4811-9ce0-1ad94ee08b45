#!/usr/bin/env node

/**
 * Test TAO → BTC Routing Analysis
 * Analyze the exact routes Li<PERSON><PERSON> uses for Bittensor to Bitcoin swaps
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🔄 TAO → BTC ROUTING ANALYSIS');
console.log('Analyzing swap routes for Bittensor to Bitcoin...\n');

const BACKEND_URL = 'http://localhost:3001';

async function analyzeTAOtoBTCRoutes() {
  console.log('🛣️ Analyzing TAO → BTC Swap Routes...\n');
  
  const testAmounts = [
    { name: 'Small Trade', tao: '1', usd: 344 },
    { name: 'Medium Trade', tao: '10', usd: 3440 },
    { name: 'Large Trade', tao: '50', usd: 17200 }
  ];
  
  for (const test of testAmounts) {
    console.log(`🧪 Testing ${test.name}: ${test.tao} TAO → BTC`);
    console.log(`   USD Value: ~$${test.usd.toLocaleString()}`);
    
    try {
      // TAO is ERC-20 on Ethereum, BTC is wrapped on various chains
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1', // Ethereum (where TAO exists)
        toChain: '1', // Ethereum (for WBTC)
        fromToken: '******************************************', // TAO contract
        toToken: '******************************************', // WBTC contract
        fromAmount: (parseFloat(test.tao) * 1e9).toString(), // TAO has 9 decimals
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }));
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          const outputBTC = parseFloat(data.estimate.toAmount) / 1e8; // BTC has 8 decimals
          const feeRevenue = test.usd * 0.03; // 3% fee
          
          console.log(`   ✅ Route Found:`);
          console.log(`      Input: ${test.tao} TAO`);
          console.log(`      Output: ${outputBTC.toFixed(8)} WBTC`);
          console.log(`      Your Fee: $${feeRevenue.toLocaleString()}`);
          console.log(`      Tool Used: ${data.tool || 'Unknown'}`);
          console.log(`      Gas Estimate: ${data.estimate.gasCosts?.[0]?.estimate || 'Unknown'}`);
          console.log(`      Execution Time: ${data.estimate.executionDuration || 'Unknown'}ms`);
          
          // Analyze the route steps
          if (data.estimate.steps && data.estimate.steps.length > 0) {
            console.log(`      Route Steps:`);
            data.estimate.steps.forEach((step, index) => {
              console.log(`         ${index + 1}. ${step.tool} on ${getChainName(step.action.fromChainId)}`);
              console.log(`            ${step.action.fromToken.symbol} → ${step.action.toToken.symbol}`);
              if (step.estimate) {
                console.log(`            Amount: ${(parseFloat(step.estimate.fromAmount) / Math.pow(10, step.action.fromToken.decimals)).toFixed(6)} → ${(parseFloat(step.estimate.toAmount) / Math.pow(10, step.action.toToken.decimals)).toFixed(6)}`);
              }
            });
          }
          
          // Analyze liquidity sources
          if (data.estimate.toolDetails) {
            console.log(`      Liquidity Source: ${data.estimate.toolDetails.name || data.tool}`);
            console.log(`      DEX/Protocol: ${data.estimate.toolDetails.logoURI ? 'Verified' : 'Standard'}`);
          }
          
        } else {
          console.log(`   ❌ No route available: ${data.message || 'Unknown reason'}`);
        }
      } else {
        const errorData = await response.json();
        console.log(`   ❌ API Error: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   ❌ Network Error: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
  }
}

async function testAlternativeRoutes() {
  console.log('🔀 Testing Alternative TAO → BTC Routes...\n');
  
  const routeTests = [
    {
      name: 'TAO → WBTC (Ethereum)',
      fromChain: '1',
      toChain: '1',
      fromToken: '******************************************', // TAO
      toToken: '******************************************', // WBTC
      description: 'Direct Ethereum swap'
    },
    {
      name: 'TAO → BTCB (BSC)',
      fromChain: '1',
      toChain: '56',
      fromToken: '******************************************', // TAO on Ethereum
      toToken: '******************************************', // BTCB on BSC
      description: 'Cross-chain to BSC'
    },
    {
      name: 'TAO → WBTC (Arbitrum)',
      fromChain: '1',
      toChain: '42161',
      fromToken: '******************************************', // TAO on Ethereum
      toToken: '******************************************', // WBTC on Arbitrum
      description: 'Cross-chain to Arbitrum'
    },
    {
      name: 'TAO → WBTC (Polygon)',
      fromChain: '1',
      toChain: '137',
      fromToken: '******************************************', // TAO on Ethereum
      toToken: '******************************************', // WBTC on Polygon
      description: 'Cross-chain to Polygon'
    }
  ];
  
  let workingRoutes = 0;
  
  for (const route of routeTests) {
    console.log(`🛣️ Testing ${route.name}`);
    console.log(`   ${route.description}`);
    
    try {
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: route.fromChain,
        toChain: route.toChain,
        fromToken: route.fromToken,
        toToken: route.toToken,
        fromAmount: (10 * 1e9).toString(), // 10 TAO test
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }));
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          const outputBTC = parseFloat(data.estimate.toAmount) / 1e8;
          console.log(`   ✅ Route Available:`);
          console.log(`      Tool: ${data.tool}`);
          console.log(`      Output: ${outputBTC.toFixed(8)} BTC`);
          console.log(`      Gas Cost: ${data.estimate.gasCosts?.[0]?.estimate || 'Unknown'}`);
          console.log(`      Steps: ${data.estimate.steps?.length || 1}`);
          workingRoutes++;
        } else {
          console.log(`   ❌ No liquidity available`);
        }
      } else {
        console.log(`   ❌ API error`);
      }
    } catch (error) {
      console.log(`   ❌ Network error`);
    }
    
    console.log('');
    await new Promise(resolve => setTimeout(resolve, 1500));
  }
  
  return workingRoutes;
}

async function analyzeRoutingStrategy() {
  console.log('📊 TAO → BTC Routing Strategy Analysis...\n');
  
  console.log('🔍 Understanding the Route:');
  console.log('   1. TAO Token Details:');
  console.log('      - Network: Ethereum (ERC-20)');
  console.log('      - Contract: ******************************************');
  console.log('      - Decimals: 9');
  console.log('      - Type: Wrapped Bittensor token');
  
  console.log('\n   2. BTC Token Options:');
  console.log('      - WBTC (Ethereum): ******************************************');
  console.log('      - BTCB (BSC): ******************************************');
  console.log('      - WBTC (Arbitrum): ******************************************');
  console.log('      - WBTC (Polygon): ******************************************');
  
  console.log('\n   3. Likely Routing Paths:');
  console.log('      🛣️ Direct Route (Same Chain):');
  console.log('         TAO (Ethereum) → WBTC (Ethereum)');
  console.log('         - Uses: Uniswap, SushiSwap, or 1inch');
  console.log('         - Steps: 1 (direct swap)');
  console.log('         - Gas: ~$20-50');
  console.log('         - Speed: Fastest');
  
  console.log('\n      🌉 Cross-Chain Routes:');
  console.log('         TAO (Ethereum) → Bridge → WBTC (Other Chain)');
  console.log('         - Uses: Stargate, Hop, or native bridges');
  console.log('         - Steps: 2-3 (swap + bridge + swap)');
  console.log('         - Gas: ~$50-100');
  console.log('         - Speed: 5-30 minutes');
  
  console.log('\n      💡 Optimal Strategy:');
  console.log('         - Small trades (<$10k): Direct Ethereum route');
  console.log('         - Large trades (>$10k): Best liquidity route');
  console.log('         - Cross-chain: Only if better rates');
  
  console.log('\n   4. Liquidity Sources:');
  console.log('      - Primary: Uniswap V3 (deepest TAO liquidity)');
  console.log('      - Secondary: SushiSwap, 1inch aggregation');
  console.log('      - Cross-chain: Stargate, Hop Protocol');
  console.log('      - Backup: KyberSwap, Balancer');
}

function getChainName(chainId) {
  const chains = {
    '1': 'Ethereum',
    '56': 'BSC',
    '137': 'Polygon',
    '42161': 'Arbitrum',
    '10': 'Optimism',
    '43114': 'Avalanche'
  };
  return chains[chainId.toString()] || `Chain ${chainId}`;
}

async function runTAOtoBTCAnalysis() {
  console.log('🚀 Starting TAO → BTC Route Analysis...\n');
  
  // Analyze routing strategy first
  await analyzeRoutingStrategy();
  
  console.log('\n' + '='.repeat(60));
  console.log('🧪 LIVE ROUTE TESTING');
  console.log('='.repeat(60));
  
  // Test actual routes
  await analyzeTAOtoBTCRoutes();
  
  // Test alternative routes
  const workingRoutes = await testAlternativeRoutes();
  
  console.log('='.repeat(60));
  console.log('📊 TAO → BTC ROUTING SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`\n✅ Working Routes: ${workingRoutes}/4 tested`);
  
  console.log('\n🎯 Recommended Route Priority:');
  console.log('   1. 🥇 TAO → WBTC (Ethereum) - Direct, fastest');
  console.log('   2. 🥈 TAO → BTCB (BSC) - Lower fees');
  console.log('   3. 🥉 TAO → WBTC (Arbitrum) - L2 benefits');
  console.log('   4. 🏅 TAO → WBTC (Polygon) - Backup option');
  
  console.log('\n💰 Revenue Implications:');
  console.log('   - All routes charge 3% fee');
  console.log('   - TAO high value = high fee revenue');
  console.log('   - BTC high value = premium trades');
  console.log('   - AI → Digital Gold narrative');
  
  console.log('\n🚀 Marketing Angle:');
  console.log('   "Trade AI for Digital Gold"');
  console.log('   "TAO → BTC: Future Tech to Store of Value"');
  console.log('   "No KYC AI-to-Bitcoin swaps"');
  
  if (workingRoutes >= 2) {
    console.log('\n🎉 EXCELLENT: Multiple TAO → BTC routes available!');
    console.log('Your exchange can handle AI-to-Bitcoin swaps efficiently!');
  } else if (workingRoutes >= 1) {
    console.log('\n👍 GOOD: TAO → BTC swaps are working!');
  } else {
    console.log('\n⚠️ LIMITED: TAO → BTC routes may need optimization');
  }
}

// Run the analysis
runTAOtoBTCAnalysis().catch(error => {
  console.error('💥 TAO → BTC analysis crashed:', error);
  process.exit(1);
});
