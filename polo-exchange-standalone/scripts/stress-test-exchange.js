#!/usr/bin/env node

/**
 * Comprehensive Stress Test for Polo Exchange
 * Tests all scenarios, edge cases, and failure modes
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🔥 POLO EXCHANGE STRESS TEST');
console.log('Testing all scenarios and edge cases...\n');

// Test configuration
const TEST_CONFIG = {
  backendUrl: 'http://localhost:3001',
  maxRetries: 3,
  timeoutMs: 15000,
  concurrentRequests: 5
};

// Major token pairs to test
const TOKEN_PAIRS = [
  // Same chain swaps
  { from: 'ETH', to: 'USDC', fromChain: '1', toChain: '1', type: 'same-chain' },
  { from: 'BNB', to: 'BUSD', fromChain: '56', toChain: '56', type: 'same-chain' },
  
  // Cross-chain swaps
  { from: 'ETH', to: 'BNB', fromChain: '1', to<PERSON>hain: '56', type: 'cross-chain' },
  { from: 'USDC', to: 'USDT', from<PERSON>hain: '1', to<PERSON>hain: '56', type: 'cross-chain' },
  { from: 'MATIC', to: 'AVAX', fromChain: '137', toChain: '43114', type: 'cross-chain' },
  { from: 'ETH', to: 'SOL', fromChain: '1', toChain: '101', type: 'cross-chain' },
  
  // Exotic pairs
  { from: 'CRV', to: 'UNI', fromChain: '1', toChain: '1', type: 'exotic' },
  { from: 'LINK', to: 'AAVE', fromChain: '1', toChain: '1', type: 'exotic' },
  { from: 'HYPE', to: 'SOL', fromChain: '101', toChain: '101', type: 'exotic' },
];

// Test amounts (in smallest units)
const TEST_AMOUNTS = [
  '1000000000000000', // 0.001 ETH (very small)
  '1000000000000000000', // 1 ETH (normal)
  '10000000000000000000', // 10 ETH (large)
  '100000000000000000000', // 100 ETH (very large)
];

// Token address mappings
const TOKEN_ADDRESSES = {
  'ETH': '******************************************',
  'USDC': '******************************************',
  'USDT': '******************************************',
  'BNB': '******************************************',
  'BUSD': '******************************************',
  'MATIC': '******************************************',
  'AVAX': '******************************************',
  'SOL': '******************************************',
  'CRV': '******************************************',
  'UNI': '******************************************',
  'LINK': '******************************************',
  'AAVE': '******************************************',
  'HYPE': '******************************************'
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.timeoutMs);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Test 1: Backend Health and Connectivity
async function testBackendHealth() {
  console.log('🏥 Testing Backend Health...');
  
  try {
    const response = await makeRequest(`${TEST_CONFIG.backendUrl}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Backend health: ${data.status}`);
      return true;
    } else {
      console.log(`   ❌ Backend health failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Backend health error: ${error.message}`);
    return false;
  }
}

// Test 2: LiFi API Connectivity
async function testLiFiConnectivity() {
  console.log('🌉 Testing LiFi API Connectivity...');
  
  try {
    const response = await makeRequest('https://li.quest/v1/chains', {
      headers: {
        'x-lifi-api-key': process.env.LIFI_API_KEY,
        'Accept': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ LiFi API: ${data.chains?.length || 0} chains available`);
      return true;
    } else {
      console.log(`   ❌ LiFi API failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ LiFi API error: ${error.message}`);
    return false;
  }
}

// Test 3: Quote Generation for All Pairs
async function testQuoteGeneration() {
  console.log('💱 Testing Quote Generation for All Pairs...');
  
  let successCount = 0;
  let totalTests = 0;
  
  for (const pair of TOKEN_PAIRS) {
    for (const amount of TEST_AMOUNTS.slice(0, 2)) { // Test with 2 amounts to save time
      totalTests++;
      
      try {
        const params = new URLSearchParams({
          fromChain: pair.fromChain,
          toChain: pair.toChain,
          fromToken: TOKEN_ADDRESSES[pair.from],
          toToken: TOKEN_ADDRESSES[pair.to],
          fromAmount: amount,
          fromAddress: process.env.ETHEREUM_ADDRESS,
          toAddress: process.env.ETHEREUM_ADDRESS
        });
        
        const response = await makeRequest(`${TEST_CONFIG.backendUrl}/api/lifi/quote?${params}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.estimate || data.toAmount) {
            console.log(`   ✅ ${pair.from} → ${pair.to} (${pair.type}): Quote available`);
            successCount++;
          } else {
            console.log(`   ⚠️ ${pair.from} → ${pair.to}: No estimate returned`);
          }
        } else {
          console.log(`   ❌ ${pair.from} → ${pair.to}: Quote failed (${response.status})`);
        }
        
        // Rate limiting
        await sleep(200);
        
      } catch (error) {
        console.log(`   ❌ ${pair.from} → ${pair.to}: Error - ${error.message}`);
        testResults.errors.push(`Quote ${pair.from}→${pair.to}: ${error.message}`);
      }
    }
  }
  
  console.log(`   📊 Quote Success Rate: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
  return successCount > totalTests * 0.7; // 70% success rate threshold
}

// Test 4: Concurrent Request Handling
async function testConcurrentRequests() {
  console.log('⚡ Testing Concurrent Request Handling...');
  
  const concurrentPromises = [];
  const testPair = TOKEN_PAIRS[0]; // Use ETH → USDC for concurrent test
  
  for (let i = 0; i < TEST_CONFIG.concurrentRequests; i++) {
    const params = new URLSearchParams({
      fromChain: testPair.fromChain,
      toChain: testPair.toChain,
      fromToken: TOKEN_ADDRESSES[testPair.from],
      toToken: TOKEN_ADDRESSES[testPair.to],
      fromAmount: TEST_AMOUNTS[1],
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.ETHEREUM_ADDRESS
    });
    
    concurrentPromises.push(
      makeRequest(`${TEST_CONFIG.backendUrl}/api/lifi/quote?${params}`)
        .then(response => ({ success: response.ok, status: response.status }))
        .catch(error => ({ success: false, error: error.message }))
    );
  }
  
  try {
    const results = await Promise.all(concurrentPromises);
    const successCount = results.filter(r => r.success).length;
    
    console.log(`   📊 Concurrent Requests: ${successCount}/${TEST_CONFIG.concurrentRequests} succeeded`);
    
    if (successCount >= TEST_CONFIG.concurrentRequests * 0.8) {
      console.log(`   ✅ Concurrent handling: PASSED`);
      return true;
    } else {
      console.log(`   ❌ Concurrent handling: FAILED`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Concurrent test error: ${error.message}`);
    return false;
  }
}

// Test 5: Error Handling and Edge Cases
async function testErrorHandling() {
  console.log('🚨 Testing Error Handling and Edge Cases...');
  
  const errorTests = [
    {
      name: 'Invalid chain ID',
      params: { fromChain: '999999', toChain: '1', fromToken: TOKEN_ADDRESSES.ETH, toToken: TOKEN_ADDRESSES.USDC, fromAmount: TEST_AMOUNTS[1], fromAddress: process.env.ETHEREUM_ADDRESS, toAddress: process.env.ETHEREUM_ADDRESS }
    },
    {
      name: 'Invalid token address',
      params: { fromChain: '1', toChain: '1', fromToken: '0xinvalid', toToken: TOKEN_ADDRESSES.USDC, fromAmount: TEST_AMOUNTS[1], fromAddress: process.env.ETHEREUM_ADDRESS, toAddress: process.env.ETHEREUM_ADDRESS }
    },
    {
      name: 'Zero amount',
      params: { fromChain: '1', toChain: '1', fromToken: TOKEN_ADDRESSES.ETH, toToken: TOKEN_ADDRESSES.USDC, fromAmount: '0', fromAddress: process.env.ETHEREUM_ADDRESS, toAddress: process.env.ETHEREUM_ADDRESS }
    },
    {
      name: 'Missing parameters',
      params: { fromChain: '1', toChain: '1' }
    }
  ];
  
  let errorHandlingPassed = 0;
  
  for (const test of errorTests) {
    try {
      const params = new URLSearchParams(test.params);
      const response = await makeRequest(`${TEST_CONFIG.backendUrl}/api/lifi/quote?${params}`);
      
      // We expect these to fail gracefully (not crash the server)
      if (response.status >= 400 && response.status < 500) {
        console.log(`   ✅ ${test.name}: Handled gracefully (${response.status})`);
        errorHandlingPassed++;
      } else if (response.ok) {
        console.log(`   ⚠️ ${test.name}: Unexpectedly succeeded`);
      } else {
        console.log(`   ❌ ${test.name}: Server error (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.name}: Exception - ${error.message}`);
    }
    
    await sleep(100);
  }
  
  console.log(`   📊 Error Handling: ${errorHandlingPassed}/${errorTests.length} tests passed`);
  return errorHandlingPassed >= errorTests.length * 0.75;
}

// Test 6: Performance and Response Times
async function testPerformance() {
  console.log('⚡ Testing Performance and Response Times...');
  
  const performanceTests = [];
  const testPair = TOKEN_PAIRS[0];
  
  for (let i = 0; i < 10; i++) {
    const startTime = Date.now();
    
    try {
      const params = new URLSearchParams({
        fromChain: testPair.fromChain,
        toChain: testPair.toChain,
        fromToken: TOKEN_ADDRESSES[testPair.from],
        toToken: TOKEN_ADDRESSES[testPair.to],
        fromAmount: TEST_AMOUNTS[1],
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      });
      
      const response = await makeRequest(`${TEST_CONFIG.backendUrl}/api/lifi/quote?${params}`);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      performanceTests.push({
        success: response.ok,
        responseTime
      });
      
    } catch (error) {
      const endTime = Date.now();
      performanceTests.push({
        success: false,
        responseTime: endTime - startTime,
        error: error.message
      });
    }
    
    await sleep(100);
  }
  
  const successfulTests = performanceTests.filter(t => t.success);
  const avgResponseTime = successfulTests.reduce((sum, t) => sum + t.responseTime, 0) / successfulTests.length;
  const maxResponseTime = Math.max(...successfulTests.map(t => t.responseTime));
  
  console.log(`   📊 Average Response Time: ${Math.round(avgResponseTime)}ms`);
  console.log(`   📊 Max Response Time: ${maxResponseTime}ms`);
  console.log(`   📊 Success Rate: ${successfulTests.length}/10`);
  
  const performancePassed = avgResponseTime < 5000 && successfulTests.length >= 8;
  console.log(`   ${performancePassed ? '✅' : '❌'} Performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);
  
  return performancePassed;
}

// Main stress test runner
async function runStressTest() {
  console.log('🚀 Starting Comprehensive Stress Test...\n');
  
  const tests = [
    { name: 'Backend Health', fn: testBackendHealth },
    { name: 'LiFi Connectivity', fn: testLiFiConnectivity },
    { name: 'Quote Generation', fn: testQuoteGeneration },
    { name: 'Concurrent Requests', fn: testConcurrentRequests },
    { name: 'Error Handling', fn: testErrorHandling },
    { name: 'Performance', fn: testPerformance }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      testResults.total++;
      if (result) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      console.log(`❌ ${test.name} crashed: ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
      testResults.total++;
      testResults.failed++;
      testResults.errors.push(`${test.name}: ${error.message}`);
    }
    
    await sleep(1000); // Brief pause between tests
  }
  
  // Final Summary
  console.log(`\n${'='.repeat(60)}`);
  console.log('🏁 STRESS TEST COMPLETE');
  console.log(`${'='.repeat(60)}`);
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) console.log(`    Error: ${result.error}`);
  });
  
  const passRate = Math.round((testResults.passed / testResults.total) * 100);
  console.log(`\n📊 Overall Results: ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`);
  
  if (passRate >= 80) {
    console.log('\n🎉 STRESS TEST PASSED! Your exchange is production-ready!');
    console.log('\n💡 Recommendations:');
    console.log('   ✅ Exchange is stable and reliable');
    console.log('   ✅ Ready for real user traffic');
    console.log('   ✅ Error handling is robust');
    console.log('   ✅ Performance is acceptable');
  } else {
    console.log('\n⚠️ STRESS TEST FAILED! Issues need to be addressed.');
    console.log('\n🔧 Critical Issues:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('   1. Fund your wallets with operational crypto');
  console.log('   2. Test with real small amounts ($1-5)');
  console.log('   3. Monitor logs during operation');
  console.log('   4. Set up automated monitoring');
}

// Run the stress test
runStressTest().catch(error => {
  console.error('💥 Stress test crashed:', error);
  process.exit(1);
});
