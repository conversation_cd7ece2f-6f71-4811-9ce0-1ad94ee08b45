#!/usr/bin/env node

/**
 * Check Browser Limit Display
 * Verify what limit the browser is actually showing
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🔍 CHECKING BROWSER LIMIT DISPLAY');
console.log('Verifying what limit is actually shown in the browser...\n');

async function checkBrowserContent() {
  console.log('📱 Fetching current frontend content...');
  
  try {
    // Fetch the current page content
    const response = await fetch('http://localhost:3000/exchange');
    if (!response.ok) {
      console.log('❌ Frontend not responding');
      return false;
    }
    
    const html = await response.text();
    
    // Look for limit-related content in the HTML
    console.log('\n🔍 Searching for limit references in HTML...');
    
    const searches = [
      { term: '510000', name: 'Raw 510000' },
      { term: '510,000', name: 'Formatted 510,000' },
      { term: '$510', name: '$510 reference' },
      { term: '50000', name: 'Old 50000 (bad)' },
      { term: '50,000', name: 'Old 50,000 (bad)' },
      { term: '$50', name: '$50 reference (bad)' },
      { term: 'Maximum Per Transaction', name: 'Transaction limit text' },
      { term: 'transactionLimitUSD', name: 'Variable name' }
    ];
    
    searches.forEach(search => {
      const found = html.includes(search.term);
      const status = search.name.includes('(bad)') ? 
        (found ? '⚠️ FOUND (should not be there)' : '✅ NOT FOUND (good)') :
        (found ? '✅ FOUND' : '❌ NOT FOUND');
      console.log(`   ${status} ${search.name}: ${search.term}`);
    });
    
    // Check if the JavaScript bundle contains the right values
    console.log('\n🔍 Checking for JavaScript references...');
    
    // Look for useState patterns
    const useStatePattern = /useState<number>\((\d+)\)/g;
    const matches = [...html.matchAll(useStatePattern)];
    
    if (matches.length > 0) {
      console.log('   📄 Found useState number values:');
      matches.forEach(match => {
        const value = parseInt(match[1]);
        if (value === 510000) {
          console.log(`   ✅ CORRECT: useState(${value}) - New limit found!`);
        } else if (value === 50000) {
          console.log(`   ⚠️ OLD LIMIT: useState(${value}) - Old limit still present`);
        } else if (value > 1000) {
          console.log(`   📊 OTHER: useState(${value}) - Other numeric value`);
        }
      });
    } else {
      console.log('   📄 No useState patterns found in HTML (normal for bundled JS)');
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Error checking browser content: ${error.message}`);
    return false;
  }
}

async function testLimitInBrowser() {
  console.log('\n🧪 Testing Limit Behavior...');
  
  console.log('\n📋 MANUAL VERIFICATION STEPS:');
  console.log('1. Open browser developer console (F12)');
  console.log('2. Go to: http://localhost:3000/exchange');
  console.log('3. Look for this debug message:');
  console.log('   "🎯 TRANSACTION LIMIT LOADED: { transactionLimitUSD: 510000 }"');
  console.log('4. Select ETH → BNB');
  console.log('5. Enter 100 ETH');
  console.log('6. Check if USD value shows without error');
  console.log('7. Enter 200 ETH');
  console.log('8. Check if error mentions $510,000 limit');
  console.log('9. Click "Execute Swap" on valid amount');
  console.log('10. Verify payment dialog shows "Maximum Per Transaction: $510,000 USD"');
  
  console.log('\n✅ SUCCESS INDICATORS:');
  console.log('   • Console shows: transactionLimitUSD: 510000');
  console.log('   • Error messages mention $510,000');
  console.log('   • Payment dialog shows $510,000 maximum');
  console.log('   • Large amounts (100-150 ETH) accepted');
  
  console.log('\n❌ FAILURE INDICATORS:');
  console.log('   • Console shows: transactionLimitUSD: 50000');
  console.log('   • Error messages mention $50,000');
  console.log('   • Payment dialog shows $50,000 maximum');
  console.log('   • Large amounts rejected at old limit');
}

async function provideTroubleshootingSteps() {
  console.log('\n🔧 TROUBLESHOOTING STEPS:');
  console.log('='.repeat(40));
  
  console.log('\n1. 🔄 HARD REFRESH:');
  console.log('   • Windows/Linux: Ctrl + F5 or Ctrl + Shift + R');
  console.log('   • Mac: Cmd + Shift + R');
  console.log('   • This clears browser cache');
  
  console.log('\n2. 🧹 CLEAR BROWSER CACHE:');
  console.log('   • Chrome: Settings → Privacy → Clear browsing data');
  console.log('   • Firefox: Settings → Privacy → Clear Data');
  console.log('   • Safari: Develop → Empty Caches');
  
  console.log('\n3. 🔍 CHECK DEVELOPER CONSOLE:');
  console.log('   • Press F12 to open developer tools');
  console.log('   • Go to Console tab');
  console.log('   • Look for "🎯 TRANSACTION LIMIT LOADED" message');
  console.log('   • Should show transactionLimitUSD: 510000');
  
  console.log('\n4. 🌐 TRY INCOGNITO/PRIVATE MODE:');
  console.log('   • Opens fresh browser session');
  console.log('   • No cached data');
  console.log('   • Should show new limits immediately');
  
  console.log('\n5. 🔄 RESTART FRONTEND:');
  console.log('   • Kill current frontend process');
  console.log('   • Run: npm run dev');
  console.log('   • Wait for "ready" message');
  console.log('   • Open fresh browser tab');
  
  console.log('\n6. 📱 TRY DIFFERENT BROWSER:');
  console.log('   • Chrome, Firefox, Safari, Edge');
  console.log('   • Each has separate cache');
  console.log('   • Should show new limits');
}

async function runBrowserLimitCheck() {
  console.log('🚀 Starting Browser Limit Check...\n');
  
  const contentChecked = await checkBrowserContent();
  await testLimitInBrowser();
  await provideTroubleshootingSteps();
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 BROWSER LIMIT CHECK RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n✅ Frontend Content: ${contentChecked ? 'ACCESSIBLE' : 'ISSUES'}`);
  
  console.log('\n🎯 EXPECTED BEHAVIOR:');
  console.log('   💰 Transaction Limit: $510,000 USD');
  console.log('   🔢 Max ETH per trade: 150 ETH');
  console.log('   📈 Revenue per max trade: $15,300');
  console.log('   🚀 10.2x improvement over old limit');
  
  console.log('\n🔗 QUICK TEST:');
  console.log('   1. Open: http://localhost:3000/exchange?v=510k');
  console.log('   2. Press F12 for developer console');
  console.log('   3. Look for: "🎯 TRANSACTION LIMIT LOADED"');
  console.log('   4. Verify: transactionLimitUSD: 510000');
  
  if (contentChecked) {
    console.log('\n🎉 Frontend is running - check browser console for debug info!');
  } else {
    console.log('\n⚠️ Frontend issues detected - restart may be needed');
  }
  
  console.log('\n💡 If still showing $50k:');
  console.log('   • Hard refresh browser (Ctrl+F5)');
  console.log('   • Clear browser cache completely');
  console.log('   • Try incognito/private mode');
  console.log('   • Check developer console for debug messages');
}

// Run the browser check
runBrowserLimitCheck().catch(error => {
  console.error('💥 Browser limit check crashed:', error);
  process.exit(1);
});
