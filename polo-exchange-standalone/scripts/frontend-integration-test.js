#!/usr/bin/env node

/**
 * Frontend Integration Test
 * Tests the complete frontend-backend integration flow
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import { JSDOM } from 'jsdom';

dotenv.config();

console.log('🖥️ FRONTEND INTEGRATION TEST');
console.log('Testing complete frontend-backend integration...\n');

const BACKEND_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:3000';

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: []
};

// Test 1: Frontend Accessibility
async function testFrontendAccessibility() {
  console.log('🌐 Testing Frontend Accessibility...');
  
  try {
    const response = await fetch(FRONTEND_URL, {
      timeout: 10000
    });
    
    if (response.ok) {
      const html = await response.text();
      
      // Check for key elements
      const checks = [
        { name: 'HTML Structure', test: html.includes('<html') },
        { name: 'Title Tag', test: html.includes('<title>') },
        { name: 'Polo Exchange Branding', test: html.includes('Polo') || html.includes('polo') },
        { name: 'Swap Interface', test: html.includes('swap') || html.includes('Swap') },
        { name: 'CSS Styles', test: html.includes('<style') || html.includes('.css') },
        { name: 'JavaScript', test: html.includes('<script') || html.includes('.js') }
      ];
      
      let passedChecks = 0;
      checks.forEach(check => {
        if (check.test) {
          console.log(`   ✅ ${check.name}: Found`);
          passedChecks++;
        } else {
          console.log(`   ❌ ${check.name}: Missing`);
        }
      });
      
      const frontendScore = passedChecks / checks.length;
      console.log(`   📊 Frontend Elements: ${passedChecks}/${checks.length} (${Math.round(frontendScore * 100)}%)`);
      
      return frontendScore >= 0.8;
    } else {
      console.log(`   ❌ Frontend not accessible: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Frontend connection failed: ${error.message}`);
    console.log('   💡 Make sure frontend is running: npm run dev');
    return false;
  }
}

// Test 2: API Endpoint Integration
async function testAPIEndpointIntegration() {
  console.log('\n🔌 Testing API Endpoint Integration...');
  
  const endpoints = [
    { name: 'Health Check', path: '/api/health', method: 'GET' },
    { name: 'LiFi Quote', path: '/api/lifi/quote?fromChain=1&toChain=56&fromToken=******************************************&toToken=******************************************&fromAmount=1000000000000000000&fromAddress=' + process.env.ETHEREUM_ADDRESS + '&toAddress=' + process.env.BSC_ADDRESS, method: 'GET' },
    { name: 'CORS Headers', path: '/api/health', method: 'OPTIONS' }
  ];
  
  let passedEndpoints = 0;
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BACKEND_URL}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Origin': FRONTEND_URL,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });
      
      if (response.ok || response.status === 204) {
        console.log(`   ✅ ${endpoint.name}: Working (${response.status})`);
        
        // Check CORS headers for cross-origin requests
        if (endpoint.name === 'CORS Headers') {
          const corsHeader = response.headers.get('access-control-allow-origin');
          if (corsHeader) {
            console.log(`      ✅ CORS enabled: ${corsHeader}`);
          } else {
            console.log(`      ⚠️ CORS headers missing`);
            testResults.warnings.push('CORS headers not properly configured');
          }
        }
        
        passedEndpoints++;
      } else {
        console.log(`   ❌ ${endpoint.name}: Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ ${endpoint.name}: Error - ${error.message}`);
    }
  }
  
  const endpointScore = passedEndpoints / endpoints.length;
  console.log(`   📊 API Endpoints: ${passedEndpoints}/${endpoints.length} (${Math.round(endpointScore * 100)}%)`);
  
  return endpointScore >= 0.8;
}

// Test 3: Swap Flow Simulation
async function testSwapFlowSimulation() {
  console.log('\n🔄 Testing Complete Swap Flow Simulation...');
  
  try {
    // Step 1: Simulate user selecting tokens
    console.log('   Step 1: User selects ETH → BNB...');
    const swapConfig = {
      fromToken: 'ETH',
      toToken: 'BNB',
      fromChain: '1',
      toChain: '56',
      amount: '0.1'
    };
    console.log(`   ✅ Swap configuration: ${swapConfig.amount} ${swapConfig.fromToken} → ${swapConfig.toToken}`);
    
    // Step 2: Get quote from backend
    console.log('   Step 2: Getting quote from backend...');
    const quoteResponse = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
      fromChain: swapConfig.fromChain,
      toChain: swapConfig.toChain,
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: '100000000000000000', // 0.1 ETH
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.BSC_ADDRESS
    }));
    
    if (!quoteResponse.ok) {
      console.log(`   ❌ Quote request failed: ${quoteResponse.status}`);
      return false;
    }
    
    const quote = await quoteResponse.json();
    if (!quote.estimate) {
      console.log('   ❌ No quote estimate received');
      return false;
    }
    
    const outputAmount = parseFloat(quote.estimate.toAmount) / 1e18;
    const feeAmount = outputAmount * 0.03; // 3% fee
    const userReceives = outputAmount - feeAmount;
    
    console.log(`   ✅ Quote received:`);
    console.log(`      Input: 0.1 ETH`);
    console.log(`      LiFi Output: ${outputAmount.toFixed(4)} BNB`);
    console.log(`      Exchange Fee (3%): ${feeAmount.toFixed(4)} BNB`);
    console.log(`      User Receives: ${userReceives.toFixed(4)} BNB`);
    
    // Step 3: Generate deposit address
    console.log('   Step 3: Generating deposit address...');
    const depositAddress = process.env.ETHEREUM_ADDRESS;
    console.log(`   ✅ Deposit address: ${depositAddress}`);
    
    // Step 4: Simulate user deposit detection
    console.log('   Step 4: Simulating deposit detection...');
    console.log('   💰 User would send 0.1 ETH to deposit address');
    console.log('   🔍 System would detect incoming transaction');
    console.log('   ✅ Deposit simulation complete');
    
    // Step 5: Simulate swap execution
    console.log('   Step 5: Simulating swap execution...');
    console.log('   🔄 LiFi would execute cross-chain swap');
    console.log('   💱 Using exchange operational wallets');
    console.log('   ✅ Execution simulation complete');
    
    // Step 6: Simulate token delivery
    console.log('   Step 6: Simulating token delivery...');
    console.log(`   📤 User would receive ${userReceives.toFixed(4)} BNB`);
    console.log('   💰 Exchange keeps 3% fee');
    console.log('   ✅ Delivery simulation complete');
    
    console.log('   🎉 Complete swap flow: SUCCESS');
    return true;
    
  } catch (error) {
    console.log(`   ❌ Swap flow simulation failed: ${error.message}`);
    return false;
  }
}

// Test 4: Error Handling in Frontend Flow
async function testFrontendErrorHandling() {
  console.log('\n🚨 Testing Frontend Error Handling...');
  
  const errorScenarios = [
    {
      name: 'Invalid Token Pair',
      params: {
        fromChain: '1',
        toChain: '999999',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }
    },
    {
      name: 'Zero Amount',
      params: {
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '0',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      }
    },
    {
      name: 'Missing Parameters',
      params: {
        fromChain: '1',
        toChain: '56'
      }
    }
  ];
  
  let errorHandlingPassed = 0;
  
  for (const scenario of errorScenarios) {
    try {
      const params = new URLSearchParams(scenario.params);
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?${params}`);
      
      if (response.status >= 400 && response.status < 500) {
        console.log(`   ✅ ${scenario.name}: Properly rejected (${response.status})`);
        errorHandlingPassed++;
      } else if (response.ok) {
        const data = await response.json();
        if (data.error || data.message) {
          console.log(`   ✅ ${scenario.name}: Error message provided`);
          errorHandlingPassed++;
        } else {
          console.log(`   ⚠️ ${scenario.name}: No error handling`);
        }
      } else {
        console.log(`   ❌ ${scenario.name}: Unexpected response (${response.status})`);
      }
    } catch (error) {
      console.log(`   ✅ ${scenario.name}: Network error handled`);
      errorHandlingPassed++;
    }
  }
  
  const errorScore = errorHandlingPassed / errorScenarios.length;
  console.log(`   📊 Error Scenarios: ${errorHandlingPassed}/${errorScenarios.length} (${Math.round(errorScore * 100)}%)`);
  
  return errorScore >= 0.8;
}

// Test 5: Performance Under Load
async function testFrontendPerformance() {
  console.log('\n⚡ Testing Frontend Performance Under Load...');
  
  const performanceTests = [];
  const testCount = 20;
  
  console.log(`   Running ${testCount} concurrent quote requests...`);
  
  for (let i = 0; i < testCount; i++) {
    const startTime = Date.now();
    
    performanceTests.push(
      fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '100000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      })).then(response => ({
        success: response.ok,
        responseTime: Date.now() - startTime,
        status: response.status
      })).catch(error => ({
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message
      }))
    );
  }
  
  try {
    const results = await Promise.all(performanceTests);
    const successCount = results.filter(r => r.success).length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
    const maxResponseTime = Math.max(...results.map(r => r.responseTime));
    
    console.log(`   📊 Successful Requests: ${successCount}/${testCount}`);
    console.log(`   📊 Average Response Time: ${Math.round(avgResponseTime)}ms`);
    console.log(`   📊 Max Response Time: ${maxResponseTime}ms`);
    
    const performancePassed = successCount >= testCount * 0.9 && avgResponseTime < 30000;
    console.log(`   ${performancePassed ? '✅' : '❌'} Performance test: ${performancePassed ? 'PASSED' : 'FAILED'}`);
    
    return performancePassed;
  } catch (error) {
    console.log(`   ❌ Performance test failed: ${error.message}`);
    return false;
  }
}

// Main frontend integration test runner
async function runFrontendIntegrationTest() {
  console.log('🚀 Starting Frontend Integration Test...\n');
  
  const tests = [
    { name: 'Frontend Accessibility', fn: testFrontendAccessibility },
    { name: 'API Endpoint Integration', fn: testAPIEndpointIntegration },
    { name: 'Swap Flow Simulation', fn: testSwapFlowSimulation },
    { name: 'Frontend Error Handling', fn: testFrontendErrorHandling },
    { name: 'Frontend Performance', fn: testFrontendPerformance }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(60)}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      testResults.total++;
      if (result) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      console.log(`❌ ${test.name} crashed: ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
      testResults.total++;
      testResults.failed++;
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Final Assessment
  console.log(`\n${'='.repeat(80)}`);
  console.log('🏁 FRONTEND INTEGRATION TEST COMPLETE');
  console.log(`${'='.repeat(80)}`);
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) console.log(`    Error: ${result.error}`);
  });
  
  const passRate = Math.round((testResults.passed / testResults.total) * 100);
  console.log(`\n📊 Frontend Integration: ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`);
  
  if (testResults.warnings.length > 0) {
    console.log('\n⚠️ Warnings:');
    testResults.warnings.forEach(warning => console.log(`   - ${warning}`));
  }
  
  if (passRate >= 80) {
    console.log('\n🎉 FRONTEND INTEGRATION PASSED!');
    console.log('Your frontend and backend are properly integrated!');
    console.log('\n✅ Ready for user testing:');
    console.log('   1. Frontend loads correctly');
    console.log('   2. API endpoints working');
    console.log('   3. Swap flow functional');
    console.log('   4. Error handling in place');
    console.log('   5. Performance acceptable');
  } else {
    console.log('\n⚠️ FRONTEND INTEGRATION ISSUES DETECTED');
    console.log('Fix integration issues before user testing.');
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('   1. Start both frontend and backend');
  console.log('   2. Test manually in browser');
  console.log('   3. Try real swap with small amount');
  console.log('   4. Monitor all transactions');
}

// Run the frontend integration test
runFrontendIntegrationTest().catch(error => {
  console.error('💥 Frontend integration test crashed:', error);
  process.exit(1);
});
