const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

// Token addresses for each network
const TOKEN_ADDRESSES = {
  ethereum: {
    USDC: "0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505",
    USDT: "******************************************",
    DAI: "******************************************",
    WETH: "******************************************",
    WBTC: "******************************************",
    UNI: "******************************************",
    LINK: "******************************************",
    AAVE: "******************************************",
    CRV: "******************************************",
  },
  bsc: {
    USDC: "******************************************",
    USDT: "******************************************",
    BUSD: "******************************************",
    WBNB: "******************************************",
    BTCB: "******************************************",
  },
  polygon: {
    USDC: "******************************************",
    USDT: "******************************************",
    DAI: "******************************************",
    WMATIC: "******************************************",
    WETH: "******************************************",
  },
  avalanche: {
    USDC: "******************************************",
    USDT: "******************************************",
    DAI: "******************************************",
    WAVAX: "******************************************",
    WETH: "******************************************",
  }
};

// LiFi Diamond addresses
const LIFI_DIAMONDS = {
  ethereum: "******************************************",
  sepolia: "******************************************",
  bsc: "******************************************",
  bscTestnet: "******************************************",
  polygon: "******************************************",
  polygonMumbai: "******************************************",
  avalanche: "******************************************",
  arbitrum: "******************************************",
  optimism: "******************************************"
};

async function main() {
  console.log("🚀 Starting Polo Exchange Smart Contract Deployment...\n");

  // Get network info
  const network = hre.network.name;
  const [deployer] = await ethers.getSigners();
  
  console.log(`📍 Network: ${network}`);
  console.log(`👤 Deployer: ${deployer.address}`);
  
  // Check balance
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log(`💰 Balance: ${ethers.formatEther(balance)} ETH`);
  
  if (balance === 0n) {
    throw new Error("❌ Insufficient balance for deployment");
  }

  // Get LiFi Diamond address for this network
  const lifiDiamond = LIFI_DIAMONDS[network];
  if (!lifiDiamond) {
    throw new Error(`❌ LiFi Diamond address not configured for network: ${network}`);
  }

  // Fee collector (use deployer address if not specified)
  const feeCollector = process.env.FEE_COLLECTOR_ADDRESS || deployer.address;
  
  console.log(`🔗 LiFi Diamond: ${lifiDiamond}`);
  console.log(`💰 Fee Collector: ${feeCollector}\n`);

  // Deploy PoloSwapContract
  console.log("📄 Deploying PoloSwapContract...");
  
  const PoloSwapContract = await ethers.getContractFactory("PoloSwapContract");
  const contract = await PoloSwapContract.deploy(lifiDiamond, feeCollector);
  
  await contract.waitForDeployment();
  const contractAddress = await contract.getAddress();
  
  console.log(`✅ PoloSwapContract deployed to: ${contractAddress}`);
  console.log(`📋 Transaction hash: ${contract.deploymentTransaction()?.hash}\n`);

  // Add supported tokens
  console.log("🪙 Adding supported tokens...");
  const tokens = TOKEN_ADDRESSES[network] || {};
  
  for (const [symbol, address] of Object.entries(tokens)) {
    try {
      console.log(`   Adding ${symbol}: ${address}`);
      const tx = await contract.addSupportedToken(address);
      await tx.wait();
      console.log(`   ✅ ${symbol} added successfully`);
    } catch (error) {
      console.log(`   ❌ Failed to add ${symbol}: ${error.message}`);
    }
  }

  // Add native token (ETH/BNB/MATIC/AVAX)
  try {
    console.log(`   Adding native token (zero address)`);
    const tx = await contract.addSupportedToken(ethers.ZeroAddress);
    await tx.wait();
    console.log(`   ✅ Native token added successfully`);
  } catch (error) {
    console.log(`   ❌ Failed to add native token: ${error.message}`);
  }

  console.log(`\n🎉 Deployment completed successfully!`);
  
  // Save deployment info
  const deploymentInfo = {
    network: network,
    contractAddress: contractAddress,
    lifiDiamond: lifiDiamond,
    feeCollector: feeCollector,
    deployer: deployer.address,
    deploymentTx: contract.deploymentTransaction()?.hash,
    supportedTokens: tokens,
    timestamp: new Date().toISOString(),
    blockNumber: await ethers.provider.getBlockNumber()
  };

  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir);
  }

  // Save deployment info
  const deploymentFile = path.join(deploymentsDir, `${network}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  
  console.log(`💾 Deployment info saved to: ${deploymentFile}`);

  // Update frontend config
  updateFrontendConfig(network, deploymentInfo);

  // Contract verification instructions
  console.log(`\n🔍 To verify the contract, run:`);
  console.log(`npx hardhat verify --network ${network} ${contractAddress} "${lifiDiamond}" "${feeCollector}"`);

  // Usage instructions
  console.log(`\n📖 Next steps:`);
  console.log(`1. Verify the contract on the block explorer`);
  console.log(`2. Update your frontend with the contract address`);
  console.log(`3. Test the contract with small amounts`);
  console.log(`4. Set up monitoring for swap events`);
  console.log(`5. Configure your backend to execute swaps`);

  console.log(`\n🔗 Contract deployed successfully on ${network}!`);
  console.log(`📍 Address: ${contractAddress}`);
  console.log(`🌐 Block Explorer: ${getBlockExplorerUrl(network, contractAddress)}`);
}

function updateFrontendConfig(network, deploymentInfo) {
  try {
    const configPath = path.join(__dirname, "../src/config/contracts.json");
    let config = {};
    
    // Load existing config if it exists
    if (fs.existsSync(configPath)) {
      config = JSON.parse(fs.readFileSync(configPath, "utf8"));
    }

    // Update config
    if (!config.contracts) config.contracts = {};
    if (!config.networks) config.networks = {};

    config.contracts[network] = {
      address: deploymentInfo.contractAddress,
      chainId: getChainId(network),
      supportedTokens: deploymentInfo.supportedTokens
    };

    config.networks[getChainId(network)] = {
      name: network,
      contractAddress: deploymentInfo.contractAddress,
      lifiDiamond: deploymentInfo.lifiDiamond
    };

    // Ensure directory exists
    const configDir = path.dirname(configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log(`🔧 Frontend config updated: ${configPath}`);
  } catch (error) {
    console.log(`⚠️  Failed to update frontend config: ${error.message}`);
  }
}

function getChainId(network) {
  const chainIds = {
    ethereum: 1,
    sepolia: 11155111,
    bsc: 56,
    bscTestnet: 97,
    polygon: 137,
    polygonMumbai: 80001,
    avalanche: 43114,
    arbitrum: 42161,
    optimism: 10
  };
  return chainIds[network] || 1;
}

function getBlockExplorerUrl(network, address) {
  const explorers = {
    ethereum: `https://etherscan.io/address/${address}`,
    sepolia: `https://sepolia.etherscan.io/address/${address}`,
    bsc: `https://bscscan.com/address/${address}`,
    bscTestnet: `https://testnet.bscscan.com/address/${address}`,
    polygon: `https://polygonscan.com/address/${address}`,
    polygonMumbai: `https://mumbai.polygonscan.com/address/${address}`,
    avalanche: `https://snowtrace.io/address/${address}`,
    arbitrum: `https://arbiscan.io/address/${address}`,
    optimism: `https://optimistic.etherscan.io/address/${address}`
  };
  return explorers[network] || `Unknown explorer for ${network}`;
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
