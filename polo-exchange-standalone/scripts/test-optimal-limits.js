#!/usr/bin/env node

/**
 * Test Optimal Transaction Limits
 * Focus on the working range for maximum revenue
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🎯 TESTING OPTIMAL TRANSACTION LIMITS');
console.log('Finding the sweet spot for maximum revenue...\n');

const BACKEND_URL = 'http://localhost:3001';

// Test realistic trade sizes that we know work
const optimalTestAmounts = [
  { name: 'Small Retail', ethAmount: '0.1', usdValue: 340 },
  { name: 'Medium Retail', ethAmount: '1', usdValue: 3400 },
  { name: 'Large Retail', ethAmount: '5', usdValue: 17000 },
  { name: 'Serious Trader', ethAmount: '10', usdValue: 34000 },
  { name: 'Whale Entry', ethAmount: '25', usdValue: 85000 },
  { name: 'Whale Trade', ethAmount: '50', usdValue: 170000 },
  { name: 'Large Whale', ethAmount: '100', usdValue: 340000 },
  { name: 'Max Tested', ethAmount: '150', usdValue: 510000 } // Push the boundary
];

async function testOptimalRange(testCase) {
  console.log(`💰 Testing ${testCase.name}: ${testCase.ethAmount} ETH (~$${testCase.usdValue.toLocaleString()})`);
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
      fromChain: '1',
      toChain: '56',
      fromToken: '******************************************',
      toToken: '******************************************',
      fromAmount: (parseFloat(testCase.ethAmount) * 1e18).toString(),
      fromAddress: process.env.ETHEREUM_ADDRESS,
      toAddress: process.env.BSC_ADDRESS
    }));
    
    if (response.ok) {
      const data = await response.json();
      if (data.estimate) {
        const outputBNB = parseFloat(data.estimate.toAmount) / 1e18;
        const feeRevenue = testCase.usdValue * 0.03; // 3% fee
        const executionTime = data.estimate.executionDuration || 'Unknown';
        
        console.log(`   ✅ SUCCESS:`);
        console.log(`      Input: ${testCase.ethAmount} ETH`);
        console.log(`      Output: ${outputBNB.toFixed(4)} BNB`);
        console.log(`      Your Revenue: $${feeRevenue.toLocaleString()}`);
        console.log(`      Tool: ${data.tool || 'Unknown'}`);
        console.log(`      Execution: ${executionTime}ms`);
        return { success: true, revenue: feeRevenue, tool: data.tool };
      } else {
        console.log(`   ⚠️ No estimate: ${data.message || 'Unknown reason'}`);
        return { success: false, reason: 'No estimate' };
      }
    } else {
      const errorData = await response.json();
      console.log(`   ❌ Failed: ${errorData.error || 'Unknown error'}`);
      return { success: false, reason: errorData.error };
    }
  } catch (error) {
    console.log(`   ❌ Network error: ${error.message}`);
    return { success: false, reason: error.message };
  }
}

async function findOptimalLimit() {
  console.log('🔍 Finding Optimal Transaction Limit...\n');
  
  let results = [];
  let totalRevenue = 0;
  let successCount = 0;
  let maxWorkingAmount = 0;
  
  for (const testCase of optimalTestAmounts) {
    const result = await testOptimalRange(testCase);
    results.push({
      ...testCase,
      ...result
    });
    
    if (result.success) {
      successCount++;
      totalRevenue += result.revenue;
      maxWorkingAmount = Math.max(maxWorkingAmount, testCase.usdValue);
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 OPTIMAL LIMIT ANALYSIS');
  console.log('='.repeat(60));
  
  console.log(`\n✅ Success Rate: ${successCount}/${optimalTestAmounts.length} (${Math.round(successCount/optimalTestAmounts.length*100)}%)`);
  console.log(`💰 Total Revenue from Test Trades: $${totalRevenue.toLocaleString()}`);
  console.log(`🎯 Maximum Working Amount: $${maxWorkingAmount.toLocaleString()}`);
  
  // Find the optimal limit
  const workingResults = results.filter(r => r.success);
  if (workingResults.length > 0) {
    const optimalLimit = Math.max(...workingResults.map(r => r.usdValue));
    console.log(`\n🎯 RECOMMENDED LIMIT: $${optimalLimit.toLocaleString()}`);
    
    // Calculate revenue projections
    console.log('\n💰 REVENUE PROJECTIONS:');
    
    const scenarios = [
      { name: 'Conservative', dailyTrades: 10, avgTradeSize: optimalLimit * 0.1 },
      { name: 'Moderate', dailyTrades: 50, avgTradeSize: optimalLimit * 0.3 },
      { name: 'Aggressive', dailyTrades: 100, avgTradeSize: optimalLimit * 0.5 },
      { name: 'Whale Focused', dailyTrades: 20, avgTradeSize: optimalLimit * 0.8 }
    ];
    
    scenarios.forEach(scenario => {
      const dailyVolume = scenario.dailyTrades * scenario.avgTradeSize;
      const dailyRevenue = dailyVolume * 0.03;
      const monthlyRevenue = dailyRevenue * 30;
      const yearlyRevenue = dailyRevenue * 365;
      
      console.log(`\n📈 ${scenario.name}:`);
      console.log(`   ${scenario.dailyTrades} trades/day @ $${scenario.avgTradeSize.toLocaleString()} avg`);
      console.log(`   Daily Revenue: $${dailyRevenue.toLocaleString()}`);
      console.log(`   Monthly Revenue: $${monthlyRevenue.toLocaleString()}`);
      console.log(`   Yearly Revenue: $${yearlyRevenue.toLocaleString()}`);
    });
    
    return optimalLimit;
  } else {
    console.log('\n❌ No working trades found');
    return 0;
  }
}

async function testCrossChainPairs() {
  console.log('\n🌉 Testing Cross-Chain Pairs...');
  
  const crossChainTests = [
    { name: 'ETH → BNB', fromChain: '1', toChain: '56', amount: '10' },
    { name: 'ETH → MATIC', fromChain: '1', toChain: '137', amount: '10' },
    { name: 'ETH → AVAX', fromChain: '1', toChain: '43114', amount: '10' },
    { name: 'BNB → ETH', fromChain: '56', toChain: '1', amount: '10' },
    { name: 'MATIC → ETH', fromChain: '137', toChain: '1', amount: '100' }
  ];
  
  let workingPairs = 0;
  
  for (const test of crossChainTests) {
    try {
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: test.fromChain,
        toChain: test.toChain,
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: (parseFloat(test.amount) * 1e18).toString(),
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      }));
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          console.log(`   ✅ ${test.name}: Working (${data.tool})`);
          workingPairs++;
        } else {
          console.log(`   ❌ ${test.name}: No liquidity`);
        }
      } else {
        console.log(`   ❌ ${test.name}: API error`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.name}: Network error`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n📊 Working Cross-Chain Pairs: ${workingPairs}/${crossChainTests.length}`);
  return workingPairs;
}

async function runOptimalLimitTest() {
  console.log('🚀 Starting Optimal Limit Analysis...\n');
  
  // Find optimal single-trade limit
  const optimalLimit = await findOptimalLimit();
  
  // Test cross-chain capabilities
  const workingPairs = await testCrossChainPairs();
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 FINAL RECOMMENDATIONS');
  console.log('='.repeat(80));
  
  if (optimalLimit > 0) {
    console.log(`\n✅ OPTIMAL TRANSACTION LIMIT: $${optimalLimit.toLocaleString()}`);
    console.log(`✅ WORKING CROSS-CHAIN PAIRS: ${workingPairs}/5`);
    
    console.log('\n🎯 IMPLEMENTATION STRATEGY:');
    console.log(`   1. Set limit to $${optimalLimit.toLocaleString()} for guaranteed execution`);
    console.log(`   2. Focus on ${workingPairs} working cross-chain pairs`);
    console.log(`   3. Target retail and serious traders (most reliable)`);
    console.log(`   4. Scale up gradually as volume increases`);
    
    console.log('\n💰 REVENUE POTENTIAL:');
    const maxDailyRevenue = (optimalLimit * 0.03) * 100; // 100 max trades per day
    console.log(`   Max single trade fee: $${(optimalLimit * 0.03).toLocaleString()}`);
    console.log(`   Max daily revenue (100 trades): $${maxDailyRevenue.toLocaleString()}`);
    console.log(`   Max yearly revenue: $${(maxDailyRevenue * 365).toLocaleString()}`);
    
    console.log('\n🚀 COMPETITIVE ADVANTAGE:');
    console.log(`   ✅ Higher limits than most DEXs`);
    console.log(`   ✅ Cross-chain capabilities`);
    console.log(`   ✅ $2B+ liquidity access`);
    console.log(`   ✅ No KYC required`);
    console.log(`   ✅ 3% fee structure`);
    
    if (optimalLimit >= 100000) {
      console.log('\n🎉 EXCELLENT! Your exchange can handle serious traders!');
    } else if (optimalLimit >= 50000) {
      console.log('\n👍 GOOD! Your exchange is ready for retail and medium traders!');
    } else {
      console.log('\n📈 STARTING POINT! Focus on retail traders and scale up!');
    }
    
  } else {
    console.log('\n❌ ISSUES DETECTED - Need to investigate further');
  }
}

// Run the optimal limit test
runOptimalLimitTest().catch(error => {
  console.error('💥 Optimal limit test crashed:', error);
  process.exit(1);
});
