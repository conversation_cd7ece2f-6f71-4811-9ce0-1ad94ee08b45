#!/usr/bin/env node

/**
 * Extreme Stress Test for Polo Exchange
 * Pushes the exchange to its absolute limits
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🔥 EXTREME STRESS TEST - POLO EXCHANGE');
console.log('Pushing your exchange to its absolute limits...\n');

const BACKEND_URL = 'http://localhost:3001';
const LIFI_API_KEY = process.env.LIFI_API_KEY;

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  performance: []
};

// Test 1: Massive Concurrent Load (50 simultaneous users)
async function testMassiveConcurrentLoad() {
  console.log('⚡ Testing Massive Concurrent Load (50 users)...');
  
  const concurrentUsers = 50;
  const userRequests = [];
  
  // Create 50 simultaneous requests
  for (let i = 0; i < concurrentUsers; i++) {
    const startTime = Date.now();
    
    userRequests.push(
      fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '100000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      })).then(response => ({
        user: i + 1,
        success: response.ok,
        status: response.status,
        responseTime: Date.now() - startTime
      })).catch(error => ({
        user: i + 1,
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      }))
    );
  }
  
  try {
    const overallStart = Date.now();
    const results = await Promise.all(userRequests);
    const overallTime = Date.now() - overallStart;
    
    const successCount = results.filter(r => r.success).length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
    const maxResponseTime = Math.max(...results.map(r => r.responseTime));
    const minResponseTime = Math.min(...results.map(r => r.responseTime));
    
    console.log(`   📊 Total Users: ${concurrentUsers}`);
    console.log(`   📊 Successful: ${successCount}/${concurrentUsers} (${Math.round(successCount/concurrentUsers*100)}%)`);
    console.log(`   📊 Overall Time: ${overallTime}ms`);
    console.log(`   📊 Avg Response: ${Math.round(avgResponseTime)}ms`);
    console.log(`   📊 Min Response: ${minResponseTime}ms`);
    console.log(`   📊 Max Response: ${maxResponseTime}ms`);
    
    // Check for failures
    const failures = results.filter(r => !r.success);
    if (failures.length > 0) {
      console.log(`   ⚠️ Failed Users: ${failures.map(f => f.user).join(', ')}`);
    }
    
    testResults.performance.push({
      test: 'Massive Concurrent Load',
      users: concurrentUsers,
      successRate: successCount/concurrentUsers,
      avgResponseTime,
      maxResponseTime
    });
    
    const passed = successCount >= concurrentUsers * 0.8; // 80% success threshold
    console.log(`   ${passed ? '✅' : '❌'} Massive load test: ${passed ? 'PASSED' : 'FAILED'}`);
    return passed;
    
  } catch (error) {
    console.log(`   ❌ Massive load test crashed: ${error.message}`);
    return false;
  }
}

// Test 2: Rapid Fire Requests (Speed Test)
async function testRapidFireRequests() {
  console.log('\n🚀 Testing Rapid Fire Requests (100 requests in 10 seconds)...');
  
  const totalRequests = 100;
  const timeLimit = 10000; // 10 seconds
  const requests = [];
  
  const startTime = Date.now();
  
  for (let i = 0; i < totalRequests; i++) {
    requests.push(
      fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1',
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '10000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      })).then(response => ({
        request: i + 1,
        success: response.ok,
        timestamp: Date.now()
      })).catch(error => ({
        request: i + 1,
        success: false,
        error: error.message,
        timestamp: Date.now()
      }))
    );
    
    // Small delay to spread requests
    await new Promise(resolve => setTimeout(resolve, 50));
  }
  
  try {
    const results = await Promise.all(requests);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const requestsPerSecond = Math.round((totalRequests / totalTime) * 1000);
    
    console.log(`   📊 Total Requests: ${totalRequests}`);
    console.log(`   📊 Time Taken: ${totalTime}ms`);
    console.log(`   📊 Successful: ${successCount}/${totalRequests}`);
    console.log(`   📊 Requests/Second: ${requestsPerSecond}`);
    
    const passed = successCount >= totalRequests * 0.9 && totalTime <= timeLimit * 1.5;
    console.log(`   ${passed ? '✅' : '❌'} Rapid fire test: ${passed ? 'PASSED' : 'FAILED'}`);
    return passed;
    
  } catch (error) {
    console.log(`   ❌ Rapid fire test crashed: ${error.message}`);
    return false;
  }
}

// Test 3: Memory Leak Detection
async function testMemoryLeakDetection() {
  console.log('\n🧠 Testing Memory Leak Detection...');
  
  const iterations = 1000;
  let memoryUsage = [];
  
  console.log(`   Running ${iterations} iterations to detect memory leaks...`);
  
  for (let i = 0; i < iterations; i++) {
    try {
      // Make request
      await fetch(`${BACKEND_URL}/api/health`);
      
      // Record memory usage every 100 iterations
      if (i % 100 === 0) {
        const usage = process.memoryUsage();
        memoryUsage.push({
          iteration: i,
          heapUsed: usage.heapUsed / 1024 / 1024, // MB
          heapTotal: usage.heapTotal / 1024 / 1024, // MB
          rss: usage.rss / 1024 / 1024 // MB
        });
        console.log(`   📊 Iteration ${i}: Heap ${Math.round(usage.heapUsed / 1024 / 1024)}MB`);
      }
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 10));
      
    } catch (error) {
      console.log(`   ⚠️ Request ${i} failed: ${error.message}`);
    }
  }
  
  // Analyze memory trend
  if (memoryUsage.length >= 2) {
    const firstReading = memoryUsage[0];
    const lastReading = memoryUsage[memoryUsage.length - 1];
    const memoryIncrease = lastReading.heapUsed - firstReading.heapUsed;
    const percentIncrease = (memoryIncrease / firstReading.heapUsed) * 100;
    
    console.log(`   📊 Memory Analysis:`);
    console.log(`      Start: ${Math.round(firstReading.heapUsed)}MB`);
    console.log(`      End: ${Math.round(lastReading.heapUsed)}MB`);
    console.log(`      Increase: ${Math.round(memoryIncrease)}MB (${Math.round(percentIncrease)}%)`);
    
    const passed = percentIncrease < 50; // Less than 50% increase is acceptable
    console.log(`   ${passed ? '✅' : '❌'} Memory leak test: ${passed ? 'PASSED' : 'FAILED'}`);
    return passed;
  } else {
    console.log(`   ❌ Insufficient memory readings`);
    return false;
  }
}

// Test 4: Edge Case Token Amounts
async function testEdgeCaseAmounts() {
  console.log('\n💰 Testing Edge Case Token Amounts...');
  
  const edgeCases = [
    { name: 'Minimum (1 wei)', amount: '1' },
    { name: 'Very Small (100 wei)', amount: '100' },
    { name: 'Dust (1000 wei)', amount: '1000' },
    { name: 'Micro (0.000001 ETH)', amount: '1000000000000' },
    { name: 'Tiny (0.00001 ETH)', amount: '10000000000000' },
    { name: 'Maximum Safe Integer', amount: '9007199254740991' },
    { name: 'Very Large (1M ETH)', amount: '1000000000000000000000000' }
  ];
  
  let passedTests = 0;
  
  for (const testCase of edgeCases) {
    try {
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1',
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: testCase.amount,
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }));
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate || data.message) {
          console.log(`   ✅ ${testCase.name}: Handled gracefully`);
          passedTests++;
        } else {
          console.log(`   ⚠️ ${testCase.name}: No response data`);
        }
      } else {
        console.log(`   ✅ ${testCase.name}: Properly rejected (${response.status})`);
        passedTests++; // Proper rejection is also a pass
      }
      
    } catch (error) {
      console.log(`   ❌ ${testCase.name}: Error - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  const successRate = passedTests / edgeCases.length;
  console.log(`   📊 Edge Cases Handled: ${passedTests}/${edgeCases.length} (${Math.round(successRate * 100)}%)`);
  
  const passed = successRate >= 0.8;
  console.log(`   ${passed ? '✅' : '❌'} Edge case test: ${passed ? 'PASSED' : 'FAILED'}`);
  return passed;
}

// Test 5: Malicious Input Testing
async function testMaliciousInputs() {
  console.log('\n🛡️ Testing Malicious Input Handling...');
  
  const maliciousInputs = [
    {
      name: 'SQL Injection',
      params: {
        fromChain: "1'; DROP TABLE users; --",
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000'
      }
    },
    {
      name: 'XSS Attempt',
      params: {
        fromChain: '1',
        toChain: '<script>alert("xss")</script>',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000'
      }
    },
    {
      name: 'Buffer Overflow',
      params: {
        fromChain: '1',
        toChain: '1',
        fromToken: 'A'.repeat(10000),
        toToken: '******************************************',
        fromAmount: '1000000000000000000'
      }
    },
    {
      name: 'Null Bytes',
      params: {
        fromChain: '1\0',
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000'
      }
    }
  ];
  
  let securityPassed = 0;
  
  for (const test of maliciousInputs) {
    try {
      const params = new URLSearchParams(test.params);
      const response = await fetch(`${BACKEND_URL}/api/lifi/quote?${params}`);
      
      // Server should either reject malicious input or handle it safely
      if (response.status >= 400 && response.status < 500) {
        console.log(`   ✅ ${test.name}: Properly rejected (${response.status})`);
        securityPassed++;
      } else if (response.ok) {
        // If it passes through, check if server is still responsive
        const healthCheck = await fetch(`${BACKEND_URL}/api/health`);
        if (healthCheck.ok) {
          console.log(`   ⚠️ ${test.name}: Passed through but server stable`);
          securityPassed++;
        } else {
          console.log(`   ❌ ${test.name}: Server compromised!`);
        }
      } else {
        console.log(`   ❌ ${test.name}: Unexpected response (${response.status})`);
      }
      
    } catch (error) {
      console.log(`   ✅ ${test.name}: Network error (safe) - ${error.message}`);
      securityPassed++;
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  const securityScore = securityPassed / maliciousInputs.length;
  console.log(`   📊 Security Tests Passed: ${securityPassed}/${maliciousInputs.length} (${Math.round(securityScore * 100)}%)`);
  
  const passed = securityScore >= 1.0; // All security tests must pass
  console.log(`   ${passed ? '✅' : '❌'} Security test: ${passed ? 'PASSED' : 'FAILED'}`);
  return passed;
}

// Test 6: Network Failure Simulation
async function testNetworkFailureRecovery() {
  console.log('\n🌐 Testing Network Failure Recovery...');
  
  // Test with invalid endpoints to simulate network failures
  const failureTests = [
    {
      name: 'Invalid Backend URL',
      url: 'http://localhost:9999/api/health'
    },
    {
      name: 'Timeout Simulation',
      url: `${BACKEND_URL}/api/lifi/quote?` + new URLSearchParams({
        fromChain: '1',
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }),
      timeout: 100 // Very short timeout
    }
  ];
  
  let recoveryPassed = 0;
  
  for (const test of failureTests) {
    try {
      const controller = new AbortController();
      if (test.timeout) {
        setTimeout(() => controller.abort(), test.timeout);
      }
      
      const response = await fetch(test.url, {
        signal: controller.signal,
        timeout: test.timeout || 5000
      });
      
      console.log(`   ⚠️ ${test.name}: Unexpectedly succeeded`);
      
    } catch (error) {
      console.log(`   ✅ ${test.name}: Properly handled - ${error.message.substring(0, 50)}`);
      recoveryPassed++;
    }
    
    // Test if main server is still responsive after failure
    try {
      const healthCheck = await fetch(`${BACKEND_URL}/api/health`);
      if (healthCheck.ok) {
        console.log(`      ✅ Server remained stable after ${test.name}`);
      } else {
        console.log(`      ❌ Server unstable after ${test.name}`);
      }
    } catch (error) {
      console.log(`      ❌ Server unreachable after ${test.name}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  const recoveryScore = recoveryPassed / failureTests.length;
  console.log(`   📊 Recovery Tests: ${recoveryPassed}/${failureTests.length} (${Math.round(recoveryScore * 100)}%)`);
  
  const passed = recoveryScore >= 1.0;
  console.log(`   ${passed ? '✅' : '❌'} Network failure test: ${passed ? 'PASSED' : 'FAILED'}`);
  return passed;
}

// Main extreme test runner
async function runExtremeStressTest() {
  console.log('🚀 Starting Extreme Stress Test...\n');
  
  const tests = [
    { name: 'Massive Concurrent Load', fn: testMassiveConcurrentLoad },
    { name: 'Rapid Fire Requests', fn: testRapidFireRequests },
    { name: 'Memory Leak Detection', fn: testMemoryLeakDetection },
    { name: 'Edge Case Amounts', fn: testEdgeCaseAmounts },
    { name: 'Malicious Input Handling', fn: testMaliciousInputs },
    { name: 'Network Failure Recovery', fn: testNetworkFailureRecovery }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(60)}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      testResults.total++;
      if (result) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      console.log(`❌ ${test.name} crashed: ${error.message}`);
      results.push({ name: test.name, passed: false, error: error.message });
      testResults.total++;
      testResults.failed++;
      testResults.errors.push(`${test.name}: ${error.message}`);
    }
    
    // Cool down period between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Final Assessment
  console.log(`\n${'='.repeat(80)}`);
  console.log('🏁 EXTREME STRESS TEST COMPLETE');
  console.log(`${'='.repeat(80)}`);
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.error) console.log(`    Error: ${result.error}`);
  });
  
  const passRate = Math.round((testResults.passed / testResults.total) * 100);
  console.log(`\n📊 Extreme Test Results: ${testResults.passed}/${testResults.total} tests passed (${passRate}%)`);
  
  // Performance Summary
  if (testResults.performance.length > 0) {
    console.log('\n⚡ Performance Summary:');
    testResults.performance.forEach(perf => {
      console.log(`   ${perf.test}: ${Math.round(perf.successRate * 100)}% success, ${Math.round(perf.avgResponseTime)}ms avg`);
    });
  }
  
  if (passRate >= 85) {
    console.log('\n🎉 EXTREME STRESS TEST PASSED!');
    console.log('Your exchange is BULLETPROOF and ready for heavy production load!');
    console.log('\n🚀 Your exchange can handle:');
    console.log('   ✅ 50+ concurrent users');
    console.log('   ✅ 100+ requests per minute');
    console.log('   ✅ Malicious attacks');
    console.log('   ✅ Network failures');
    console.log('   ✅ Edge cases and errors');
    console.log('   ✅ Memory-intensive operations');
  } else {
    console.log('\n⚠️ EXTREME STRESS TEST REVEALED ISSUES');
    console.log('Some advanced scenarios need attention before heavy production load.');
    console.log('\n🔧 Critical Issues:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n💡 Recommendations:');
  console.log('   1. Monitor server resources during peak usage');
  console.log('   2. Implement rate limiting for production');
  console.log('   3. Set up automated alerts for failures');
  console.log('   4. Consider load balancing for scale');
}

// Run the extreme stress test
runExtremeStressTest().catch(error => {
  console.error('💥 Extreme stress test crashed:', error);
  process.exit(1);
});
