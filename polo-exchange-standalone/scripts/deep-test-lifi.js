#!/usr/bin/env node

/**
 * Deep LiFi Integration Test
 * Investigates specific LiFi API behaviors and edge cases
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

console.log('🔬 DEEP LIFI INTEGRATION TEST');
console.log('Investigating LiFi API behaviors and limitations...\n');

const LIFI_API_KEY = process.env.LIFI_API_KEY;
const BACKEND_URL = 'http://localhost:3001';

// Test 1: Direct LiFi API vs Backend Proxy
async function testDirectVsProxy() {
  console.log('🔄 Testing Direct LiFi API vs Backend Proxy...');
  
  const testParams = {
    fromChain: '1',
    toChain: '56',
    fromToken: '******************************************',
    toToken: '******************************************',
    fromAmount: '1000000000000000000',
    fromAddress: process.env.ETHEREUM_ADDRESS,
    toAddress: process.env.BSC_ADDRESS
  };
  
  // Test direct LiFi API
  console.log('   Testing direct LiFi API...');
  try {
    const params = new URLSearchParams(testParams);
    const directResponse = await fetch(`https://li.quest/v1/quote?${params}`, {
      headers: {
        'x-lifi-api-key': LIFI_API_KEY,
        'Accept': 'application/json'
      }
    });
    
    if (directResponse.ok) {
      const directData = await directResponse.json();
      console.log(`   ✅ Direct LiFi API: Success`);
      console.log(`      - Estimate: ${directData.estimate ? 'Available' : 'Missing'}`);
      console.log(`      - Tool: ${directData.tool || 'Unknown'}`);
      console.log(`      - Execution time: ${directData.estimate?.executionDuration || 'Unknown'}ms`);
    } else {
      const errorText = await directResponse.text();
      console.log(`   ❌ Direct LiFi API failed: ${directResponse.status}`);
      console.log(`      Error: ${errorText.substring(0, 200)}`);
    }
  } catch (error) {
    console.log(`   ❌ Direct LiFi API error: ${error.message}`);
  }
  
  // Test backend proxy
  console.log('   Testing backend proxy...');
  try {
    const params = new URLSearchParams(testParams);
    const proxyResponse = await fetch(`${BACKEND_URL}/api/lifi/quote?${params}`);
    
    if (proxyResponse.ok) {
      const proxyData = await proxyResponse.json();
      console.log(`   ✅ Backend proxy: Success`);
      console.log(`      - Estimate: ${proxyData.estimate ? 'Available' : 'Missing'}`);
      console.log(`      - Tool: ${proxyData.tool || 'Unknown'}`);
    } else {
      const errorText = await proxyResponse.text();
      console.log(`   ❌ Backend proxy failed: ${proxyResponse.status}`);
      console.log(`      Error: ${errorText.substring(0, 200)}`);
    }
  } catch (error) {
    console.log(`   ❌ Backend proxy error: ${error.message}`);
  }
}

// Test 2: Investigate Solana Integration
async function testSolanaIntegration() {
  console.log('\n☀️ Testing Solana Integration...');
  
  const solanaTests = [
    {
      name: 'ETH → SOL',
      fromChain: '1',
      toChain: '101',
      fromToken: '******************************************',
      toToken: '******************************************'
    },
    {
      name: 'SOL → ETH',
      fromChain: '101',
      toChain: '1',
      fromToken: '******************************************',
      toToken: '******************************************'
    },
    {
      name: 'SOL → USDC',
      fromChain: '101',
      toChain: '1',
      fromToken: '******************************************',
      toToken: '******************************************'
    }
  ];
  
  for (const test of solanaTests) {
    try {
      const params = new URLSearchParams({
        ...test,
        fromAmount: '1000000000', // 1 SOL in lamports
        fromAddress: process.env.SOLANA_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      });
      
      const response = await fetch(`https://li.quest/v1/quote?${params}`, {
        headers: {
          'x-lifi-api-key': LIFI_API_KEY,
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          console.log(`   ✅ ${test.name}: Available`);
          console.log(`      - Tool: ${data.tool}`);
          console.log(`      - Execution time: ${data.estimate.executionDuration}ms`);
        } else {
          console.log(`   ⚠️ ${test.name}: No estimate (${data.message || 'Unknown reason'})`);
        }
      } else {
        const errorText = await response.text();
        console.log(`   ❌ ${test.name}: Failed (${response.status})`);
        console.log(`      Error: ${errorText.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.name}: Error - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Test 3: Test Different Amount Ranges
async function testAmountRanges() {
  console.log('\n💰 Testing Different Amount Ranges...');
  
  const amounts = [
    { name: 'Micro (0.001 ETH)', amount: '1000000000000000' },
    { name: 'Small (0.01 ETH)', amount: '10000000000000000' },
    { name: 'Normal (0.1 ETH)', amount: '100000000000000000' },
    { name: 'Large (1 ETH)', amount: '1000000000000000000' },
    { name: 'Very Large (10 ETH)', amount: '10000000000000000000' },
    { name: 'Huge (100 ETH)', amount: '100000000000000000000' }
  ];
  
  for (const amountTest of amounts) {
    try {
      const params = new URLSearchParams({
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: amountTest.amount,
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      });
      
      const response = await fetch(`https://li.quest/v1/quote?${params}`, {
        headers: {
          'x-lifi-api-key': LIFI_API_KEY,
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          const outputAmount = parseFloat(data.estimate.toAmount) / 1e18;
          console.log(`   ✅ ${amountTest.name}: ~${outputAmount.toFixed(4)} BNB`);
        } else {
          console.log(`   ⚠️ ${amountTest.name}: No estimate available`);
        }
      } else {
        console.log(`   ❌ ${amountTest.name}: Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ ${amountTest.name}: Error - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 300));
  }
}

// Test 4: Test Exotic Token Pairs
async function testExoticPairs() {
  console.log('\n🦄 Testing Exotic Token Pairs...');
  
  const exoticPairs = [
    {
      name: 'CRV → LINK',
      fromToken: '******************************************',
      toToken: '******************************************'
    },
    {
      name: 'UNI → AAVE',
      fromToken: '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
      toToken: '******************************************'
    },
    {
      name: 'LINK → CRV',
      fromToken: '******************************************',
      toToken: '******************************************'
    }
  ];
  
  for (const pair of exoticPairs) {
    try {
      const params = new URLSearchParams({
        fromChain: '1',
        toChain: '1',
        fromToken: pair.fromToken,
        toToken: pair.toToken,
        fromAmount: '1000000000000000000', // 1 token
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      });
      
      const response = await fetch(`https://li.quest/v1/quote?${params}`, {
        headers: {
          'x-lifi-api-key': LIFI_API_KEY,
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.estimate) {
          console.log(`   ✅ ${pair.name}: Available via ${data.tool}`);
          console.log(`      - Gas cost: ${data.estimate.gasCosts?.[0]?.estimate || 'Unknown'}`);
        } else {
          console.log(`   ⚠️ ${pair.name}: No route found`);
        }
      } else {
        console.log(`   ❌ ${pair.name}: Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ ${pair.name}: Error - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 400));
  }
}

// Test 5: Test Error Conditions
async function testErrorConditions() {
  console.log('\n🚨 Testing Error Conditions...');
  
  const errorTests = [
    {
      name: 'Invalid API Key',
      headers: { 'x-lifi-api-key': 'invalid-key', 'Accept': 'application/json' },
      params: {
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      }
    },
    {
      name: 'Unsupported Chain',
      headers: { 'x-lifi-api-key': LIFI_API_KEY, 'Accept': 'application/json' },
      params: {
        fromChain: '999999',
        toChain: '1',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }
    },
    {
      name: 'Invalid Token Address',
      headers: { 'x-lifi-api-key': LIFI_API_KEY, 'Accept': 'application/json' },
      params: {
        fromChain: '1',
        toChain: '1',
        fromToken: '0xinvalidaddress',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.ETHEREUM_ADDRESS
      }
    }
  ];
  
  for (const test of errorTests) {
    try {
      const params = new URLSearchParams(test.params);
      const response = await fetch(`https://li.quest/v1/quote?${params}`, {
        headers: test.headers
      });
      
      if (response.ok) {
        console.log(`   ⚠️ ${test.name}: Unexpectedly succeeded`);
      } else {
        const errorText = await response.text();
        console.log(`   ✅ ${test.name}: Properly failed (${response.status})`);
        console.log(`      Error: ${errorText.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`   ✅ ${test.name}: Network error (expected) - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Test 6: Rate Limiting Test
async function testRateLimiting() {
  console.log('\n⚡ Testing Rate Limiting...');
  
  const rapidRequests = [];
  const startTime = Date.now();
  
  // Send 20 rapid requests
  for (let i = 0; i < 20; i++) {
    rapidRequests.push(
      fetch('https://li.quest/v1/quote?' + new URLSearchParams({
        fromChain: '1',
        toChain: '56',
        fromToken: '******************************************',
        toToken: '******************************************',
        fromAmount: '1000000000000000000',
        fromAddress: process.env.ETHEREUM_ADDRESS,
        toAddress: process.env.BSC_ADDRESS
      }), {
        headers: {
          'x-lifi-api-key': LIFI_API_KEY,
          'Accept': 'application/json'
        }
      }).then(response => ({
        status: response.status,
        ok: response.ok,
        timestamp: Date.now()
      })).catch(error => ({
        status: 0,
        ok: false,
        error: error.message,
        timestamp: Date.now()
      }))
    );
  }
  
  try {
    const results = await Promise.all(rapidRequests);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    const successCount = results.filter(r => r.ok).length;
    const rateLimitedCount = results.filter(r => r.status === 429).length;
    
    console.log(`   📊 Rapid Requests: ${successCount}/20 succeeded in ${totalTime}ms`);
    console.log(`   📊 Rate Limited: ${rateLimitedCount} requests`);
    
    if (rateLimitedCount > 0) {
      console.log(`   ✅ Rate limiting is working properly`);
    } else {
      console.log(`   ⚠️ No rate limiting detected (may be high limits)`);
    }
  } catch (error) {
    console.log(`   ❌ Rate limiting test failed: ${error.message}`);
  }
}

// Main test runner
async function runDeepTest() {
  console.log('🚀 Starting Deep LiFi Integration Test...\n');
  
  await testDirectVsProxy();
  await testSolanaIntegration();
  await testAmountRanges();
  await testExoticPairs();
  await testErrorConditions();
  await testRateLimiting();
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 DEEP TEST COMPLETE');
  console.log('='.repeat(60));
  
  console.log('\n💡 Key Findings:');
  console.log('   - LiFi API behavior and limitations identified');
  console.log('   - Token pair availability mapped');
  console.log('   - Error handling patterns documented');
  console.log('   - Rate limiting behavior observed');
  
  console.log('\n🔧 Recommendations:');
  console.log('   1. Implement fallback for unsupported pairs');
  console.log('   2. Add proper error handling for edge cases');
  console.log('   3. Implement rate limiting on your backend');
  console.log('   4. Cache quotes for better performance');
}

// Run the deep test
runDeepTest().catch(error => {
  console.error('💥 Deep test crashed:', error);
  process.exit(1);
});
