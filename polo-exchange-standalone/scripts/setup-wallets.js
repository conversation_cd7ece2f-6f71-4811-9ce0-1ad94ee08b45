#!/usr/bin/env node

/**
 * Polo Exchange Wallet Setup Script
 * Generates and configures operational wallets for your exchange
 */

import dotenv from 'dotenv';
import { ethers } from 'ethers';
import { Keypair } from '@solana/web3.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

console.log('🔐 Polo Exchange Wallet Setup\n');

// Check if wallets are already configured
function checkExistingWallets() {
  const existingWallets = {
    ethereum: !!process.env.ETHEREUM_PRIVATE_KEY,
    bsc: !!process.env.BSC_PRIVATE_KEY,
    polygon: !!process.env.POLYGON_PRIVATE_KEY,
    avalanche: !!process.env.AVALANCHE_PRIVATE_KEY,
    arbitrum: !!process.env.ARBITRUM_PRIVATE_KEY,
    optimism: !!process.env.OPTIMISM_PRIVATE_KEY,
    solana: !!process.env.SOLANA_PRIVATE_KEY
  };

  const configured = Object.entries(existingWallets).filter(([_, isConfigured]) => isConfigured);
  const missing = Object.entries(existingWallets).filter(([_, isConfigured]) => !isConfigured);

  console.log('📊 Current Wallet Status:');
  if (configured.length > 0) {
    console.log(`   ✅ Configured: ${configured.map(([network]) => network).join(', ')}`);
  }
  if (missing.length > 0) {
    console.log(`   ❌ Missing: ${missing.map(([network]) => network).join(', ')}`);
  }
  console.log();

  return { configured: configured.map(([network]) => network), missing: missing.map(([network]) => network) };
}

// Generate new EVM wallet (Ethereum, BSC, Polygon, etc.)
function generateEVMWallet() {
  const wallet = ethers.Wallet.createRandom();
  return {
    privateKey: wallet.privateKey,
    address: wallet.address,
    mnemonic: wallet.mnemonic?.phrase || 'N/A'
  };
}

// Generate new Solana wallet
function generateSolanaWallet() {
  const keypair = Keypair.generate();
  return {
    privateKey: Buffer.from(keypair.secretKey).toString('base64'),
    address: keypair.publicKey.toString(),
    publicKey: keypair.publicKey.toString()
  };
}

// Generate all missing wallets
function generateMissingWallets(missingNetworks) {
  const newWallets = {};

  console.log('🎲 Generating new wallets...\n');

  missingNetworks.forEach(network => {
    if (network === 'solana') {
      const wallet = generateSolanaWallet();
      newWallets[network] = wallet;
      console.log(`✅ Generated ${network.toUpperCase()} wallet:`);
      console.log(`   Address: ${wallet.address}`);
      console.log(`   Private Key: ${wallet.privateKey.substring(0, 20)}...`);
    } else {
      // EVM networks (ethereum, bsc, polygon, avalanche, arbitrum, optimism)
      const wallet = generateEVMWallet();
      newWallets[network] = wallet;
      console.log(`✅ Generated ${network.toUpperCase()} wallet:`);
      console.log(`   Address: ${wallet.address}`);
      console.log(`   Private Key: ${wallet.privateKey.substring(0, 20)}...`);
    }
    console.log();
  });

  return newWallets;
}

// Update .env file with new wallet configurations
function updateEnvFile(newWallets) {
  const envPath = path.join(__dirname, '../.env');
  let envContent = fs.readFileSync(envPath, 'utf8');

  console.log('📝 Updating .env file with new wallet configurations...\n');

  Object.entries(newWallets).forEach(([network, wallet]) => {
    const networkUpper = network.toUpperCase();
    
    // Update private key
    const privateKeyPattern = new RegExp(`^${networkUpper}_PRIVATE_KEY=.*$`, 'm');
    if (privateKeyPattern.test(envContent)) {
      envContent = envContent.replace(privateKeyPattern, `${networkUpper}_PRIVATE_KEY=${wallet.privateKey}`);
    } else {
      envContent += `\n${networkUpper}_PRIVATE_KEY=${wallet.privateKey}`;
    }

    // Update address
    const addressPattern = new RegExp(`^${networkUpper}_ADDRESS=.*$`, 'm');
    if (addressPattern.test(envContent)) {
      envContent = envContent.replace(addressPattern, `${networkUpper}_ADDRESS=${wallet.address}`);
    } else {
      envContent += `\n${networkUpper}_ADDRESS=${wallet.address}`;
    }

    console.log(`✅ Updated ${networkUpper} configuration in .env`);
  });

  // Write updated content back to .env file
  fs.writeFileSync(envPath, envContent);
  console.log('\n🎉 .env file updated successfully!');
}

// Display wallet summary
function displayWalletSummary() {
  console.log('\n📋 Wallet Summary for Polo Exchange:');
  console.log('=' .repeat(60));

  const networks = [
    { name: 'Ethereum', env: 'ETHEREUM', description: 'ETH, USDC, USDT, LINK, UNI, etc.' },
    { name: 'BSC', env: 'BSC', description: 'BNB, BUSD, etc.' },
    { name: 'Polygon', env: 'POLYGON', description: 'MATIC, USDC.e, etc.' },
    { name: 'Avalanche', env: 'AVALANCHE', description: 'AVAX, USDC.e, etc.' },
    { name: 'Arbitrum', env: 'ARBITRUM', description: 'ETH, USDC, etc.' },
    { name: 'Optimism', env: 'OPTIMISM', description: 'ETH, USDC, etc.' },
    { name: 'Solana', env: 'SOLANA', description: 'SOL, HYPE, etc.' }
  ];

  networks.forEach(network => {
    const privateKey = process.env[`${network.env}_PRIVATE_KEY`];
    const address = process.env[`${network.env}_ADDRESS`];
    
    console.log(`\n🌐 ${network.name}:`);
    if (privateKey && address) {
      console.log(`   ✅ Address: ${address}`);
      console.log(`   🔑 Private Key: ${privateKey.substring(0, 20)}...`);
      console.log(`   💰 Supports: ${network.description}`);
    } else {
      console.log(`   ❌ Not configured`);
    }
  });
}

// Display security instructions
function displaySecurityInstructions() {
  console.log('\n🔒 SECURITY INSTRUCTIONS:');
  console.log('=' .repeat(60));
  console.log('1. 🚨 NEVER share your private keys with anyone');
  console.log('2. 🔐 Keep your .env file secure and never commit it to git');
  console.log('3. 💰 Fund these wallets with operational amounts for swaps');
  console.log('4. 🔄 These wallets will execute LiFi swaps on behalf of users');
  console.log('5. 📊 Monitor wallet balances regularly');
  console.log('6. 🛡️ Consider using hardware wallets for large amounts');
  console.log('\n💡 Next Steps:');
  console.log('   1. Fund your wallets with operational crypto');
  console.log('   2. Test the exchange with small amounts first');
  console.log('   3. Monitor swap execution and wallet balances');
  console.log('   4. Set up automated balance alerts');
}

// Display funding instructions
function displayFundingInstructions() {
  console.log('\n💰 FUNDING INSTRUCTIONS:');
  console.log('=' .repeat(60));
  console.log('Send operational funds to these addresses:');
  
  const fundingAmounts = {
    'ETHEREUM': { amount: '0.5 ETH + 1000 USDC', purpose: 'Gas fees + ERC20 swaps' },
    'BSC': { amount: '1 BNB + 500 BUSD', purpose: 'Gas fees + BEP20 swaps' },
    'POLYGON': { amount: '100 MATIC + 500 USDC', purpose: 'Gas fees + Polygon swaps' },
    'AVALANCHE': { amount: '10 AVAX + 500 USDC', purpose: 'Gas fees + Avalanche swaps' },
    'ARBITRUM': { amount: '0.1 ETH + 500 USDC', purpose: 'Gas fees + Arbitrum swaps' },
    'OPTIMISM': { amount: '0.1 ETH + 500 USDC', purpose: 'Gas fees + Optimism swaps' },
    'SOLANA': { amount: '5 SOL', purpose: 'Gas fees + SPL token swaps' }
  };

  Object.entries(fundingAmounts).forEach(([network, info]) => {
    const address = process.env[`${network}_ADDRESS`];
    if (address) {
      console.log(`\n🌐 ${network}:`);
      console.log(`   📍 Address: ${address}`);
      console.log(`   💰 Suggested: ${info.amount}`);
      console.log(`   🎯 Purpose: ${info.purpose}`);
    }
  });
}

// Main execution
async function main() {
  try {
    // Check existing wallet configuration
    const { configured, missing } = checkExistingWallets();

    if (missing.length === 0) {
      console.log('🎉 All wallets are already configured!');
      displayWalletSummary();
      displayFundingInstructions();
      displaySecurityInstructions();
      return;
    }

    // Generate missing wallets
    const newWallets = generateMissingWallets(missing);

    // Update .env file
    updateEnvFile(newWallets);

    // Reload environment variables
    require('dotenv').config();

    // Display summary
    displayWalletSummary();
    displayFundingInstructions();
    displaySecurityInstructions();

    console.log('\n🚀 Wallet setup complete! Your exchange is ready for LiFi integration.');

  } catch (error) {
    console.error('❌ Error setting up wallets:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  generateEVMWallet,
  generateSolanaWallet,
  checkExistingWallets
};
