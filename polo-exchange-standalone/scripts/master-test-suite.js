#!/usr/bin/env node

/**
 * Master Test Suite - Polo Exchange
 * Runs all tests in sequence for comprehensive validation
 */

import dotenv from 'dotenv';
import { spawn } from 'child_process';

dotenv.config();

console.log('🎯 MASTER TEST SUITE - POLO EXCHANGE');
console.log('Running complete test battery for final validation...\n');

const testSuites = [
  {
    name: 'Quick Health Test',
    command: 'npm run quick-test',
    description: 'Basic functionality and connectivity',
    critical: true,
    timeout: 30000
  },
  {
    name: 'Production Ready Test',
    command: 'npm run production-test',
    description: 'Production deployment readiness',
    critical: true,
    timeout: 90000
  },
  {
    name: 'Extreme Stress Test',
    command: 'npm run extreme-test',
    description: 'Performance under extreme load',
    critical: false,
    timeout: 180000
  },
  {
    name: 'Deep LiFi Integration',
    command: 'npm run deep-test',
    description: 'LiFi API behavior and edge cases',
    critical: false,
    timeout: 120000
  }
];

let overallResults = {
  total: 0,
  passed: 0,
  failed: 0,
  critical_failed: 0,
  results: []
};

function runTest(testSuite) {
  return new Promise((resolve) => {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🧪 Running: ${testSuite.name}`);
    console.log(`📝 Description: ${testSuite.description}`);
    console.log(`⏱️ Timeout: ${testSuite.timeout / 1000}s`);
    console.log(`🚨 Critical: ${testSuite.critical ? 'YES' : 'NO'}`);
    console.log(`${'='.repeat(80)}`);

    const startTime = Date.now();
    let output = '';
    let errorOutput = '';

    const child = spawn('bash', ['-c', testSuite.command], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: process.cwd()
    });

    // Capture stdout
    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
    });

    // Capture stderr
    child.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      process.stderr.write(text);
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      child.kill('SIGTERM');
      console.log(`\n⏰ Test timed out after ${testSuite.timeout / 1000}s`);
    }, testSuite.timeout);

    child.on('close', (code) => {
      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;

      const result = {
        name: testSuite.name,
        passed: code === 0,
        critical: testSuite.critical,
        duration: duration,
        exitCode: code,
        output: output,
        errorOutput: errorOutput
      };

      overallResults.total++;
      if (result.passed) {
        overallResults.passed++;
        console.log(`\n✅ ${testSuite.name} PASSED (${duration}ms)`);
      } else {
        overallResults.failed++;
        if (testSuite.critical) {
          overallResults.critical_failed++;
        }
        console.log(`\n❌ ${testSuite.name} FAILED (${duration}ms, exit code: ${code})`);
      }

      overallResults.results.push(result);
      resolve(result);
    });

    child.on('error', (error) => {
      clearTimeout(timeoutId);
      console.log(`\n💥 ${testSuite.name} CRASHED: ${error.message}`);
      
      const result = {
        name: testSuite.name,
        passed: false,
        critical: testSuite.critical,
        duration: Date.now() - startTime,
        error: error.message
      };

      overallResults.total++;
      overallResults.failed++;
      if (testSuite.critical) {
        overallResults.critical_failed++;
      }
      overallResults.results.push(result);
      resolve(result);
    });
  });
}

function generateFinalReport() {
  console.log('\n' + '='.repeat(100));
  console.log('🏁 MASTER TEST SUITE COMPLETE');
  console.log('='.repeat(100));

  // Test Results Summary
  console.log('\n📊 TEST RESULTS SUMMARY:');
  console.log('-'.repeat(50));
  
  overallResults.results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    const critical = result.critical ? '🚨' : '📝';
    const duration = `${Math.round(result.duration / 1000)}s`;
    console.log(`${status} ${critical} ${result.name} (${duration})`);
    
    if (result.error) {
      console.log(`    💥 Error: ${result.error}`);
    }
  });

  // Overall Statistics
  const passRate = Math.round((overallResults.passed / overallResults.total) * 100);
  const totalDuration = overallResults.results.reduce((sum, r) => sum + r.duration, 0);
  
  console.log('\n📈 OVERALL STATISTICS:');
  console.log('-'.repeat(50));
  console.log(`Total Tests: ${overallResults.total}`);
  console.log(`Passed: ${overallResults.passed}`);
  console.log(`Failed: ${overallResults.failed}`);
  console.log(`Critical Failures: ${overallResults.critical_failed}`);
  console.log(`Pass Rate: ${passRate}%`);
  console.log(`Total Duration: ${Math.round(totalDuration / 1000)}s`);

  // Final Assessment
  console.log('\n🎯 FINAL ASSESSMENT:');
  console.log('-'.repeat(50));

  if (overallResults.critical_failed === 0 && passRate >= 75) {
    console.log('🎉 EXCHANGE IS PRODUCTION READY!');
    console.log('\n✅ Your Polo Exchange has passed comprehensive testing!');
    console.log('✅ All critical systems are functional');
    console.log('✅ Performance is acceptable');
    console.log('✅ Security measures are in place');
    console.log('✅ Ready for real user transactions');
    
    console.log('\n🚀 DEPLOYMENT CHECKLIST:');
    console.log('   1. ✅ Backend tested and working');
    console.log('   2. ✅ LiFi integration verified');
    console.log('   3. ✅ Wallet configuration complete');
    console.log('   4. ✅ Error handling robust');
    console.log('   5. 🔄 Fund operational wallets');
    console.log('   6. 🔄 Start with small test swaps');
    console.log('   7. 🔄 Monitor all transactions');
    
  } else if (overallResults.critical_failed > 0) {
    console.log('🚨 CRITICAL ISSUES DETECTED!');
    console.log('\n❌ Your exchange has critical failures that must be fixed');
    console.log('❌ DO NOT deploy to production until issues are resolved');
    
    console.log('\n🔧 CRITICAL ISSUES TO FIX:');
    overallResults.results
      .filter(r => !r.passed && r.critical)
      .forEach(r => console.log(`   - ${r.name}: ${r.error || 'Test failed'}`));
      
  } else {
    console.log('⚠️ MINOR ISSUES DETECTED');
    console.log('\n🟡 Your exchange is mostly ready but has some minor issues');
    console.log('🟡 Can deploy with caution and close monitoring');
    
    console.log('\n🔧 MINOR ISSUES TO ADDRESS:');
    overallResults.results
      .filter(r => !r.passed && !r.critical)
      .forEach(r => console.log(`   - ${r.name}: ${r.error || 'Test failed'}`));
  }

  // Next Steps
  console.log('\n💡 IMMEDIATE NEXT STEPS:');
  console.log('-'.repeat(50));
  
  if (overallResults.critical_failed === 0) {
    console.log('1. 💰 Fund your operational wallets');
    console.log('2. 🚀 Start backend: npm run lifi-backend');
    console.log('3. 🌐 Start frontend: npm run dev');
    console.log('4. 🧪 Test with small amounts ($5-10)');
    console.log('5. 📊 Monitor all transactions closely');
    console.log('6. 📈 Scale up gradually as confidence grows');
  } else {
    console.log('1. 🔧 Fix all critical issues identified above');
    console.log('2. 🧪 Re-run master test suite');
    console.log('3. 📝 Verify all tests pass');
    console.log('4. 🚀 Then proceed with deployment');
  }

  // Support Information
  console.log('\n📞 SUPPORT COMMANDS:');
  console.log('-'.repeat(50));
  console.log('npm run quick-test      # Quick health check');
  console.log('npm run production-test # Production readiness');
  console.log('npm run extreme-test    # Stress testing');
  console.log('npm run deep-test       # LiFi integration');
  console.log('npm run master-test     # This complete suite');

  console.log('\n🎯 EXCHANGE STATUS:');
  if (overallResults.critical_failed === 0 && passRate >= 75) {
    console.log('🟢 READY FOR PRODUCTION DEPLOYMENT');
  } else if (overallResults.critical_failed > 0) {
    console.log('🔴 NOT READY - CRITICAL ISSUES NEED FIXING');
  } else {
    console.log('🟡 MOSTLY READY - MINOR ISSUES TO ADDRESS');
  }
}

async function runMasterTestSuite() {
  console.log('🚀 Starting Master Test Suite...');
  console.log(`📋 Running ${testSuites.length} comprehensive test suites...`);
  console.log('⏱️ This may take several minutes...\n');

  // Check if backend is running
  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch('http://localhost:3001/api/health', { timeout: 5000 });
    if (response.ok) {
      console.log('✅ Backend server detected and running');
    } else {
      console.log('⚠️ Backend server not responding properly');
    }
  } catch (error) {
    console.log('❌ Backend server not running - some tests may fail');
    console.log('💡 Start backend with: npm run lifi-backend');
  }

  // Run all test suites sequentially
  for (const testSuite of testSuites) {
    await runTest(testSuite);
    
    // Brief pause between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Generate final comprehensive report
  generateFinalReport();
}

// Run the master test suite
runMasterTestSuite().catch(error => {
  console.error('💥 Master test suite crashed:', error);
  process.exit(1);
});
