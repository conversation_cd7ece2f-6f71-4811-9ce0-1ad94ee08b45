const { ethers } = require("hardhat");

async function main() {
  console.log("💰 CHECKING DEPLOYMENT WALLET BALANCE");
  console.log("=====================================\n");

  // Get network info
  const network = hre.network.name;
  const [deployer] = await ethers.getSigners();
  
  console.log(`📍 Network: ${network}`);
  console.log(`👤 Deployer: ${deployer.address}`);
  
  try {
    // Check balance
    const balance = await ethers.provider.getBalance(deployer.address);
    const balanceEth = ethers.formatEther(balance);
    
    console.log(`💰 Balance: ${balanceEth} ${getNativeCurrency(network)}`);
    
    // Check if sufficient for deployment
    const requiredBalance = getRequiredBalance(network);
    const isEnough = parseFloat(balanceEth) >= requiredBalance;
    
    console.log(`📊 Required: ${requiredBalance} ${getNativeCurrency(network)}`);
    console.log(`✅ Status: ${isEnough ? 'SUFFICIENT' : 'INSUFFICIENT'}`);
    
    if (!isEnough) {
      console.log(`\n❌ INSUFFICIENT BALANCE FOR DEPLOYMENT`);
      console.log(`💸 Need to add: ${(requiredBalance - parseFloat(balanceEth)).toFixed(6)} ${getNativeCurrency(network)}`);
      console.log(`💰 Estimated cost: $${getEstimatedCost(network)}`);
      console.log(`\n🔗 Fund your wallet:`);
      console.log(`   Address: ${deployer.address}`);
      console.log(`   Explorer: ${getExplorerUrl(network, deployer.address)}`);
      console.log(`\n📖 See FUNDING-GUIDE.md for detailed instructions`);
    } else {
      console.log(`\n✅ READY FOR DEPLOYMENT!`);
      console.log(`🚀 Run: npx hardhat run scripts/deploy.cjs --network ${network}`);
    }
    
    // Show gas price info
    try {
      const gasPrice = await ethers.provider.getFeeData();
      console.log(`\n⛽ Current Gas Price: ${ethers.formatUnits(gasPrice.gasPrice || 0, 'gwei')} gwei`);
    } catch (error) {
      console.log(`\n⛽ Gas Price: Unable to fetch`);
    }
    
  } catch (error) {
    console.error(`❌ Error checking balance:`, error.message);
  }
}

function getNativeCurrency(network) {
  const currencies = {
    ethereum: 'ETH',
    sepolia: 'ETH',
    bsc: 'BNB',
    bscTestnet: 'BNB',
    polygon: 'MATIC',
    polygonMumbai: 'MATIC',
    avalanche: 'AVAX',
    arbitrum: 'ETH',
    optimism: 'ETH'
  };
  return currencies[network] || 'ETH';
}

function getRequiredBalance(network) {
  const requirements = {
    ethereum: 0.01,      // ~$30-50
    sepolia: 0.01,       // Testnet
    bsc: 0.01,           // ~$3-5
    bscTestnet: 0.01,    // Testnet
    polygon: 0.1,        // ~$0.10
    polygonMumbai: 0.1,  // Testnet
    avalanche: 0.01,     // ~$0.50
    arbitrum: 0.005,     // ~$15-25
    optimism: 0.005      // ~$15-25
  };
  return requirements[network] || 0.01;
}

function getEstimatedCost(network) {
  const costs = {
    ethereum: '$15-45',
    sepolia: 'Free (testnet)',
    bsc: '$1-3',
    bscTestnet: 'Free (testnet)',
    polygon: '$0.05-0.25',
    polygonMumbai: 'Free (testnet)',
    avalanche: '$0.25-1',
    arbitrum: '$5-15',
    optimism: '$5-15'
  };
  return costs[network] || '$10-30';
}

function getExplorerUrl(network, address) {
  const explorers = {
    ethereum: `https://etherscan.io/address/${address}`,
    sepolia: `https://sepolia.etherscan.io/address/${address}`,
    bsc: `https://bscscan.com/address/${address}`,
    bscTestnet: `https://testnet.bscscan.com/address/${address}`,
    polygon: `https://polygonscan.com/address/${address}`,
    polygonMumbai: `https://mumbai.polygonscan.com/address/${address}`,
    avalanche: `https://snowtrace.io/address/${address}`,
    arbitrum: `https://arbiscan.io/address/${address}`,
    optimism: `https://optimistic.etherscan.io/address/${address}`
  };
  return explorers[network] || `Unknown explorer for ${network}`;
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Balance check failed:", error);
    process.exit(1);
  });
