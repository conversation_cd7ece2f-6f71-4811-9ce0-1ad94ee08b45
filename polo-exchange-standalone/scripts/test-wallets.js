#!/usr/bin/env node

/**
 * Polo Exchange Wallet Testing Script
 * Tests wallet connectivity and balance checking
 */

import dotenv from 'dotenv';
import { ethers } from 'ethers';
import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import fetch from 'node-fetch';

dotenv.config();

console.log('🧪 Testing Polo Exchange Wallet Configuration\n');

// Test EVM wallet connectivity
async function testEVMWallet(network, config) {
  try {
    console.log(`🔗 Testing ${network.toUpperCase()} wallet...`);

    if (!config.privateKey) {
      console.log(`   ❌ Private key not configured`);
      return false;
    }

    // Create provider and wallet
    const provider = new ethers.JsonRpcProvider(config.rpcUrl);
    const wallet = new ethers.Wallet(config.privateKey, provider);

    console.log(`   📍 Address: ${wallet.address}`);

    // Test connection
    const blockNumber = await provider.getBlockNumber();
    console.log(`   🔗 Connected to block: ${blockNumber}`);

    // Get balance
    const balance = await provider.getBalance(wallet.address);
    const balanceEth = ethers.formatEther(balance);
    console.log(`   💰 Balance: ${balanceEth} ${config.nativeCurrency}`);

    // Check if wallet has funds for gas
    const hasGas = parseFloat(balanceEth) > 0.001;
    console.log(`   ⛽ Gas available: ${hasGas ? '✅' : '❌'}`);

    console.log(`   ✅ ${network.toUpperCase()} wallet test passed\n`);
    return true;

  } catch (error) {
    console.log(`   ❌ ${network.toUpperCase()} wallet test failed:`, error.message);
    console.log();
    return false;
  }
}

// Test Solana wallet connectivity
async function testSolanaWallet() {
  try {
    console.log('☀️ Testing SOLANA wallet...');

    const privateKey = process.env.SOLANA_PRIVATE_KEY;
    if (!privateKey) {
      console.log('   ❌ Private key not configured');
      return false;
    }

    // Create connection and keypair
    const rpcUrl = process.env.SOLANA_RPC_URL || 'https://go.getblock.io/657dc00c73c84600906bfb1c02953e32';
    const connection = new Connection(rpcUrl);
    
    // Parse private key (handle both base64 and array formats)
    let secretKey;
    try {
      secretKey = Buffer.from(privateKey, 'base64');
    } catch {
      // Try parsing as JSON array
      secretKey = new Uint8Array(JSON.parse(privateKey));
    }

    // Keypair already imported at top
    const keypair = Keypair.fromSecretKey(secretKey);

    console.log(`   📍 Address: ${keypair.publicKey.toString()}`);

    // Test connection
    const slot = await connection.getSlot();
    console.log(`   🔗 Connected to slot: ${slot}`);

    // Get balance
    const balance = await connection.getBalance(keypair.publicKey);
    const balanceSol = balance / 1e9; // Convert lamports to SOL
    console.log(`   💰 Balance: ${balanceSol} SOL`);

    // Check if wallet has funds for gas
    const hasGas = balanceSol > 0.01;
    console.log(`   ⛽ Gas available: ${hasGas ? '✅' : '❌'}`);

    console.log('   ✅ SOLANA wallet test passed\n');
    return true;

  } catch (error) {
    console.log('   ❌ SOLANA wallet test failed:', error.message);
    console.log();
    return false;
  }
}

// Test LiFi API connectivity
async function testLiFiAPI() {
  try {
    console.log('🌉 Testing LiFi API connectivity...');

    const apiKey = process.env.LIFI_API_KEY;
    if (!apiKey) {
      console.log('   ❌ LiFi API key not configured');
      return false;
    }

    console.log(`   🔑 API Key: ${apiKey.substring(0, 20)}...`);

    // Test basic API call
    // fetch already imported at top
    const response = await fetch('https://li.quest/v1/chains', {
      headers: {
        'x-lifi-api-key': apiKey,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const chains = await response.json();
    console.log(`   🌐 Available chains: ${chains.chains?.length || 0}`);

    // Test quote API
    const quoteResponse = await fetch(
      'https://li.quest/v1/quote?' + new URLSearchParams({
        fromChain: '1',
        toChain: '56',
        fromToken: '0x0000000000000000000000000000000000000000',
        toToken: '0x0000000000000000000000000000000000000000',
        fromAmount: '1000000000000000000',
        fromAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0',
        toAddress: '0x552008c0f6870c2f77e5cC1d2eb9bdff03e30Ea0'
      }),
      {
        headers: {
          'x-lifi-api-key': apiKey,
          'Accept': 'application/json'
        }
      }
    );

    if (quoteResponse.ok) {
      const quote = await quoteResponse.json();
      console.log(`   💱 Quote test: ${quote.estimate ? '✅' : '❌'}`);
    } else {
      console.log('   💱 Quote test: ❌');
    }

    console.log('   ✅ LiFi API test passed\n');
    return true;

  } catch (error) {
    console.log('   ❌ LiFi API test failed:', error.message);
    console.log();
    return false;
  }
}

// Generate test summary
function generateTestSummary(results) {
  console.log('📊 Test Summary:');
  console.log('=' .repeat(50));

  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  console.log(`Overall: ${passed}/${total} tests passed\n`);

  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });

  if (passed === total) {
    console.log('\n🎉 All tests passed! Your exchange is ready for operation.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check your configuration.');
  }

  console.log('\n💡 Next Steps:');
  if (passed < total) {
    console.log('   1. Fix failed wallet configurations');
    console.log('   2. Fund wallets with operational amounts');
    console.log('   3. Re-run this test script');
  } else {
    console.log('   1. Start your LiFi backend server');
    console.log('   2. Test swap execution with small amounts');
    console.log('   3. Monitor wallet balances during operation');
  }
}

// Main test execution
async function main() {
  const results = [];

  // Test EVM wallets
  const evmNetworks = [
    { name: 'ethereum', rpcUrl: process.env.ETHEREUM_RPC_URL, privateKey: process.env.ETHEREUM_PRIVATE_KEY, nativeCurrency: 'ETH' },
    { name: 'bsc', rpcUrl: process.env.BSC_RPC_URL, privateKey: process.env.BSC_PRIVATE_KEY, nativeCurrency: 'BNB' },
    { name: 'polygon', rpcUrl: process.env.POLYGON_RPC_URL, privateKey: process.env.POLYGON_PRIVATE_KEY, nativeCurrency: 'MATIC' },
    { name: 'avalanche', rpcUrl: process.env.AVALANCHE_RPC_URL, privateKey: process.env.AVALANCHE_PRIVATE_KEY, nativeCurrency: 'AVAX' },
    { name: 'arbitrum', rpcUrl: process.env.ARBITRUM_RPC_URL, privateKey: process.env.ARBITRUM_PRIVATE_KEY, nativeCurrency: 'ETH' },
    { name: 'optimism', rpcUrl: process.env.OPTIMISM_RPC_URL, privateKey: process.env.OPTIMISM_PRIVATE_KEY, nativeCurrency: 'ETH' }
  ];

  for (const network of evmNetworks) {
    const passed = await testEVMWallet(network.name, network);
    results.push({ name: `${network.name.toUpperCase()} Wallet`, passed });
  }

  // Test Solana wallet
  const solanaResult = await testSolanaWallet();
  results.push({ name: 'SOLANA Wallet', passed: solanaResult });

  // Test LiFi API
  const lifiResult = await testLiFiAPI();
  results.push({ name: 'LiFi API', passed: lifiResult });

  // Generate summary
  generateTestSummary(results);
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

export { testEVMWallet, testSolanaWallet, testLiFiAPI };
