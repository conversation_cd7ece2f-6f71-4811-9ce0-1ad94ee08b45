#!/usr/bin/env node

/**
 * SUPER EASY Trust Wallet Setup
 * Just copy/paste your addresses - no complex automation needed!
 */

const fs = require('fs');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function easySetup() {
  console.log('🎯 SUPER EASY TRUST WALLET SETUP');
  console.log('================================');
  console.log('');
  console.log('This is the EASIEST way to integrate your Trust Wallet!');
  console.log('Just copy/paste a few addresses and you\'re done.');
  console.log('');

  // Get the essential addresses
  console.log('📱 Step 1: Get Your Trust Wallet Addresses');
  console.log('------------------------------------------');
  console.log('');
  console.log('Open your Trust Wallet app and copy these addresses:');
  console.log('');

  // Bitcoin
  console.log('🟠 Bitcoin (BTC):');
  console.log('   Tap Bitcoin → Receive → Copy address');
  const btcAddress = await question('   Paste your BTC address: ');
  console.log('');

  // Ethereum (handles ETH, USDC, USDT, etc.)
  console.log('🔵 Ethereum (ETH):');
  console.log('   Tap Ethereum → Receive → Copy address');
  console.log('   (This same address works for USDC, USDT, and all ERC-20 tokens)');
  const ethAddress = await question('   Paste your ETH address: ');
  console.log('');

  // Solana
  console.log('🟣 Solana (SOL):');
  console.log('   Tap Solana → Receive → Copy address');
  const solAddress = await question('   Paste your SOL address: ');
  console.log('');

  // BNB (optional but recommended)
  console.log('🟡 BNB - Choose your network (optional):');
  console.log('   Option 1: Binance Chain (BEP-2) - starts with "bnb1"');
  console.log('   Option 2: BSC Smart Chain (BEP-20) - starts with "0x"');
  console.log('   Tap your preferred BNB network → Receive → Copy address');
  const bnbAddress = await question('   Paste your BNB address (or press Enter to skip): ');
  console.log('');

  // Cardano (optional)
  console.log('🔵 Cardano ADA (optional):');
  console.log('   Tap Cardano → Receive → Copy address');
  const adaAddress = await question('   Paste your ADA address (or press Enter to skip): ');
  console.log('');

  // Litecoin (optional)
  console.log('🔶 Litecoin LTC (optional):');
  console.log('   Tap Litecoin → Receive → Copy address');
  const ltcAddress = await question('   Paste your LTC address (or press Enter to skip): ');
  console.log('');

  // Generate the configuration
  console.log('⚙️ Step 2: Generating Your Configuration');
  console.log('----------------------------------------');
  console.log('');

  const config = `
# ===== EASY TRUST WALLET SETUP =====
# Generated on ${new Date().toISOString()}

# Your Trust Wallet addresses (KEEP THESE PRIVATE!)
TRUST_WALLET_BTC=${btcAddress}
TRUST_WALLET_ETH=${ethAddress}
TRUST_WALLET_SOL=${solAddress}
${bnbAddress ? `TRUST_WALLET_BNB=${bnbAddress}` : '# TRUST_WALLET_BNB=YourBNBAddress'}

# ERC-20 tokens use the same ETH address
TRUST_WALLET_USDC=${ethAddress}
TRUST_WALLET_USDT=${ethAddress}
TRUST_WALLET_WLFI=${ethAddress}
TRUST_WALLET_CRV=${ethAddress}
TRUST_WALLET_AAVE=${ethAddress}

# BSC tokens use the same BNB address
${bnbAddress ? `TRUST_WALLET_BUSD=${bnbAddress}` : '# TRUST_WALLET_BUSD=YourBNBAddress'}

# Cardano
${adaAddress ? `TRUST_WALLET_ADA=${adaAddress}` : '# TRUST_WALLET_ADA=YourCardanoAddress'}

# Litecoin
${ltcAddress ? `TRUST_WALLET_LTC=${ltcAddress}` : '# TRUST_WALLET_LTC=YourLitecoinAddress'}

# Exchange settings
EXCHANGE_FEE_RATE=0.03
AUTO_MONITORING=true
`;

  // Save to .env file
  fs.writeFileSync('.env.trustwallet', config);
  
  console.log('✅ Configuration saved to .env.trustwallet');
  console.log('');

  // Show what was configured
  console.log('📋 Your Exchange Configuration:');
  console.log('------------------------------');
  console.log(`🟠 Bitcoin deposits go to: ${btcAddress}`);
  console.log(`🔵 Ethereum/ERC-20 deposits go to: ${ethAddress}`);
  console.log(`🟣 Solana deposits go to: ${solAddress}`);
  if (bnbAddress) {
    const bnbType = bnbAddress.startsWith('bnb1') ? 'BNB (Binance Chain)' : 'BNB (BSC)';
    console.log(`🟡 ${bnbType} deposits go to: ${bnbAddress}`);
  }
  if (adaAddress) {
    console.log(`🔵 Cardano/ADA deposits go to: ${adaAddress}`);
  }
  if (ltcAddress) {
    console.log(`🔶 Litecoin/LTC deposits go to: ${ltcAddress}`);
  }
  console.log('💰 Fee rate: 3% per swap');
  console.log('');

  // Test the addresses
  console.log('🧪 Step 3: Testing Your Setup');
  console.log('-----------------------------');
  console.log('');

  const testResults = [];

  // Test BTC address
  if (btcAddress.startsWith('bc1') || btcAddress.startsWith('1') || btcAddress.startsWith('3')) {
    testResults.push('✅ Bitcoin address looks valid');
  } else {
    testResults.push('⚠️ Bitcoin address might be invalid');
  }

  // Test ETH address
  if (ethAddress.startsWith('0x') && ethAddress.length === 42) {
    testResults.push('✅ Ethereum address looks valid');
  } else {
    testResults.push('⚠️ Ethereum address might be invalid');
  }

  // Test SOL address
  if (solAddress.length >= 32 && solAddress.length <= 44) {
    testResults.push('✅ Solana address looks valid');
  } else {
    testResults.push('⚠️ Solana address might be invalid');
  }

  // Test BNB address
  if (bnbAddress) {
    if (bnbAddress.startsWith('bnb1')) {
      testResults.push('✅ BNB (Binance Chain BEP-2) address looks valid');
    } else if (bnbAddress.startsWith('0x') && bnbAddress.length === 42) {
      testResults.push('✅ BNB (BSC BEP-20) address looks valid');
    } else {
      testResults.push('⚠️ BNB address might be invalid');
    }
  }

  // Test ADA address
  if (adaAddress) {
    if (adaAddress.startsWith('addr1')) {
      testResults.push('✅ Cardano address looks valid');
    } else {
      testResults.push('⚠️ Cardano address might be invalid');
    }
  }

  // Test LTC address
  if (ltcAddress) {
    if (ltcAddress.startsWith('ltc1') || ltcAddress.startsWith('L') || ltcAddress.startsWith('M')) {
      testResults.push('✅ Litecoin address looks valid');
    } else {
      testResults.push('⚠️ Litecoin address might be invalid');
    }
  }

  testResults.forEach(result => console.log(result));
  console.log('');

  // Generate smart integration code with detection
  const integrationCode = `
// SMART TRUST WALLET INTEGRATION
// Automatically detects wallet types and tokens

const trustWalletAddresses = {
  'BTC': '${btcAddress}',
  'ETH': '${ethAddress}',
  'USDC': '${ethAddress}', // Same as ETH (ERC-20)
  'USDT': '${ethAddress}', // Same as ETH (ERC-20)
  'WLFI': '${ethAddress}', // Same as ETH (ERC-20)
  'CRV': '${ethAddress}',  // Same as ETH (ERC-20)
  'AAVE': '${ethAddress}', // Same as ETH (ERC-20)
  'SOL': '${solAddress}',
  ${bnbAddress ? `'BNB': '${bnbAddress}',` : '// BNB: Not configured'}
  ${bnbAddress ? `'BUSD': '${bnbAddress}', // Same as BNB (BEP-20)` : '// BUSD: Not configured'}
  ${adaAddress ? `'ADA': '${adaAddress}',` : '// ADA: Not configured'}
  ${ltcAddress ? `'LTC': '${ltcAddress}',` : '// LTC: Not configured'}
};

// Smart address detection
function detectAddressType(address) {
  if (address.startsWith('bc1') || address.startsWith('1') || address.startsWith('3')) {
    return { type: 'Bitcoin', network: 'BTC' };
  }
  if (address.startsWith('ltc1') || address.startsWith('L') || address.startsWith('M')) {
    return { type: 'Litecoin', network: 'LTC' };
  }
  if (address.startsWith('0x') && address.length === 42) {
    return { type: 'EVM', network: 'ETH/BSC/Polygon/AVAX' };
  }
  if (address.startsWith('0x') && address.length === 66) {
    return { type: 'Transaction Hash', network: 'EVM/Other' };
  }
  if (address.startsWith('bnb1')) {
    return { type: 'Binance Chain', network: 'BNB (BEP-2)' };
  }
  if (address.startsWith('addr1')) {
    return { type: 'Cardano', network: 'ADA' };
  }
  if (address.startsWith('cosmos')) {
    return { type: 'Cosmos', network: 'ATOM' };
  }
  if (address.startsWith('ak_')) {
    return { type: 'Aeternity', network: 'AE' };
  }
  if (address.startsWith('UQ') || address.startsWith('EQ')) {
    return { type: 'TON', network: 'TON' };
  }
  if (address.length === 58 && /^[A-Z2-7]+$/.test(address)) {
    return { type: 'Algorand', network: 'ALGO' };
  }
  if (address.length >= 47 && address.length <= 48 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(address)) {
    return { type: 'Polkadot', network: 'DOT' };
  }
  if (address.length >= 32 && address.length <= 44 && !address.startsWith('0x') && !address.startsWith('addr1') && !address.startsWith('bnb1') && !address.startsWith('ltc1') && !address.startsWith('cosmos') && !address.startsWith('ak_')) {
    return { type: 'Solana', network: 'SOL' };
  }
  return { type: 'Unknown', network: 'Unknown' };
}

// Get deposit address for any currency
function getDepositAddress(currency) {
  const address = trustWalletAddresses[currency.toUpperCase()];
  if (address) {
    const detection = detectAddressType(address);
    console.log(\`💰 \${currency} deposits go to \${detection.type} address: \${address}\`);
    return address;
  }
  return null;
}

// Smart token detection from transaction
function detectTokenFromTransaction(toAddress, amount, txHash) {
  // Find which currency this address belongs to
  for (const [currency, address] of Object.entries(trustWalletAddresses)) {
    if (address === toAddress) {
      const detection = detectAddressType(address);
      console.log(\`🎯 Detected: \${amount} \${currency} deposit to \${detection.type} wallet\`);
      console.log(\`📍 TX: \${txHash}\`);
      console.log(\`🏦 Network: \${detection.network}\`);
      return {
        currency,
        amount,
        network: detection.network,
        addressType: detection.type,
        txHash
      };
    }
  }
  return null;
}

// Usage examples:
// const btcAddress = getDepositAddress('BTC');
// const ethAddress = getDepositAddress('ETH');
// const usdcAddress = getDepositAddress('USDC'); // Returns same as ETH

// When deposit detected:
// const deposit = detectTokenFromTransaction('${ethAddress}', '100', '0x123...');
// Result: { currency: 'USDC', amount: '100', network: 'ETH/BSC/Polygon', addressType: 'EVM' }
`;

  fs.writeFileSync('trust-wallet-integration.js', integrationCode);
  console.log('💾 Integration code saved to trust-wallet-integration.js');
  console.log('');

  // Final instructions
  console.log('🎉 SETUP COMPLETE!');
  console.log('==================');
  console.log('');
  console.log('Your Trust Wallet is now integrated! Here\'s what you can do:');
  console.log('');
  console.log('📱 Monitor Your Wallet:');
  console.log('   • Open Trust Wallet app anytime');
  console.log('   • See incoming deposits from users');
  console.log('   • Watch your 3% fees accumulate');
  console.log('');
  console.log('💰 Revenue Model:');
  console.log('   • Users send crypto to YOUR addresses');
  console.log('   • You keep 3% fee automatically');
  console.log('   • Execute swaps via LiFi');
  console.log('   • Send results to users');
  console.log('');
  console.log('🔧 Next Steps:');
  console.log('   1. Test with small amounts first');
  console.log('   2. Monitor deposits in Trust Wallet app');
  console.log('   3. Scale up as you gain confidence');
  console.log('');
  console.log('📁 Files Created:');
  console.log('   • .env.trustwallet (your configuration)');
  console.log('   • trust-wallet-integration.js (integration code)');
  console.log('');
  console.log('⚠️ SECURITY REMINDER:');
  console.log('   • Keep your addresses private');
  console.log('   • Never share your private keys');
  console.log('   • Monitor your wallet regularly');
  console.log('');

  const startNow = await question('Would you like to start your exchange now? (y/n): ');
  
  if (startNow.toLowerCase() === 'y') {
    console.log('');
    console.log('🚀 Starting your exchange...');
    console.log('');
    console.log('Your exchange is now ready to:');
    console.log('✅ Accept deposits to your Trust Wallet addresses');
    console.log('✅ Generate 3% profit on every swap');
    console.log('✅ Scale to unlimited volume');
    console.log('');
    console.log('💰 Start promoting your exchange and watch the profits roll in!');
  } else {
    console.log('');
    console.log('👍 No problem! Your setup is saved and ready when you are.');
    console.log('');
    console.log('Run this script again anytime to update your addresses.');
  }

  rl.close();
}

// Run the easy setup
easySetup().catch(console.error);
