<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INK Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .logo-test {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        .logo-test img {
            width: 32px;
            height: 32px;
            margin-right: 10px;
            border-radius: 50%;
        }
        .status {
            margin-left: auto;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        .success { background: #22c55e; }
        .error { background: #ef4444; }
        .loading { background: #f59e0b; }
    </style>
</head>
<body>
    <h1>🔷 INK Layer 2 Logo Test</h1>
    <p>Testing if the Ink Layer 2 logo loads correctly from CoinMarketCap:</p>

    <div class="logo-test">
        <img id="ink-logo" src="https://s2.coinmarketcap.com/static/img/coins/64x64/36851.png" alt="INK Logo">
        <div>
            <strong>INK (Ink Layer 2)</strong><br>
            <small>Kraken's Layer 2 Solution</small>
        </div>
        <div id="ink-status" class="status loading">Loading...</div>
    </div>

    <div class="logo-test">
        <img id="wlfi-logo" src="https://static.worldlibertyfinancial.com/assets/brand/wlfi.svg" alt="WLFI Logo">
        <div>
            <strong>WLFI (World Liberty Financial)</strong><br>
            <small>Comparison Test</small>
        </div>
        <div id="wlfi-status" class="status loading">Loading...</div>
    </div>

    <div class="logo-test">
        <img id="btc-logo" src="https://s2.coinmarketcap.com/static/img/coins/64x64/1.png" alt="BTC Logo">
        <div>
            <strong>BTC (Bitcoin)</strong><br>
            <small>Control Test</small>
        </div>
        <div id="btc-status" class="status loading">Loading...</div>
    </div>

    <h2>🔧 Troubleshooting</h2>
    <div id="troubleshooting">
        <p>If the INK logo doesn't load:</p>
        <ul>
            <li>Check browser console for errors</li>
            <li>Try clearing browser cache</li>
            <li>Verify CoinMarketCap URL is accessible</li>
            <li>Check if CORS is blocking the request</li>
        </ul>
    </div>

    <h2>📊 Logo URLs</h2>
    <div style="background: #2a2a2a; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
        <div><strong>INK:</strong> https://s2.coinmarketcap.com/static/img/coins/64x64/36851.png</div>
        <div><strong>WLFI:</strong> https://static.worldlibertyfinancial.com/assets/brand/wlfi.svg</div>
        <div><strong>BTC:</strong> https://s2.coinmarketcap.com/static/img/coins/64x64/1.png</div>
    </div>

    <script>
        // Test logo loading
        function testLogo(id, statusId) {
            const img = document.getElementById(id);
            const status = document.getElementById(statusId);
            
            img.onload = function() {
                status.textContent = '✅ Loaded';
                status.className = 'status success';
                console.log(`✅ ${id} logo loaded successfully`);
            };
            
            img.onerror = function() {
                status.textContent = '❌ Failed';
                status.className = 'status error';
                console.error(`❌ ${id} logo failed to load`);
            };
            
            // Force reload to test
            img.src = img.src + '?t=' + Date.now();
        }

        // Test all logos
        testLogo('ink-logo', 'ink-status');
        testLogo('wlfi-logo', 'wlfi-status');
        testLogo('btc-logo', 'btc-status');

        // Log browser info
        console.log('🔍 Browser Info:', {
            userAgent: navigator.userAgent,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        });
    </script>
</body>
</html>
