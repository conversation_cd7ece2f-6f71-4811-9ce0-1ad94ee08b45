# Marko Polo Capital - Instant Liquidity Provider API Keys
# Copy this file to .env and add your real API keys

# =============================================================================
# INSTANT LIQUIDITY PROVIDERS
# =============================================================================

# 1inch DEX Aggregator (Ethereum, BSC, Polygon, Arbitrum)
# Get your API key from: https://portal.1inch.dev/
# Free tier: 100 requests/second
ONEINCH_API_KEY=your_1inch_api_key_here

# ChangeNOW Cross-Chain Swaps (BTC, ETH, SOL, 200+ coins)
# Get your API key from: https://changenow.io/api
# Free tier: 100 requests/month
CHANGENOW_API_KEY=your_changenow_api_key_here

# 0x Protocol Professional Liquidity (Ethereum, Polygon, BSC)
# Get your API key from: https://0x.org/docs/api
# Free tier: 100,000 requests/month
ZEROX_API_KEY=your_0x_api_key_here

# ParaSwap Multi-Chain DEX Aggregator
# Get your API key from: https://developers.paraswap.network/
# Free tier: No API key required for basic usage
PARASWAP_API_KEY=your_paraswap_api_key_here

# KyberSwap Liquidity Aggregator
# Get your API key from: https://docs.kyberswap.com/
KYBERSWAP_API_KEY=your_kyberswap_api_key_here

# =============================================================================
# FREE PROVIDERS (NO API KEY REQUIRED)
# =============================================================================

# Jupiter (Solana) - FREE, no API key needed
# Supports: SOL, USDC, USDT, RAY, SRM, ORCA, MNGO
# Rate limit: 600 requests/minute

# ParaSwap - FREE for basic quotes
# Supports: ETH, USDC, USDT, DAI, WBTC
# Rate limit: 5 requests/second

# Uniswap V3 - FREE via The Graph
# Supports: ETH, USDC, USDT, DAI, WBTC, UNI
# Rate limit: 1000 requests/day

# =============================================================================
# BLOCKCHAIN RPC ENDPOINTS
# =============================================================================

# Ethereum Mainnet
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/your_alchemy_key

# Solana Mainnet (for Jupiter integration)
SOLANA_RPC_URL=https://go.getblock.io/657dc00c73c84600906bfb1c02953e32

# BSC Mainnet
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# Polygon Mainnet
POLYGON_RPC_URL=https://polygon-rpc.com/

# =============================================================================
# WALLET INTEGRATION
# =============================================================================

# Phantom Wallet (Solana)
PHANTOM_APP_ID=your_phantom_app_id

# MetaMask/WalletConnect Project ID
WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Environment
NODE_ENV=development

# Server Port
PORT=3000

# Enable debug logging
DEBUG=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT Secret for API authentication
JWT_SECRET=your_super_secret_jwt_key_here

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# Optional: Analytics tracking
ANALYTICS_API_KEY=your_analytics_key

# Optional: Error monitoring (Sentry)
SENTRY_DSN=your_sentry_dsn

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env
# 2. Sign up for API keys from the providers you want to use
# 3. Replace the placeholder values with your real API keys
# 4. Restart your development server

# PRIORITY ORDER (start with these):
# 1. Jupiter (FREE) - Works immediately for Solana tokens
# 2. ParaSwap (FREE) - Works immediately for Ethereum tokens  
# 3. 1inch (PAID) - Best rates for Ethereum ecosystem
# 4. ChangeNOW (PAID) - Cross-chain swaps
# 5. 0x Protocol (PAID) - Professional-grade liquidity
