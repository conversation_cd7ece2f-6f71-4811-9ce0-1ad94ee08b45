# Trust Wallet Configuration for Polo Exchange
# Replace these with your actual Trust Wallet addresses

# ===== TRUST WALLET ADDRESSES (Your Exchange's Operational Wallets) =====

# Ethereum & ERC-20 Tokens
TRUST_WALLET_ETH=0xYourTrustWalletEthereumAddress
TRUST_WALLET_ETH_PRIVATE_KEY=0xYourTrustWalletEthereumPrivateKey

# Binance Smart Chain & BEP-20 Tokens  
TRUST_WALLET_BNB=0xYourTrustWalletBSCAddress
TRUST_WALLET_BNB_PRIVATE_KEY=0xYourTrustWalletBSCPrivateKey

# Polygon & Polygon Tokens
TRUST_WALLET_MATIC=0xYourTrustWalletPolygonAddress
TRUST_WALLET_MATIC_PRIVATE_KEY=0xYourTrustWalletPolygonPrivateKey

# Avalanche & AVAX Tokens
TRUST_WALLET_AVAX=0xYourTrustWalletAvalancheAddress
TRUST_WALLET_AVAX_PRIVATE_KEY=0xYourTrustWalletAvalanchePrivateKey

# Solana & SPL Tokens
TRUST_WALLET_SOL=YourTrustWalletSolanaAddress
TRUST_WALLET_SOL_PRIVATE_KEY=YourTrustWalletSolanaPrivateKey

# Bitcoin
TRUST_WALLET_BTC=YourTrustWalletBitcoinAddress
TRUST_WALLET_BTC_PRIVATE_KEY=YourTrustWalletBitcoinPrivateKey

# Litecoin
TRUST_WALLET_LTC=YourTrustWalletLitecoinAddress
TRUST_WALLET_LTC_PRIVATE_KEY=YourTrustWalletLitecoinPrivateKey

# Dogecoin
TRUST_WALLET_DOGE=YourTrustWalletDogecoinAddress
TRUST_WALLET_DOGE_PRIVATE_KEY=YourTrustWalletDogecoinPrivateKey

# Cardano
TRUST_WALLET_ADA=YourTrustWalletCardanoAddress
TRUST_WALLET_ADA_PRIVATE_KEY=YourTrustWalletCardanoPrivateKey

# Polkadot
TRUST_WALLET_DOT=YourTrustWalletPolkadotAddress
TRUST_WALLET_DOT_PRIVATE_KEY=YourTrustWalletPolkadotPrivateKey

# Cosmos
TRUST_WALLET_ATOM=YourTrustWalletCosmosAddress
TRUST_WALLET_ATOM_PRIVATE_KEY=YourTrustWalletCosmosPrivateKey

# Tron
TRUST_WALLET_TRX=YourTrustWalletTronAddress
TRUST_WALLET_TRX_PRIVATE_KEY=YourTrustWalletTronPrivateKey

# Ripple
TRUST_WALLET_XRP=YourTrustWalletRippleAddress
TRUST_WALLET_XRP_PRIVATE_KEY=YourTrustWalletRipplePrivateKey

# Toncoin
TRUST_WALLET_TON=YourTrustWalletToncoinAddress
TRUST_WALLET_TON_PRIVATE_KEY=YourTrustWalletToncoinPrivateKey

# Sui
TRUST_WALLET_SUI=YourTrustWalletSuiAddress
TRUST_WALLET_SUI_PRIVATE_KEY=YourTrustWalletSuiPrivateKey

# Aptos
TRUST_WALLET_APT=YourTrustWalletAptosAddress
TRUST_WALLET_APT_PRIVATE_KEY=YourTrustWalletAptosPrivateKey

# NEAR Protocol
TRUST_WALLET_NEAR=YourTrustWalletNearAddress
TRUST_WALLET_NEAR_PRIVATE_KEY=YourTrustWalletNearPrivateKey

# Filecoin
TRUST_WALLET_FIL=YourTrustWalletFilecoinAddress
TRUST_WALLET_FIL_PRIVATE_KEY=YourTrustWalletFilecoinPrivateKey

# VeChain
TRUST_WALLET_VET=YourTrustWalletVeChainAddress
TRUST_WALLET_VET_PRIVATE_KEY=YourTrustWalletVeChainPrivateKey

# Algorand
TRUST_WALLET_ALGO=YourTrustWalletAlgorandAddress
TRUST_WALLET_ALGO_PRIVATE_KEY=YourTrustWalletAlgorandPrivateKey

# Hedera
TRUST_WALLET_HBAR=YourTrustWalletHederaAddress
TRUST_WALLET_HBAR_PRIVATE_KEY=YourTrustWalletHederaPrivateKey

# Internet Computer
TRUST_WALLET_ICP=YourTrustWalletInternetComputerAddress
TRUST_WALLET_ICP_PRIVATE_KEY=YourTrustWalletInternetComputerPrivateKey

# Bittensor
TRUST_WALLET_TAO=YourTrustWalletBittensorAddress
TRUST_WALLET_TAO_PRIVATE_KEY=YourTrustWalletBittensorPrivateKey

# ===== LiFi CONFIGURATION =====
LIFI_API_KEY=e7535464-9b0a-43a9-8e91-92eb4b49b964.d17c3d47-942c-4253-9f18-456df12ca70e
LIFI_INTEGRATOR=marko-polo-capital

# ===== POLO SWAP CONFIGURATION =====
POLO_SWAP_FEE_RATE=0.03
POLO_SWAP_MIN_AMOUNT=20
POLO_SWAP_MAX_AMOUNT=49000

# ===== SECURITY SETTINGS =====
# Master password for encrypting private keys
MASTER_PASSWORD=YourSecureMasterPasswordHere

# ===== INSTRUCTIONS =====
# 1. Replace all "YourTrustWallet..." placeholders with your actual Trust Wallet addresses
# 2. Add your private keys securely (consider using encrypted storage)
# 3. Test with small amounts first
# 4. Monitor deposits and swaps carefully
# 5. Keep private keys secure and never share them

# ===== TRUST WALLET SETUP GUIDE =====
# 1. Open Trust Wallet app
# 2. Go to Settings > Wallets
# 3. For each blockchain you want to support:
#    - Add the blockchain if not already added
#    - Copy the receive address
#    - Export the private key (keep secure!)
# 4. Update this file with your addresses
# 5. Test the integration with small amounts

# ===== SUPPORTED BLOCKCHAINS =====
# Trust Wallet supports 70+ blockchains including:
# - Ethereum & all ERC-20 tokens
# - Binance Smart Chain & BEP-20 tokens
# - Bitcoin & Bitcoin-based chains
# - Solana & SPL tokens
# - Polygon, Avalanche, Arbitrum, Optimism
# - Cardano, Polkadot, Cosmos, NEAR
# - And many more...

# ===== LIFI INTEGRATION BENEFITS =====
# - Access to $2B+ liquidity across 20+ chains
# - Best rates through aggregated DEXs
# - No need to maintain your own liquidity
# - Automatic route optimization
# - Cross-chain swaps with unlimited combinations
# - Zero capital risk for your exchange
