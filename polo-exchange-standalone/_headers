# Security headers for Cloudflare Pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.coingecko.com https://pro-api.coinmarketcap.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.coingecko.com https://pro-api.coinmarketcap.com https://go.getblock.io wss://; font-src 'self' data:; object-src 'none'; base-uri 'self'; form-action 'self';

# Cache static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
*.jpg
  Cache-Control: public, max-age=31536000
*.png
  Cache-Control: public, max-age=31536000
*.svg
  Cache-Control: public, max-age=31536000
*.webp
  Cache-Control: public, max-age=31536000

# Don't cache HTML files
*.html
  Cache-Control: public, max-age=0, must-revalidate
