#!/usr/bin/env node

/**
 * Test NEAR Token Configuration
 * Quick test to verify NEAR is properly configured
 */

// Import the tokens configuration
import { ALL_TOKENS } from './src/config/tokens.js';

console.log('🔍 TESTING NEAR TOKEN CONFIGURATION');
console.log('='.repeat(50));

console.log('\n📊 Total tokens loaded:', Object.keys(ALL_TOKENS).length);
console.log('📋 All token symbols:', Object.keys(ALL_TOKENS).join(', '));

console.log('\n🎯 Looking for NEAR...');
if (ALL_TOKENS.NEAR) {
  console.log('✅ NEAR FOUND in ALL_TOKENS!');
  console.log('📄 NEAR configuration:');
  console.log(JSON.stringify(ALL_TOKENS.NEAR, null, 2));
} else {
  console.log('❌ NEAR NOT FOUND in ALL_TOKENS');
  console.log('🔍 Available tokens:', Object.keys(ALL_TOKENS));
}

console.log('\n🔍 Checking for similar tokens...');
const nearLike = Object.keys(ALL_TOKENS).filter(symbol => 
  symbol.toLowerCase().includes('near') || 
  ALL_TOKENS[symbol].name.toLowerCase().includes('near')
);

if (nearLike.length > 0) {
  console.log('📋 Found NEAR-like tokens:', nearLike);
  nearLike.forEach(symbol => {
    console.log(`   ${symbol}: ${ALL_TOKENS[symbol].name}`);
  });
} else {
  console.log('❌ No NEAR-like tokens found');
}

console.log('\n🎯 TIER 3 TOKENS (where NEAR should be):');
import { TIER_3_TOKENS } from './src/config/tokens.js';
console.log('📊 TIER 3 tokens:', Object.keys(TIER_3_TOKENS).length);
console.log('📋 TIER 3 symbols:', Object.keys(TIER_3_TOKENS).join(', '));

if (TIER_3_TOKENS.NEAR) {
  console.log('✅ NEAR found in TIER_3_TOKENS');
} else {
  console.log('❌ NEAR NOT found in TIER_3_TOKENS');
}

console.log('\n🔧 DIAGNOSIS:');
if (ALL_TOKENS.NEAR && TIER_3_TOKENS.NEAR) {
  console.log('✅ NEAR is properly configured in both ALL_TOKENS and TIER_3_TOKENS');
  console.log('🎯 Issue is likely in frontend rendering or browser cache');
  console.log('💡 Try hard refresh (Ctrl+F5) or incognito mode');
} else if (TIER_3_TOKENS.NEAR && !ALL_TOKENS.NEAR) {
  console.log('⚠️ NEAR in TIER_3 but not in ALL_TOKENS - export issue');
} else if (!TIER_3_TOKENS.NEAR) {
  console.log('❌ NEAR not in TIER_3_TOKENS - configuration issue');
} else {
  console.log('🤔 Unexpected configuration state');
}

console.log('\n🚀 NEXT STEPS:');
if (ALL_TOKENS.NEAR) {
  console.log('1. ✅ Backend configuration is correct');
  console.log('2. 🔄 Hard refresh browser (Ctrl+F5 or Cmd+Shift+R)');
  console.log('3. 🌐 Try incognito/private browsing mode');
  console.log('4. 🔍 Check browser console for debug messages');
  console.log('5. 📱 Look in currency dropdown - scroll down to find NEAR');
} else {
  console.log('1. ❌ Fix backend configuration first');
  console.log('2. 🔧 Check tokens.ts file structure');
  console.log('3. 🔄 Restart frontend after fixing');
}
